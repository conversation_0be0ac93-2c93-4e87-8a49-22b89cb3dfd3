{"add_prefix_space": false, "added_tokens_decoder": {"0": {"content": "<s>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": true}, "1": {"content": "<pad>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": true}, "2": {"content": "<unk>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": true}, "3": {"content": "</s>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": true}, "4": {"content": "<mask>", "lstrip": true, "normalized": true, "rstrip": false, "single_word": false, "special": true}}, "bos_token": "<s>", "clean_up_tokenization_spaces": true, "cls_token": "<s>", "eos_token": "</s>", "errors": "replace", "mask_token": "<mask>", "model_max_length": 512, "pad_token": "<pad>", "sep_token": "</s>", "tokenizer_class": "<PERSON><PERSON><PERSON><PERSON>", "trim_offsets": true, "unk_token": "<unk>"}