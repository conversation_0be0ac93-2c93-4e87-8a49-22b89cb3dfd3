"""
Configuration management with Pydantic validation and environment variable substitution.
"""

import os
import re
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field, validator


class ModelConfig(BaseModel):
    """Model configuration."""
    encoder_weights: str = Field(..., description="Path to encoder weights")
    max_length: int = Field(512, description="Maximum sequence length")
    num_labels: Dict[str, int] = Field(..., description="Number of labels per task")


class TrainingConfig(BaseModel):
    """Training configuration."""
    epochs: int = Field(3, description="Number of training epochs")
    batch_size: int = Field(8, description="Training batch size")
    learning_rate: float = Field(2e-5, description="Learning rate")
    warmup_steps: int = Field(500, description="Warmup steps")
    weight_decay: float = Field(0.01, description="Weight decay")
    gradient_accumulation_steps: int = Field(1, description="Gradient accumulation steps")
    max_grad_norm: float = Field(1.0, description="Maximum gradient norm")
    loss_weights: Dict[str, float] = Field(default_factory=dict, description="Loss weights per task")


class APIConfig(BaseModel):
    """API server configuration."""
    host: str = Field("0.0.0.0", description="API host")
    port: int = Field(8000, description="API port")
    workers: int = Field(1, description="Number of workers")


class InferenceConfig(BaseModel):
    """Inference configuration."""
    batch_size: int = Field(16, description="Inference batch size")
    thresholds: Dict[str, float] = Field(default_factory=dict, description="Classification thresholds")
    api: APIConfig = Field(default_factory=APIConfig, description="API configuration")


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = Field("INFO", description="Logging level")
    format: str = Field(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format string"
    )

    @validator('level')
    def validate_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level: {v}. Must be one of {valid_levels}")
        return v.upper()


class Config(BaseModel):
    """Main configuration class."""
    model: ModelConfig
    training: TrainingConfig
    inference: InferenceConfig
    logging: LoggingConfig


def substitute_env_vars(data: Any) -> Any:
    """Recursively substitute environment variables in configuration data."""
    if isinstance(data, dict):
        return {key: substitute_env_vars(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [substitute_env_vars(item) for item in data]
    elif isinstance(data, str):
        # Pattern to match ${VAR} or ${VAR:-default}
        pattern = r'\$\{([^}]+)\}'

        def replace_var(match):
            var_expr = match.group(1)
            if ':-' in var_expr:
                var_name, default_value = var_expr.split(':-', 1)
                return os.getenv(var_name, default_value)
            else:
                return os.getenv(var_expr, match.group(0))  # Return original if not found

        return re.sub(pattern, replace_var, data)
    else:
        return data


def load_config(config_path: str) -> Config:
    """
    Load configuration from YAML file with environment variable substitution.

    Args:
        config_path: Path to YAML configuration file

    Returns:
        Validated configuration object

    Raises:
        FileNotFoundError: If config file doesn't exist
        yaml.YAMLError: If YAML parsing fails
        ValidationError: If configuration validation fails
    """
    config_file = Path(config_path)

    if not config_file.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            raw_config = yaml.safe_load(f)
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"Failed to parse YAML configuration: {e}")

    # Substitute environment variables
    processed_config = substitute_env_vars(raw_config)

    # Validate with Pydantic
    try:
        return Config(**processed_config)
    except Exception as e:
        raise ValueError(f"Configuration validation failed: {e}")


def get_default_config() -> Config:
    """Get default configuration."""
    return load_config('src/config/default.yaml')


# Export main classes and functions
__all__ = [
    'Config',
    'ModelConfig',
    'TrainingConfig',
    'InferenceConfig',
    'LoggingConfig',
    'APIConfig',
    'load_config',
    'get_default_config'
]