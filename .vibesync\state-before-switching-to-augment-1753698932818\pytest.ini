[tool:pytest]
# Pytest configuration for comprehensive HF integration testing

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    unit: Unit tests (fast, isolated)
    integration: Integration tests (slower, multiple components)
    slow: Slow tests (performance, benchmarking)
    wandb: Tests requiring WandB (may be skipped in CI)
    gpu: Tests requiring GPU (may be skipped on CPU-only systems)

# Test execution options
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    -p no:cacheprovider

# Timeout for slow tests (in seconds)
timeout = 300

# Minimum version requirements
minversion = 6.0

# Test filtering
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:transformers.*
    ignore::UserWarning:torch.*

# Coverage options (if pytest-cov is installed)
# addopts = --cov=src --cov-report=html --cov-report=term-missing

# Parallel execution (if pytest-xdist is installed)
# addopts = -n auto