"""
Tests for enhanced Hugging Face callbacks.

This module tests the enhanced early stopping, learning rate scheduling,
and training monitoring callbacks for RobBERT-2023 NER training.
"""

import pytest
import torch
import numpy as np
from unittest.mock import Mock, MagicMock, patch
from typing import Dict, List, Any

from transformers import TrainingArguments, TrainerState, TrainerControl
from transformers.optimization import get_scheduler

from src.training.hf_callbacks import (
    EnhancedEarlyStoppingCallback,
    LearningRateSchedulerCallback,
    TrainingMonitorCallback,
    create_enhanced_callbacks
)
from src.training.hf_config import HFTrainingConfig


class TestEnhancedEarlyStoppingCallback:
    """Test enhanced early stopping callback functionality."""
    
    def test_initialization(self):
        """Test callback initialization with default parameters."""
        callback = EnhancedEarlyStoppingCallback(
            early_stopping_patience=3,
            early_stopping_threshold=0.001,
            threshold_type="absolute",
            monitor_metrics=["eval_f1", "eval_loss"]
        )
        
        assert callback.early_stopping_patience == 3
        assert callback.early_stopping_threshold == 0.001
        assert callback.threshold_type == "absolute"
        assert callback.monitor_metrics == ["eval_f1", "eval_loss"]
        assert callback.best_metrics["eval_f1"] == float('-inf')
        assert callback.best_metrics["eval_loss"] == float('inf')
        assert callback.wait_count["eval_f1"] == 0
        assert callback.wait_count["eval_loss"] == 0
    
    def test_metric_improvement_detection_f1(self):
        """Test F1 score improvement detection."""
        callback = EnhancedEarlyStoppingCallback(
            early_stopping_patience=2,
            early_stopping_threshold=0.01,
            threshold_type="absolute",
            monitor_metrics=["eval_f1"]
        )
        
        # Test F1 improvement (higher is better)
        assert callback.check_metric_improved(0.85, 0.80, "eval_f1") == True   # 0.05 > 0.01 threshold
        assert callback.check_metric_improved(0.80, 0.85, "eval_f1") == False  # No improvement
        assert callback.check_metric_improved(0.811, 0.80, "eval_f1") == True  # 0.011 > 0.01 threshold
        assert callback.check_metric_improved(0.805, 0.80, "eval_f1") == False # 0.005 < 0.01 threshold
    
    def test_metric_improvement_detection_loss(self):
        """Test loss improvement detection."""
        callback = EnhancedEarlyStoppingCallback(
            early_stopping_patience=2,
            early_stopping_threshold=0.01,
            threshold_type="absolute",
            monitor_metrics=["eval_loss"]
        )
        
        # Test loss improvement (lower is better)
        assert callback.check_metric_improved(0.30, 0.35, "eval_loss") == True   # 0.05 > 0.01 threshold
        assert callback.check_metric_improved(0.35, 0.30, "eval_loss") == False  # No improvement
        assert callback.check_metric_improved(0.339, 0.35, "eval_loss") == True  # 0.011 > 0.01 threshold
        assert callback.check_metric_improved(0.345, 0.35, "eval_loss") == False # 0.005 < 0.01 threshold
    
    def test_relative_threshold(self):
        """Test relative threshold improvement detection."""
        callback = EnhancedEarlyStoppingCallback(
            early_stopping_patience=2,
            early_stopping_threshold=0.05,  # 5% improvement
            threshold_type="relative",
            monitor_metrics=["eval_f1"]
        )
        
        # Test relative improvement for F1 (5% of 0.80 = 0.04)
        assert callback.check_metric_improved(0.85, 0.80, "eval_f1") == True  # 6.25% improvement
        assert callback.check_metric_improved(0.83, 0.80, "eval_f1") == False  # 3.75% improvement
    
    def test_early_stopping_behavior(self):
        """Test early stopping trigger behavior."""
        callback = EnhancedEarlyStoppingCallback(
            early_stopping_patience=2,
            early_stopping_threshold=0.01,
            monitor_metrics=["eval_f1"]
        )
        
        # Mock trainer components
        args = Mock()
        state = Mock()
        state.epoch = 1
        state.global_step = 100
        control = Mock()
        control.should_training_stop = False
        
        # First evaluation - initial value (should be improvement from -inf)
        state.log_history = [{"eval_f1": 0.80}]
        callback.on_evaluate(args, state, control)
        assert control.should_training_stop == False
        assert callback.wait_count["eval_f1"] == 0  # Improved from -inf
        
        # Second evaluation - no improvement (wait_count = 1)
        state.log_history = [{"eval_f1": 0.80}, {"eval_f1": 0.79}]
        callback.on_evaluate(args, state, control)
        assert control.should_training_stop == False
        assert callback.wait_count["eval_f1"] == 1
        
        # Third evaluation - still no improvement (wait_count = 2, should stop)
        state.log_history = [{"eval_f1": 0.80}, {"eval_f1": 0.79}, {"eval_f1": 0.78}]
        callback.on_evaluate(args, state, control)
        assert control.should_training_stop == True
        assert callback.stopped_epoch == 1
    
    def test_get_stopping_summary(self):
        """Test stopping summary generation."""
        callback = EnhancedEarlyStoppingCallback(
            early_stopping_patience=3,
            early_stopping_threshold=0.001,
            monitor_metrics=["eval_f1", "eval_loss"]
        )
        
        summary = callback.get_stopping_summary()
        
        assert summary["stopped"] == False
        assert summary["stopped_epoch"] is None
        assert summary["patience"] == 3
        assert summary["threshold"] == 0.001
        assert summary["threshold_type"] == "absolute"
        assert "best_metrics" in summary
        assert "final_wait_counts" in summary


class TestLearningRateSchedulerCallback:
    """Test learning rate scheduler callback functionality."""
    
    def test_initialization(self):
        """Test LR monitoring callback initialization."""
        callback = LearningRateSchedulerCallback(
            log_lr_every_step=True,
            verbose=True
        )
        
        assert callback.log_lr_every_step == True
        assert callback.verbose == True
        assert callback.lr_history == []
        assert callback.num_warmup_steps == 0  # Will be set from TrainingArguments
        assert callback.num_training_steps == 0  # Will be set from TrainerState
        assert callback.scheduler_type == "unknown"  # Will be set from TrainingArguments
    
    def test_scheduler_monitoring_setup(self):
        """Test learning rate monitoring setup during training."""
        import torch
        
        callback = LearningRateSchedulerCallback(
            log_lr_every_step=True,
            verbose=True
        )
        
        # Create real optimizer for testing
        model = torch.nn.Linear(10, 1)
        optimizer = torch.optim.AdamW(model.parameters(), lr=5e-5)
        
        # Mock trainer components with training arguments
        args = Mock()
        args.warmup_steps = 100
        args.lr_scheduler_type = "linear"
        state = Mock()
        state.max_steps = 1000
        control = Mock()
        
        # Test monitoring setup
        callback.on_train_begin(args, state, control, optimizer=optimizer)
        
        assert callback.num_warmup_steps == 100
        assert callback.num_training_steps == 1000
        assert callback.scheduler_type == "linear"
    
    def test_lr_step_tracking(self):
        """Test learning rate step tracking."""
        import torch
        
        callback = LearningRateSchedulerCallback(
            log_lr_every_step=True,
            verbose=False
        )
        
        # Create real optimizer for testing
        model = torch.nn.Linear(10, 1)
        optimizer = torch.optim.AdamW(model.parameters(), lr=5e-5)
        
        # Initialize monitoring
        args = Mock()
        args.warmup_steps = 0
        args.lr_scheduler_type = "constant"
        state = Mock()
        state.max_steps = 100
        control = Mock()
        callback.on_train_begin(args, state, control, optimizer=optimizer)
        
        # Mock step (simulate what Trainer would do)
        state.global_step = 10
        state.epoch = 1
        callback.on_step_end(args, state, control, optimizer=optimizer)
        
        # Check history tracking
        assert len(callback.lr_history) == 1
        assert callback.lr_history[0]["step"] == 10
        assert callback.lr_history[0]["learning_rate"] == 5e-5
    
    def test_get_lr_summary(self):
        """Test learning rate summary generation."""
        callback = LearningRateSchedulerCallback(
            log_lr_every_step=False,
            verbose=False
        )
        
        # Set up monitoring info (normally set by on_train_begin)
        callback.scheduler_type = "linear"
        callback.num_warmup_steps = 100
        callback.num_training_steps = 1000
        
        # Add some mock history
        callback.lr_history = [
            {"step": 1, "learning_rate": 1e-6, "warmup_phase": True},
            {"step": 50, "learning_rate": 2.5e-5, "warmup_phase": True},
            {"step": 100, "learning_rate": 5e-5, "warmup_phase": False},
            {"step": 500, "learning_rate": 2.5e-5, "warmup_phase": False},
            {"step": 1000, "learning_rate": 0.0, "warmup_phase": False},
        ]
        
        summary = callback.get_lr_summary()
        
        assert summary["scheduler_type"] == "linear"
        assert summary["warmup_steps_configured"] == 100
        assert summary["warmup_steps_actual"] == 2
        assert summary["total_steps"] == 5
        assert summary["initial_lr"] == 1e-6
        assert summary["final_lr"] == 0.0
        assert summary["max_lr"] == 5e-5


class TestTrainingMonitorCallback:
    """Test training monitor callback functionality."""
    
    def test_initialization(self):
        """Test monitor callback initialization."""
        callback = TrainingMonitorCallback(
            log_interval=50,
            track_gradients=True,
            track_memory=True,
            smooth_loss_window=100
        )
        
        assert callback.log_interval == 50
        assert callback.track_gradients == True
        assert callback.track_memory == True
        assert callback.smooth_loss_window == 100
        assert len(callback.loss_history) == 0
        assert len(callback.gradient_norms) == 0
    
    def test_loss_tracking(self):
        """Test loss tracking functionality."""
        callback = TrainingMonitorCallback()
        
        # Mock trainer components
        args = Mock()
        state = Mock()
        state.global_step = 100
        state.epoch = 1
        control = Mock()
        
        logs = {"train_loss": 0.5}
        
        callback.on_log(args, state, control, logs=logs)
        
        assert len(callback.loss_history) == 1
        assert callback.loss_history[0]["step"] == 100
        assert callback.loss_history[0]["loss"] == 0.5
    
    @patch('torch.cuda.is_available', return_value=True)
    @patch('torch.cuda.memory_allocated', return_value=1024**3)  # 1GB
    @patch('torch.cuda.memory_reserved', return_value=2*1024**3)  # 2GB
    def test_memory_tracking(self, mock_reserved, mock_allocated, mock_cuda_available):
        """Test GPU memory tracking."""
        callback = TrainingMonitorCallback(track_memory=True)
        
        # Mock trainer components
        args = Mock()
        state = Mock()
        state.global_step = 100
        state.max_steps = 1000  # Add max_steps to avoid comparison error
        control = Mock()
        
        callback.on_step_end(args, state, control)
        
        assert len(callback.memory_usage) == 1
        assert callback.memory_usage[0]["step"] == 100
        assert callback.memory_usage[0]["memory_allocated_gb"] == 1.0
        assert callback.memory_usage[0]["memory_reserved_gb"] == 2.0
    
    def test_gradient_tracking(self):
        """Test gradient norm tracking."""
        callback = TrainingMonitorCallback(track_gradients=True)
        
        # Mock model with gradients
        model = Mock()
        param1 = Mock()
        param1.grad = Mock()
        param1.grad.data.norm.return_value = torch.tensor(0.5)
        param2 = Mock()
        param2.grad = Mock()
        param2.grad.data.norm.return_value = torch.tensor(0.3)
        
        model.parameters.return_value = [param1, param2]
        
        # Mock trainer components
        args = Mock()
        state = Mock()
        state.global_step = 100
        state.max_steps = 1000  # Add max_steps to avoid comparison error
        control = Mock()
        
        callback.on_step_end(args, state, control, model=model)
        
        assert len(callback.gradient_norms) == 1
        assert callback.gradient_norms[0]["step"] == 100
        assert callback.gradient_norms[0]["param_count"] == 2
        # Gradient norm should be sqrt(0.5^2 + 0.3^2) = sqrt(0.34) ≈ 0.583
        assert abs(callback.gradient_norms[0]["grad_norm"] - 0.583) < 0.01
    
    def test_get_monitoring_summary(self):
        """Test monitoring summary generation."""
        callback = TrainingMonitorCallback()
        
        # Add some mock data
        callback.loss_history = [
            {"step": 1, "loss": 1.0},
            {"step": 2, "loss": 0.8},
            {"step": 3, "loss": 0.6},
        ]
        callback.gradient_norms = [
            {"step": 1, "grad_norm": 1.0},
            {"step": 2, "grad_norm": 0.8},
        ]
        callback.step_times = [0.1, 0.12, 0.11]
        
        summary = callback.get_monitoring_summary()
        
        assert summary["total_steps"] == 3
        assert "loss_stats" in summary
        assert summary["loss_stats"]["initial"] == 1.0
        assert summary["loss_stats"]["final"] == 0.6
        assert "gradient_stats" in summary
        assert "speed_stats" in summary
        assert abs(summary["speed_stats"]["mean_step_time"] - 0.11) < 0.01


class TestEnhancedCallbacksIntegration:
    """Test enhanced callbacks integration and creation."""
    
    def test_create_enhanced_callbacks_basic(self):
        """Test basic enhanced callbacks creation."""
        config = HFTrainingConfig(
            early_stopping_patience=3,
            lr_scheduler_type="linear",
            warmup_steps=100
        )
        
        callbacks = create_enhanced_callbacks(
            config=config,
            label_list=["O", "B-PER", "I-PER"],
            num_training_steps=1000
        )
        
        # Should create early stopping, LR scheduler, and training monitor
        assert len(callbacks) == 3
        
        callback_types = [type(cb).__name__ for cb in callbacks]
        assert "EnhancedEarlyStoppingCallback" in callback_types
        assert "LearningRateSchedulerCallback" in callback_types
        assert "TrainingMonitorCallback" in callback_types
    
    def test_create_enhanced_callbacks_no_early_stopping(self):
        """Test callbacks creation without early stopping."""
        config = HFTrainingConfig(
            early_stopping_patience=0,  # Disabled
            lr_scheduler_type="cosine",
            warmup_steps=200
        )
        
        callbacks = create_enhanced_callbacks(
            config=config,
            num_training_steps=1000
        )
        
        # Should create LR scheduler and training monitor only
        assert len(callbacks) == 2
        
        callback_types = [type(cb).__name__ for cb in callbacks]
        assert "EnhancedEarlyStoppingCallback" not in callback_types
        assert "LearningRateSchedulerCallback" in callback_types
        assert "TrainingMonitorCallback" in callback_types
    
    def test_create_enhanced_callbacks_constant_lr(self):
        """Test callbacks creation with constant learning rate."""
        config = HFTrainingConfig(
            early_stopping_patience=2,
            lr_scheduler_type="constant",  # No scheduling
            warmup_steps=0
        )
        
        callbacks = create_enhanced_callbacks(
            config=config,
            num_training_steps=1000
        )
        
        # Should create all three callbacks (early stopping, LR monitor, training monitor)
        assert len(callbacks) == 3
        
        callback_types = [type(cb).__name__ for cb in callbacks]
        assert "EnhancedEarlyStoppingCallback" in callback_types
        assert "LearningRateSchedulerCallback" in callback_types  # Always present for monitoring
        assert "TrainingMonitorCallback" in callback_types
    
    def test_create_enhanced_callbacks_all_features(self):
        """Test callbacks creation with all features enabled."""
        config = HFTrainingConfig(
            early_stopping_patience=5,
            early_stopping_threshold=0.001,
            lr_scheduler_type="cosine_with_restarts",
            warmup_steps=500,
            cosine_schedule_num_cycles=0.5,
            logging_steps=25
        )
        
        callbacks = create_enhanced_callbacks(
            config=config,
            label_list=["O", "B-PER", "I-PER", "B-LOC", "I-LOC"],
            num_training_steps=2000
        )
        
        # Should create all three callbacks
        assert len(callbacks) == 3
        
        # Verify early stopping configuration
        early_stopping_cb = next(cb for cb in callbacks if isinstance(cb, EnhancedEarlyStoppingCallback))
        assert early_stopping_cb.early_stopping_patience == 5
        assert early_stopping_cb.early_stopping_threshold == 0.001
        
        # Verify LR monitoring callback exists (scheduler_type will be set during training)
        lr_monitor_cb = next(cb for cb in callbacks if isinstance(cb, LearningRateSchedulerCallback))
        assert lr_monitor_cb is not None
        # scheduler_type, num_warmup_steps, num_training_steps are set during on_train_begin
        
        # Verify training monitor configuration
        monitor_cb = next(cb for cb in callbacks if isinstance(cb, TrainingMonitorCallback))
        assert monitor_cb.log_interval == 25


if __name__ == "__main__":
    pytest.main([__file__])