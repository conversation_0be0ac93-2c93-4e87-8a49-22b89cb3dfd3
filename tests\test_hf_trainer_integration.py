"""
Integration tests for Hugging Face Trainer.

These tests verify the complete training pipeline works end-to-end
with small datasets and test configurations.
"""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import patch, Mock

from src.training.hf_trainer import HFNERTrainer
from src.training.hf_config import create_test_hf_config


class TestHFTrainerIntegration:
    """Integration tests for HF Trainer."""
    
    def create_sample_data(self, temp_dir: Path, num_examples: int = 5) -> Path:
        """Create sample training data."""
        sample_data = []
        
        sentences = [
            "<PERSON> woon<PERSON> in Amsterdam.",
            "Marie de <PERSON>it werkt bij Google.",
            "<PERSON><PERSON> is een dokter.",
            "<PERSON> in Utrecht.",
            "Tom <PERSON> speelt voetbal."
        ]
        
        entities_list = [
            [{"text": "<PERSON>", "label": "PER", "start": 0, "end": 10}],
            [{"text": "<PERSON> de <PERSON>", "label": "PER", "start": 0, "end": 12}],
            [{"text": "<PERSON><PERSON>", "label": "PER", "start": 0, "end": 17}],
            [{"text": "Lisa Smit", "label": "PER", "start": 0, "end": 9}],
            [{"text": "Tom de Jong", "label": "PER", "start": 0, "end": 11}]
        ]
        
        for i in range(min(num_examples, len(sentences))):
            sample_data.append({
                "id": i + 1,
                "sentence": sentences[i],
                "entities": entities_list[i]
            })
        
        data_file = temp_dir / "sample_data.json"
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, indent=2)
        
        return data_file
    
    @patch('src.training.hf_trainer.Trainer')
    @patch('src.training.hf_trainer.wandb')
    def test_trainer_initialization_and_setup(self, mock_wandb, mock_trainer_class):
        """Test trainer initialization and basic setup."""
        # Mock WandB
        mock_wandb.run = None
        mock_wandb.init.return_value = None
        mock_wandb.finish.return_value = None
        
        # Mock Trainer
        mock_trainer = Mock()
        mock_trainer.train.return_value = Mock(training_loss=0.5)
        mock_trainer.evaluate.return_value = {"eval_f1": 0.8, "eval_precision": 0.75}
        mock_trainer.save_model.return_value = None
        mock_trainer.save_state.return_value = None
        mock_trainer.predict.return_value = Mock(predictions=[], label_ids=[])
        mock_trainer_class.return_value = mock_trainer
        
        config = create_test_hf_config()
        config.test_disable_wandb = True  # Disable WandB for test
        
        trainer = HFNERTrainer(config)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            data_file = self.create_sample_data(temp_path)
            
            # This should not crash and should set up all components
            with patch('src.training.hf_trainer.create_ner_model') as mock_create_model:
                with patch('src.training.hf_trainer.validate_model_compatibility') as mock_validate:
                    with patch('src.training.hf_trainer.prepare_ner_dataset') as mock_prepare:
                        # Mock model creation
                        mock_model = Mock()
                        mock_tokenizer = Mock()
                        mock_label2id = {"O": 0, "B-PER": 1, "I-PER": 2}
                        mock_id2label = {0: "O", 1: "B-PER", 2: "I-PER"}
                        mock_create_model.return_value = (mock_model, mock_tokenizer, mock_label2id, mock_id2label)
                        mock_validate.return_value = True
                        
                        # Mock dataset preparation
                        from datasets import Dataset
                        mock_dataset_dict = {
                            'train': Dataset.from_dict({
                                'input_ids': [[1, 2, 3], [4, 5, 6]],
                                'attention_mask': [[1, 1, 1], [1, 1, 1]],
                                'labels': [[0, 1, 2], [0, 0, 1]]
                            }),
                            'validation': Dataset.from_dict({
                                'input_ids': [[7, 8, 9]],
                                'attention_mask': [[1, 1, 1]],
                                'labels': [[0, 1, 0]]
                            })
                        }
                        mock_statistics = Mock()
                        mock_statistics.to_dict.return_value = {"total_examples": 3}
                        mock_prepare.return_value = (
                            mock_dataset_dict, 
                            ["O", "B-PER", "I-PER"], 
                            mock_label2id, 
                            mock_id2label, 
                            mock_statistics
                        )
                        
                        # Run training
                        results = trainer.train(data_path=str(data_file))
                        
                        # Verify results
                        assert "train_result" in results
                        assert "eval_result" in results
                        assert "output_dir" in results
                        
                        # Verify trainer was called
                        mock_trainer.train.assert_called_once()
                        mock_trainer.evaluate.assert_called_once()
    
    def test_config_validation(self):
        """Test configuration validation."""
        config = create_test_hf_config()
        
        # Test valid configuration
        trainer = HFNERTrainer(config)
        assert trainer.config.epochs == 1  # Test mode default
        assert trainer.config.test_mode is True
        
        # Test configuration modification
        config.epochs = 5
        config.batch_size = 16
        trainer = HFNERTrainer(config)
        assert trainer.config.epochs == 5
        assert trainer.config.batch_size == 16
    
    def test_output_directory_creation(self):
        """Test output directory creation."""
        config = create_test_hf_config()
        trainer = HFNERTrainer(config)
        
        # Test auto-generated output directory
        output_dir = config.create_output_dir("test_training")
        assert Path(output_dir).exists()
        assert "test_training" in output_dir
        assert config.run_version in output_dir
    
    @patch('src.training.hf_trainer.wandb')
    def test_wandb_setup_disabled(self, mock_wandb):
        """Test WandB setup when disabled."""
        config = create_test_hf_config()
        config.test_disable_wandb = True
        
        trainer = HFNERTrainer(config)
        trainer.setup_wandb()
        
        # WandB should not be initialized
        mock_wandb.init.assert_not_called()
    
    @patch('src.training.hf_trainer.wandb')
    def test_wandb_setup_enabled(self, mock_wandb):
        """Test WandB setup when enabled."""
        config = create_test_hf_config()
        config.test_disable_wandb = False
        config.wandb_project = "test-project"
        
        trainer = HFNERTrainer(config)
        trainer.setup_wandb()
        
        # WandB should be initialized
        mock_wandb.init.assert_called_once()
    
    def test_callbacks_creation(self):
        """Test training callbacks creation."""
        config = create_test_hf_config()
        config.early_stopping_patience = 2
        
        trainer = HFNERTrainer(config)
        trainer.label_list = ["O", "B-PER", "I-PER"]
        
        callbacks = trainer.create_callbacks()
        
        # Should have early stopping and metrics callbacks
        assert len(callbacks) == 2
        
        # Test without early stopping
        config.early_stopping_patience = 0
        trainer = HFNERTrainer(config)
        trainer.label_list = ["O", "B-PER", "I-PER"]
        
        callbacks = trainer.create_callbacks()
        assert len(callbacks) == 1  # Only metrics callback


if __name__ == "__main__":
    pytest.main([__file__])