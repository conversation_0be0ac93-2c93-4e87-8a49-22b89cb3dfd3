"""
Enhanced callbacks for Hugging Face Trainer integration.

This module provides comprehensive callback implementations for early stopping,
learning rate scheduling, training monitoring, and control with advanced features
for RobBERT-2023 NER training.
"""

import os
import torch
import numpy as np
from typing import Dict, List, Any, Optional, Union, Callable
from pathlib import Path
from datetime import datetime
import json

from transformers import (
    TrainerCallback,
    TrainingArguments,
    TrainerState,
    TrainerControl,
    EarlyStoppingCallback,
    get_scheduler
)
from transformers.trainer_utils import IntervalStrategy

from ..utils.logging_utils import get_logger


class EnhancedEarlyStoppingCallback(EarlyStoppingCallback):
    """
    Enhanced early stopping callback with additional features.
    
    Extends the default EarlyStoppingCallback with:
    - Configurable threshold types (absolute, relative)
    - Multiple metric monitoring
    - Detailed logging and history tracking
    - Custom stopping conditions
    """
    
    def __init__(
        self,
        early_stopping_patience: int = 1,
        early_stopping_threshold: float = 0.0,
        threshold_type: str = "absolute",  # "absolute" or "relative"
        monitor_metrics: Optional[List[str]] = None,
        min_delta: float = 0.0,
        restore_best_weights: bool = True,
        verbose: bool = True
    ):
        """
        Initialize enhanced early stopping callback.
        
        Args:
            early_stopping_patience: Number of evaluations with no improvement after which training will be stopped
            early_stopping_threshold: Minimum change to qualify as an improvement
            threshold_type: Type of threshold - "absolute" or "relative"
            monitor_metrics: List of metrics to monitor (default: ["eval_loss"])
            min_delta: Minimum change in monitored quantity to qualify as improvement
            restore_best_weights: Whether to restore model weights from best checkpoint
            verbose: Whether to log early stopping events
        """
        # Initialize parent class
        super().__init__(
            early_stopping_patience=early_stopping_patience,
            early_stopping_threshold=early_stopping_threshold
        )
        
        self.threshold_type = threshold_type
        self.monitor_metrics = monitor_metrics or ["eval_loss"]
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.verbose = verbose
        
        # Initialize tracking variables
        self.best_metrics = {}
        self.wait_count = {}
        self.stopped_epoch = None
        self.history = []
        
        # Initialize best values for each metric
        for metric in self.monitor_metrics:
            self.best_metrics[metric] = float('inf') if 'loss' in metric else float('-inf')
            self.wait_count[metric] = 0
        
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        if self.verbose:
            self.logger.logger.info(f"Enhanced early stopping initialized:")
            self.logger.logger.info(f"  - Patience: {early_stopping_patience}")
            self.logger.logger.info(f"  - Threshold: {early_stopping_threshold} ({threshold_type})")
            self.logger.logger.info(f"  - Monitoring metrics: {self.monitor_metrics}")
    
    def check_metric_improved(self, current_value: float, best_value: float, metric_name: str) -> bool:
        """
        Check if a metric has improved based on threshold type.
        
        Args:
            current_value: Current metric value
            best_value: Best metric value so far
            metric_name: Name of the metric
            
        Returns:
            True if metric improved, False otherwise
        """
        # Determine if higher is better
        higher_is_better = 'loss' not in metric_name.lower()
        
        # Calculate the minimum improvement threshold
        min_improvement = max(self.min_delta, self.early_stopping_threshold)
        
        if higher_is_better:
            if self.threshold_type == "relative":
                improvement = (current_value - best_value) / abs(best_value) if best_value != 0 else current_value
            else:
                improvement = current_value - best_value
            
            return improvement > min_improvement
        else:
            if self.threshold_type == "relative":
                improvement = (best_value - current_value) / abs(best_value) if best_value != 0 else -current_value
            else:
                improvement = best_value - current_value
            
            return improvement > min_improvement
    
    def on_evaluate(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        model=None,
        **kwargs
    ):
        """
        Check for early stopping after evaluation.
        
        Args:
            args: Training arguments
            state: Trainer state
            control: Trainer control
            model: Model being trained
        """
        if not state.log_history:
            return
        
        # Get latest metrics
        latest_log = state.log_history[-1]
        current_metrics = {}
        
        # Extract monitored metrics
        for metric in self.monitor_metrics:
            if metric in latest_log:
                current_metrics[metric] = latest_log[metric]
        
        if not current_metrics:
            return
        
        # Check each monitored metric
        improvements = {}
        
        for metric, current_value in current_metrics.items():
            best_value = self.best_metrics[metric]
            
            # Check if metric improved
            if self.check_metric_improved(current_value, best_value, metric):
                self.best_metrics[metric] = current_value
                self.wait_count[metric] = 0
                improvements[metric] = True
                
                if self.verbose:
                    self.logger.logger.info(f"Metric {metric} improved: {current_value:.6f} (previous best: {best_value:.6f})")
            else:
                self.wait_count[metric] += 1
                improvements[metric] = False
                
                if self.verbose:
                    self.logger.logger.info(f"Metric {metric} did not improve: {current_value:.6f} (best: {best_value:.6f}, patience: {self.wait_count[metric]}/{self.early_stopping_patience})")
        
        # Record history
        history_entry = {
            "epoch": state.epoch,
            "step": state.global_step,
            "metrics": current_metrics.copy(),
            "best_metrics": self.best_metrics.copy(),
            "wait_counts": self.wait_count.copy(),
            "improvements": improvements
        }
        self.history.append(history_entry)
        
        # Check if any metric has exceeded patience (stop if ANY metric exceeds patience)
        max_wait = max(self.wait_count.values())
        
        # Stop training if any metric has exceeded patience
        if max_wait >= self.early_stopping_patience:
            control.should_training_stop = True
            self.stopped_epoch = state.epoch
            
            if self.verbose:
                self.logger.logger.info(f"Early stopping triggered at epoch {state.epoch}")
                self.logger.logger.info(f"Best metrics: {self.best_metrics}")
                
                # Log which metrics caused stopping
                stopping_metrics = [metric for metric, count in self.wait_count.items() 
                                  if count >= self.early_stopping_patience]
                self.logger.logger.info(f"Stopping due to metrics: {stopping_metrics}")
    
    def get_stopping_summary(self) -> Dict[str, Any]:
        """
        Get summary of early stopping behavior.
        
        Returns:
            Dictionary with stopping summary
        """
        return {
            "stopped": self.stopped_epoch is not None,
            "stopped_epoch": self.stopped_epoch,
            "best_metrics": self.best_metrics,
            "final_wait_counts": self.wait_count,
            "total_evaluations": len(self.history),
            "patience": self.early_stopping_patience,
            "threshold": self.early_stopping_threshold,
            "threshold_type": self.threshold_type
        }


class LearningRateSchedulerCallback(TrainerCallback):
    """
    Learning rate monitoring callback for Hugging Face Trainer.
    
    This callback monitors and logs learning rate changes during training.
    The actual learning rate scheduling is handled by the Hugging Face Trainer
    through TrainingArguments (lr_scheduler_type, warmup_steps, etc.).
    
    This callback provides:
    - Learning rate history tracking
    - Detailed logging of LR changes
    - Warmup phase monitoring
    """
    
    def __init__(
        self,
        log_lr_every_step: bool = False,
        verbose: bool = True
    ):
        """
        Initialize learning rate monitoring callback.
        
        Args:
            log_lr_every_step: Whether to log learning rate every step
            verbose: Whether to log scheduler events
        """
        self.log_lr_every_step = log_lr_every_step
        self.verbose = verbose
        
        # These will be set from TrainingArguments during training
        self.num_warmup_steps = 0
        self.num_training_steps = 0
        self.scheduler_type = "unknown"
        
        self.lr_history = []
        
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        if self.verbose:
            self.logger.logger.info(f"Learning rate monitor callback initialized")
    
    def on_train_begin(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        model=None,
        optimizer=None,
        **kwargs
    ):
        """
        Extract scheduler information from training arguments.
        
        Args:
            args: Training arguments
            state: Trainer state
            control: Trainer control
            model: Model being trained
            optimizer: Optimizer being used
        """
        if optimizer is None:
            self.logger.logger.warning("No optimizer provided to LR monitor callback")
            return
        
        # Extract scheduler info from training arguments
        self.num_warmup_steps = getattr(args, 'warmup_steps', 0)
        self.num_training_steps = state.max_steps
        self.scheduler_type = getattr(args, 'lr_scheduler_type', 'linear')
        
        if self.verbose:
            self.logger.logger.info(f"Learning rate monitoring started:")
            self.logger.logger.info(f"  - Scheduler type: {self.scheduler_type}")
            self.logger.logger.info(f"  - Warmup steps: {self.num_warmup_steps}")
            self.logger.logger.info(f"  - Total training steps: {self.num_training_steps}")
            
            # Log initial learning rate
            initial_lr = optimizer.param_groups[0]['lr']
            self.logger.logger.info(f"  - Initial learning rate: {initial_lr:.2e}")
    
    def on_step_end(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        optimizer=None,
        **kwargs
    ):
        """
        Record learning rate after each step.
        
        Note: We don't manually step the scheduler here because the Hugging Face Trainer
        handles learning rate scheduling internally. We just record the current LR for monitoring.
        
        Args:
            args: Training arguments
            state: Trainer state
            control: Trainer control
            optimizer: Optimizer being used
        """
        if optimizer is None:
            return
        
        # Get current learning rate (Trainer has already updated it)
        current_lr = optimizer.param_groups[0]['lr']
        
        # Record learning rate history
        lr_entry = {
            "step": state.global_step,
            "epoch": state.epoch,
            "learning_rate": current_lr,
            "warmup_phase": state.global_step < self.num_warmup_steps
        }
        self.lr_history.append(lr_entry)
        
        # Log learning rate if requested
        if self.log_lr_every_step and self.verbose:
            phase = "warmup" if lr_entry["warmup_phase"] else "main"
            self.logger.logger.info(f"Step {state.global_step}: LR = {current_lr:.2e} ({phase})")
    
    def on_epoch_end(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        optimizer=None,
        **kwargs
    ):
        """
        Log learning rate at the end of each epoch.
        
        Args:
            args: Training arguments
            state: Trainer state
            control: Trainer control
            optimizer: Optimizer being used
        """
        if optimizer is None or not self.verbose:
            return
        
        current_lr = optimizer.param_groups[0]['lr']
        warmup_complete = state.global_step >= self.num_warmup_steps
        
        self.logger.logger.info(f"Epoch {state.epoch} completed: LR = {current_lr:.2e}")
        if not warmup_complete:
            remaining_warmup = self.num_warmup_steps - state.global_step
            self.logger.logger.info(f"Warmup progress: {state.global_step}/{self.num_warmup_steps} ({remaining_warmup} steps remaining)")
    
    def get_lr_summary(self) -> Dict[str, Any]:
        """
        Get summary of learning rate monitoring.
        
        Returns:
            Dictionary with LR monitoring summary
        """
        if not self.lr_history:
            return {"error": "No learning rate history available"}
        
        # Calculate statistics
        lrs = [entry["learning_rate"] for entry in self.lr_history]
        warmup_steps_actual = sum(1 for entry in self.lr_history if entry["warmup_phase"])
        
        return {
            "scheduler_type": self.scheduler_type,
            "warmup_steps_configured": self.num_warmup_steps,
            "warmup_steps_actual": warmup_steps_actual,
            "total_steps": len(self.lr_history),
            "initial_lr": lrs[0] if lrs else None,
            "final_lr": lrs[-1] if lrs else None,
            "max_lr": max(lrs) if lrs else None,
            "min_lr": min(lrs) if lrs else None
        }


class TrainingMonitorCallback(TrainerCallback):
    """
    Comprehensive training monitoring callback.
    
    Provides detailed monitoring of training progress with:
    - Loss tracking and smoothing
    - Gradient norm monitoring
    - Memory usage tracking
    - Training speed metrics
    - Custom metric logging
    """
    
    def __init__(
        self,
        log_interval: int = 50,
        track_gradients: bool = True,
        track_memory: bool = True,
        smooth_loss_window: int = 100,
        verbose: bool = True
    ):
        """
        Initialize training monitor callback.
        
        Args:
            log_interval: Interval for detailed logging
            track_gradients: Whether to track gradient norms
            track_memory: Whether to track memory usage
            smooth_loss_window: Window size for loss smoothing
            verbose: Whether to log monitoring events
        """
        self.log_interval = log_interval
        self.track_gradients = track_gradients
        self.track_memory = track_memory
        self.smooth_loss_window = smooth_loss_window
        self.verbose = verbose
        
        # Initialize tracking variables
        self.loss_history = []
        self.gradient_norms = []
        self.memory_usage = []
        self.step_times = []
        self.last_step_time = None
        
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        if self.verbose:
            self.logger.logger.info("Training monitor callback initialized")
    
    def on_step_begin(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        **kwargs
    ):
        """Record step start time."""
        self.last_step_time = datetime.now()
    
    def on_step_end(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        model=None,
        optimizer=None,
        **kwargs
    ):
        """
        Monitor training progress after each step.
        
        Args:
            args: Training arguments
            state: Trainer state
            control: Trainer control
            model: Model being trained
            optimizer: Optimizer being used
        """
        # Record step time
        if self.last_step_time:
            step_duration = (datetime.now() - self.last_step_time).total_seconds()
            self.step_times.append(step_duration)
        
        # Track gradient norms
        if self.track_gradients and model is not None:
            total_norm = 0.0
            param_count = 0
            
            for param in model.parameters():
                if param.grad is not None:
                    param_norm = param.grad.data.norm(2)
                    total_norm += param_norm.item() ** 2
                    param_count += 1
            
            if param_count > 0:
                total_norm = total_norm ** (1. / 2)
                self.gradient_norms.append({
                    "step": state.global_step,
                    "grad_norm": total_norm,
                    "param_count": param_count
                })
        
        # Track memory usage
        if self.track_memory and torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**3  # GB
            memory_reserved = torch.cuda.memory_reserved() / 1024**3   # GB
            
            self.memory_usage.append({
                "step": state.global_step,
                "memory_allocated_gb": memory_allocated,
                "memory_reserved_gb": memory_reserved
            })
        
        # Log detailed information at intervals
        if state.global_step % self.log_interval == 0 and self.verbose:
            self._log_detailed_status(state, args)
    
    def on_log(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        logs: Optional[Dict[str, float]] = None,
        **kwargs
    ):
        """
        Track loss from logs.
        
        Args:
            args: Training arguments
            state: Trainer state
            control: Trainer control
            logs: Training logs
        """
        if logs and "train_loss" in logs:
            self.loss_history.append({
                "step": state.global_step,
                "epoch": state.epoch,
                "loss": logs["train_loss"]
            })
    
    def _log_detailed_status(self, state: TrainerState, args: TrainingArguments):
        """Log detailed training status."""
        self.logger.logger.info(f"Training Status - Step {state.global_step}:")
        
        # Loss information
        if self.loss_history:
            recent_losses = [entry["loss"] for entry in self.loss_history[-self.smooth_loss_window:]]
            if recent_losses:
                avg_loss = np.mean(recent_losses)
                self.logger.logger.info(f"  - Average loss (last {len(recent_losses)} steps): {avg_loss:.6f}")
        
        # Gradient information
        if self.gradient_norms:
            recent_grad_norm = self.gradient_norms[-1]["grad_norm"]
            self.logger.logger.info(f"  - Gradient norm: {recent_grad_norm:.6f}")
        
        # Memory information
        if self.memory_usage:
            recent_memory = self.memory_usage[-1]
            self.logger.logger.info(f"  - GPU memory: {recent_memory['memory_allocated_gb']:.2f}GB allocated, {recent_memory['memory_reserved_gb']:.2f}GB reserved")
        
        # Speed information
        if self.step_times:
            recent_times = self.step_times[-self.log_interval:]
            if recent_times:
                avg_time = np.mean(recent_times)
                steps_per_sec = 1.0 / avg_time if avg_time > 0 else 0
                self.logger.logger.info(f"  - Training speed: {steps_per_sec:.2f} steps/sec ({avg_time:.3f}s/step)")
        
        # Progress information
        progress = state.global_step / state.max_steps if state.max_steps > 0 else 0
        self.logger.logger.info(f"  - Progress: {progress:.1%} ({state.global_step}/{state.max_steps} steps)")
    
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive monitoring summary.
        
        Returns:
            Dictionary with monitoring summary
        """
        summary = {
            "total_steps": len(self.loss_history),
            "monitoring_enabled": {
                "gradients": self.track_gradients,
                "memory": self.track_memory,
                "step_timing": True
            }
        }
        
        # Loss statistics
        if self.loss_history:
            losses = [entry["loss"] for entry in self.loss_history]
            summary["loss_stats"] = {
                "initial": losses[0],
                "final": losses[-1],
                "min": min(losses),
                "max": max(losses),
                "mean": np.mean(losses),
                "std": np.std(losses)
            }
        
        # Gradient statistics
        if self.gradient_norms:
            grad_norms = [entry["grad_norm"] for entry in self.gradient_norms]
            summary["gradient_stats"] = {
                "mean_norm": np.mean(grad_norms),
                "max_norm": max(grad_norms),
                "min_norm": min(grad_norms),
                "std_norm": np.std(grad_norms)
            }
        
        # Memory statistics
        if self.memory_usage:
            allocated = [entry["memory_allocated_gb"] for entry in self.memory_usage]
            reserved = [entry["memory_reserved_gb"] for entry in self.memory_usage]
            summary["memory_stats"] = {
                "peak_allocated_gb": max(allocated),
                "peak_reserved_gb": max(reserved),
                "final_allocated_gb": allocated[-1],
                "final_reserved_gb": reserved[-1]
            }
        
        # Speed statistics
        if self.step_times:
            summary["speed_stats"] = {
                "mean_step_time": np.mean(self.step_times),
                "min_step_time": min(self.step_times),
                "max_step_time": max(self.step_times),
                "steps_per_second": 1.0 / np.mean(self.step_times) if self.step_times else 0
            }
        
        return summary


def create_enhanced_callbacks(
    config,
    label_list: Optional[List[str]] = None,
    num_training_steps: Optional[int] = None
) -> List[TrainerCallback]:
    """
    Create enhanced callback suite for comprehensive training monitoring and control.
    
    Args:
        config: Training configuration object
        label_list: List of NER labels
        num_training_steps: Total number of training steps
        
    Returns:
        List of configured callbacks
    """
    callbacks = []
    logger = get_logger(__name__)
    
    # Enhanced early stopping callback
    if hasattr(config, 'early_stopping_patience') and config.early_stopping_patience > 0:
        monitor_metrics = ["eval_f1", "eval_loss"]  # Monitor both F1 and loss
        
        early_stopping = EnhancedEarlyStoppingCallback(
            early_stopping_patience=config.early_stopping_patience,
            early_stopping_threshold=getattr(config, 'early_stopping_threshold', 0.001),
            threshold_type="absolute",
            monitor_metrics=monitor_metrics,
            min_delta=0.0001,
            restore_best_weights=True,
            verbose=True
        )
        callbacks.append(early_stopping)
        logger.logger.info(f"Added enhanced early stopping callback (patience={config.early_stopping_patience})")
    
    # Learning rate monitoring callback (always add for monitoring)
    lr_monitor = LearningRateSchedulerCallback(
        log_lr_every_step=False,
        verbose=True
    )
    callbacks.append(lr_monitor)
    logger.logger.info("Added learning rate monitoring callback")
    
    # Training monitor callback
    training_monitor = TrainingMonitorCallback(
        log_interval=getattr(config, 'logging_steps', 50),
        track_gradients=True,
        track_memory=torch.cuda.is_available(),
        smooth_loss_window=100,
        verbose=True
    )
    callbacks.append(training_monitor)
    logger.logger.info("Added training monitor callback")
    
    return callbacks


# Export main classes and functions
__all__ = [
    'EnhancedEarlyStoppingCallback',
    'LearningRateSchedulerCallback', 
    'TrainingMonitorCallback',
    'create_enhanced_callbacks'
]