# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Model files
*.safetensors
*.bin
*.onnx
src/models/weights/
src/models/checkpoints/
*.pt
*.pth
*.model

# Data
*.jsonl
*.csv
data/
datasets/
*.tsv
*.txt.gz
*.json.gz

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
*.log
logs/
wandb/
tensorboard/

# Cache
.cache/
.huggingface/
.transformers_cache/
.torch/

# Jupyter
.ipynb_checkpoints/
*.ipynb

# Environment variables
.env
.env.local
.env.*.local

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST