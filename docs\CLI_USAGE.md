# RobBERT-2023 NER Training CLI Usage Guide

This document provides comprehensive usage instructions for the RobBERT-2023 NER training CLI with Hugging Face Trainer integration.

## Installation

Ensure all dependencies are installed:

```bash
pip install -r requirements.txt
```

## CLI Access Methods

The CLI can be accessed in multiple ways:

### 1. Standalone Script (Recommended)
```bash
python train.py --help
```

### 2. Module Execution
```bash
python -m src.training --help
```

### 3. Direct Module Import
```python
from src.training.cli import app
app()
```

## Available Commands

### 1. Single-Head Training

Train a model for a specific entity type with dataset filtering.

```bash
python train.py single-head [ENTITY_TYPE] [DATA_PATH] [OPTIONS]
```

**Arguments:**
- `ENTITY_TYPE`: Entity type to train (PER, LOC, ORG, MISC)
- `DATA_PATH`: Path to training data (JSON/<PERSON><PERSON>NL file)

**Options:**
- `--filtering-strategy`: Dataset filtering strategy (strict, mixed) [default: strict]
- `--train-split`: Training split ratio [default: 0.8]
- `--resume-from-checkpoint`: Path to checkpoint to resume from
- `--config, -c`: Path to YAML configuration file
- `--epochs, -e`: Number of training epochs
- `--batch-size, -b`: Training batch size
- `--learning-rate, -lr`: Learning rate
- `--max-length`: Maximum sequence length
- `--output-dir, -o`: Output directory for checkpoints
- `--wandb-project`: WandB project name
- `--wandb-entity`: WandB entity name
- `--run-name`: WandB run name
- `--run-version`: Training run version
- `--test-mode`: Enable test mode
- `--use-class-weights`: Enable class weight calculation
- `--early-stopping-patience`: Early stopping patience
- `--seed`: Random seed
- `--no-gpu`: Disable GPU usage
- `--fp16`: Enable mixed precision training

**Examples:**

```bash
# Basic PER head training
python train.py single-head PER data_sets/dutch_ner.json

# LOC head with mixed filtering and custom config
python train.py single-head LOC data_sets/dutch_ner.json \
    --filtering-strategy mixed \
    --config examples/configs/production_training.yaml

# Resume training from checkpoint
python train.py single-head PER data_sets/dutch_ner.json \
    --resume-from-checkpoint checkpoints/PER_20250126_143022_v1.0/checkpoint-500

# Test mode training
python train.py single-head PER data_sets/dutch_ner.json \
    --test-mode \
    --epochs 1 \
    --batch-size 4

# Training with class weights and custom parameters
python train.py single-head PER data_sets/dutch_ner.json \
    --use-class-weights \
    --epochs 5 \
    --batch-size 16 \
    --learning-rate 2e-5 \
    --run-version v2.0
```

### 2. Multi-Head Training

Train multiple entity type heads either sequentially or in parallel.

```bash
python train.py multi-head [ENTITY_TYPES...] [DATA_PATH] [OPTIONS]
```

**Arguments:**
- `ENTITY_TYPES`: Space-separated list of entity types (PER LOC ORG)
- `DATA_PATH`: Path to training data (JSON/JSONL file)

**Options:**
- `--filtering-strategy`: Dataset filtering strategy (strict, mixed) [default: strict]
- `--parallel`: Train heads in parallel (default: sequential)
- `--max-workers`: Maximum parallel workers (auto-detect if not specified)
- All single-head options are also available

**Examples:**

```bash
# Sequential training of all heads
python train.py multi-head PER LOC ORG data_sets/dutch_ner.json

# Parallel training with 2 workers
python train.py multi-head PER LOC data_sets/dutch_ner.json \
    --parallel \
    --max-workers 2

# Multi-head with custom config and mixed filtering
python train.py multi-head PER LOC ORG data_sets/dutch_ner.json \
    --config examples/configs/production_training.yaml \
    --filtering-strategy mixed \
    --parallel

# Test mode multi-head training
python train.py multi-head PER LOC data_sets/dutch_ner.json \
    --test-mode \
    --parallel \
    --max-workers 2
```

### 3. Data Preprocessing

Convert raw sentence + entities format to tokenized format for training.

```bash
python train.py preprocess [INPUT_PATH] [OUTPUT_PATH] [OPTIONS]
```

**Arguments:**
- `INPUT_PATH`: Path to input data file (JSON/JSONL)
- `OUTPUT_PATH`: Path to output preprocessed data file (JSON)

**Options:**
- `--model-name`: Hugging Face model name for tokenizer [default: DTAI-KULeuven/robbert-2023-dutch-base]
- `--max-length`: Maximum sequence length [default: 512]
- `--validate-output/--no-validate-output`: Validate output format [default: True]

**Input Format:**
```json
{
    "id": 1,
    "sentence": "Jan Jansen woont in Amsterdam.",
    "entities": [
        {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10}
    ]
}
```

**Output Format:**
```json
{
    "id": 1,
    "sentence": "Jan Jansen woont in Amsterdam.",
    "tokens": ["<s>", "ĠJan", "ĠJansen", "Ġwoont", "ĠIn", "ĠAmsterdam", ".", "</s>"],
    "labels": ["O", "B-PER", "I-PER", "O", "O", "O", "O", "O"],
    "input_ids": [0, 2335, 8554, 23, 4, 812, 5, 2],
    "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1]
}
```

**Examples:**

```bash
# Basic preprocessing
python train.py preprocess data_sets/raw_dutch_ner.json data_sets/processed_dutch_ner.json

# Custom model and max length
python train.py preprocess data_sets/raw_dutch_ner.json data_sets/processed_dutch_ner.json \
    --model-name DTAI-KULeuven/robbert-2023-dutch-base \
    --max-length 256

# Skip output validation
python train.py preprocess data_sets/raw_dutch_ner.json data_sets/processed_dutch_ner.json \
    --no-validate-output
```

### 4. Configuration Management

Create and validate configuration files.

#### Create Configuration

```bash
python train.py config [OUTPUT_PATH] [OPTIONS]
```

**Arguments:**
- `OUTPUT_PATH`: Path to output configuration file [default: config/hf_training.yaml]

**Options:**
- `--template`: Configuration template (default, test, production) [default: default]

**Examples:**

```bash
# Create default config
python train.py config config/training.yaml

# Create test mode config
python train.py config config/test.yaml --template test

# Create production config
python train.py config config/production.yaml --template production
```

#### Validate Configuration

```bash
python train.py validate [CONFIG_PATH]
```

**Arguments:**
- `CONFIG_PATH`: Path to configuration file to validate

**Examples:**

```bash
# Validate configuration
python train.py validate config/training.yaml
```

## Configuration Files

Configuration files use YAML format with the following structure:

```yaml
hf_training:
  # Model configuration
  model_name: "DTAI-KULeuven/robbert-2023-dutch-base"
  
  # Training parameters
  epochs: 3
  batch_size: 8
  learning_rate: 5e-5
  
  # WandB integration
  wandb_project: "robbert2023-ner"
  wandb_entity: "slippydongle"
  
  # Hardware optimization
  use_gpu: true
  fp16: true
  
  # And many more options...
```

### Configuration Templates

1. **Default Template**: Standard settings for general use
2. **Test Template**: Reduced settings for quick testing
3. **Production Template**: Optimized settings for production training

### Environment Variable Substitution

Configuration files support environment variable substitution:

```yaml
hf_training:
  wandb_entity: "${WANDB_ENTITY:-default_entity}"
  output_dir: "${OUTPUT_DIR:-checkpoints}"
```

## Data Format Requirements

### Input Data Format (for preprocessing)

The CLI expects input data in sentence + entities format:

```json
[
    {
        "id": 1,
        "sentence": "Jan Jansen woont in Amsterdam.",
        "entities": [
            {
                "text": "Jan Jansen",
                "label": "PER",
                "start": 0,
                "end": 10
            },
            {
                "text": "Amsterdam",
                "label": "LOC",
                "start": 20,
                "end": 29
            }
        ]
    }
]
```

**Notes:**
- `start` and `end` fields are optional (auto-detection will be used)
- Supports both JSON and JSONL formats
- Entity labels should be one of: PER, LOC, ORG, MISC

### Processed Data Format (for training)

After preprocessing, data is in tokenized format:

```json
[
    {
        "id": 1,
        "sentence": "Jan Jansen woont in Amsterdam.",
        "tokens": ["<s>", "ĠJan", "ĠJansen", "Ġwoont", "ĠIn", "ĠAmsterdam", ".", "</s>"],
        "labels": ["O", "B-PER", "I-PER", "O", "O", "B-LOC", "O", "O"],
        "input_ids": [0, 2335, 8554, 23, 4, 812, 5, 2],
        "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1]
    }
]
```

## Filtering Strategies

### Strict Filtering
- Only keeps examples that contain the target entity type
- Reduces dataset size but focuses training on relevant examples
- Recommended for entity types with good representation

### Mixed Filtering
- Keeps all examples but maps non-target entities to "O" labels
- Maintains dataset size while focusing on target entity
- Recommended for entity types with limited representation

## Output Structure

Training outputs are organized as follows:

```
checkpoints/
├── PER_20250126_143022_v1.0/
│   ├── model/                    # Trained model files
│   ├── logs/                     # Training logs
│   ├── metrics/                  # Evaluation metrics
│   ├── config.yaml              # Training configuration used
│   ├── model_card.md            # Auto-generated model card
│   ├── README.md                # Usage instructions
│   ├── predictions.jsonl        # Validation predictions
│   └── dataset_info.json        # Dataset statistics
└── batch_report_20250126_143500/
    ├── batch_training_summary.json
    └── batch_training_report.md
```

## WandB Integration

The CLI automatically integrates with Weights & Biases for experiment tracking:

- **Automatic Logging**: Loss, metrics, and hyperparameters
- **Model Artifacts**: Trained models and checkpoints
- **Confusion Matrix**: Per-class performance visualization
- **Custom Metrics**: Entity-specific F1 scores and precision/recall

Configure WandB settings in your configuration file or via CLI options.

## Error Handling and Validation

The CLI includes comprehensive error handling:

- **Input Validation**: Checks file existence and parameter ranges
- **Configuration Validation**: Validates YAML structure and parameter compatibility
- **Data Validation**: Ensures proper data format and entity alignment
- **Hardware Detection**: Automatically handles GPU/CPU fallback
- **Graceful Interruption**: Handles Ctrl+C interruption cleanly

## Performance Tips

1. **Use GPU**: Enable GPU training with `--fp16` for mixed precision
2. **Batch Size**: Increase batch size for better GPU utilization
3. **Parallel Training**: Use `--parallel` for multi-head training
4. **Class Weights**: Use `--use-class-weights` for imbalanced datasets
5. **Early Stopping**: Configure appropriate patience to avoid overfitting

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce batch size or enable gradient accumulation
2. **WandB Login**: Run `wandb login` before training
3. **Data Format**: Ensure input data matches expected format
4. **Configuration Errors**: Use `validate` command to check config files

### Debug Mode

Enable debug logging by setting environment variable:

```bash
export PYTHONPATH=src
export LOG_LEVEL=DEBUG
python train.py single-head PER data_sets/dutch_ner.json
```

## Examples Directory

The `examples/configs/` directory contains sample configuration files:

- `default_training.yaml`: Standard configuration
- `test_training.yaml`: Quick testing configuration
- `production_training.yaml`: Production-optimized configuration

Use these as starting points for your own configurations.