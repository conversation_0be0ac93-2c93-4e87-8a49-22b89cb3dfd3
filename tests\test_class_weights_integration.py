"""
Integration tests for class weight functionality with training pipeline.

This module tests the integration of class weights with the actual
Hugging Face training pipeline and configuration system.
"""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch

import torch
from datasets import Dataset, DatasetDict

from src.training.class_weights import ClassWeightCalculator
from src.training.hf_config import HFTrainingConfig


@pytest.fixture
def sample_imbalanced_dataset():
    """Create a realistic imbalanced dataset for integration testing."""
    examples = []
    
    # Many examples with only O labels (realistic imbalance)
    for i in range(20):
        examples.append({
            'input_ids': [0, 1, 2, 3, 4, 5],
            'attention_mask': [1, 1, 1, 1, 1, 1],
            'labels': [0, 0, 0, 0, 0, 0]  # All O
        })
    
    # Few examples with PER entities
    for i in range(3):
        examples.append({
            'input_ids': [0, 6, 7, 8, 9, 10],
            'attention_mask': [1, 1, 1, 1, 1, 1],
            'labels': [0, 1, 2, 0, 0, 0]  # O, B-PER, I-PER, O, O, O
        })
    
    # Very few examples with LOC entities
    examples.append({
        'input_ids': [0, 11, 12, 13, 14, 15],
        'attention_mask': [1, 1, 1, 1, 1, 1],
        'labels': [0, 3, 4, 0, 0, 0]  # O, B-LOC, I-LOC, O, O, O
    })
    
    dataset = Dataset.from_list(examples)
    
    # Create train/validation split
    split_dataset = dataset.train_test_split(test_size=0.2, seed=42)
    
    return DatasetDict({
        'train': split_dataset['train'],
        'validation': split_dataset['test']
    })


@pytest.fixture
def label_list():
    """Standard label list for testing."""
    return ["O", "B-PER", "I-PER", "B-LOC", "I-LOC"]


class TestClassWeightIntegration:
    """Test class weight integration with training pipeline."""
    
    def test_config_class_weight_computation(self, sample_imbalanced_dataset, label_list):
        """Test class weight computation through HFTrainingConfig."""
        config = HFTrainingConfig(
            use_class_weights=True,
            class_weight_method="balanced",
            save_class_weights=True
        )
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Compute weights
            weights = config.compute_and_set_class_weights(
                dataset=sample_imbalanced_dataset,
                label_list=label_list,
                output_dir=temp_dir
            )
            
            # Verify weights were computed and set
            assert weights is not None
            assert config.class_weights is not None
            assert len(config.class_weights) == len(label_list)
            
            # Verify O label has lower weight (more frequent)
            assert config.class_weights["O"] < config.class_weights["B-PER"]
            
            # Verify weights file was saved
            weights_file = Path(temp_dir) / "class_weights.json"
            assert weights_file.exists()
            
            # Verify file contents
            with open(weights_file, 'r') as f:
                saved_data = json.load(f)
            
            assert "class_weights" in saved_data
            assert "metadata" in saved_data
            assert saved_data["class_weights"] == weights
    
    def test_per_head_class_weight_computation(self, sample_imbalanced_dataset, label_list):
        """Test per-head class weight computation through config."""
        config = HFTrainingConfig(
            use_class_weights=True,
            per_head_class_weights=True,
            class_weight_method="balanced",
            save_class_weights=True
        )
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Compute per-head weights
            per_head_weights = config.compute_per_head_class_weights(
                dataset=sample_imbalanced_dataset,
                label_list=label_list,
                output_dir=temp_dir
            )
            
            # Verify weights were computed
            assert per_head_weights is not None
            assert "PER" in per_head_weights
            assert "LOC" in per_head_weights
            
            # Verify each head has 3 labels
            for entity_type, weights in per_head_weights.items():
                assert len(weights) == 3  # O, B-ENTITY, I-ENTITY
                assert "O" in weights
                assert f"B-{entity_type}" in weights
                assert f"I-{entity_type}" in weights
            
            # Verify weights file was saved
            weights_file = Path(temp_dir) / "per_head_class_weights.json"
            assert weights_file.exists()
    
    def test_class_weight_tensor_creation(self, sample_imbalanced_dataset, label_list):
        """Test PyTorch tensor creation for loss computation."""
        calculator = ClassWeightCalculator()
        
        # Compute weights
        weights = calculator.compute_class_weights(
            dataset=sample_imbalanced_dataset,
            label_list=label_list,
            method="balanced"
        )
        
        # Create tensor
        weight_tensor = calculator.create_class_weight_tensor(
            weights, label_list
        )
        
        # Verify tensor properties
        assert isinstance(weight_tensor, torch.Tensor)
        assert weight_tensor.shape == (len(label_list),)
        assert weight_tensor.dtype == torch.float32
        
        # Verify tensor values match weights
        for i, label in enumerate(label_list):
            expected_weight = weights[label]
            actual_weight = weight_tensor[i].item()
            assert abs(actual_weight - expected_weight) < 1e-6
    
    def test_class_weight_validation(self, label_list):
        """Test class weight validation in config."""
        # Valid weights should pass
        valid_weights = {label: 1.0 for label in label_list}
        config = HFTrainingConfig(
            use_class_weights=True,
            class_weights=valid_weights
        )
        
        # Should not raise error
        config.validate_class_weights(label_list)
        
        # Invalid weights should fail
        invalid_weights = {"O": 1.0}  # Missing labels
        config_invalid = HFTrainingConfig(
            use_class_weights=True,
            class_weights=invalid_weights
        )
        
        with pytest.raises(ValueError, match="Missing class weights"):
            config_invalid.validate_class_weights(label_list)
    
    def test_disabled_class_weights(self, sample_imbalanced_dataset, label_list):
        """Test that disabled class weights work correctly."""
        config = HFTrainingConfig(use_class_weights=False)
        
        # Should return None when disabled
        weights = config.compute_and_set_class_weights(
            dataset=sample_imbalanced_dataset,
            label_list=label_list
        )
        
        assert weights is None
        assert config.class_weights is None
    
    def test_class_weight_effectiveness_analysis(self, sample_imbalanced_dataset, label_list):
        """Test analysis of class weight effectiveness."""
        calculator = ClassWeightCalculator()
        
        # Analyze original distribution
        original_analysis = calculator.analyze_label_distribution(
            sample_imbalanced_dataset, label_list
        )
        
        # Compute weights
        weights = calculator.compute_class_weights(
            sample_imbalanced_dataset, label_list, method="balanced"
        )
        
        # Verify imbalance is addressed by weights
        o_weight = weights["O"]
        per_weight = weights["B-PER"]
        
        # Rare labels should have higher weights
        assert per_weight > o_weight
        
        # Weight ratio should be inversely related to frequency
        o_count = original_analysis['label_counts'].get("O", 1)
        per_count = original_analysis['label_counts'].get("B-PER", 1)
        
        # Higher frequency should lead to lower weight
        if o_count > per_count:
            assert o_weight < per_weight
    
    def test_yaml_config_integration(self, sample_imbalanced_dataset, label_list):
        """Test class weight configuration through YAML."""
        yaml_content = """
hf_training:
  use_class_weights: true
  class_weight_method: "balanced"
  per_head_class_weights: true
  save_class_weights: true
  epochs: 1
  batch_size: 4
"""
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save YAML config
            config_path = Path(temp_dir) / "config.yaml"
            with open(config_path, 'w') as f:
                f.write(yaml_content)
            
            # Load config from YAML
            config = HFTrainingConfig.from_yaml(str(config_path))
            
            # Verify class weight settings
            assert config.use_class_weights is True
            assert config.class_weight_method == "balanced"
            assert config.per_head_class_weights is True
            assert config.save_class_weights is True
            
            # Test weight computation
            weights = config.compute_and_set_class_weights(
                dataset=sample_imbalanced_dataset,
                label_list=label_list
            )
            
            assert weights is not None
            assert config.class_weights is not None
    
    def test_edge_case_single_entity_type(self, label_list):
        """Test class weights with dataset containing only one entity type."""
        # Create dataset with only PER entities
        examples = [
            {
                'input_ids': [0, 1, 2, 3],
                'attention_mask': [1, 1, 1, 1],
                'labels': [0, 1, 2, 0]  # O, B-PER, I-PER, O
            },
            {
                'input_ids': [0, 4, 5, 6],
                'attention_mask': [1, 1, 1, 1],
                'labels': [0, 0, 0, 0]  # All O
            }
        ]
        
        dataset = Dataset.from_list(examples)
        calculator = ClassWeightCalculator()
        
        # Should handle single entity type gracefully
        per_head_weights = calculator.compute_per_head_class_weights(
            dataset=dataset,
            label_list=label_list,
            entity_types=["PER"],  # Only PER
            method="balanced"
        )
        
        assert "PER" in per_head_weights
        assert "LOC" not in per_head_weights
        assert len(per_head_weights["PER"]) == 3  # O, B-PER, I-PER
    
    def test_class_weight_with_special_tokens(self, label_list):
        """Test class weight computation with special tokens (-100)."""
        examples = [
            {
                'input_ids': [0, 1, 2, 3],
                'attention_mask': [1, 1, 1, 1],
                'labels': [-100, 0, 1, -100]  # Special tokens should be ignored
            },
            {
                'input_ids': [0, 4, 5, 6],
                'attention_mask': [1, 1, 1, 1],
                'labels': [0, -100, 0, 0]
            }
        ]
        
        dataset = Dataset.from_list(examples)
        calculator = ClassWeightCalculator()
        
        # Should ignore special tokens
        weights = calculator.compute_class_weights(
            dataset=dataset,
            label_list=label_list,
            method="balanced"
        )
        
        # Should work without errors
        assert isinstance(weights, dict)
        assert len(weights) == len(label_list)
    
    @patch('src.training.class_weights.compute_class_weight')
    def test_sklearn_integration(self, mock_sklearn_compute, sample_imbalanced_dataset, label_list):
        """Test integration with sklearn's compute_class_weight."""
        # Mock sklearn function
        mock_sklearn_compute.return_value = [0.2, 2.0, 1.5]  # Example weights
        
        calculator = ClassWeightCalculator()
        
        weights = calculator.compute_class_weights(
            dataset=sample_imbalanced_dataset,
            label_list=label_list,
            method="balanced"
        )
        
        # Verify sklearn function was called
        mock_sklearn_compute.assert_called_once()
        
        # Verify call arguments
        call_args = mock_sklearn_compute.call_args
        assert call_args[1]['class_weight'] == 'balanced'
        
        # Should return weights dictionary
        assert isinstance(weights, dict)


class TestRealWorldScenarios:
    """Test class weights with realistic scenarios."""
    
    def test_highly_imbalanced_dataset(self):
        """Test with extremely imbalanced dataset (99% O labels)."""
        examples = []
        
        # 99 examples with only O labels
        for i in range(99):
            examples.append({
                'input_ids': [0, 1, 2, 3, 4],
                'attention_mask': [1, 1, 1, 1, 1],
                'labels': [0, 0, 0, 0, 0]
            })
        
        # 1 example with entity
        examples.append({
            'input_ids': [0, 5, 6, 7, 8],
            'attention_mask': [1, 1, 1, 1, 1],
            'labels': [0, 1, 2, 0, 0]  # B-PER, I-PER
        })
        
        dataset = Dataset.from_list(examples)
        label_list = ["O", "B-PER", "I-PER"]
        
        calculator = ClassWeightCalculator()
        weights = calculator.compute_class_weights(
            dataset=dataset,
            label_list=label_list,
            method="balanced"
        )
        
        # Entity labels should have much higher weights
        assert weights["B-PER"] > weights["O"] * 10
        assert weights["I-PER"] > weights["O"] * 10
    
    def test_multiple_entity_types_different_imbalances(self):
        """Test with multiple entity types having different imbalance levels."""
        examples = []
        
        # Base O labels
        for i in range(50):
            examples.append({
                'input_ids': [0, 1, 2, 3],
                'attention_mask': [1, 1, 1, 1],
                'labels': [0, 0, 0, 0]
            })
        
        # Common entity type (PER) - 10 examples
        for i in range(10):
            examples.append({
                'input_ids': [0, 4, 5, 6],
                'attention_mask': [1, 1, 1, 1],
                'labels': [0, 1, 2, 0]  # B-PER, I-PER
            })
        
        # Rare entity type (ORG) - 2 examples
        for i in range(2):
            examples.append({
                'input_ids': [0, 7, 8, 9],
                'attention_mask': [1, 1, 1, 1],
                'labels': [0, 3, 4, 0]  # B-ORG, I-ORG
            })
        
        dataset = Dataset.from_list(examples)
        label_list = ["O", "B-PER", "I-PER", "B-ORG", "I-ORG"]
        
        calculator = ClassWeightCalculator()
        per_head_weights = calculator.compute_per_head_class_weights(
            dataset=dataset,
            label_list=label_list,
            method="balanced"
        )
        
        # ORG should have higher weights than PER (more imbalanced)
        org_b_weight = per_head_weights["ORG"]["B-ORG"]
        per_b_weight = per_head_weights["PER"]["B-PER"]
        
        assert org_b_weight > per_b_weight
    
    def test_config_with_real_training_args(self, sample_imbalanced_dataset, label_list):
        """Test class weights with realistic training configuration."""
        config = HFTrainingConfig(
            # Training settings
            epochs=3,
            batch_size=16,
            learning_rate=2e-5,
            
            # Class weight settings
            use_class_weights=True,
            class_weight_method="balanced",
            per_head_class_weights=True,
            save_class_weights=True,
            
            # WandB settings
            wandb_project="test-class-weights",
            run_name="imbalanced-ner-test"
        )
        
        # Compute weights
        weights = config.compute_and_set_class_weights(
            dataset=sample_imbalanced_dataset,
            label_list=label_list
        )
        
        # Convert to training args (should work without errors)
        training_args = config.to_training_args("test_output")
        
        # Verify training args were created successfully
        assert training_args is not None
        assert training_args.num_train_epochs == 3
        assert training_args.per_device_train_batch_size == 16
        assert training_args.learning_rate == 2e-5
        
        # Verify weights were computed
        assert weights is not None
        assert config.class_weights is not None