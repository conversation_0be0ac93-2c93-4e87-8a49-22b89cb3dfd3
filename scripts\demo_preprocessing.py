#!/usr/bin/env python3
"""
Demonstration script for the sentence + entities preprocessing functionality.

This script shows how to use the preprocessing utilities to convert raw Dutch text
with entity annotations to tokenized format suitable for Hugging Face Trainer.
"""

import json
import tempfile
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.data.preprocessing import preprocess_raw_data, SentenceEntityPreprocessor


def demo_basic_preprocessing():
    """Demonstrate basic preprocessing functionality."""
    print("=== Basic Preprocessing Demo ===\n")
    
    # Sample Dutch data with various entity types and formats
    sample_data = [
        {
            "id": 1,
            "sentence": "<PERSON><PERSON><PERSON> mev<PERSON><PERSON>,",
            "entities": [
                {"text": "van der <PERSON>", "label": "PER"}
            ]
        },
        {
            "id": 2,
            "sentence": "<PERSON> woon<PERSON> in Amsterdam en werkt bij Google.",
            "entities": [
                {"text": "<PERSON>", "label": "PER", "start": 0, "end": 10},
                {"text": "Amsterdam", "label": "LOC", "start": 20, "end": 29},
                {"text": "Google", "label": "ORG", "start": 43, "end": 49}
            ]
        },
        {
            "id": 3,
            "sentence": "<PERSON>-<PERSON> de Jong en Anne-<PERSON> <PERSON> wonen hier.",
            "entities": [
                {"text": "<PERSON>-<PERSON> de Jong", "label": "PER"},
                {"text": "Anne-<PERSON> <PERSON>", "label": "PER"}
            ]
        },
        {
            "id": 4,
            "sentence": "Prof. dr. <PERSON> Jan<PERSON> spreekt vandaag in Utrecht.",
            "entities": [
                {"text": "Willem Jansen", "label": "PER"},
                {"text": "Utrecht", "label": "LOC"}
            ]
        }
    ]
    
    # Create temporary files
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as input_file:
        json.dump(sample_data, input_file, indent=2, ensure_ascii=False)
        input_path = input_file.name
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as output_file:
        output_path = output_file.name
    
    try:
        print(f"Input data ({len(sample_data)} examples):")
        for example in sample_data:
            print(f"  {example['id']}: {example['sentence']}")
            for entity in example['entities']:
                span_info = f" [{entity.get('start', '?')}:{entity.get('end', '?')}]" if 'start' in entity else " [auto-detect]"
                print(f"    - {entity['text']} ({entity['label']}){span_info}")
        print()
        
        # Process the data
        print("Processing data...")
        result_path = preprocess_raw_data(input_path, output_path)
        print(f"✅ Processing complete! Output saved to: {result_path}\n")
        
        # Show results
        with open(output_path, 'r', encoding='utf-8') as f:
            processed_data = json.load(f)
        
        print(f"Processed data ({len(processed_data)} examples):")
        for example in processed_data:
            print(f"  Example {example['id']}:")
            print(f"    Sentence: {example['sentence']}")
            print(f"    Tokens ({len(example['tokens'])}): {example['tokens'][:10]}{'...' if len(example['tokens']) > 10 else ''}")
            print(f"    Labels ({len(example['labels'])}): {example['labels'][:10]}{'...' if len(example['labels']) > 10 else ''}")
            
            # Count entity labels
            entity_labels = [label for label in example['labels'] if label != 'O']
            print(f"    Entity tokens: {len(entity_labels)} ({', '.join(set(entity_labels))})")
            print()
    
    finally:
        # Clean up
        Path(input_path).unlink(missing_ok=True)
        Path(output_path).unlink(missing_ok=True)


def demo_span_validation():
    """Demonstrate span validation and auto-detection features."""
    print("=== Span Validation & Auto-Detection Demo ===\n")
    
    # Create preprocessor (using mock for demo to avoid model download)
    try:
        preprocessor = SentenceEntityPreprocessor("DTAI-KULeuven/robbert-2023-dutch-base")
        print("✅ Using real RobBERT tokenizer")
    except Exception:
        print("⚠️  RobBERT model not available, using mock for demo")
        from unittest.mock import Mock, patch
        
        with patch('src.data.preprocessing.AutoTokenizer') as mock_tokenizer_class:
            mock_tokenizer = Mock()
            mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
            preprocessor = SentenceEntityPreprocessor("mock-model")
    
    # Test cases for span validation
    test_cases = [
        {
            "sentence": "Jan Jansen woont in Amsterdam.",
            "entities": [
                {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10},  # Correct
                {"text": "Amsterdam", "label": "LOC", "start": 20, "end": 29}   # Correct
            ],
            "description": "Correct explicit spans"
        },
        {
            "sentence": "Jan Jansen woont in Amsterdam.",
            "entities": [
                {"text": "Jan Jansen", "label": "PER", "start": 5, "end": 15},  # Wrong span
                {"text": "Amsterdam", "label": "LOC"}  # Auto-detect
            ],
            "description": "Mixed: wrong explicit span (fallback to auto-detect) + auto-detect"
        },
        {
            "sentence": "Marie-Claire de Jong en Anne-Sophie Müller.",
            "entities": [
                {"text": "Marie-Claire de Jong", "label": "PER"},  # Auto-detect hyphenated
                {"text": "Anne-Sophie Müller", "label": "PER"}    # Auto-detect hyphenated
            ],
            "description": "Auto-detect hyphenated Dutch names"
        },
        {
            "sentence": "Dhr. J. van der Berg en Mevr. A. de Vries.",
            "entities": [
                {"text": "J. van der Berg", "label": "PER"},  # Auto-detect with particles
                {"text": "A. de Vries", "label": "PER"}       # Auto-detect with particles
            ],
            "description": "Auto-detect names with Dutch particles"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test Case {i}: {test_case['description']}")
        print(f"  Sentence: {test_case['sentence']}")
        print(f"  Entities:")
        
        for entity in test_case['entities']:
            span_info = f" [{entity.get('start', '?')}:{entity.get('end', '?')}]" if 'start' in entity else " [auto-detect]"
            print(f"    - {entity['text']} ({entity['label']}){span_info}")
        
        # Get entity spans
        try:
            spans = preprocessor.get_entity_spans(test_case['sentence'], test_case['entities'])
            print(f"  Results:")
            for span in spans:
                extracted = test_case['sentence'][span.start:span.end]
                match_status = "✅" if extracted == span.text else "⚠️"
                print(f"    {match_status} {span.text} ({span.label}) [{span.start}:{span.end}] -> '{extracted}'")
        except Exception as e:
            print(f"    ❌ Error: {e}")
        
        print()


def demo_statistics():
    """Demonstrate preprocessing statistics tracking."""
    print("=== Preprocessing Statistics Demo ===\n")
    
    # Create preprocessor with mock to avoid model download
    from unittest.mock import Mock, patch
    
    with patch('src.data.preprocessing.AutoTokenizer') as mock_tokenizer_class:
        mock_tokenizer = Mock()
        mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
        
        # Configure mock tokenizer
        mock_tokenizer.side_effect = [
            {
                "input_ids": [0, 1, 2, 3, 4, 5, 6, 2],
                "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1],
                "offset_mapping": [(0, 0), (0, 3), (4, 10), (11, 16), (17, 19), (20, 29), (29, 30), (0, 0)]
            }
        ] * 10  # Repeat for multiple examples
        
        mock_tokenizer.convert_ids_to_tokens.return_value = ["<s>", "ĠJan", "ĠJansen", "Ġwoont", "Ġin", "ĠAmsterdam", ".", "</s>"]
        
        preprocessor = SentenceEntityPreprocessor("mock-model")
        
        # Test data with various scenarios
        test_examples = [
            {
                "sentence": "Jan Jansen woont in Amsterdam.",
                "entities": [
                    {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10},  # Explicit, correct
                    {"text": "Amsterdam", "label": "LOC"}  # Auto-detect
                ]
            },
            {
                "sentence": "Marie de Vries werkt hier.",
                "entities": [
                    {"text": "Marie de Vries", "label": "PER"}  # Auto-detect
                ]
            },
            {
                "sentence": "Piet woont in Rotterdam.",
                "entities": [
                    {"text": "Piet", "label": "PER", "start": 5, "end": 9},  # Wrong explicit span
                    {"text": "NonExistent", "label": "PER"}  # Not found
                ]
            }
        ]
        
        print("Processing examples to collect statistics...")
        for example in test_examples:
            try:
                preprocessor.preprocess_example(example)
            except Exception as e:
                print(f"Error processing example: {e}")
        
        # Show statistics
        stats = preprocessor.stats
        print(f"\nProcessing Statistics:")
        print(f"  Processed examples: {stats['processed_examples']}")
        print(f"  Total entities: {stats['total_entities']}")
        print(f"  Aligned entities: {stats['aligned_entities']}")
        print(f"  Unaligned entities: {stats['unaligned_entities']}")
        print(f"  Truncated entities: {stats['truncated_entities']}")
        print(f"  Auto-detected spans: {stats['auto_detected_spans']}")
        print(f"  Explicit spans: {stats['explicit_spans']}")
        
        # Calculate success rates
        if stats['total_entities'] > 0:
            alignment_rate = (stats['aligned_entities'] / stats['total_entities']) * 100
            auto_detect_rate = (stats['auto_detected_spans'] / stats['total_entities']) * 100
            print(f"\nSuccess Rates:")
            print(f"  Entity alignment: {alignment_rate:.1f}%")
            print(f"  Auto-detection usage: {auto_detect_rate:.1f}%")


def main():
    """Run all demonstrations."""
    print("🚀 Dutch NER Preprocessing Utilities Demo\n")
    print("This demo shows the preprocessing functionality for converting")
    print("sentence + entities format to tokenized format for Hugging Face Trainer.\n")
    
    try:
        demo_basic_preprocessing()
        print("\n" + "="*60 + "\n")
        
        demo_span_validation()
        print("\n" + "="*60 + "\n")
        
        demo_statistics()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ Demo complete!")


if __name__ == "__main__":
    main()