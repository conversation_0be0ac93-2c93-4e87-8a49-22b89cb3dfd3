"""
Enhanced WandB integration for Hugging Face Trainer with advanced logging.

This module provides CustomWandbCallback for confusion matrix logging, per-class metrics,
model artifact tracking, and detailed evaluation results in WandB tables.
"""

import os
import json
import wandb
import numpy as np
import torch
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from datetime import datetime
from collections import defaultdict

from transformers import TrainerCallback, TrainingArguments, TrainerState, TrainerControl
from transformers.integrations import WandbCallback
from sklearn.metrics import confusion_matrix

from .hf_evaluation_metrics import create_confusion_matrix, compute_metrics
from ..utils.logging_utils import get_logger


class CustomWandbCallback(WandbCallback):
    """
    Enhanced WandB callback with confusion matrix, per-class metrics, and detailed logging.
    
    This callback extends the default WandbCallback to provide:
    - Confusion matrix visualization
    - Per-entity-type metrics tracking
    - Model artifact logging with metadata
    - Detailed evaluation results in WandB tables
    - Training progress visualization
    """
    
    def __init__(self, label_list: List[str], config: Optional[Dict[str, Any]] = None):
        """
        Initialize enhanced WandB callback.
        
        Args:
            label_list: List of NER labels (e.g., ["O", "B-PER", "I-PER"])
            config: Additional configuration for logging behavior
        """
        super().__init__()
        self.label_list = label_list
        self.config = config or {}
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        # Tracking variables
        self.best_metrics = {}
        self.metrics_history = []
        self.confusion_matrices = []
        self.evaluation_tables = []
        
        # Configuration options
        self.log_confusion_matrix = self.config.get('log_confusion_matrix', True)
        self.log_per_class_metrics = self.config.get('log_per_class_metrics', True)
        self.log_model_artifacts = self.config.get('log_model_artifacts', True)
        self.log_evaluation_tables = self.config.get('log_evaluation_tables', True)
        self.confusion_matrix_frequency = self.config.get('confusion_matrix_frequency', 'epoch')  # 'epoch' or 'step'
        
        self.logger.logger.info(f"CustomWandbCallback initialized with {len(label_list)} labels")
    
    def setup(self, args: TrainingArguments, state: TrainerState, model, **kwargs):
        """
        Setup WandB run with enhanced configuration.
        
        Args:
            args: Training arguments
            state: Trainer state
            model: Model being trained
            **kwargs: Additional arguments
        """
        super().setup(args, state, model, **kwargs)
        
        if self._wandb is None:
            return
        
        # Log model architecture summary
        self._log_model_summary(model)
        
        # Log training configuration
        self._log_training_config(args)
        
        # Log label information
        self._log_label_info()
        
        self.logger.logger.info("Enhanced WandB logging setup completed")
    
    def on_evaluate(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        model=None,
        tokenizer=None,
        eval_dataloader=None,
        **kwargs
    ):
        """
        Enhanced evaluation logging with confusion matrix and detailed metrics.
        
        Args:
            args: Training arguments
            state: Trainer state
            control: Trainer control
            model: Model being evaluated
            tokenizer: Tokenizer
            eval_dataloader: Evaluation dataloader
            **kwargs: Additional arguments
        """
        # Call parent method for standard logging
        super().on_evaluate(args, state, control, **kwargs)
        
        if self._wandb is None:
            return
        
        # Get latest evaluation metrics
        if state.log_history:
            latest_log = state.log_history[-1]
            eval_metrics = {k: v for k, v in latest_log.items() if k.startswith("eval_")}
            
            if eval_metrics:
                # Log enhanced metrics
                self._log_enhanced_metrics(eval_metrics, state)
                
                # Log confusion matrix if enabled
                if self.log_confusion_matrix and self._should_log_confusion_matrix(state):
                    self._log_confusion_matrix_from_model(
                        model, tokenizer, eval_dataloader, state
                    )
                
                # Log per-class metrics if available
                if self.log_per_class_metrics:
                    self._log_per_class_metrics(eval_metrics, state)
                
                # Create evaluation table entry
                if self.log_evaluation_tables:
                    self._add_evaluation_table_entry(eval_metrics, state)
                
                # Track best metrics
                self._update_best_metrics(eval_metrics, state)
    
    def on_train_end(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        model=None,
        tokenizer=None,
        **kwargs
    ):
        """
        Enhanced training end logging with final artifacts and summaries.
        
        Args:
            args: Training arguments
            state: Trainer state
            control: Trainer control
            model: Final trained model
            tokenizer: Tokenizer
            **kwargs: Additional arguments
        """
        # Call parent method
        super().on_train_end(args, state, control, model, tokenizer, **kwargs)
        
        if self._wandb is None:
            return
        
        # Log final model artifacts
        if self.log_model_artifacts and model is not None:
            self._log_final_model_artifact(args, model, tokenizer, state)
        
        # Log training summary
        self._log_training_summary(state)
        
        # Log evaluation tables
        if self.log_evaluation_tables and self.evaluation_tables:
            self._log_evaluation_tables()
        
        # Log best metrics summary
        self._log_best_metrics_summary()
        
        self.logger.logger.info("Enhanced WandB training end logging completed")
    
    def _log_model_summary(self, model):
        """Log model architecture summary."""
        try:
            # Count parameters
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            model_info = {
                "model_architecture": model.__class__.__name__,
                "total_parameters": total_params,
                "trainable_parameters": trainable_params,
                "model_size_mb": total_params * 4 / (1024 * 1024),  # Assuming float32
                "num_labels": len(self.label_list),
                "labels": self.label_list
            }
            
            self._wandb.log({"model_info": model_info})
            self.logger.logger.info(f"Logged model summary: {trainable_params:,} trainable parameters")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to log model summary: {e}")
    
    def _log_training_config(self, args: TrainingArguments):
        """Log detailed training configuration."""
        try:
            training_config = {
                "learning_rate": args.learning_rate,
                "train_batch_size": args.per_device_train_batch_size,
                "eval_batch_size": args.per_device_eval_batch_size,
                "num_train_epochs": args.num_train_epochs,
                "warmup_steps": args.warmup_steps,
                "weight_decay": args.weight_decay,
                "lr_scheduler_type": args.lr_scheduler_type,
                "evaluation_strategy": args.evaluation_strategy,
                "eval_steps": args.eval_steps,
                "logging_steps": args.logging_steps,
                "save_steps": args.save_steps,
                "fp16": args.fp16,
                "bf16": args.bf16,
                "gradient_accumulation_steps": args.gradient_accumulation_steps,
                "max_grad_norm": args.max_grad_norm,
                "seed": args.seed,
            }
            
            self._wandb.config.update(training_config)
            self.logger.logger.info("Logged training configuration to WandB")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to log training configuration: {e}")
    
    def _log_label_info(self):
        """Log label information and statistics."""
        try:
            # Extract entity types from BIO labels
            entity_types = set()
            for label in self.label_list:
                if label != "O" and "-" in label:
                    entity_types.add(label.split("-", 1)[1])
            
            label_info = {
                "total_labels": len(self.label_list),
                "entity_types": sorted(list(entity_types)),
                "label_scheme": "BIO" if any(label.startswith("B-") for label in self.label_list) else "IO",
                "all_labels": self.label_list
            }
            
            self._wandb.log({"label_info": label_info})
            self.logger.logger.info(f"Logged label information: {len(entity_types)} entity types")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to log label information: {e}")
    
    def _log_enhanced_metrics(self, eval_metrics: Dict[str, float], state: TrainerState):
        """Log enhanced evaluation metrics with additional context."""
        try:
            # Add context to metrics
            enhanced_metrics = {
                "epoch": state.epoch,
                "global_step": state.global_step,
                **eval_metrics
            }
            
            # Calculate metric improvements
            if self.metrics_history:
                prev_metrics = self.metrics_history[-1]
                for metric_name, current_value in eval_metrics.items():
                    if metric_name in prev_metrics:
                        improvement = current_value - prev_metrics[metric_name]
                        enhanced_metrics[f"{metric_name}_improvement"] = improvement
            
            # Store in history
            self.metrics_history.append(enhanced_metrics)
            
            # Log to WandB
            self._wandb.log(enhanced_metrics, step=state.global_step)
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to log enhanced metrics: {e}")
    
    def _should_log_confusion_matrix(self, state: TrainerState) -> bool:
        """Determine if confusion matrix should be logged at this step."""
        if self.confusion_matrix_frequency == 'epoch':
            # Log at end of each epoch
            return True
        elif self.confusion_matrix_frequency == 'step':
            # Log every N steps (configurable)
            log_frequency = self.config.get('confusion_matrix_steps', 500)
            return state.global_step % log_frequency == 0
        return False
    
    def _log_confusion_matrix_from_model(
        self,
        model,
        tokenizer,
        eval_dataloader,
        state: TrainerState
    ):
        """Generate and log confusion matrix from model predictions."""
        try:
            if eval_dataloader is None:
                self.logger.logger.warning("No evaluation dataloader available for confusion matrix")
                return
            
            # Get predictions from model
            model.eval()
            all_predictions = []
            all_labels = []
            
            with torch.no_grad():
                for batch in eval_dataloader:
                    # Move batch to model device
                    batch = {k: v.to(model.device) if isinstance(v, torch.Tensor) else v 
                            for k, v in batch.items()}
                    
                    outputs = model(**batch)
                    predictions = torch.argmax(outputs.logits, dim=-1)
                    
                    # Convert to CPU and numpy
                    predictions = predictions.cpu().numpy()
                    labels = batch['labels'].cpu().numpy()
                    
                    all_predictions.extend(predictions)
                    all_labels.extend(labels)
            
            # Convert to label sequences
            pred_sequences = []
            label_sequences = []
            
            for pred_seq, label_seq in zip(all_predictions, all_labels):
                pred_labels = []
                true_labels = []
                
                for pred_id, label_id in zip(pred_seq, label_seq):
                    if label_id != -100:  # Skip ignored tokens
                        if pred_id < len(self.label_list):
                            pred_labels.append(self.label_list[pred_id])
                        else:
                            pred_labels.append("O")
                        
                        if label_id < len(self.label_list):
                            true_labels.append(self.label_list[label_id])
                        else:
                            true_labels.append("O")
                
                if pred_labels and true_labels:
                    pred_sequences.append(pred_labels)
                    label_sequences.append(true_labels)
            
            # Create and log confusion matrix
            self._log_confusion_matrix(pred_sequences, label_sequences, state)
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to generate confusion matrix from model: {e}")
    
    def _log_confusion_matrix(
        self,
        predictions: List[List[str]],
        labels: List[List[str]],
        state: TrainerState
    ):
        """Log confusion matrix visualization to WandB."""
        try:
            # Flatten sequences for sklearn confusion matrix
            flat_predictions = [label for seq in predictions for label in seq]
            flat_labels = [label for seq in labels for label in seq]
            
            # Create confusion matrix
            cm = confusion_matrix(flat_labels, flat_predictions, labels=self.label_list)
            
            # Create WandB confusion matrix
            wandb_cm = wandb.plot.confusion_matrix(
                probs=None,
                y_true=flat_labels,
                preds=flat_predictions,
                class_names=self.label_list,
                title=f"Confusion Matrix - Epoch {state.epoch}"
            )
            
            # Log to WandB
            self._wandb.log({
                "confusion_matrix": wandb_cm,
                "confusion_matrix_epoch": state.epoch
            }, step=state.global_step)
            
            # Store for later analysis
            self.confusion_matrices.append({
                "epoch": state.epoch,
                "step": state.global_step,
                "matrix": cm.tolist(),
                "labels": self.label_list
            })
            
            self.logger.logger.info(f"Logged confusion matrix for epoch {state.epoch}")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to log confusion matrix: {e}")
    
    def _log_per_class_metrics(self, eval_metrics: Dict[str, float], state: TrainerState):
        """Log per-class metrics if available."""
        try:
            per_class_metrics = {}
            
            # Extract per-class metrics from eval_metrics
            for metric_name, value in eval_metrics.items():
                if any(entity in metric_name for entity in ["PER", "LOC", "ORG", "MISC"]):
                    per_class_metrics[metric_name] = value
            
            if per_class_metrics:
                # Create per-class metrics table
                per_class_data = []
                entity_types = set()
                
                for metric_name, value in per_class_metrics.items():
                    parts = metric_name.replace("eval_", "").split("_")
                    if len(parts) >= 2:
                        entity_type = parts[0]
                        metric_type = "_".join(parts[1:])
                        entity_types.add(entity_type)
                        
                        per_class_data.append({
                            "entity_type": entity_type,
                            "metric": metric_type,
                            "value": value,
                            "epoch": state.epoch
                        })
                
                # Log per-class metrics
                if per_class_data:
                    per_class_table = wandb.Table(
                        columns=["entity_type", "metric", "value", "epoch"],
                        data=[[row["entity_type"], row["metric"], row["value"], row["epoch"]] 
                              for row in per_class_data]
                    )
                    
                    self._wandb.log({
                        "per_class_metrics": per_class_table,
                        **per_class_metrics
                    }, step=state.global_step)
                    
                    self.logger.logger.info(f"Logged per-class metrics for {len(entity_types)} entity types")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to log per-class metrics: {e}")
    
    def _add_evaluation_table_entry(self, eval_metrics: Dict[str, float], state: TrainerState):
        """Add entry to evaluation results table."""
        try:
            table_entry = {
                "epoch": state.epoch,
                "step": state.global_step,
                "timestamp": datetime.now().isoformat(),
                **{k.replace("eval_", ""): v for k, v in eval_metrics.items()}
            }
            
            self.evaluation_tables.append(table_entry)
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to add evaluation table entry: {e}")
    
    def _update_best_metrics(self, eval_metrics: Dict[str, float], state: TrainerState):
        """Update best metrics tracking."""
        try:
            for metric_name, value in eval_metrics.items():
                if metric_name not in self.best_metrics or value > self.best_metrics[metric_name]["value"]:
                    self.best_metrics[metric_name] = {
                        "value": value,
                        "epoch": state.epoch,
                        "step": state.global_step
                    }
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to update best metrics: {e}")
    
    def _log_final_model_artifact(
        self,
        args: TrainingArguments,
        model,
        tokenizer,
        state: TrainerState
    ):
        """Log final trained model as WandB artifact."""
        try:
            # Create artifact name
            artifact_name = f"robbert-ner-model-{state.global_step}"
            
            # Create artifact
            artifact = wandb.Artifact(
                name=artifact_name,
                type="model",
                description=f"RobBERT NER model trained for {state.epoch} epochs",
                metadata={
                    "epochs": state.epoch,
                    "global_step": state.global_step,
                    "labels": self.label_list,
                    "model_class": model.__class__.__name__,
                    "final_metrics": self.best_metrics,
                    "training_args": {
                        "learning_rate": args.learning_rate,
                        "batch_size": args.per_device_train_batch_size,
                        "num_epochs": args.num_train_epochs,
                    }
                }
            )
            
            # Add model files
            if Path(args.output_dir).exists():
                artifact.add_dir(args.output_dir, name="model")
            
            # Log artifact
            self._wandb.log_artifact(artifact)
            
            self.logger.logger.info(f"Logged model artifact: {artifact_name}")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to log model artifact: {e}")
    
    def _log_training_summary(self, state: TrainerState):
        """Log comprehensive training summary."""
        try:
            summary = {
                "training_completed": True,
                "total_epochs": state.epoch,
                "total_steps": state.global_step,
                "best_metrics": self.best_metrics,
                "metrics_history_length": len(self.metrics_history),
                "confusion_matrices_logged": len(self.confusion_matrices)
            }
            
            self._wandb.log({"training_summary": summary})
            
            # Log training progress chart
            if self.metrics_history:
                self._log_training_progress_chart()
            
            self.logger.logger.info("Logged training summary")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to log training summary: {e}")
    
    def _log_training_progress_chart(self):
        """Log training progress visualization."""
        try:
            # Extract metrics for plotting
            epochs = [entry["epoch"] for entry in self.metrics_history]
            f1_scores = [entry.get("eval_f1", 0) for entry in self.metrics_history]
            losses = [entry.get("eval_loss", 0) for entry in self.metrics_history]
            
            # Create progress table
            progress_data = []
            for i, entry in enumerate(self.metrics_history):
                progress_data.append([
                    entry["epoch"],
                    entry["global_step"],
                    entry.get("eval_f1", 0),
                    entry.get("eval_precision", 0),
                    entry.get("eval_recall", 0),
                    entry.get("eval_loss", 0)
                ])
            
            progress_table = wandb.Table(
                columns=["epoch", "step", "f1", "precision", "recall", "loss"],
                data=progress_data
            )
            
            self._wandb.log({"training_progress": progress_table})
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to log training progress chart: {e}")
    
    def _log_evaluation_tables(self):
        """Log detailed evaluation results table."""
        try:
            if not self.evaluation_tables:
                return
            
            # Create comprehensive evaluation table
            columns = list(self.evaluation_tables[0].keys())
            data = [[entry[col] for col in columns] for entry in self.evaluation_tables]
            
            eval_table = wandb.Table(columns=columns, data=data)
            
            self._wandb.log({"detailed_evaluation_results": eval_table})
            
            self.logger.logger.info(f"Logged evaluation table with {len(self.evaluation_tables)} entries")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to log evaluation tables: {e}")
    
    def _log_best_metrics_summary(self):
        """Log summary of best achieved metrics."""
        try:
            if not self.best_metrics:
                return
            
            best_summary = {}
            for metric_name, metric_info in self.best_metrics.items():
                best_summary[f"best_{metric_name}"] = metric_info["value"]
                best_summary[f"best_{metric_name}_epoch"] = metric_info["epoch"]
                best_summary[f"best_{metric_name}_step"] = metric_info["step"]
            
            self._wandb.log({"best_metrics_summary": best_summary})
            
            # Log best metrics as summary metrics
            self._wandb.run.summary.update(best_summary)
            
            self.logger.logger.info("Logged best metrics summary")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to log best metrics summary: {e}")


def create_enhanced_wandb_callback(
    label_list: List[str],
    config: Optional[Dict[str, Any]] = None
) -> CustomWandbCallback:
    """
    Factory function to create enhanced WandB callback.
    
    Args:
        label_list: List of NER labels
        config: Configuration for callback behavior
        
    Returns:
        CustomWandbCallback instance
    """
    return CustomWandbCallback(label_list, config)


def setup_wandb_for_hf_trainer(
    project: str,
    entity: Optional[str] = None,
    name: Optional[str] = None,
    tags: Optional[List[str]] = None,
    notes: Optional[str] = None,
    config: Optional[Dict[str, Any]] = None
) -> None:
    """
    Setup WandB for Hugging Face Trainer integration.
    
    Args:
        project: WandB project name
        entity: WandB entity name
        name: Run name
        tags: List of tags
        notes: Run notes
        config: Run configuration
    """
    try:
        wandb.init(
            project=project,
            entity=entity,
            name=name,
            tags=tags or [],
            notes=notes or "",
            config=config or {},
            reinit=True
        )
        
        logger = get_logger(__name__)
        logger.logger.info(f"WandB initialized for project: {project}")
        
    except Exception as e:
        logger = get_logger(__name__)
        logger.logger.warning(f"Failed to initialize WandB: {e}")


# Export main classes and functions
__all__ = [
    'CustomWandbCallback',
    'create_enhanced_wandb_callback',
    'setup_wandb_for_hf_trainer'
]