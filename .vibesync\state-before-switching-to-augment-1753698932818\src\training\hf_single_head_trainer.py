"""
Per-head training isolation system for RobBERT-2023 NER training.

This module provides functionality to train individual entity type heads in isolation,
with configurable filtering strategies and 3-label schemes per head.
"""

import os
import json
import wandb
from typing import Dict, List, Optional, Tuple, Any, Union, Set
from pathlib import Path
from datetime import datetime
from collections import Counter

from datasets import Dataset, DatasetDict
from transformers import (
    Trainer,
    TrainingArguments,
    DataCollatorForTokenClassification,
    RobertaForTokenClassification,
    AutoTokenizer
)

from .hf_config import HFTrainingConfig
from .hf_trainer import HFNERTrainer
from .hf_model_setup import create_ner_model
from .hf_evaluation_metrics import compute_metrics
from ..data.hf_dataset_preparation import prepare_ner_dataset, DatasetStatistics
from ..utils.logging_utils import get_logger
from ..exceptions import TrainingError, DataProcessingError


class SingleHeadDatasetFilter:
    """
    Dataset filtering utilities for per-head training isolation.
    
    Supports different filtering strategies to prevent models from learning
    only "O" labels when training on specific entity types.
    """
    
    def __init__(self, entity_type: str, filtering_strategy: str = "strict"):
        """
        Initialize dataset filter for specific entity type.
        
        Args:
            entity_type: Target entity type (e.g., "PER", "LOC", "ORG")
            filtering_strategy: Filtering strategy ("strict" or "mixed")
        """
        self.entity_type = entity_type
        self.filtering_strategy = filtering_strategy
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        # Validate filtering strategy
        valid_strategies = ["strict", "mixed"]
        if filtering_strategy not in valid_strategies:
            raise ValueError(f"Invalid filtering strategy: {filtering_strategy}. "
                           f"Must be one of {valid_strategies}")
    
    def filter_dataset_for_entity_type(
        self,
        dataset: Dataset,
        label_list: List[str]
    ) -> Tuple[Dataset, List[str], Dict[str, int], Dict[int, str], Dict[str, Any]]:
        """
        Filter dataset to focus on specific entity type.
        
        Args:
            dataset: Input dataset with all entity types
            label_list: Original label list with all entity types
            
        Returns:
            Tuple of (filtered_dataset, head_label_list, head_label2id, head_id2label, filter_stats)
        """
        self.logger.logger.info(f"Filtering dataset for entity type: {self.entity_type}")
        self.logger.logger.info(f"Using filtering strategy: {self.filtering_strategy}")
        
        # Create 3-label scheme for this head: ["O", "B-ENTITY", "I-ENTITY"]
        head_label_list = ["O", f"B-{self.entity_type}", f"I-{self.entity_type}"]
        head_label2id = {label: i for i, label in enumerate(head_label_list)}
        head_id2label = {i: label for i, label in enumerate(head_label_list)}
        
        # Filter examples based on strategy
        if self.filtering_strategy == "strict":
            filtered_dataset, filter_stats = self._filter_strict(dataset, label_list, head_label2id)
        else:  # mixed
            filtered_dataset, filter_stats = self._filter_mixed(dataset, label_list, head_label2id)
        
        # Log filtering results
        self._log_filtering_results(filter_stats)
        
        return filtered_dataset, head_label_list, head_label2id, head_id2label, filter_stats
    
    def _filter_strict(
        self,
        dataset: Dataset,
        original_label_list: List[str],
        head_label2id: Dict[str, int]
    ) -> Tuple[Dataset, Dict[str, Any]]:
        """
        Strict filtering: Only keep examples that contain the target entity type.
        
        This prevents the model from learning only "O" labels by ensuring
        every training example has at least one target entity.
        """
        self.logger.logger.info("Applying strict filtering (only examples with target entity)")
        
        # Create mapping from original labels to head labels
        original_label2id = {label: i for i, label in enumerate(original_label_list)}
        
        def has_target_entity(example):
            """Check if example contains target entity type."""
            labels = example['labels']
            for label_id in labels:
                if label_id >= 0 and label_id < len(original_label_list):
                    label = original_label_list[label_id]
                    if label.endswith(f"-{self.entity_type}"):
                        return True
            return False
        
        def convert_labels(example):
            """Convert labels to 3-label scheme for this head."""
            original_labels = example['labels']
            converted_labels = []
            
            for label_id in original_labels:
                if label_id == -100:  # Special token
                    converted_labels.append(-100)
                elif label_id < len(original_label_list):
                    original_label = original_label_list[label_id]
                    
                    if original_label == "O":
                        converted_labels.append(head_label2id["O"])
                    elif original_label == f"B-{self.entity_type}":
                        converted_labels.append(head_label2id[f"B-{self.entity_type}"])
                    elif original_label == f"I-{self.entity_type}":
                        converted_labels.append(head_label2id[f"I-{self.entity_type}"])
                    else:
                        # Non-target entity -> O in strict mode
                        converted_labels.append(head_label2id["O"])
                else:
                    # Unknown label -> O
                    converted_labels.append(head_label2id["O"])
            
            return {**example, 'labels': converted_labels}
        
        # Filter examples with target entity
        original_size = len(dataset)
        filtered_dataset = dataset.filter(has_target_entity)
        filtered_size = len(filtered_dataset)
        
        # Convert labels to head scheme
        filtered_dataset = filtered_dataset.map(convert_labels)
        
        # Calculate statistics
        filter_stats = {
            "filtering_strategy": "strict",
            "entity_type": self.entity_type,
            "original_size": original_size,
            "filtered_size": filtered_size,
            "examples_removed": original_size - filtered_size,
            "removal_rate": (original_size - filtered_size) / original_size if original_size > 0 else 0,
            "label_conversion": "non_target_to_O"
        }
        
        return filtered_dataset, filter_stats
    
    def _filter_mixed(
        self,
        dataset: Dataset,
        original_label_list: List[str],
        head_label2id: Dict[str, int]
    ) -> Tuple[Dataset, Dict[str, Any]]:
        """
        Mixed filtering: Keep all examples but map non-target entities to "O".
        
        This approach maintains dataset size while focusing the model on
        the target entity type by treating other entities as background.
        """
        self.logger.logger.info("Applying mixed filtering (map non-target entities to O)")
        
        def convert_labels(example):
            """Convert labels to 3-label scheme, mapping non-target entities to O."""
            original_labels = example['labels']
            converted_labels = []
            
            for label_id in original_labels:
                if label_id == -100:  # Special token
                    converted_labels.append(-100)
                elif label_id < len(original_label_list):
                    original_label = original_label_list[label_id]
                    
                    if original_label == "O":
                        converted_labels.append(head_label2id["O"])
                    elif original_label == f"B-{self.entity_type}":
                        converted_labels.append(head_label2id[f"B-{self.entity_type}"])
                    elif original_label == f"I-{self.entity_type}":
                        converted_labels.append(head_label2id[f"I-{self.entity_type}"])
                    else:
                        # Non-target entity -> O in mixed mode
                        converted_labels.append(head_label2id["O"])
                else:
                    # Unknown label -> O
                    converted_labels.append(head_label2id["O"])
            
            return {**example, 'labels': converted_labels}
        
        # Convert all examples (no filtering)
        filtered_dataset = dataset.map(convert_labels)
        
        # Calculate statistics
        original_size = len(dataset)
        filter_stats = {
            "filtering_strategy": "mixed",
            "entity_type": self.entity_type,
            "original_size": original_size,
            "filtered_size": original_size,
            "examples_removed": 0,
            "removal_rate": 0.0,
            "label_conversion": "non_target_to_O"
        }
        
        return filtered_dataset, filter_stats
    
    def _log_filtering_results(self, filter_stats: Dict[str, Any]) -> None:
        """Log filtering results."""
        self.logger.logger.info("Dataset filtering completed:")
        self.logger.logger.info(f"  - Strategy: {filter_stats['filtering_strategy']}")
        self.logger.logger.info(f"  - Entity type: {filter_stats['entity_type']}")
        self.logger.logger.info(f"  - Original size: {filter_stats['original_size']}")
        self.logger.logger.info(f"  - Filtered size: {filter_stats['filtered_size']}")
        self.logger.logger.info(f"  - Examples removed: {filter_stats['examples_removed']}")
        self.logger.logger.info(f"  - Removal rate: {filter_stats['removal_rate']:.2%}")


class SingleHeadTrainer:
    """
    Single-head trainer for entity-type-specific training isolation.
    
    This class provides complete training pipeline for individual entity types
    with isolated checkpoints, logging, and WandB run naming.
    """
    
    def __init__(self, config: HFTrainingConfig, entity_type: str, filtering_strategy: str = "strict"):
        """
        Initialize single-head trainer.
        
        Args:
            config: Training configuration
            entity_type: Target entity type (e.g., "PER", "LOC", "ORG")
            filtering_strategy: Dataset filtering strategy ("strict" or "mixed")
        """
        self.config = config
        self.entity_type = entity_type
        self.filtering_strategy = filtering_strategy
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        # Initialize dataset filter
        self.dataset_filter = SingleHeadDatasetFilter(entity_type, filtering_strategy)
        
        # Training state
        self.output_dir = None
        self.wandb_run_name = None
        self.head_trainer = None
    
    def create_head_output_dir(self, base_output_dir: Optional[str] = None) -> str:
        """
        Create isolated output directory for this head.
        
        Args:
            base_output_dir: Base output directory (uses config default if None)
            
        Returns:
            Path to head-specific output directory
        """
        if base_output_dir is None:
            base_output_dir = self.config.output_dir
        
        # Create timestamped directory name with entity type
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        version = self.config.run_version
        
        dir_name = f"{self.entity_type}_{timestamp}_{version}"
        
        output_path = Path(base_output_dir) / dir_name
        output_path.mkdir(parents=True, exist_ok=True)
        
        self.output_dir = str(output_path)
        self.logger.logger.info(f"Created head output directory: {self.output_dir}")
        
        return self.output_dir
    
    def create_wandb_run_name(self) -> str:
        """
        Create WandB run name with entity type suffix.
        
        Returns:
            WandB run name for this head
        """
        base_name = self.config.run_name
        if base_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name = f"robbert_ner_{timestamp}"
        
        # Add entity type and filtering strategy suffix
        self.wandb_run_name = f"{base_name}_{self.entity_type}_{self.filtering_strategy}"
        
        self.logger.logger.info(f"Created WandB run name: {self.wandb_run_name}")
        
        return self.wandb_run_name
    
    def prepare_head_dataset(
        self,
        data_path: Union[str, Path],
        train_split: float = 0.8,
        max_length: Optional[int] = None
    ) -> Tuple[DatasetDict, List[str], Dict[str, int], Dict[int, str], DatasetStatistics, Dict[str, Any]]:
        """
        Prepare dataset for single-head training.
        
        Args:
            data_path: Path to training data
            train_split: Training split ratio
            max_length: Maximum sequence length
            
        Returns:
            Tuple of (dataset_dict, head_label_list, head_label2id, head_id2label, statistics, filter_stats)
        """
        self.logger.logger.info(f"Preparing dataset for {self.entity_type} head training")
        
        if max_length is None:
            max_length = self.config.max_length
        
        # First, prepare the full dataset
        full_dataset_dict, full_label_list, _, _, full_statistics = prepare_ner_dataset(
            data_path=data_path,
            model_name=self.config.model_name,
            train_split=train_split,
            max_length=max_length,
            stratify_by_entities=True,
            random_state=self.config.seed
        )
        
        # Filter datasets for this head
        train_filtered, head_label_list, head_label2id, head_id2label, train_filter_stats = \
            self.dataset_filter.filter_dataset_for_entity_type(
                full_dataset_dict['train'], full_label_list
            )
        
        val_filtered, _, _, _, val_filter_stats = \
            self.dataset_filter.filter_dataset_for_entity_type(
                full_dataset_dict['validation'], full_label_list
            )
        
        # Create filtered dataset dict
        filtered_dataset_dict = DatasetDict({
            'train': train_filtered,
            'validation': val_filtered
        })
        
        # Calculate head-specific statistics
        head_statistics = self._calculate_head_statistics(
            filtered_dataset_dict, head_label_list, full_statistics
        )
        
        # Combine filter statistics
        combined_filter_stats = {
            "train": train_filter_stats,
            "validation": val_filter_stats,
            "entity_type": self.entity_type,
            "filtering_strategy": self.filtering_strategy,
            "head_label_scheme": head_label_list
        }
        
        return filtered_dataset_dict, head_label_list, head_label2id, head_id2label, head_statistics, combined_filter_stats
    
    def _calculate_head_statistics(
        self,
        dataset_dict: DatasetDict,
        head_label_list: List[str],
        original_statistics: DatasetStatistics
    ) -> DatasetStatistics:
        """Calculate statistics for head-specific dataset."""
        # Combine train and validation for overall statistics
        all_examples = []
        all_examples.extend(dataset_dict['train'])
        all_examples.extend(dataset_dict['validation'])
        
        total_examples = len(all_examples)
        total_entities = 0
        examples_with_entities = 0
        sentence_lengths = []
        entities_per_sentence = []
        label_counts = Counter()
        
        for example in all_examples:
            # Count sentence length (approximate from input_ids)
            sentence_length = len([token_id for token_id in example['input_ids'] if token_id != 0])
            sentence_lengths.append(sentence_length)
            
            # Count entities in this example
            labels = example['labels']
            example_entities = 0
            
            for label_id in labels:
                if label_id >= 0 and label_id < len(head_label_list):  # Valid label
                    label_name = head_label_list[label_id]
                    label_counts[label_name] += 1
                    
                    # Count B- labels as entities
                    if label_name.startswith('B-'):
                        example_entities += 1
            
            total_entities += example_entities
            entities_per_sentence.append(example_entities)
            
            if example_entities > 0:
                examples_with_entities += 1
        
        # Calculate statistics
        entity_coverage = (examples_with_entities / total_examples) * 100 if total_examples > 0 else 0
        avg_sentence_length = sum(sentence_lengths) / len(sentence_lengths) if sentence_lengths else 0
        avg_entities_per_sentence = sum(entities_per_sentence) / len(entities_per_sentence) if entities_per_sentence else 0
        
        return DatasetStatistics(
            total_examples=total_examples,
            total_entities=total_entities,
            entity_coverage=entity_coverage,
            label_distribution=dict(label_counts),
            avg_sentence_length=avg_sentence_length,
            avg_entities_per_sentence=avg_entities_per_sentence,
            truncated_examples=original_statistics.truncated_examples,
            alignment_warnings=original_statistics.alignment_warnings,
            train_size=len(dataset_dict['train']),
            val_size=len(dataset_dict['validation'])
        )
    
    def train_single_head(
        self,
        data_path: Union[str, Path],
        output_dir: Optional[str] = None,
        resume_from_checkpoint: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Train single head with entity-type-specific isolation.
        
        Args:
            data_path: Path to training data
            output_dir: Output directory (auto-generated if None)
            resume_from_checkpoint: Path to checkpoint to resume from
            
        Returns:
            Training results dictionary with head-specific information
        """
        self.logger.logger.info(f"Starting single-head training for entity type: {self.entity_type}")
        
        try:
            # Create head-specific output directory
            if output_dir is None:
                output_dir = self.create_head_output_dir()
            else:
                output_dir = str(Path(output_dir) / f"{self.entity_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
                Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            self.output_dir = output_dir
            
            # Prepare head-specific dataset
            dataset_dict, head_label_list, head_label2id, head_id2label, statistics, filter_stats = \
                self.prepare_head_dataset(data_path)
            
            # Create head-specific configuration
            head_config = self._create_head_config(head_label_list, output_dir)
            
            # Create head trainer
            self.head_trainer = HFNERTrainer(head_config)
            
            # Setup model with head-specific labels
            model, tokenizer, label2id, id2label = self.head_trainer.setup_model_and_tokenizer(head_label_list)
            
            # Set dataset directly (skip preparation in HFNERTrainer)
            self.head_trainer.dataset_dict = dataset_dict
            self.head_trainer.dataset_statistics = statistics
            self.head_trainer.label_list = head_label_list
            self.head_trainer.label2id = head_label2id
            self.head_trainer.id2label = head_id2label
            
            # Setup WandB with head-specific run name
            self.head_trainer.setup_wandb()
            
            # Create trainer
            trainer = self.head_trainer.create_trainer(dataset_dict, output_dir)
            
            # Override resume_from_checkpoint if provided
            if resume_from_checkpoint:
                trainer.args.resume_from_checkpoint = resume_from_checkpoint
                self.logger.logger.info(f"Resuming from checkpoint: {resume_from_checkpoint}")
            
            # Start training
            self.logger.logger.info("Starting head-specific training...")
            train_result = trainer.train(resume_from_checkpoint=resume_from_checkpoint)
            
            # Save final model
            self.logger.logger.info("Saving head-specific model...")
            trainer.save_model()
            trainer.save_state()
            
            # Run final evaluation
            self.logger.logger.info("Running final evaluation...")
            eval_result = trainer.evaluate()
            
            # Compile results with head-specific information
            results = {
                "train_result": train_result,
                "eval_result": eval_result,
                "output_dir": output_dir,
                "model_path": os.path.join(output_dir, "pytorch_model.bin"),
                "config_path": os.path.join(output_dir, "config.json"),
                "tokenizer_path": output_dir,
                "entity_type": self.entity_type,
                "filtering_strategy": self.filtering_strategy,
                "head_label_scheme": head_label_list,
                "dataset_statistics": statistics.to_dict(),
                "filter_statistics": filter_stats,
                "wandb_run_name": self.wandb_run_name
            }
            
            # Log final results
            self._log_head_training_results(results)
            
            # Save head-specific configuration and metadata
            self._save_head_training_metadata(output_dir, results)
            
            # Generate head-specific model card
            if self.config.generate_model_card:
                self._generate_head_model_card(output_dir, results)
            
            # Save predictions if enabled
            if self.config.save_predictions:
                self.head_trainer._save_predictions(trainer, dataset_dict, output_dir)
            
            self.logger.logger.info(f"Single-head training completed for {self.entity_type}")
            
            return results
            
        except Exception as e:
            error_msg = f"Single-head training failed for {self.entity_type}: {e}"
            self.logger.logger.error(error_msg)
            raise TrainingError(error_msg) from e
        
        finally:
            # Clean up WandB
            if wandb.run is not None:
                wandb.finish()
    
    def _create_head_config(self, head_label_list: List[str], output_dir: str) -> HFTrainingConfig:
        """Create head-specific training configuration."""
        # Create a copy of the original config
        head_config = HFTrainingConfig(**self.config.to_dict())
        
        # Update head-specific settings
        head_config.output_dir = output_dir
        head_config.run_name = self.create_wandb_run_name()
        head_config.experiment_name = f"{self.entity_type}_head"
        
        # Add head-specific tags to WandB
        head_config.wandb_tags = self.config.wandb_tags + [
            f"entity-{self.entity_type.lower()}",
            f"filter-{self.filtering_strategy}",
            "single-head"
        ]
        
        # Update WandB notes
        head_config.wandb_notes = (
            f"Single-head training for {self.entity_type} entity type using "
            f"{self.filtering_strategy} filtering strategy with 3-label scheme: {head_label_list}"
        )
        
        return head_config
    
    def _log_head_training_results(self, results: Dict[str, Any]) -> None:
        """Log head-specific training results."""
        self.logger.logger.info(f"Single-Head Training Results for {self.entity_type}:")
        
        # Head-specific information
        self.logger.logger.info(f"  - Entity type: {results['entity_type']}")
        self.logger.logger.info(f"  - Filtering strategy: {results['filtering_strategy']}")
        self.logger.logger.info(f"  - Label scheme: {results['head_label_scheme']}")
        self.logger.logger.info(f"  - WandB run: {results['wandb_run_name']}")
        
        # Training metrics
        if "train_result" in results:
            train_result = results["train_result"]
            if hasattr(train_result, 'training_loss'):
                self.logger.logger.info(f"  - Final training loss: {train_result.training_loss:.4f}")
        
        # Evaluation metrics
        if "eval_result" in results:
            eval_result = results["eval_result"]
            for key, value in eval_result.items():
                if isinstance(value, (int, float)):
                    self.logger.logger.info(f"  - {key}: {value:.4f}")
        
        # Dataset statistics
        if "dataset_statistics" in results:
            stats = results["dataset_statistics"]
            self.logger.logger.info(f"  - Train/Val split: {stats['train_size']}/{stats['val_size']}")
            self.logger.logger.info(f"  - Entity coverage: {stats['entity_coverage']:.1f}%")
        
        # Filter statistics
        if "filter_statistics" in results:
            filter_stats = results["filter_statistics"]
            train_stats = filter_stats.get("train", {})
            self.logger.logger.info(f"  - Examples removed (train): {train_stats.get('examples_removed', 0)}")
            self.logger.logger.info(f"  - Removal rate (train): {train_stats.get('removal_rate', 0):.2%}")
        
        # Output paths
        self.logger.logger.info(f"  - Model saved to: {results['model_path']}")
        self.logger.logger.info(f"  - Output directory: {results['output_dir']}")
    
    def _save_head_training_metadata(self, output_dir: str, results: Dict[str, Any]) -> None:
        """Save head-specific training metadata."""
        try:
            # Save head training configuration
            head_config_path = Path(output_dir) / "head_training_config.yaml"
            self.config.save_yaml(str(head_config_path))
            
            # Save head-specific metadata
            metadata = {
                "entity_type": self.entity_type,
                "filtering_strategy": self.filtering_strategy,
                "head_label_scheme": results["head_label_scheme"],
                "dataset_statistics": results["dataset_statistics"],
                "filter_statistics": results["filter_statistics"],
                "wandb_run_name": results["wandb_run_name"],
                "training_completed_at": datetime.now().isoformat(),
                "model_name": self.config.model_name,
                "run_version": self.config.run_version
            }
            
            metadata_path = Path(output_dir) / "head_metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            self.logger.logger.info(f"Head training metadata saved to: {metadata_path}")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to save head training metadata: {e}")
    
    def _generate_head_model_card(self, output_dir: str, results: Dict[str, Any]) -> None:
        """Generate head-specific model card."""
        try:
            model_card_path = Path(output_dir) / "model_card.md"
            
            # Create head-specific model card content
            model_card_content = f"""# RobBERT-2023 {self.entity_type} NER Head

## Model Description

This model is a single-head variant of RobBERT-2023 trained specifically for {self.entity_type} entity recognition in Dutch text.

## Training Configuration

- **Entity Type**: {self.entity_type}
- **Filtering Strategy**: {self.filtering_strategy}
- **Label Scheme**: {results['head_label_scheme']}
- **Model Base**: {self.config.model_name}
- **Training Version**: {self.config.run_version}

## Dataset Statistics

- **Training Examples**: {results['dataset_statistics']['train_size']}
- **Validation Examples**: {results['dataset_statistics']['val_size']}
- **Entity Coverage**: {results['dataset_statistics']['entity_coverage']:.1f}%
- **Average Entities per Sentence**: {results['dataset_statistics']['avg_entities_per_sentence']:.2f}

## Filtering Statistics

- **Strategy**: {self.filtering_strategy}
- **Examples Removed (Train)**: {results['filter_statistics']['train']['examples_removed']}
- **Removal Rate (Train)**: {results['filter_statistics']['train']['removal_rate']:.2%}

## Performance Metrics

"""
            
            # Add evaluation metrics
            if "eval_result" in results:
                eval_result = results["eval_result"]
                for key, value in eval_result.items():
                    if isinstance(value, (int, float)):
                        model_card_content += f"- **{key}**: {value:.4f}\n"
            
            model_card_content += f"""
## Usage

```python
from transformers import RobertaForTokenClassification, AutoTokenizer

# Load model and tokenizer
model = RobertaForTokenClassification.from_pretrained("{output_dir}")
tokenizer = AutoTokenizer.from_pretrained("{output_dir}")

# Example usage
text = "Jan Jansen woont in Amsterdam."
inputs = tokenizer(text, return_tensors="pt")
outputs = model(**inputs)
```

## Training Details

- **WandB Run**: {results['wandb_run_name']}
- **Training Completed**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **Output Directory**: {output_dir}

## Label Scheme

This model uses a 3-label scheme specific to {self.entity_type} entities:

- `O`: Outside any entity
- `B-{self.entity_type}`: Beginning of {self.entity_type} entity
- `I-{self.entity_type}`: Inside {self.entity_type} entity

## Filtering Strategy: {self.filtering_strategy.title()}

"""
            
            if self.filtering_strategy == "strict":
                model_card_content += """
The strict filtering strategy was used, which means:
- Only training examples containing the target entity type were kept
- Non-target entities in kept examples were mapped to "O" labels
- This prevents the model from learning only "O" labels by ensuring entity presence
"""
            else:
                model_card_content += """
The mixed filtering strategy was used, which means:
- All training examples were kept
- Non-target entities were mapped to "O" labels
- This maintains dataset size while focusing on the target entity type
"""
            
            # Write model card
            with open(model_card_path, 'w', encoding='utf-8') as f:
                f.write(model_card_content)
            
            self.logger.logger.info(f"Head-specific model card generated: {model_card_path}")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to generate head-specific model card: {e}")


def train_single_head(
    entity_type: str,
    data_path: Union[str, Path],
    filtering_strategy: str = "strict",
    config_path: Optional[str] = None,
    config: Optional[HFTrainingConfig] = None,
    output_dir: Optional[str] = None,
    resume_from_checkpoint: Optional[str] = None
) -> Dict[str, Any]:
    """
    Convenience function to train a single entity type head.
    
    Args:
        entity_type: Target entity type (e.g., "PER", "LOC", "ORG")
        data_path: Path to training data
        filtering_strategy: Dataset filtering strategy ("strict" or "mixed")
        config_path: Path to YAML configuration file
        config: HFTrainingConfig instance (overrides config_path)
        output_dir: Output directory for checkpoints
        resume_from_checkpoint: Path to checkpoint to resume from
        
    Returns:
        Training results dictionary with head-specific information
    """
    # Load configuration
    if config is None:
        if config_path:
            config = HFTrainingConfig.from_yaml(config_path)
        else:
            config = HFTrainingConfig()
    
    # Create single-head trainer and run training
    trainer = SingleHeadTrainer(config, entity_type, filtering_strategy)
    return trainer.train_single_head(
        data_path=data_path,
        output_dir=output_dir,
        resume_from_checkpoint=resume_from_checkpoint
    )


# Export main classes and functions
__all__ = [
    'SingleHeadDatasetFilter',
    'SingleHeadTrainer',
    'train_single_head'
]