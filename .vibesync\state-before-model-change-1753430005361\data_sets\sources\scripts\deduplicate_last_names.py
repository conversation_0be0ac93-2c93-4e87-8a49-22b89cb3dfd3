import pandas as pd
import os

# Get current script directory
base_dir = os.path.dirname(__file__)

# Define filenames
input_file = os.path.join(base_dir, "last_names.csv")  # Replace with your actual CSV filename
output_file = os.path.join(base_dir, "cleaned_sorted_names.csv")

# Load CSV (no header assumed)
df = pd.read_csv(input_file, header=None, names=["last_name"])

# Drop duplicates
df = df.drop_duplicates()

# Sort alphabetically
df = df.sort_values(by="last_name")

# Save result
df.to_csv(output_file, index=False, header=False)

print(f"✅ Saved deduplicated and sorted file to: {output_file}")
