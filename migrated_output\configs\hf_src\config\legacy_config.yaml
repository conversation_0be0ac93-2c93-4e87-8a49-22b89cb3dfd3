hf_training:
  batch_size: 16
  cache_dir: data/cache
  dataloader_num_workers: 2
  dataloader_pin_memory: true
  early_stopping_patience: 3
  early_stopping_threshold: 0.001
  epochs: 5
  eval_batch_size: 8
  eval_steps: 200
  fp16: true
  generate_model_card: true
  learning_rate: 2e-5
  load_best_model_at_end: true
  logging_steps: 100
  metric_for_best_model: eval_f1
  model_name: DTAI-KULeuven/robbert-2023-dutch-base
  push_to_hub: false
  run_version: v1.0
  save_predictions: true
  save_steps: 1000
  save_total_limit: 3
  test_epochs: 1
  test_mode: false
  test_sample_limit: 50
  use_cache: true
  use_class_weights: false
  use_gpu: true
  wandb_entity: test-entity
  wandb_project: legacy-robbert-ner
  warmup_steps: 1000
  weight_decay: 0.01
