#!/usr/bin/env python3
"""
Test cases for compound Dutch names to demonstrate current model limitations.
These tests are designed to fail and highlight tokenization issues.
"""

import sys
import torch
import pytest
from pathlib import Path
from typing import List, Dict

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.models.multitask_robbert import MultiTaskRobBERT


class TestCompoundNames:
    """Test compound Dutch names that expose model limitations."""
    
    @pytest.fixture(scope="class")
    def model(self):
        """Load model once for all tests."""
        model = MultiTaskRobBERT.from_pretrained()
        model.eval()
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model.to(device)
        return model
    
    @pytest.fixture(scope="class")
    def device(self):
        """Get device for testing."""
        return torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    def extract_entities(self, model, text: str, device):
        """Extract entities from text."""
        inputs = model.tokenizer.tokenizer(
            text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        )
        
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = model(
                input_ids=inputs['input_ids'],
                attention_mask=inputs['attention_mask'],
                heads=['ner']
            )
        
        # Extract entities
        ner_output = outputs['ner']
        logits = ner_output['logits']
        predictions = torch.argmax(logits, dim=-1)
        
        # CoNLL-2002 label mapping
        id2label = {
            0: 'B-LOC', 1: 'B-MISC', 2: 'B-ORG', 3: 'B-PER', 4: 'I-LOC',
            5: 'I-MISC', 6: 'I-ORG', 7: 'I-PER', 8: 'O'
        }
        
        tokens = model.tokenizer.tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])
        entities = []
        current_entity = None
        
        for i, (token, pred_id) in enumerate(zip(tokens, predictions[0])):
            if token in ['[CLS]', '[SEP]', '[PAD]']:
                continue
                
            label = id2label.get(pred_id.item(), 'O')
            
            if label.startswith('B-'):
                if current_entity:
                    entities.append(current_entity)
                
                entity_type = label[2:]
                current_entity = {
                    'text': token.replace('##', ''),
                    'label': entity_type,
                    'tokens': [token]
                }
            elif label.startswith('I-') and current_entity:
                entity_type = label[2:]
                if current_entity['label'] == entity_type:
                    current_entity['text'] += token.replace('##', '')
                    current_entity['tokens'].append(token)
            else:
                if current_entity:
                    entities.append(current_entity)
                    current_entity = None
        
        if current_entity:
            entities.append(current_entity)
        
        return entities
    
    def test_compound_person_name_with_hyphen(self, model, device):
        """Test compound person name with hyphen - EXPECTED TO FAIL."""
        text = "Jan-Willem van der Berg werkt bij de gemeente."
        entities = self.extract_entities(model, text, device)
        
        # Find person entities
        person_entities = [e for e in entities if e['label'] == 'PER']
        
        # This test is designed to demonstrate the limitation
        # The model should detect "Jan-Willem van der Berg" but likely only detects "Jan"
        full_name_detected = any('Jan-Willem van der Berg'.replace(' ', '').replace('-', '').lower() 
                               in e['text'].replace(' ', '').replace('-', '').lower() 
                               for e in person_entities)
        
        print(f"Text: {text}")
        print(f"Detected person entities: {person_entities}")
        print(f"Full compound name detected: {full_name_detected}")
        
        # This assertion will likely fail, demonstrating the issue
        assert full_name_detected, f"Model should detect full compound name 'Jan-Willem van der Berg', but detected: {person_entities}"
    
    def test_long_formal_name(self, model, device):
        """Test long formal name - EXPECTED TO FAIL."""
        text = "Dr. Johannes Wilhelmus van den Broek spreekt morgen."
        entities = self.extract_entities(model, text, device)
        
        person_entities = [e for e in entities if e['label'] == 'PER']
        
        # Check if full name is detected
        expected_name = "Johannes Wilhelmus van den Broek"
        full_name_detected = any(expected_name.replace(' ', '').lower() 
                               in e['text'].replace(' ', '').lower() 
                               for e in person_entities)
        
        print(f"Text: {text}")
        print(f"Detected person entities: {person_entities}")
        print(f"Expected: {expected_name}")
        
        # This will likely fail
        assert full_name_detected, f"Model should detect full formal name '{expected_name}', but detected: {person_entities}"
    
    def test_compound_organization_with_abbreviation(self, model, device):
        """Test compound organization with abbreviation - EXPECTED TO FAIL."""
        text = "Koninklijke Philips N.V. heeft een nieuwe CEO aangesteld."
        entities = self.extract_entities(model, text, device)
        
        org_entities = [e for e in entities if e['label'] == 'ORG']
        
        # Check if full organization name is detected
        expected_org = "Koninklijke Philips N.V."
        full_org_detected = any(expected_org.replace(' ', '').replace('.', '').lower() 
                              in e['text'].replace(' ', '').replace('.', '').lower() 
                              for e in org_entities)
        
        print(f"Text: {text}")
        print(f"Detected org entities: {org_entities}")
        print(f"Expected: {expected_org}")
        
        # This will likely fail due to abbreviation handling
        assert full_org_detected, f"Model should detect full organization name '{expected_org}', but detected: {org_entities}"
    
    def test_location_with_prepositions(self, model, device):
        """Test location with prepositions - EXPECTED TO FAIL."""
        text = "De gemeente Alphen aan den Rijn organiseert een festival."
        entities = self.extract_entities(model, text, device)
        
        loc_entities = [e for e in entities if e['label'] == 'LOC']
        
        # Check if full location name is detected
        expected_loc = "Alphen aan den Rijn"
        full_loc_detected = any(expected_loc.replace(' ', '').lower() 
                              in e['text'].replace(' ', '').lower() 
                              for e in loc_entities)
        
        print(f"Text: {text}")
        print(f"Detected location entities: {loc_entities}")
        print(f"Expected: {expected_loc}")
        
        # This might fail due to preposition handling
        assert full_loc_detected, f"Model should detect full location name '{expected_loc}', but detected: {loc_entities}"
    
    def test_multiple_compound_names_in_sentence(self, model, device):
        """Test multiple compound names in one sentence - EXPECTED TO FAIL."""
        text = "Marie-Claire de Jong-Bakker en Jan-Willem van der Berg werken samen."
        entities = self.extract_entities(model, text, device)
        
        person_entities = [e for e in entities if e['label'] == 'PER']
        
        expected_names = ["Marie-Claire de Jong-Bakker", "Jan-Willem van der Berg"]
        detected_names = []
        
        for expected in expected_names:
            normalized_expected = expected.replace(' ', '').replace('-', '').lower()
            for entity in person_entities:
                normalized_entity = entity['text'].replace(' ', '').replace('-', '').lower()
                if normalized_expected in normalized_entity or normalized_entity in normalized_expected:
                    detected_names.append(expected)
                    break
        
        print(f"Text: {text}")
        print(f"Detected person entities: {person_entities}")
        print(f"Expected names: {expected_names}")
        print(f"Successfully detected: {detected_names}")
        
        # This will likely fail
        assert len(detected_names) == len(expected_names), f"Model should detect both compound names {expected_names}, but only detected: {detected_names}"
    
    def test_tokenization_analysis(self, model, device):
        """Analyze tokenization patterns for compound names."""
        test_cases = [
            "Jan-Willem van der Berg",
            "Marie-Claire de Jong-Bakker", 
            "Dr. Johannes Wilhelmus van den Broek",
            "Koninklijke Philips N.V.",
            "Alphen aan den Rijn",
            "Amsterdam-Noord"
        ]
        
        tokenization_issues = []
        
        for text in test_cases:
            tokens = model.tokenizer.tokenize(text)
            subword_count = sum(1 for token in tokens if token.startswith('##'))
            total_tokens = len(tokens)
            subword_ratio = subword_count / total_tokens if total_tokens > 0 else 0
            
            print(f"\nText: '{text}'")
            print(f"Tokens: {tokens}")
            print(f"Total tokens: {total_tokens}, Subwords: {subword_count}, Ratio: {subword_ratio:.2%}")
            
            if subword_ratio > 0.2:  # More than 20% subwords indicates fragmentation
                tokenization_issues.append({
                    'text': text,
                    'tokens': tokens,
                    'subword_ratio': subword_ratio
                })
        
        print(f"\nTokenization issues found: {len(tokenization_issues)}")
        for issue in tokenization_issues:
            print(f"- '{issue['text']}': {issue['subword_ratio']:.1%} fragmentation")
        
        # This assertion documents the tokenization challenges
        # It may pass or fail depending on the specific tokenizer behavior
        assert len(tokenization_issues) < len(test_cases), f"Too many tokenization issues detected: {tokenization_issues}"


if __name__ == '__main__':
    # Run tests to demonstrate limitations
    pytest.main([__file__, '-v', '-s'])
