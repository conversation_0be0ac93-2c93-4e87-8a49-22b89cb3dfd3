#!/usr/bin/env python3
"""
Demo script for Hugging Face dataset preparation pipeline.

This script demonstrates how to use the HF dataset preparation pipeline
with sample Dutch NER data.
"""

import json
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.data.hf_dataset_preparation import prepare_ner_dataset


def create_sample_data():
    """Create sample Dutch NER data for demonstration."""
    sample_data = [
        {
            "id": 1,
            "sentence": "<PERSON> woont in Amsterdam.",
            "entities": [
                {"text": "<PERSON>", "label": "PER", "start": 0, "end": 10},
                {"text": "Amsterdam", "label": "LOC", "start": 20, "end": 29}
            ]
        },
        {
            "id": 2,
            "sentence": "Marie <PERSON> werkt bij Google in Nederland.",
            "entities": [
                {"text": "<PERSON> Berg", "label": "PER", "start": 0, "end": 18},
                {"text": "Google", "label": "ORG", "start": 29, "end": 35},
                {"text": "Nederland", "label": "LOC", "start": 39, "end": 48}
            ]
        },
        {
            "id": 3,
            "sentence": "Dit is een zin zonder entiteiten.",
            "entities": []
        },
        {
            "id": 4,
            "sentence": "Piet de Vries bezoekt Rotterdam en Den Haag.",
            "entities": [
                {"text": "Piet de Vries", "label": "PER", "start": 0, "end": 13},
                {"text": "Rotterdam", "label": "LOC", "start": 22, "end": 31},
                {"text": "Den Haag", "label": "LOC", "start": 35, "end": 43}
            ]
        },
        {
            "id": 5,
            "sentence": "Microsoft heeft een kantoor in Amsterdam.",
            "entities": [
                {"text": "Microsoft", "label": "ORG", "start": 0, "end": 9},
                {"text": "Amsterdam", "label": "LOC", "start": 31, "end": 40}
            ]
        },
        {
            "id": 6,
            "sentence": "De minister-president sprak met de burgemeester.",
            "entities": [
                {"text": "minister-president", "label": "PER", "start": 3, "end": 21},
                {"text": "burgemeester", "label": "PER", "start": 35, "end": 47}
            ]
        }
    ]
    
    return sample_data


def main():
    """Main demo function."""
    print("🚀 Hugging Face Dataset Preparation Demo")
    print("=" * 50)
    
    # Create sample data file
    sample_data = create_sample_data()
    data_file = Path("data_sets/demo_ner_data.json")
    data_file.parent.mkdir(exist_ok=True)
    
    with open(data_file, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, indent=2, ensure_ascii=False)
    
    print(f"📝 Created sample data file: {data_file}")
    print(f"   - {len(sample_data)} examples")
    print(f"   - Entity types: PER, LOC, ORG")
    
    try:
        print("\n🔄 Preparing dataset...")
        
        # Prepare dataset
        dataset_dict, label_list, label2id, id2label, statistics = prepare_ner_dataset(
            data_path=data_file,
            model_name="DTAI-KULeuven/robbert-2023-dutch-base",
            train_split=0.7,
            max_length=128,  # Smaller for demo
            stratify_by_entities=True,
            random_state=42
        )
        
        print("✅ Dataset preparation completed!")
        
        # Display results
        print(f"\n📊 Dataset Statistics:")
        stats_dict = statistics.to_dict()
        for key, value in stats_dict.items():
            if isinstance(value, dict):
                print(f"   {key}:")
                for k, v in value.items():
                    print(f"     {k}: {v}")
            else:
                print(f"   {key}: {value}")
        
        print(f"\n🏷️  Label Scheme:")
        print(f"   Labels: {label_list}")
        print(f"   Total labels: {len(label_list)}")
        
        print(f"\n📚 Dataset Splits:")
        print(f"   Train: {len(dataset_dict['train'])} examples")
        print(f"   Validation: {len(dataset_dict['validation'])} examples")
        
        # Show sample from train set
        print(f"\n🔍 Sample from training set:")
        sample = dataset_dict['train'][0]
        print(f"   Input IDs length: {len(sample['input_ids'])}")
        print(f"   Labels length: {len(sample['labels'])}")
        print(f"   Labels: {[id2label[label_id] for label_id in sample['labels'][:10]]}...")  # First 10 labels
        
        # Show label distribution
        print(f"\n📈 Label Distribution in Training Set:")
        label_counts = {}
        for example in dataset_dict['train']:
            for label_id in example['labels']:
                label_name = id2label[label_id]
                label_counts[label_name] = label_counts.get(label_name, 0) + 1
        
        for label, count in sorted(label_counts.items()):
            print(f"   {label}: {count}")
        
        print(f"\n🎉 Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during dataset preparation: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        # Clean up demo file
        if data_file.exists():
            data_file.unlink()
            print(f"🧹 Cleaned up demo file: {data_file}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())