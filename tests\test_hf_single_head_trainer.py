"""
Tests for single-head training isolation system.

This module tests the per-head training functionality including dataset filtering,
isolated training, and WandB run naming.
"""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from datasets import Dataset, DatasetDict
from transformers import <PERSON><PERSON><PERSON><PERSON>okenClassification, AutoTokenizer

from src.training.hf_config import HFTrainingConfig
from src.training.hf_single_head_trainer import (
    SingleHeadDatasetFilter,
    SingleHeadTrainer,
    train_single_head
)
from src.data.hf_dataset_preparation import DatasetStatistics


class TestSingleHeadDatasetFilter:
    """Test dataset filtering for single-head training."""
    
    @pytest.fixture
    def sample_dataset(self):
        """Create sample dataset with multiple entity types."""
        # Create sample data with mixed entity types
        data = [
            {
                'input_ids': [0, 1, 2, 3, 4, 5],
                'attention_mask': [1, 1, 1, 1, 1, 1],
                'labels': [0, 1, 2, 0, 3, 4]  # O, B-<PERSON><PERSON>, I-<PERSON><PERSON>, O, B-L<PERSON>, I-LOC
            },
            {
                'input_ids': [0, 1, 2, 3, 4, 5],
                'attention_mask': [1, 1, 1, 1, 1, 1],
                'labels': [0, 0, 0, 1, 2, 0]  # O, O, O, B-PER, I-PER, O
            },
            {
                'input_ids': [0, 1, 2, 3, 4, 5],
                'attention_mask': [1, 1, 1, 1, 1, 1],
                'labels': [0, 3, 4, 0, 0, 0]  # O, B-LOC, I-LOC, O, O, O
            },
            {
                'input_ids': [0, 1, 2, 3, 4, 5],
                'attention_mask': [1, 1, 1, 1, 1, 1],
                'labels': [0, 0, 0, 0, 0, 0]  # All O labels
            }
        ]
        
        return Dataset.from_list(data)
    
    @pytest.fixture
    def sample_label_list(self):
        """Sample label list with multiple entity types."""
        return ["O", "B-PER", "I-PER", "B-LOC", "I-LOC"]
    
    def test_filter_initialization(self):
        """Test filter initialization with different strategies."""
        # Test valid strategies
        filter_strict = SingleHeadDatasetFilter("PER", "strict")
        assert filter_strict.entity_type == "PER"
        assert filter_strict.filtering_strategy == "strict"
        
        filter_mixed = SingleHeadDatasetFilter("LOC", "mixed")
        assert filter_mixed.entity_type == "LOC"
        assert filter_mixed.filtering_strategy == "mixed"
        
        # Test invalid strategy
        with pytest.raises(ValueError, match="Invalid filtering strategy"):
            SingleHeadDatasetFilter("PER", "invalid")
    
    def test_strict_filtering_per(self, sample_dataset, sample_label_list):
        """Test strict filtering for PER entity type."""
        filter_per = SingleHeadDatasetFilter("PER", "strict")
        
        filtered_dataset, head_labels, head_label2id, head_id2label, stats = \
            filter_per.filter_dataset_for_entity_type(sample_dataset, sample_label_list)
        
        # Check head label scheme
        expected_labels = ["O", "B-PER", "I-PER"]
        assert head_labels == expected_labels
        assert head_label2id == {"O": 0, "B-PER": 1, "I-PER": 2}
        assert head_id2label == {0: "O", 1: "B-PER", 2: "I-PER"}
        
        # Check filtering (should keep only examples with PER entities)
        assert len(filtered_dataset) == 2  # First two examples have PER entities
        
        # Check label conversion
        first_example = filtered_dataset[0]
        # Original: [0, 1, 2, 0, 3, 4] -> [O, B-PER, I-PER, O, B-LOC, I-LOC]
        # Expected: [0, 1, 2, 0, 0, 0] -> [O, B-PER, I-PER, O, O, O] (LOC mapped to O)
        expected_labels = [0, 1, 2, 0, 0, 0]
        assert first_example['labels'] == expected_labels
        
        # Check statistics
        assert stats["filtering_strategy"] == "strict"
        assert stats["entity_type"] == "PER"
        assert stats["original_size"] == 4
        assert stats["filtered_size"] == 2
        assert stats["examples_removed"] == 2
        assert stats["removal_rate"] == 0.5
    
    def test_mixed_filtering_loc(self, sample_dataset, sample_label_list):
        """Test mixed filtering for LOC entity type."""
        filter_loc = SingleHeadDatasetFilter("LOC", "mixed")
        
        filtered_dataset, head_labels, head_label2id, head_id2label, stats = \
            filter_loc.filter_dataset_for_entity_type(sample_dataset, sample_label_list)
        
        # Check head label scheme
        expected_labels = ["O", "B-LOC", "I-LOC"]
        assert head_labels == expected_labels
        
        # Check filtering (should keep all examples)
        assert len(filtered_dataset) == 4  # All examples kept
        
        # Check label conversion for first example
        first_example = filtered_dataset[0]
        # Original: [0, 1, 2, 0, 3, 4] -> [O, B-PER, I-PER, O, B-LOC, I-LOC]
        # Expected: [0, 0, 0, 0, 1, 2] -> [O, O, O, O, B-LOC, I-LOC] (PER mapped to O)
        expected_labels = [0, 0, 0, 0, 1, 2]
        assert first_example['labels'] == expected_labels
        
        # Check statistics
        assert stats["filtering_strategy"] == "mixed"
        assert stats["entity_type"] == "LOC"
        assert stats["original_size"] == 4
        assert stats["filtered_size"] == 4
        assert stats["examples_removed"] == 0
        assert stats["removal_rate"] == 0.0
    
    def test_special_token_handling(self, sample_label_list):
        """Test handling of special tokens (-100 labels)."""
        # Create dataset with special tokens
        data = [
            {
                'input_ids': [0, 1, 2, 3, 4, 5],
                'attention_mask': [1, 1, 1, 1, 1, 1],
                'labels': [-100, 1, 2, -100, 0, -100]  # Special tokens mixed with labels
            }
        ]
        dataset = Dataset.from_list(data)
        
        filter_per = SingleHeadDatasetFilter("PER", "strict")
        filtered_dataset, _, _, _, _ = filter_per.filter_dataset_for_entity_type(dataset, sample_label_list)
        
        # Check that special tokens are preserved
        example = filtered_dataset[0]
        expected_labels = [-100, 1, 2, -100, 0, -100]  # B-PER, I-PER preserved, special tokens unchanged
        assert example['labels'] == expected_labels


class TestSingleHeadTrainer:
    """Test single-head trainer functionality."""
    
    @pytest.fixture
    def test_config(self):
        """Create test configuration."""
        return HFTrainingConfig(
            test_mode=True,
            test_epochs=1,
            test_sample_limit=10,
            test_disable_wandb=True,
            epochs=1,
            batch_size=2,
            eval_steps=5,
            logging_steps=2,
            save_steps=10
        )
    
    @pytest.fixture
    def sample_data_file(self):
        """Create temporary sample data file."""
        sample_data = [
            {
                "id": 1,
                "sentence": "Jan Jansen woont in Amsterdam.",
                "entities": [
                    {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10},
                    {"text": "Amsterdam", "label": "LOC", "start": 20, "end": 29}
                ]
            },
            {
                "id": 2,
                "sentence": "Marie de Wit werkt bij Google.",
                "entities": [
                    {"text": "Marie de Wit", "label": "PER", "start": 0, "end": 12},
                    {"text": "Google", "label": "ORG", "start": 23, "end": 29}
                ]
            },
            {
                "id": 3,
                "sentence": "Dit is een test zonder entiteiten.",
                "entities": []
            }
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(sample_data, f, indent=2)
            return f.name
    
    def test_trainer_initialization(self, test_config):
        """Test trainer initialization."""
        trainer = SingleHeadTrainer(test_config, "PER", "strict")
        
        assert trainer.entity_type == "PER"
        assert trainer.filtering_strategy == "strict"
        assert trainer.config == test_config
        assert trainer.output_dir is None
        assert trainer.wandb_run_name is None
    
    def test_create_head_output_dir(self, test_config):
        """Test head-specific output directory creation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            trainer = SingleHeadTrainer(test_config, "PER", "strict")
            
            output_dir = trainer.create_head_output_dir(temp_dir)
            
            # Check directory was created
            assert Path(output_dir).exists()
            assert Path(output_dir).is_dir()
            
            # Check naming pattern
            dir_name = Path(output_dir).name
            assert dir_name.startswith("PER_")
            assert dir_name.endswith(f"_{test_config.run_version}")
    
    def test_create_wandb_run_name(self, test_config):
        """Test WandB run name creation."""
        trainer = SingleHeadTrainer(test_config, "LOC", "mixed")
        
        run_name = trainer.create_wandb_run_name()
        
        # Check run name format
        assert "LOC" in run_name
        assert "mixed" in run_name
        assert trainer.wandb_run_name == run_name
    
    @patch('src.training.hf_single_head_trainer.prepare_ner_dataset')
    @patch('src.training.hf_single_head_trainer.HFNERTrainer')
    def test_prepare_head_dataset(self, mock_trainer_class, mock_prepare_dataset, test_config, sample_data_file):
        """Test head-specific dataset preparation."""
        # Mock dataset preparation
        mock_dataset_dict = DatasetDict({
            'train': Dataset.from_list([{'input_ids': [1, 2, 3], 'labels': [0, 1, 2]}]),
            'validation': Dataset.from_list([{'input_ids': [4, 5, 6], 'labels': [0, 1, 0]}])
        })
        mock_label_list = ["O", "B-PER", "I-PER", "B-LOC", "I-LOC"]
        mock_statistics = DatasetStatistics(
            total_examples=2, total_entities=2, entity_coverage=100.0,
            label_distribution={"O": 3, "B-PER": 1, "I-PER": 1, "B-LOC": 1},
            avg_sentence_length=10.0, avg_entities_per_sentence=1.0,
            truncated_examples=0, alignment_warnings=0,
            train_size=1, val_size=1
        )
        
        mock_prepare_dataset.return_value = (
            mock_dataset_dict, mock_label_list, {}, {}, mock_statistics
        )
        
        trainer = SingleHeadTrainer(test_config, "PER", "strict")
        
        result = trainer.prepare_head_dataset(sample_data_file)
        dataset_dict, head_labels, head_label2id, head_id2label, statistics, filter_stats = result
        
        # Check that prepare_ner_dataset was called
        mock_prepare_dataset.assert_called_once()
        
        # Check head-specific labels
        assert head_labels == ["O", "B-PER", "I-PER"]
        assert head_label2id == {"O": 0, "B-PER": 1, "I-PER": 2}
        assert head_id2label == {0: "O", 1: "B-PER", 2: "I-PER"}
        
        # Check filter statistics
        assert filter_stats["entity_type"] == "PER"
        assert filter_stats["filtering_strategy"] == "strict"
        assert filter_stats["head_label_scheme"] == head_labels
    
    @patch('src.training.hf_single_head_trainer.wandb')
    @patch('src.training.hf_single_head_trainer.prepare_ner_dataset')
    @patch('src.training.hf_single_head_trainer.HFNERTrainer')
    def test_head_config_creation(self, mock_trainer_class, mock_prepare_dataset, mock_wandb, test_config):
        """Test head-specific configuration creation."""
        trainer = SingleHeadTrainer(test_config, "ORG", "mixed")
        
        head_labels = ["O", "B-ORG", "I-ORG"]
        head_config = trainer._create_head_config(head_labels, "/test/output")
        
        # Check head-specific settings
        assert head_config.output_dir == "/test/output"
        assert "ORG" in head_config.run_name
        assert "mixed" in head_config.run_name
        assert head_config.experiment_name == "ORG_head"
        
        # Check WandB tags
        assert "entity-org" in head_config.wandb_tags
        assert "filter-mixed" in head_config.wandb_tags
        assert "single-head" in head_config.wandb_tags
        
        # Check WandB notes
        assert "ORG" in head_config.wandb_notes
        assert "mixed" in head_config.wandb_notes
        assert str(head_labels) in head_config.wandb_notes


class TestTrainSingleHeadFunction:
    """Test the convenience function for single-head training."""
    
    @pytest.fixture
    def test_config(self):
        """Create test configuration."""
        return HFTrainingConfig(
            test_mode=True,
            test_epochs=1,
            test_sample_limit=5,
            test_disable_wandb=True
        )
    
    @pytest.fixture
    def sample_data_file(self):
        """Create temporary sample data file."""
        sample_data = [
            {
                "id": 1,
                "sentence": "Test sentence with Jan Jansen.",
                "entities": [{"text": "Jan Jansen", "label": "PER", "start": 18, "end": 28}]
            }
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(sample_data, f, indent=2)
            return f.name
    
    @patch('src.training.hf_single_head_trainer.SingleHeadTrainer')
    def test_train_single_head_function(self, mock_trainer_class, test_config, sample_data_file):
        """Test the train_single_head convenience function."""
        # Mock trainer
        mock_trainer = Mock()
        mock_trainer.train_single_head.return_value = {
            "status": "completed",
            "entity_type": "PER",
            "output_dir": "/test/output"
        }
        mock_trainer_class.return_value = mock_trainer
        
        # Call function
        result = train_single_head(
            entity_type="PER",
            data_path=sample_data_file,
            filtering_strategy="strict",
            config=test_config
        )
        
        # Check trainer was created and called correctly
        mock_trainer_class.assert_called_once_with(test_config, "PER", "strict")
        mock_trainer.train_single_head.assert_called_once_with(
            data_path=sample_data_file,
            output_dir=None,
            resume_from_checkpoint=None
        )
        
        # Check result
        assert result["entity_type"] == "PER"
        assert result["status"] == "completed"
    
    @patch('src.training.hf_single_head_trainer.HFTrainingConfig')
    @patch('src.training.hf_single_head_trainer.SingleHeadTrainer')
    def test_train_single_head_with_config_path(self, mock_trainer_class, mock_config_class, sample_data_file):
        """Test train_single_head with config file path."""
        # Mock config loading
        mock_config = Mock()
        mock_config_class.from_yaml.return_value = mock_config
        
        # Mock trainer
        mock_trainer = Mock()
        mock_trainer.train_single_head.return_value = {"status": "completed"}
        mock_trainer_class.return_value = mock_trainer
        
        # Call function with config path
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as config_file:
            config_file.write("test: config")
            config_path = config_file.name
        
        train_single_head(
            entity_type="LOC",
            data_path=sample_data_file,
            config_path=config_path
        )
        
        # Check config was loaded from file
        mock_config_class.from_yaml.assert_called_once_with(config_path)
        mock_trainer_class.assert_called_once_with(mock_config, "LOC", "strict")


class TestIntegration:
    """Integration tests for single-head training system."""
    
    @pytest.fixture
    def integration_config(self):
        """Create integration test configuration."""
        return HFTrainingConfig(
            test_mode=True,
            test_epochs=1,
            test_sample_limit=3,
            test_disable_wandb=True,
            batch_size=1,
            eval_steps=2,
            logging_steps=1,
            save_steps=4,  # Make it a multiple of eval_steps
            early_stopping_patience=0,  # Disable early stopping for test
            load_best_model_at_end=True
        )
    
    @pytest.fixture
    def integration_data_file(self):
        """Create integration test data file."""
        sample_data = [
            {
                "id": 1,
                "sentence": "Jan woont in Amsterdam.",
                "entities": [
                    {"text": "Jan", "label": "PER", "start": 0, "end": 3},
                    {"text": "Amsterdam", "label": "LOC", "start": 13, "end": 22}
                ]
            },
            {
                "id": 2,
                "sentence": "Marie werkt bij Google.",
                "entities": [
                    {"text": "Marie", "label": "PER", "start": 0, "end": 5},
                    {"text": "Google", "label": "ORG", "start": 16, "end": 22}
                ]
            },
            {
                "id": 3,
                "sentence": "Dit is een test.",
                "entities": []
            }
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(sample_data, f, indent=2)
            return f.name
    
    @patch('src.training.hf_single_head_trainer.wandb')
    @patch('src.training.hf_single_head_trainer.SingleHeadTrainer.prepare_head_dataset')
    def test_end_to_end_single_head_training(
        self, mock_prepare_dataset, mock_wandb, 
        integration_config, integration_data_file
    ):
        """Test end-to-end single-head training flow."""
        # Mock dataset preparation to avoid empty validation set
        from datasets import Dataset, DatasetDict
        
        mock_train_data = [
            {'input_ids': [0, 1, 2, 3], 'attention_mask': [1, 1, 1, 1], 'labels': [0, 1, 2, 0]},
            {'input_ids': [0, 4, 5, 6], 'attention_mask': [1, 1, 1, 1], 'labels': [0, 0, 1, 2]}
        ]
        mock_val_data = [
            {'input_ids': [0, 7, 8, 9], 'attention_mask': [1, 1, 1, 1], 'labels': [0, 1, 0, 0]}
        ]
        
        mock_dataset_dict = DatasetDict({
            'train': Dataset.from_list(mock_train_data),
            'validation': Dataset.from_list(mock_val_data)
        })
        
        mock_head_labels = ["O", "B-PER", "I-PER"]
        mock_head_label2id = {"O": 0, "B-PER": 1, "I-PER": 2}
        mock_head_id2label = {0: "O", 1: "B-PER", 2: "I-PER"}
        
        mock_statistics = DatasetStatistics(
            total_examples=3, total_entities=2, entity_coverage=66.7,
            label_distribution={"O": 4, "B-PER": 2, "I-PER": 2},
            avg_sentence_length=4.0, avg_entities_per_sentence=0.67,
            truncated_examples=0, alignment_warnings=0,
            train_size=2, val_size=1
        )
        
        mock_filter_stats = {
            "train": {"filtering_strategy": "strict", "entity_type": "PER", 
                     "original_size": 2, "filtered_size": 2, "examples_removed": 0, "removal_rate": 0.0},
            "validation": {"filtering_strategy": "strict", "entity_type": "PER", 
                          "original_size": 1, "filtered_size": 1, "examples_removed": 0, "removal_rate": 0.0},
            "entity_type": "PER", "filtering_strategy": "strict", "head_label_scheme": mock_head_labels
        }
        
        mock_prepare_dataset.return_value = (
            mock_dataset_dict, mock_head_labels, mock_head_label2id, 
            mock_head_id2label, mock_statistics, mock_filter_stats
        )
        
        # Mock WandB
        mock_wandb.run = None
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a simple trainer that doesn't actually train
            trainer = SingleHeadTrainer(integration_config, "PER", "strict")
            
            # Test dataset preparation
            result = trainer.prepare_head_dataset(integration_data_file)
            dataset_dict, head_labels, head_label2id, head_id2label, statistics, filter_stats = result
            
            # Check result structure
            assert head_labels == ["O", "B-PER", "I-PER"]
            assert head_label2id == {"O": 0, "B-PER": 1, "I-PER": 2}
            assert head_id2label == {0: "O", 1: "B-PER", 2: "I-PER"}
            assert statistics.train_size == 2
            assert statistics.val_size == 1
            assert filter_stats["entity_type"] == "PER"
            assert filter_stats["filtering_strategy"] == "strict"
            
            # Test output directory creation
            output_dir = trainer.create_head_output_dir(temp_dir)
            assert Path(output_dir).exists()
            assert "PER_" in Path(output_dir).name
            
            # Test WandB run name creation
            run_name = trainer.create_wandb_run_name()
            assert "PER" in run_name
            assert "strict" in run_name
            
            # Test head config creation
            head_config = trainer._create_head_config(head_labels, output_dir)
            assert head_config.output_dir == output_dir
            assert "PER" in head_config.run_name
            assert "entity-per" in head_config.wandb_tags
            assert "filter-strict" in head_config.wandb_tags
            assert "single-head" in head_config.wandb_tags


if __name__ == "__main__":
    pytest.main([__file__])