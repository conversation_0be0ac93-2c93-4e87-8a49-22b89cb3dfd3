#!/usr/bin/env python3
"""
RobBERT-2023 Model Sanity Check and Validation Script.

This script implements task 5 from the RobBERT transition specification:
- Implement basic inference test with minimal input: "<PERSON> woon<PERSON> in Amsterdam."
- Verify model loads correctly and produces expected output shape (batch_size, seq_len, num_labels)
- Test tokenizer and model integration without shape mismatches or errors
- Create logging for successful model initialization and basic functionality
"""

import sys
import os
import torch
import logging
from pathlib import Path
from typing import Dict, Any, Tuple

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.models.multitask_robbert import MultiTaskRobBERT
from src.config import load_config


def setup_logging() -> logging.Logger:
    """Setup comprehensive logging for sanity check."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('robbert_sanity_check.log', mode='w', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)


def validate_model_initialization(model: MultiTaskRobBERT, logger: logging.Logger) -> bool:
    """
    Validate that the RobBERT model initialized correctly.
    
    Args:
        model: The initialized MultiTaskRobBERT model
        logger: Logger instance
        
    Returns:
        bool: True if validation passes
    """
    logger.info("=== MODEL INITIALIZATION VALIDATION ===")
    
    try:
        # Check model components
        logger.info("Checking model components...")
        
        # Verify RobBERT model is loaded
        if not hasattr(model, 'model'):
            logger.error("❌ Model does not have 'model' attribute")
            return False
        
        logger.info(f"✅ Model type: {type(model.model).__name__}")
        logger.info(f"✅ Model config: {model.config.name_or_path}")
        
        # Verify tokenizer
        if not hasattr(model, 'tokenizer'):
            logger.error("❌ Model does not have tokenizer")
            return False
        
        logger.info(f"✅ Tokenizer type: {type(model.tokenizer).__name__}")
        logger.info(f"✅ Tokenizer model: {model.tokenizer.model_name}")
        
        # Check encoder
        if not hasattr(model, 'encoder'):
            logger.error("❌ Model does not have encoder")
            return False
        
        logger.info(f"✅ Encoder type: {type(model.encoder).__name__}")
        
        # Check heads
        if not hasattr(model, 'heads') or 'ner' not in model.heads:
            logger.error("❌ Model does not have NER head")
            return False
        
        logger.info(f"✅ Available heads: {list(model.heads.keys())}")
        logger.info(f"✅ NER head type: {type(model.heads['ner']).__name__}")
        
        # Check model parameters
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        logger.info(f"✅ Total parameters: {total_params:,}")
        logger.info(f"✅ Trainable parameters: {trainable_params:,}")
        
        # Check device
        device = next(model.parameters()).device
        logger.info(f"✅ Model device: {device}")
        
        logger.info("✅ Model initialization validation PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Model initialization validation FAILED: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def validate_tokenizer_functionality(model: MultiTaskRobBERT, test_text: str, logger: logging.Logger) -> bool:
    """
    Validate tokenizer functionality with the test sentence.
    
    Args:
        model: The initialized MultiTaskRobBERT model
        test_text: Test sentence to tokenize
        logger: Logger instance
        
    Returns:
        bool: True if validation passes
    """
    logger.info("=== TOKENIZER FUNCTIONALITY VALIDATION ===")
    
    try:
        logger.info(f"Test sentence: '{test_text}'")
        
        # Test basic tokenization
        logger.info("Testing basic tokenization...")
        words = test_text.split()
        
        # Test tokenizer alignment functionality
        alignment = model.tokenizer.tokenize_with_alignment(words)
        
        logger.info(f"✅ Original words: {words}")
        logger.info(f"✅ Tokenized: {alignment.tokens}")
        logger.info(f"✅ Word IDs: {alignment.word_ids}")
        logger.info(f"✅ Token count: {len(alignment.tokens)}")
        
        # Test encoding for model input
        logger.info("Testing model input encoding...")
        encoding = model.tokenizer.encode_for_model(words, max_length=512)
        
        logger.info(f"✅ Input IDs shape: {encoding['input_ids'].shape}")
        logger.info(f"✅ Attention mask shape: {encoding['attention_mask'].shape}")
        
        # Verify shapes are correct
        batch_size, seq_len = encoding['input_ids'].shape
        if batch_size != 1:
            logger.error(f"❌ Expected batch_size=1, got {batch_size}")
            return False
        
        if seq_len != 512:  # max_length
            logger.error(f"❌ Expected seq_len=512, got {seq_len}")
            return False
        
        logger.info(f"✅ Encoding shapes are correct: batch_size={batch_size}, seq_len={seq_len}")
        
        # Test with labels
        logger.info("Testing label alignment...")
        test_labels = ["B-PER", "I-PER", "O", "O", "B-LOC"]  # Jan Jansen woont in Amsterdam
        if len(test_labels) == len(words):
            encoding_with_labels = model.tokenizer.encode_for_model(words, test_labels, max_length=512)
            logger.info(f"✅ Labels shape: {encoding_with_labels['labels'].shape}")
            
            # Validate label alignment
            validation_result = model.tokenizer.validate_alignment(words, test_labels, verbose=True)
            logger.info(f"✅ Alignment validation: {validation_result['valid']}")
            logger.info(f"✅ Alignment stats: {validation_result['stats']}")
        
        logger.info("✅ Tokenizer functionality validation PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Tokenizer functionality validation FAILED: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def validate_model_inference(model: MultiTaskRobBERT, test_text: str, logger: logging.Logger) -> Tuple[bool, Dict[str, Any]]:
    """
    Validate model inference with the test sentence.
    
    Args:
        model: The initialized MultiTaskRobBERT model
        test_text: Test sentence for inference
        logger: Logger instance
        
    Returns:
        Tuple[bool, Dict]: (success, inference_results)
    """
    logger.info("=== MODEL INFERENCE VALIDATION ===")
    
    try:
        logger.info(f"Running inference on: '{test_text}'")
        
        # Prepare input
        words = test_text.split()
        encoding = model.tokenizer.encode_for_model(words, max_length=512)
        
        # Move to model device
        device = next(model.parameters()).device
        input_ids = encoding['input_ids'].to(device)
        attention_mask = encoding['attention_mask'].to(device)
        
        logger.info(f"✅ Input moved to device: {device}")
        logger.info(f"✅ Input IDs shape: {input_ids.shape}")
        logger.info(f"✅ Attention mask shape: {attention_mask.shape}")
        
        # Run inference
        logger.info("Running model forward pass...")
        model.eval()
        with torch.no_grad():
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                heads=['ner']
            )
        
        logger.info("✅ Forward pass completed successfully")
        
        # Validate output structure
        if 'ner' not in outputs:
            logger.error("❌ NER output not found in model outputs")
            return False, {}
        
        ner_output = outputs['ner']
        if 'logits' not in ner_output:
            logger.error("❌ Logits not found in NER output")
            return False, {}
        
        logits = ner_output['logits']
        logger.info(f"✅ Output logits shape: {logits.shape}")
        
        # Validate output shape
        batch_size, seq_len, num_labels = logits.shape
        expected_batch_size = 1
        expected_seq_len = 512  # max_length
        expected_num_labels = 3  # O, B-PER, I-PER
        
        if batch_size != expected_batch_size:
            logger.error(f"❌ Expected batch_size={expected_batch_size}, got {batch_size}")
            return False, {}
        
        if seq_len != expected_seq_len:
            logger.error(f"❌ Expected seq_len={expected_seq_len}, got {seq_len}")
            return False, {}
        
        if num_labels != expected_num_labels:
            logger.error(f"❌ Expected num_labels={expected_num_labels}, got {num_labels}")
            return False, {}
        
        logger.info(f"✅ Output shape validation PASSED: ({batch_size}, {seq_len}, {num_labels})")
        
        # Get predictions
        predictions = torch.argmax(logits, dim=-1)
        logger.info(f"✅ Predictions shape: {predictions.shape}")
        
        # Decode predictions
        logger.info("Decoding predictions...")
        entities = model.tokenizer.decode_predictions(input_ids, predictions, attention_mask)
        
        logger.info(f"✅ Extracted {len(entities)} entities:")
        for i, entity in enumerate(entities):
            logger.info(f"  Entity {i+1}: '{entity['text']}' -> {entity['label']}")
        
        # Calculate confidence scores
        probabilities = torch.softmax(logits, dim=-1)
        max_probs = torch.max(probabilities, dim=-1)[0]
        avg_confidence = max_probs[attention_mask.bool()].mean().item()
        
        logger.info(f"✅ Average prediction confidence: {avg_confidence:.3f}")
        
        # Prepare results
        results = {
            'logits_shape': logits.shape,
            'predictions': predictions,
            'entities': entities,
            'avg_confidence': avg_confidence,
            'raw_logits': logits,
            'probabilities': probabilities
        }
        
        logger.info("✅ Model inference validation PASSED")
        return True, results
        
    except Exception as e:
        logger.error(f"❌ Model inference validation FAILED: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False, {}


def validate_integration(model: MultiTaskRobBERT, test_text: str, logger: logging.Logger) -> bool:
    """
    Validate tokenizer and model integration without shape mismatches.
    
    Args:
        model: The initialized MultiTaskRobBERT model
        test_text: Test sentence
        logger: Logger instance
        
    Returns:
        bool: True if integration validation passes
    """
    logger.info("=== TOKENIZER-MODEL INTEGRATION VALIDATION ===")
    
    try:
        # Test multiple sentences to ensure robustness
        test_sentences = [
            test_text,
            "Marie van der Berg werkt bij Google.",
            "De minister woont in Den Haag.",
            "Jan-Willem de Vries studeerde aan de Universiteit van Amsterdam."
        ]
        
        for i, sentence in enumerate(test_sentences):
            logger.info(f"Testing sentence {i+1}: '{sentence}'")
            
            words = sentence.split()
            
            # Test tokenization
            alignment = model.tokenizer.tokenize_with_alignment(words)
            encoding = model.tokenizer.encode_for_model(words, max_length=512)
            
            # Test model forward pass
            device = next(model.parameters()).device
            input_ids = encoding['input_ids'].to(device)
            attention_mask = encoding['attention_mask'].to(device)
            
            with torch.no_grad():
                outputs = model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    heads=['ner']
                )
            
            # Validate no shape mismatches
            logits = outputs['ner']['logits']
            expected_shape = (1, 512, 3)  # (batch_size, seq_len, num_labels)
            
            if logits.shape != expected_shape:
                logger.error(f"❌ Shape mismatch for sentence {i+1}: expected {expected_shape}, got {logits.shape}")
                return False
            
            logger.info(f"✅ Sentence {i+1} processed successfully - shape: {logits.shape}")
        
        logger.info("✅ Tokenizer-model integration validation PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration validation FAILED: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def run_comprehensive_sanity_check() -> bool:
    """
    Run comprehensive sanity check for RobBERT-2023 model.
    
    Returns:
        bool: True if all validations pass
    """
    logger = setup_logging()
    
    # Test sentence as specified in task requirements
    test_text = "Jan Jansen woont in Amsterdam."
    
    logger.info("=" * 80)
    logger.info("RobBERT-2023 MODEL SANITY CHECK AND VALIDATION")
    logger.info("=" * 80)
    logger.info(f"Test sentence: '{test_text}'")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"PyTorch version: {torch.__version__}")
    logger.info(f"CUDA available: {torch.cuda.is_available()}")
    
    try:
        # Load configuration
        logger.info("Loading configuration...")
        config = load_config('src/config/default.yaml')
        logger.info("✅ Configuration loaded successfully")
        
        # Initialize model
        logger.info("Initializing RobBERT-2023 model...")
        model = MultiTaskRobBERT.from_pretrained()
        
        # Setup device
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model.to(device)
        logger.info(f"✅ Model loaded and moved to {device}")
        
        # Run validation steps
        validation_results = []
        
        # Step 1: Model initialization validation
        result1 = validate_model_initialization(model, logger)
        validation_results.append(("Model Initialization", result1))
        
        # Step 2: Tokenizer functionality validation
        result2 = validate_tokenizer_functionality(model, test_text, logger)
        validation_results.append(("Tokenizer Functionality", result2))
        
        # Step 3: Model inference validation
        result3, inference_results = validate_model_inference(model, test_text, logger)
        validation_results.append(("Model Inference", result3))
        
        # Step 4: Integration validation
        result4 = validate_integration(model, test_text, logger)
        validation_results.append(("Tokenizer-Model Integration", result4))
        
        # Summary
        logger.info("=" * 80)
        logger.info("VALIDATION SUMMARY")
        logger.info("=" * 80)
        
        all_passed = True
        for test_name, result in validation_results:
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name:30} : {status}")
            if not result:
                all_passed = False
        
        logger.info("=" * 80)
        
        if all_passed:
            logger.info("🎉 ALL VALIDATIONS PASSED!")
            logger.info("RobBERT-2023 model is working correctly and ready for use.")
            
            # Log key metrics
            if result3 and inference_results:
                logger.info("\nKey Metrics:")
                logger.info(f"  - Output shape: {inference_results['logits_shape']}")
                logger.info(f"  - Entities found: {len(inference_results['entities'])}")
                logger.info(f"  - Average confidence: {inference_results['avg_confidence']:.3f}")
                
                if inference_results['entities']:
                    logger.info("  - Detected entities:")
                    for entity in inference_results['entities']:
                        logger.info(f"    * '{entity['text']}' -> {entity['label']}")
            
        else:
            logger.error("💥 SOME VALIDATIONS FAILED!")
            logger.error("Please check the logs above for details.")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ CRITICAL FAILURE: Sanity check failed with error: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def main():
    """Main function."""
    success = run_comprehensive_sanity_check()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 ROBBERT-2023 SANITY CHECK PASSED!")
        print("The model is working correctly and ready for production use.")
    else:
        print("💥 ROBBERT-2023 SANITY CHECK FAILED!")
        print("Please check the logs for details and fix the issues.")
    print("=" * 80)
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()