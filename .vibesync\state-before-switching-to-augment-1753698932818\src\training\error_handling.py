"""
Comprehensive error handling for Hugging Face Trainer integration.

This module provides robust error handling for data loading, preprocessing,
tokenization, alignment issues, CUDA memory management, and graceful degradation
when external services like WandB are unavailable.
"""

import os
import gc
import time
import json
import torch
import psutil
import traceback
import functools
from typing import Dict, List, Optional, Any, Union, Callable, Tuple
from pathlib import Path
from contextlib import contextmanager

try:
    from ..utils.logging_utils import get_logger, RobBERTLogger
    from ..exceptions import (
        TrainingError, DataProcessingError, TokenizationError, 
        LabelAlignmentError, MemoryError as RobBERTMemoryError,
        ModelLoadingError, ConfigurationError
    )
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent.parent))
    
    from src.utils.logging_utils import get_logger, RobBERTLogger
    from src.exceptions import (
        TrainingError, DataProcessingError, TokenizationError, 
        LabelAlignmentError, MemoryError as RobBERTMemoryError,
        ModelLoadingError, ConfigurationError
    )

try:
    from transformers import TrainingArguments, Trainer
    from datasets import Dataset, DatasetDict
    HF_AVAILABLE = True
except ImportError:
    # Mock classes for when transformers is not available
    TrainingArguments = None
    Trainer = None
    Dataset = None
    DatasetDict = None
    HF_AVAILABLE = False


class CUDAMemoryManager:
    """CUDA memory management with automatic batch size reduction."""
    
    def __init__(self, logger: Optional[RobBERTLogger] = None):
        """Initialize CUDA memory manager."""
        self.logger = logger or get_logger(f"{__name__}.{self.__class__.__name__}")
        self.original_batch_sizes = {}
        self.reduction_attempts = {}
        self.max_reduction_attempts = 3
        self.reduction_factor = 0.5
        
    def get_memory_info(self) -> Dict[str, Any]:
        """Get current memory information."""
        memory_info = {
            "cuda_available": torch.cuda.is_available(),
            "system_memory_gb": psutil.virtual_memory().total / 1e9,
            "system_memory_available_gb": psutil.virtual_memory().available / 1e9,
            "system_memory_percent": psutil.virtual_memory().percent
        }
        
        if torch.cuda.is_available():
            memory_info.update({
                "cuda_memory_allocated_gb": torch.cuda.memory_allocated() / 1e9,
                "cuda_memory_reserved_gb": torch.cuda.memory_reserved() / 1e9,
                "cuda_memory_total_gb": torch.cuda.get_device_properties(0).total_memory / 1e9,
                "cuda_device_count": torch.cuda.device_count(),
                "cuda_current_device": torch.cuda.current_device()
            })
        
        return memory_info
    
    def clear_cuda_cache(self):
        """Clear CUDA cache to free up memory."""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            gc.collect()
            self.logger.info("Cleared CUDA cache and ran garbage collection")
    
    @contextmanager
    def cuda_memory_context(self, operation_name: str = "operation"):
        """Context manager for CUDA memory monitoring."""
        if not torch.cuda.is_available():
            yield
            return
        
        # Log memory before operation
        memory_before = self.get_memory_info()
        self.logger.debug(f"Memory before {operation_name}: "
                         f"Allocated: {memory_before['cuda_memory_allocated_gb']:.2f}GB, "
                         f"Reserved: {memory_before['cuda_memory_reserved_gb']:.2f}GB")
        
        try:
            yield
        except RuntimeError as e:
            if "out of memory" in str(e).lower():
                memory_after = self.get_memory_info()
                self.logger.error(f"CUDA OOM during {operation_name}: "
                                f"Allocated: {memory_after['cuda_memory_allocated_gb']:.2f}GB, "
                                f"Reserved: {memory_after['cuda_memory_reserved_gb']:.2f}GB")
                self.clear_cuda_cache()
                raise RobBERTMemoryError(
                    f"CUDA out of memory during {operation_name}",
                    operation=operation_name,
                    allocated_memory=f"{memory_after['cuda_memory_allocated_gb']:.2f}GB",
                    total_memory=f"{memory_after['cuda_memory_total_gb']:.2f}GB"
                ) from e
            else:
                raise
        finally:
            # Log memory after operation
            memory_after = self.get_memory_info()
            self.logger.debug(f"Memory after {operation_name}: "
                            f"Allocated: {memory_after['cuda_memory_allocated_gb']:.2f}GB, "
                            f"Reserved: {memory_after['cuda_memory_reserved_gb']:.2f}GB")


class DataProcessingErrorHandler:
    """Error handler for data loading and preprocessing operations."""
    
    def __init__(self, logger: Optional[RobBERTLogger] = None):
        """Initialize data processing error handler."""
        self.logger = logger or get_logger(f"{__name__}.{self.__class__.__name__}")
        self.error_counts = {}
        self.max_errors_per_type = 10
    
    def safe_json_load(self, file_path: str) -> List[Dict[str, Any]]:
        """Safely load JSON data with error handling."""
        try:
            if not os.path.exists(file_path):
                raise DataProcessingError(
                    f"Data file not found: {file_path}",
                    data_path=file_path,
                    processing_step="file_loading"
                )
            
            with open(file_path, 'r', encoding='utf-8') as f:
                # Try to load as JSON array first
                try:
                    data = json.load(f)
                    if isinstance(data, list):
                        self.logger.info(f"Loaded {len(data)} records from {file_path}")
                        return data
                    else:
                        # Single object, wrap in list
                        return [data]
                except json.JSONDecodeError:
                    # Try JSONL format
                    f.seek(0)
                    records = []
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if not line:
                            continue
                        
                        try:
                            record = json.loads(line)
                            records.append(record)
                        except json.JSONDecodeError as e:
                            self.logger.warning(f"Invalid JSON on line {line_num}: {e}")
                            continue
                    
                    if records:
                        self.logger.info(f"Loaded {len(records)} records from JSONL file {file_path}")
                        return records
                    else:
                        raise DataProcessingError(
                            f"No valid JSON records found in {file_path}",
                            data_path=file_path,
                            processing_step="json_parsing"
                        )
        
        except Exception as e:
            if isinstance(e, DataProcessingError):
                raise
            
            raise DataProcessingError(
                f"Failed to load data from {file_path}: {str(e)}",
                data_path=file_path,
                processing_step="file_loading"
            ) from e
    
    def get_error_summary(self) -> Dict[str, int]:
        """Get summary of encountered errors."""
        return self.error_counts.copy()


class WandBErrorHandler:
    """Error handler for WandB integration with graceful degradation."""
    
    def __init__(self, logger: Optional[RobBERTLogger] = None):
        """Initialize WandB error handler."""
        self.logger = logger or get_logger(f"{__name__}.{self.__class__.__name__}")
        self.wandb_available = None
        self.fallback_metrics = []
    
    def check_wandb_availability(self) -> bool:
        """Check if WandB is available and properly configured."""
        if self.wandb_available is not None:
            return self.wandb_available
        
        try:
            import wandb
            
            # Check if we can initialize wandb (this will test authentication)
            test_run = wandb.init(mode="disabled", project="test")
            if test_run:
                test_run.finish()
            
            self.wandb_available = True
            self.logger.info("WandB is available and configured")
            
        except ImportError:
            self.wandb_available = False
            self.logger.warning("WandB not installed, using fallback logging")
            
        except Exception as e:
            self.wandb_available = False
            self.logger.warning(f"WandB not available: {e}")
            self.logger.info("Training will continue with fallback logging")
        
        return self.wandb_available
    
    @contextmanager
    def safe_wandb_context(self, project: str, entity: Optional[str] = None,
                          run_name: Optional[str] = None, **kwargs):
        """Context manager for safe WandB initialization."""
        wandb_run = None
        
        try:
            if self.check_wandb_availability():
                import wandb
                
                wandb_run = wandb.init(
                    project=project,
                    entity=entity,
                    name=run_name,
                    **kwargs
                )
                
                self.logger.info(f"Initialized WandB run: {wandb_run.name}")
                yield wandb_run
            else:
                self.logger.info("Using fallback logging (WandB unavailable)")
                yield None
                
        except Exception as e:
            self.logger.error(f"WandB initialization failed: {e}")
            self.logger.info("Continuing with fallback logging")
            yield None
            
        finally:
            if wandb_run:
                try:
                    wandb_run.finish()
                    self.logger.info("WandB run finished successfully")
                except Exception as e:
                    self.logger.warning(f"Error finishing WandB run: {e}")
    
    def safe_log_metrics(self, metrics: Dict[str, Any], step: Optional[int] = None):
        """Safely log metrics to WandB with fallback."""
        try:
            if self.check_wandb_availability():
                import wandb
                
                if wandb.run is not None:
                    wandb.log(metrics, step=step)
                    return
            
            # Fallback logging
            self.fallback_metrics.append({
                "step": step,
                "metrics": metrics,
                "timestamp": time.time()
            })
            
            # Log to console as fallback
            metrics_str = ", ".join([f"{k}: {v:.4f}" if isinstance(v, float) else f"{k}: {v}" 
                                   for k, v in metrics.items()])
            step_str = f"Step {step}: " if step is not None else ""
            self.logger.info(f"Metrics - {step_str}{metrics_str}")
            
        except Exception as e:
            self.logger.error(f"Error logging metrics: {e}")
    
    def get_fallback_metrics(self) -> List[Dict[str, Any]]:
        """Get fallback metrics for manual analysis."""
        return self.fallback_metrics.copy()
    
    def save_fallback_metrics(self, output_path: str):
        """Save fallback metrics to file."""
        try:
            with open(output_path, 'w') as f:
                json.dump(self.fallback_metrics, f, indent=2)
            
            self.logger.info(f"Saved {len(self.fallback_metrics)} fallback metrics to {output_path}")
            
        except Exception as e:
            self.logger.error(f"Error saving fallback metrics: {e}")


class TrainingErrorHandler:
    """Comprehensive error handler for training operations."""
    
    def __init__(self, logger: Optional[RobBERTLogger] = None):
        """Initialize comprehensive training error handler."""
        self.logger = logger or get_logger(f"{__name__}.{self.__class__.__name__}")
        
        # Initialize sub-handlers
        self.cuda_manager = CUDAMemoryManager(logger)
        self.data_handler = DataProcessingErrorHandler(logger)
        self.wandb_handler = WandBErrorHandler(logger)
        
        # Training state tracking
        self.training_errors = []
        self.recovery_attempts = {}
    
    def handle_training_error(self, error: Exception, trainer: Optional[Any] = None,
                            training_args: Optional[Any] = None,
                            step: Optional[int] = None, epoch: Optional[int] = None) -> bool:
        """Handle training errors with automatic recovery strategies."""
        error_info = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "step": step,
            "epoch": epoch,
            "timestamp": time.time()
        }
        
        self.training_errors.append(error_info)
        self.logger.error(f"Training error at step {step}, epoch {epoch}: {error}")
        
        # Handle CUDA out of memory errors
        if isinstance(error, RuntimeError) and "out of memory" in str(error).lower():
            return self._handle_cuda_oom(error, trainer, training_args)
        
        # Handle data loading errors
        elif isinstance(error, (DataProcessingError, TokenizationError, LabelAlignmentError)):
            return self._handle_data_error(error)
        
        # Handle model loading errors
        elif isinstance(error, ModelLoadingError):
            return self._handle_model_error(error)
        
        # Handle configuration errors
        elif isinstance(error, ConfigurationError):
            return self._handle_config_error(error)
        
        # Handle WandB errors
        elif "wandb" in str(error).lower():
            return self._handle_wandb_error(error)
        
        # Unknown error - log and re-raise
        else:
            self.logger.error(f"Unhandled training error: {error}")
            self.logger.debug(f"Traceback:\n{traceback.format_exc()}")
            return False
    
    def _handle_cuda_oom(self, error: Exception, trainer: Optional[Any],
                        training_args: Optional[Any]) -> bool:
        """Handle CUDA out of memory errors."""
        try:
            self.cuda_manager.clear_cuda_cache()
            self.logger.info("Cleared CUDA cache due to OOM error")
            return False  # Cannot automatically recover from OOM
            
        except Exception as recovery_error:
            self.logger.error(f"Failed to recover from CUDA OOM: {recovery_error}")
        
        return False
    
    def _handle_data_error(self, error: Exception) -> bool:
        """Handle data processing errors."""
        # Log detailed error information
        if hasattr(error, 'details'):
            self.logger.error(f"Data processing error details: {error.details}")
        
        # For data errors, we typically can't recover automatically
        self.logger.info("Suggestions for data error resolution:")
        self.logger.info("1. Check input data format and structure")
        self.logger.info("2. Validate entity annotations and spans")
        self.logger.info("3. Ensure tokenizer compatibility")
        self.logger.info("4. Review data preprocessing pipeline")
        
        return False
    
    def _handle_model_error(self, error: Exception) -> bool:
        """Handle model loading errors."""
        self.logger.error(f"Model loading error: {error}")
        
        # Suggest recovery strategies
        self.logger.info("Suggestions for model error resolution:")
        self.logger.info("1. Check model name and availability")
        self.logger.info("2. Verify checkpoint path and permissions")
        self.logger.info("3. Ensure sufficient disk space")
        self.logger.info("4. Check network connectivity for model downloads")
        
        return False
    
    def _handle_config_error(self, error: Exception) -> bool:
        """Handle configuration errors."""
        self.logger.error(f"Configuration error: {error}")
        
        # Configuration errors typically require manual intervention
        self.logger.info("Suggestions for configuration error resolution:")
        self.logger.info("1. Validate YAML configuration syntax")
        self.logger.info("2. Check required configuration fields")
        self.logger.info("3. Verify file paths and permissions")
        self.logger.info("4. Review environment variable settings")
        
        return False
    
    def _handle_wandb_error(self, error: Exception) -> bool:
        """Handle WandB-related errors."""
        self.logger.warning(f"WandB error: {error}")
        self.logger.info("Continuing training with fallback logging")
        
        # WandB errors are typically non-fatal
        # Training can continue with fallback logging
        return True
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of all encountered errors."""
        error_types = {}
        for error in self.training_errors:
            error_type = error["error_type"]
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return {
            "total_errors": len(self.training_errors),
            "error_types": error_types,
            "data_errors": self.data_handler.get_error_summary(),
            "cuda_reductions": len(self.cuda_manager.reduction_attempts),
            "wandb_fallback_metrics": len(self.wandb_handler.get_fallback_metrics())
        }
    
    def save_error_report(self, output_path: str):
        """Save comprehensive error report."""
        try:
            report = {
                "summary": self.get_error_summary(),
                "detailed_errors": self.training_errors,
                "cuda_memory_info": self.cuda_manager.get_memory_info(),
                "fallback_metrics": self.wandb_handler.get_fallback_metrics()
            }
            
            with open(output_path, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"Saved error report to {output_path}")
            
        except Exception as e:
            self.logger.error(f"Error saving error report: {e}")


# Decorator for automatic error handling
def handle_training_errors(error_handler: Optional[TrainingErrorHandler] = None):
    """Decorator for automatic training error handling."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal error_handler
            if error_handler is None:
                error_handler = TrainingErrorHandler()
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Try to handle the error
                if error_handler.handle_training_error(e):
                    error_handler.logger.info("Error handled, retrying operation...")
                    return func(*args, **kwargs)  # Retry
                else:
                    # Re-raise if not handled
                    raise
        
        return wrapper
    return decorator