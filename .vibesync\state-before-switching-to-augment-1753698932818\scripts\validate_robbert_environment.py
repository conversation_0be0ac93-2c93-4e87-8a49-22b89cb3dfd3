#!/usr/bin/env python3
"""
Environment validation script for RobBERT-2023 compatibility.

This script validates that the environment is properly configured for the
RobBERT-2023 transition by checking:
- PyTorch version compatibility
- CUDA availability and version
- Transformers and tokenizers versions
- RobBERT model and tokenizer loading
"""

import sys
import importlib.util
from typing import Dict, List, Tuple, Optional
import warnings

def check_package_version(package_name: str, min_version: str, max_version: Optional[str] = None) -> Tuple[bool, str]:
    """
    Check if a package meets version requirements.
    
    Args:
        package_name: Name of the package to check
        min_version: Minimum required version
        max_version: Maximum allowed version (optional)
    
    Returns:
        Tuple of (is_compatible, current_version)
    """
    try:
        module = importlib.import_module(package_name)
        current_version = getattr(module, '__version__', 'unknown')
        
        if current_version == 'unknown':
            return False, 'unknown'
        
        from packaging import version
        
        # Check minimum version
        if version.parse(current_version) < version.parse(min_version):
            return False, current_version
        
        # Check maximum version if specified
        if max_version and version.parse(current_version) >= version.parse(max_version):
            return False, current_version
        
        return True, current_version
    
    except ImportError:
        return False, 'not installed'
    except Exception as e:
        return False, f'error: {str(e)}'

def check_pytorch_cuda() -> Dict[str, any]:
    """Check PyTorch and CUDA configuration."""
    try:
        import torch
        
        cuda_info = {
            'cuda_available': torch.cuda.is_available(),
            'cuda_version': None,
            'device_count': 0,
            'current_device': None
        }
        
        if torch.cuda.is_available():
            cuda_info.update({
                'cuda_version': torch.version.cuda,
                'device_count': torch.cuda.device_count(),
                'current_device': torch.cuda.get_device_name(0) if torch.cuda.device_count() > 0 else None
            })
        
        return {
            'torch_version': torch.__version__,
            'cuda_info': cuda_info,
            'torch_compatible': True
        }
    
    except Exception as e:
        return {
            'torch_version': 'error',
            'cuda_info': {'error': str(e)},
            'torch_compatible': False
        }

def test_robbert_loading() -> Dict[str, any]:
    """Test loading RobBERT-2023 tokenizer and model."""
    model_name = "DTAI-KULeuven/robbert-2023-dutch-base"
    
    results = {
        'tokenizer_loading': False,
        'model_loading': False,
        'tokenizer_error': None,
        'model_error': None,
        'tokenizer_info': {},
        'model_info': {}
    }
    
    # Test tokenizer loading
    try:
        from transformers import AutoTokenizer
        
        print(f"Loading RobBERT tokenizer: {model_name}")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        
        results['tokenizer_loading'] = True
        results['tokenizer_info'] = {
            'vocab_size': tokenizer.vocab_size,
            'model_max_length': tokenizer.model_max_length,
            'tokenizer_class': tokenizer.__class__.__name__,
            'special_tokens': {
                'pad_token': tokenizer.pad_token,
                'unk_token': tokenizer.unk_token,
                'cls_token': tokenizer.cls_token,
                'sep_token': tokenizer.sep_token,
                'mask_token': tokenizer.mask_token
            }
        }
        
        # Test basic tokenization
        test_text = "Jan Jansen woont in Amsterdam."
        tokens = tokenizer.tokenize(test_text)
        results['tokenizer_info']['test_tokenization'] = {
            'input': test_text,
            'tokens': tokens,
            'token_count': len(tokens)
        }
        
    except Exception as e:
        results['tokenizer_error'] = str(e)
        print(f"Error loading tokenizer: {e}")
    
    # Test model loading
    try:
        from transformers import AutoModelForTokenClassification
        
        print(f"Loading RobBERT model: {model_name}")
        
        # Configure for NER task
        id2label = {0: "O", 1: "B-PER", 2: "I-PER"}
        label2id = {v: k for k, v in id2label.items()}
        
        model = AutoModelForTokenClassification.from_pretrained(
            model_name,
            num_labels=3,
            id2label=id2label,
            label2id=label2id
        )
        
        results['model_loading'] = True
        results['model_info'] = {
            'model_class': model.__class__.__name__,
            'num_parameters': sum(p.numel() for p in model.parameters()),
            'num_labels': model.num_labels,
            'id2label': model.config.id2label,
            'label2id': model.config.label2id,
            'hidden_size': model.config.hidden_size,
            'num_hidden_layers': model.config.num_hidden_layers,
            'num_attention_heads': model.config.num_attention_heads
        }
        
        # Test basic inference
        if results['tokenizer_loading']:
            test_input = tokenizer("Jan Jansen woont in Amsterdam.", return_tensors="pt")
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                outputs = model(**test_input)
            
            results['model_info']['test_inference'] = {
                'input_shape': test_input['input_ids'].shape,
                'output_shape': outputs.logits.shape,
                'output_sample': outputs.logits[0, 0, :3].tolist()  # First 3 logits for first token
            }
        
    except Exception as e:
        results['model_error'] = str(e)
        print(f"Error loading model: {e}")
    
    return results

def main():
    """Main validation function."""
    print("=" * 60)
    print("RobBERT-2023 Environment Validation")
    print("=" * 60)
    
    # Required package versions
    required_packages = {
        'torch': ('2.0.0', '2.6.0'),
        'transformers': ('4.38.0', '4.42.0'),
        'tokenizers': ('0.15.0', '0.20.0'),
        'datasets': ('2.20.0', '2.21.0'),
        'accelerate': ('0.29.0', '0.30.0'),
        'huggingface_hub': ('0.23.0', '0.24.0')
    }
    
    validation_results = {
        'package_versions': {},
        'pytorch_cuda': {},
        'robbert_loading': {},
        'overall_status': True
    }
    
    # Check package versions
    print("\n1. Checking Package Versions:")
    print("-" * 40)
    
    for package, (min_ver, max_ver) in required_packages.items():
        is_compatible, current_ver = check_package_version(package, min_ver, max_ver)
        validation_results['package_versions'][package] = {
            'required': f">={min_ver},<{max_ver}",
            'current': current_ver,
            'compatible': is_compatible
        }
        
        status = "✓" if is_compatible else "✗"
        print(f"{status} {package:15} {current_ver:15} (required: >={min_ver},<{max_ver})")
        
        if not is_compatible:
            validation_results['overall_status'] = False
    
    # Check PyTorch and CUDA
    print("\n2. Checking PyTorch and CUDA:")
    print("-" * 40)
    
    pytorch_info = check_pytorch_cuda()
    validation_results['pytorch_cuda'] = pytorch_info
    
    if pytorch_info['torch_compatible']:
        print(f"✓ PyTorch version: {pytorch_info['torch_version']}")
        
        cuda_info = pytorch_info['cuda_info']
        if cuda_info['cuda_available']:
            print(f"✓ CUDA available: {cuda_info['cuda_version']}")
            print(f"  - Device count: {cuda_info['device_count']}")
            if cuda_info['current_device']:
                print(f"  - Current device: {cuda_info['current_device']}")
        else:
            print("ℹ CUDA not available (CPU-only mode)")
    else:
        print("✗ PyTorch configuration error")
        validation_results['overall_status'] = False
    
    # Test RobBERT loading
    print("\n3. Testing RobBERT-2023 Loading:")
    print("-" * 40)
    
    robbert_results = test_robbert_loading()
    validation_results['robbert_loading'] = robbert_results
    
    # Tokenizer results
    if robbert_results['tokenizer_loading']:
        print("✓ RobBERT tokenizer loaded successfully")
        tokenizer_info = robbert_results['tokenizer_info']
        print(f"  - Vocabulary size: {tokenizer_info['vocab_size']}")
        print(f"  - Max length: {tokenizer_info['model_max_length']}")
        print(f"  - Tokenizer class: {tokenizer_info['tokenizer_class']}")
        
        test_info = tokenizer_info['test_tokenization']
        print(f"  - Test tokenization: '{test_info['input']}' → {test_info['token_count']} tokens")
    else:
        print("✗ RobBERT tokenizer loading failed")
        if robbert_results['tokenizer_error']:
            print(f"    Error: {robbert_results['tokenizer_error']}")
        validation_results['overall_status'] = False
    
    # Model results
    if robbert_results['model_loading']:
        print("✓ RobBERT model loaded successfully")
        model_info = robbert_results['model_info']
        print(f"  - Model class: {model_info['model_class']}")
        print(f"  - Parameters: {model_info['num_parameters']:,}")
        print(f"  - Hidden size: {model_info['hidden_size']}")
        print(f"  - Layers: {model_info['num_hidden_layers']}")
        print(f"  - Attention heads: {model_info['num_attention_heads']}")
        print(f"  - Labels: {model_info['num_labels']} ({list(model_info['id2label'].values())})")
        
        if 'test_inference' in model_info:
            test_info = model_info['test_inference']
            print(f"  - Test inference: {test_info['input_shape']} → {test_info['output_shape']}")
    else:
        print("✗ RobBERT model loading failed")
        if robbert_results['model_error']:
            print(f"    Error: {robbert_results['model_error']}")
        validation_results['overall_status'] = False
    
    # Summary
    print("\n" + "=" * 60)
    if validation_results['overall_status']:
        print("✓ VALIDATION PASSED: Environment is ready for RobBERT-2023")
        print("\nNext steps:")
        print("- You can proceed with the RobBERT-2023 transition")
        print("- All required packages are compatible")
        print("- RobBERT model and tokenizer load successfully")
        return 0
    else:
        print("✗ VALIDATION FAILED: Environment needs fixes")
        print("\nRequired actions:")
        
        # Package version issues
        for package, info in validation_results['package_versions'].items():
            if not info['compatible']:
                if info['current'] == 'not installed':
                    print(f"- Install {package}: pip install '{package}{info['required']}'")
                else:
                    print(f"- Update {package} from {info['current']} to {info['required']}")
        
        # Model loading issues
        if not robbert_results['tokenizer_loading'] or not robbert_results['model_loading']:
            print("- Fix model loading issues (check network connection and Hugging Face access)")
        
        return 1

if __name__ == "__main__":
    try:
        # Install packaging if not available
        try:
            import packaging
        except ImportError:
            print("Installing packaging module...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "packaging"])
            import packaging
        
        exit_code = main()
        sys.exit(exit_code)
    
    except KeyboardInterrupt:
        print("\n\nValidation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nUnexpected error during validation: {e}")
        sys.exit(1)