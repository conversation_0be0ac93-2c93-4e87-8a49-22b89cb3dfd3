from transformers import AutoTokenizer
from collections import Counter
import numpy as np

MODEL = "wietsedv/bert-base-dutch-cased-finetuned-conll2002-ner"
tokenizer = AutoTokenizer.from_pretrained(MODEL, use_fast=True)

def oov_rate(texts):
    unk_id = tokenizer.unk_token_id
    counts = Counter()
    for t in texts:
        ids = tokenizer(t, add_special_tokens=False).input_ids
        counts['tokens'] += len(ids)
        counts['unk'] += sum(i == unk_id for i in ids)
    return counts['unk'] / max(1, counts['tokens'])

with open("process_data/output/ocr_moeilijke_text.txt", "r", encoding="utf-8") as f:
    text = f.read()
print("OOV rate:", oov_rate([text]))
