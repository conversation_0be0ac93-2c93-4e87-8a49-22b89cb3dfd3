#!/usr/bin/env python3
"""
Standalone entry point for RobBERT-2023 NER training CLI.

This script provides easy access to the training CLI without needing
to use the module syntax.

Usage:
    python train.py single-head PER data_sets/dutch_ner.json
    python train.py multi-head PER LOC ORG data_sets/dutch_ner.json --parallel
    python train.py preprocess data_sets/raw.json data_sets/processed.json
"""

import subprocess
import sys

if __name__ == "__main__":
    # Run the CLI as a module to avoid import issues
    cmd = [sys.executable, "-m", "src.training"] + sys.argv[1:]
    sys.exit(subprocess.call(cmd))