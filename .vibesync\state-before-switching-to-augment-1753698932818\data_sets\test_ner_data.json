[{"id": 1, "sentence": "<PERSON> woont in Amsterdam.", "entities": [{"text": "<PERSON>", "label": "PER", "start": 0, "end": 10}]}, {"id": 2, "sentence": "Marie <PERSON> Wit werkt bij Google in Utrecht.", "entities": [{"text": "<PERSON>", "label": "PER", "start": 0, "end": 12}]}, {"id": 3, "sentence": "<PERSON><PERSON> is een dokter.", "entities": [{"text": "<PERSON><PERSON>", "label": "PER", "start": 0, "end": 17}]}, {"id": 4, "sentence": "<PERSON> studeert aan de Universiteit van Amsterdam.", "entities": [{"text": "<PERSON>", "label": "PER", "start": 0, "end": 9}]}, {"id": 5, "sentence": "<PERSON> speelt voetbal bij Ajax.", "entities": [{"text": "<PERSON>", "label": "PER", "start": 0, "end": 11}]}, {"id": 6, "sentence": "<PERSON> werkt als verpleegster.", "entities": [{"text": "<PERSON>", "label": "PER", "start": 0, "end": 11}]}, {"id": 7, "sentence": "<PERSON><PERSON> is een zin zonder personen.", "entities": []}, {"id": 8, "sentence": "<PERSON><PERSON> en Petra de Vries zijn collega's.", "entities": [{"text": "<PERSON><PERSON>", "label": "PER", "start": 0, "end": 11}, {"text": "<PERSON>", "label": "PER", "start": 15, "end": 29}]}, {"id": 9, "sentence": "<PERSON><PERSON><PERSON><PERSON> van <PERSON> heeft een afspraak.", "entities": [{"text": "<PERSON>", "label": "PER", "start": 8, "end": 20}]}, {"id": 10, "sentence": "<PERSON> <PERSON><PERSON> komt morgen langs.", "entities": [{"text": "<PERSON><PERSON>", "label": "PER", "start": 8, "end": 17}]}]