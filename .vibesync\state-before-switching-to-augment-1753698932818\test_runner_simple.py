#!/usr/bin/env python3
"""
Simple test runner for comprehensive HF integration tests.
"""

import subprocess
import sys
import os

def main():
    """Run comprehensive tests in test mode."""
    
    # Set test mode environment variables
    os.environ["TEST_MODE"] = "true"
    os.environ["WANDB_MODE"] = "disabled"
    
    print("🧪 Running Comprehensive HF Integration Tests")
    print("=" * 50)
    
    # Run smoke test first
    print("1. Running smoke test...")
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/test_smoke_comprehensive.py", 
        "-v", "--tb=short"
    ])
    
    if result.returncode != 0:
        print("❌ Smoke test failed!")
        return result.returncode
    
    print("✅ Smoke test passed!")
    print()
    
    # Run unit tests
    print("2. Running unit tests...")
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/test_hf_test_mode.py::TestModeConfiguration",
        "-v", "--tb=short", "-m", "not slow"
    ])
    
    if result.returncode != 0:
        print("❌ Unit tests failed!")
        return result.returncode
    
    print("✅ Unit tests passed!")
    print()
    
    # Run a simple integration test
    print("3. Running basic integration test...")
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/test_hf_test_mode.py::TestModeIntegrationWithTraining::test_end_to_end_test_mode_training",
        "-v", "--tb=short"
    ])
    
    if result.returncode != 0:
        print("❌ Integration test failed!")
        return result.returncode
    
    print("✅ Integration test passed!")
    print()
    
    print("🎉 All tests completed successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())