# RobBERT-2023 Dutch Person Named Entity Recognition

A production-ready Python project for Dutch Person Named Entity Recognition using RobBERT-2023-dutch-base model with extensible multi-head architecture.

## 🎯 Overview

This project implements state-of-the-art Person Named Entity Recognition for Dutch text using the RobBERT-2023-dutch-base model. The system is designed with a clean, extensible architecture that can be expanded to support additional entity types and classification tasks.

### Key Features

- **RobBERT-2023 Integration**: Latest Dutch language model with byte-level BPE tokenization
- **Person Entity Focus**: Specialized for detecting Dutch person names with high accuracy
- **Extensible Architecture**: Multi-head design supports adding new classification heads
- **Production Ready**: FastAPI server, batch processing, comprehensive testing
- **WandB Integration**: Complete experiment tracking and model artifact management
- **Dutch Language Optimized**: Handles compound names, particles, and Dutch linguistic patterns

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd bertjeNER

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/macOS
.\venv\Scripts\activate   # Windows

# Install dependencies
pip install -r requirements.txt
```

### Basic Usage

#### 1. Validate Environment
```bash
python scripts/validate_robbert_environment.py
python scripts/robbert_sanity_check.py
```

#### 2. Train Person NER Model
```bash
python -m src.training.train_multitask \
    --heads ner \
    --use-head-configs \
    --wandb-config src/config/wandb.yaml \
    --epochs 5 \
    --data data_sets/your_training_data.jsonl
```

#### 3. Start API Server
```bash
uvicorn src.inference.api_fastapi:app --host 0.0.0.0 --port 8000 --reload
```

#### 4. Test the API
```bash
curl -X POST "http://localhost:8000/predict" \
     -H "Content-Type: application/json" \
     -d '{"text": "Jan Jansen woont in Amsterdam."}'
```

## 📁 Project Structure

```
bertjeNER/
├── src/                    # Source code
│   ├── config/            # Configuration management
│   ├── data/              # Data processing utilities
│   ├── heads/             # Task-specific model heads
│   ├── inference/         # API and batch processing
│   ├── models/            # RobBERT model implementation
│   ├── training/          # Training pipeline
│   └── utils/             # Utility modules
├── scripts/               # Utility and testing scripts
├── tests/                 # Comprehensive test suite
├── models/                # Model checkpoints and artifacts
├── docs/                  # Documentation
└── data_sets/             # Training and test data
```

See [PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md) for detailed structure documentation.

## 🎯 Entity Recognition

Currently supports **Person Named Entity Recognition**:

- **B-PER**: Beginning of person entity (Jan, Marie)
- **I-PER**: Inside/continuation of person entity (Jansen, van der Berg)
- **O**: Outside any entity

### Examples

```python
# Input: "Jan Jansen en Marie van der Berg werken samen."
# Output: 
# Jan -> B-PER
# Jansen -> I-PER  
# en -> O
# Marie -> B-PER
# van -> I-PER
# der -> I-PER
# Berg -> I-PER
# werken -> O
# samen -> O
```

## 🔧 Configuration

### Main Configuration (`src/config/default.yaml`)
```yaml
model:
  model_name: "DTAI-KULeuven/robbert-2023-dutch-base"
  tokenizer_name: "DTAI-KULeuven/robbert-2023-dutch-base"
  max_length: 512
  num_labels:
    ner: 3  # O, B-PER, I-PER

training:
  epochs: 3
  batch_size: 8
  learning_rate: 2.0e-5
```

### WandB Integration (`src/config/wandb.yaml`)
```yaml
wandb:
  entity: "your-entity"
  project: "robbert2023"
  enabled: true
```

### Head-Specific Config (`src/config/heads/ner.yaml`)
```yaml
head_config:
  name: "ner"
  type: "token_classification"
  model:
    num_labels: 3
  training:
    epochs: 5
    batch_size: 16
    learning_rate: 3e-5
```

## 🧪 Testing

### Run Full Test Suite
```bash
pytest tests/ -v
```

### Specific Test Categories
```bash
pytest tests/ -m "not slow"     # Skip slow tests
pytest tests/ -m "unit"         # Unit tests only
pytest tests/ -m "integration"  # Integration tests only
```

### Validation Scripts
```bash
python scripts/validate_wandb_config.py      # Validate WandB setup
python scripts/robbert_sanity_check.py       # Model validation
python scripts/test_end_to_end_pipeline.py   # End-to-end testing
```

## 📊 Training with WandB

### Single Head Training
```bash
python -m src.training.train_multitask \
    --heads ner \
    --use-head-configs \
    --wandb-config src/config/wandb.yaml \
    --epochs 5 \
    --batch-size 16
```

### Training Examples
```bash
python scripts/train_with_wandb.py  # Comprehensive training examples
```

### Monitor Training
Visit your WandB dashboard at `https://wandb.ai/your-entity/robbert2023-ner`

## 🔌 API Usage

### Start Server
```bash
uvicorn src.inference.api_fastapi:app --host 0.0.0.0 --port 8000
```

### API Endpoints

#### Health Check
```bash
GET /ping
```

#### Person Entity Recognition
```bash
POST /predict
{
  "text": "Jan Jansen woont in Amsterdam."
}
```

#### Response Format
```json
{
  "entities": [
    {
      "text": "Jan Jansen",
      "label": "PER",
      "start": 0,
      "end": 10,
      "confidence": 0.95
    }
  ],
  "processing_time": 0.123
}
```

## 🚀 Performance

- **Tokenization**: Byte-level BPE with near-zero OOV rate
- **Inference Speed**: Optimized for CPU deployment
- **Memory Usage**: Efficient model loading and inference
- **Accuracy**: Optimized for Dutch person name patterns

## 🔮 Extensibility

The architecture supports adding new heads for:

- **Location Recognition** (LOC entities)
- **Organization Recognition** (ORG entities)
- **Document Classification**
- **Sentiment Analysis**
- **Custom Classification Tasks**

### Adding New Heads

1. Create head configuration in `src/config/heads/new_head.yaml`
2. Implement head class in `src/heads/new_head.py`
3. Update training configuration
4. Train with `--heads ner,new_head`

## 📚 Documentation

- [WandB Integration Guide](docs/README_WANDB_INTEGRATION.md)
- [API Documentation](docs/README_API_ROBBERT.md)
- [Checkpoint Management](docs/checkpoint_management.md)
- [Performance Baseline](docs/performance_baseline.md)
- [Tokenization Guide](docs/robbert_tokenization_guide.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

- [RobBERT-2023](https://huggingface.co/DTAI-KULeuven/robbert-2023-dutch-base) by DTAI-KULeuven
- [Hugging Face Transformers](https://huggingface.co/transformers/)
- [Weights & Biases](https://wandb.ai/) for experiment tracking