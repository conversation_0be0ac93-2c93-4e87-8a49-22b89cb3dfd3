# Training infrastructure

# Lazy imports to avoid loading heavy dependencies when not needed
def _lazy_import():
    from .hf_config import (
        HFTrainingConfig,
        create_default_hf_config,
        create_test_hf_config
    )
    return {
        'HFTrainingConfig': HFTrainingConfig,
        'create_default_hf_config': create_default_hf_config,
        'create_test_hf_config': create_test_hf_config
    }

def __getattr__(name):
    """Lazy loading of training components."""
    if name in ['HFTrainingConfig', 'create_default_hf_config', 'create_test_hf_config']:
        return _lazy_import()[name]
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

__all__ = [
    'HFTrainingConfig',
    'create_default_hf_config',
    'create_test_hf_config'
]