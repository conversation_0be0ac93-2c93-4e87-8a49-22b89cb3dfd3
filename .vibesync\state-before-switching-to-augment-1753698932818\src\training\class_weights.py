"""
Class weight calculation for imbalanced NER datasets.

This module provides utilities to compute class weights for balanced training
across different entity types (PER, LOC, ORG) with per-head weight computation
and dataset analysis capabilities.
"""

import logging
from typing import Dict, List, Optional, Tuple, Any, Union
from collections import Counter, defaultdict
from pathlib import Path
import json

import numpy as np
from sklearn.utils.class_weight import compute_class_weight
from datasets import Dataset, DatasetDict
import torch

from ..utils.logging_utils import get_logger
from ..exceptions import DataProcessingError


class ClassWeightCalculator:
    """
    Calculate class weights for imbalanced NER datasets.
    
    This class provides methods to compute class weights using sklearn's
    compute_class_weight function, with support for per-head computation
    since label imbalance differs between entity types (PER, LOC, ORG).
    """
    
    def __init__(self):
        """Initialize class weight calculator."""
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    def compute_class_weights(
        self,
        dataset: Union[Dataset, DatasetDict],
        label_list: List[str],
        method: str = "balanced",
        entity_type: Optional[str] = None
    ) -> Dict[str, float]:
        """
        Compute class weights for balanced training using sklearn.
        
        Args:
            dataset: Hugging Face dataset or dataset dict
            label_list: List of all labels in BIO format
            method: Weight computation method ("balanced", "balanced_subsample", or custom dict)
            entity_type: Specific entity type for per-head computation (e.g., "PER")
            
        Returns:
            Dictionary mapping label names to weight values
            
        Raises:
            DataProcessingError: If weight computation fails
        """
        self.logger.logger.info(f"Computing class weights (method={method}, entity_type={entity_type})")
        
        try:
            # Extract labels from dataset
            all_labels = self._extract_labels_from_dataset(dataset)
            
            # Filter labels for specific entity type if requested
            if entity_type:
                filtered_labels, filtered_label_list = self._filter_labels_for_entity_type(
                    all_labels, label_list, entity_type
                )
                self.logger.logger.info(f"Filtered to {len(filtered_labels)} labels for entity type: {entity_type}")
            else:
                filtered_labels = all_labels
                filtered_label_list = label_list
            
            # Convert label IDs back to label names for analysis
            label_names = [filtered_label_list[label_id] for label_id in filtered_labels]
            
            # Count label distribution
            label_counts = Counter(label_names)
            self.logger.logger.info(f"Label distribution: {dict(label_counts)}")
            
            # Compute class weights using sklearn
            unique_labels = list(set(label_names))
            
            if method == "balanced":
                weights = compute_class_weight(
                    class_weight='balanced',
                    classes=np.array(unique_labels),
                    y=np.array(label_names)
                )
            elif method == "balanced_subsample":
                # Use balanced_subsample for very large datasets
                weights = compute_class_weight(
                    class_weight='balanced_subsample',
                    classes=np.array(unique_labels),
                    y=np.array(label_names)
                )
            else:
                raise ValueError(f"Unsupported weight computation method: {method}")
            
            # Create weight dictionary
            weight_dict = {}
            for label, weight in zip(unique_labels, weights):
                weight_dict[label] = float(weight)
            
            # Ensure all labels in filtered_label_list have weights (fill missing with 1.0)
            for label in filtered_label_list:
                if label not in weight_dict:
                    weight_dict[label] = 1.0
                    self.logger.logger.warning(f"No weight computed for label '{label}', using 1.0")
            
            # Log computed weights
            self.logger.logger.info("Computed class weights:")
            for label, weight in sorted(weight_dict.items()):
                self.logger.logger.info(f"  {label}: {weight:.4f}")
            
            return weight_dict
            
        except Exception as e:
            error_msg = f"Failed to compute class weights: {e}"
            self.logger.logger.error(error_msg)
            raise DataProcessingError(error_msg) from e
    
    def compute_per_head_class_weights(
        self,
        dataset: Union[Dataset, DatasetDict],
        label_list: List[str],
        entity_types: Optional[List[str]] = None,
        method: str = "balanced"
    ) -> Dict[str, Dict[str, float]]:
        """
        Compute class weights for each entity type head separately.
        
        Since label imbalance differs between PER, LOC, ORG heads, this method
        computes separate weights for each entity type to handle their specific
        imbalance patterns.
        
        Args:
            dataset: Hugging Face dataset or dataset dict
            label_list: List of all labels in BIO format
            entity_types: List of entity types to compute weights for (auto-detected if None)
            method: Weight computation method
            
        Returns:
            Dictionary mapping entity types to their class weight dictionaries
        """
        self.logger.logger.info(f"Computing per-head class weights (method={method})")
        
        # Auto-detect entity types if not provided
        if entity_types is None:
            entity_types = self._extract_entity_types_from_labels(label_list)
            self.logger.logger.info(f"Auto-detected entity types: {entity_types}")
        
        per_head_weights = {}
        
        for entity_type in entity_types:
            self.logger.logger.info(f"Computing weights for entity type: {entity_type}")
            
            try:
                # Compute weights for this specific entity type
                weights = self.compute_class_weights(
                    dataset=dataset,
                    label_list=label_list,
                    method=method,
                    entity_type=entity_type
                )
                
                per_head_weights[entity_type] = weights
                
            except Exception as e:
                self.logger.logger.error(f"Failed to compute weights for {entity_type}: {e}")
                # Use uniform weights as fallback
                entity_labels = [label for label in label_list 
                               if label == "O" or label.endswith(f"-{entity_type}")]
                fallback_weights = {label: 1.0 for label in entity_labels}
                per_head_weights[entity_type] = fallback_weights
                self.logger.logger.warning(f"Using uniform weights for {entity_type}")
        
        return per_head_weights
    
    def _extract_labels_from_dataset(self, dataset: Union[Dataset, DatasetDict]) -> List[int]:
        """Extract all label IDs from dataset."""
        all_labels = []
        
        # Handle DatasetDict (extract from train split)
        if isinstance(dataset, DatasetDict):
            if 'train' in dataset:
                dataset = dataset['train']
            else:
                # Use first available split
                dataset = dataset[list(dataset.keys())[0]]
        
        # Extract labels from all examples
        for example in dataset:
            labels = example.get('labels', [])
            if isinstance(labels, (list, tuple)):
                all_labels.extend(labels)
            else:
                all_labels.append(labels)
        
        # Filter out special tokens (typically -100)
        filtered_labels = [label for label in all_labels if label >= 0]
        
        self.logger.logger.info(f"Extracted {len(filtered_labels)} labels from {len(dataset)} examples")
        
        return filtered_labels
    
    def _filter_labels_for_entity_type(
        self,
        all_labels: List[int],
        label_list: List[str],
        entity_type: str
    ) -> Tuple[List[int], List[str]]:
        """
        Filter labels to only include those relevant for a specific entity type.
        
        For per-head training, we use a 3-label scheme per head:
        ["O", "B-LABEL", "I-LABEL"] where LABEL is the specific entity type.
        
        Args:
            all_labels: List of all label IDs
            label_list: List of all label names
            entity_type: Entity type to filter for (e.g., "PER")
            
        Returns:
            Tuple of (filtered_label_ids, filtered_label_list)
        """
        # Create mapping from entity-specific labels to simplified 3-label scheme
        entity_label_mapping = {}
        simplified_labels = ["O", f"B-{entity_type}", f"I-{entity_type}"]
        
        # Map original labels to simplified scheme
        for i, label in enumerate(label_list):
            if label == "O":
                entity_label_mapping[i] = 0  # O
            elif label == f"B-{entity_type}":
                entity_label_mapping[i] = 1  # B-ENTITY
            elif label == f"I-{entity_type}":
                entity_label_mapping[i] = 2  # I-ENTITY
            else:
                # Map other entity types to O for this head
                entity_label_mapping[i] = 0  # O
        
        # Apply mapping to all labels
        filtered_labels = [entity_label_mapping.get(label_id, 0) for label_id in all_labels]
        
        return filtered_labels, simplified_labels
    
    def _extract_entity_types_from_labels(self, label_list: List[str]) -> List[str]:
        """Extract unique entity types from BIO label list."""
        entity_types = set()
        
        for label in label_list:
            if label.startswith(('B-', 'I-')):
                entity_type = label[2:]  # Remove B- or I- prefix
                entity_types.add(entity_type)
        
        return sorted(list(entity_types))
    
    def analyze_label_distribution(
        self,
        dataset: Union[Dataset, DatasetDict],
        label_list: List[str],
        entity_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze label distribution for dataset analysis utilities.
        
        Args:
            dataset: Hugging Face dataset or dataset dict
            label_list: List of all labels in BIO format
            entity_type: Specific entity type for analysis (optional)
            
        Returns:
            Dictionary with comprehensive label distribution analysis
        """
        self.logger.logger.info(f"Analyzing label distribution (entity_type={entity_type})")
        
        # Extract labels
        all_labels = self._extract_labels_from_dataset(dataset)
        
        # Filter for specific entity type if requested
        if entity_type:
            filtered_labels, filtered_label_list = self._filter_labels_for_entity_type(
                all_labels, label_list, entity_type
            )
            analysis_labels = filtered_labels
            analysis_label_list = filtered_label_list
        else:
            analysis_labels = all_labels
            analysis_label_list = label_list
        
        # Convert label IDs to names
        label_names = []
        for label_id in analysis_labels:
            if 0 <= label_id < len(analysis_label_list):
                label_names.append(analysis_label_list[label_id])
            else:
                label_names.append("UNK")
        
        # Count labels
        label_counts = Counter(label_names)
        total_labels = len(label_names)
        
        # Calculate percentages and imbalance metrics
        label_percentages = {}
        for label, count in label_counts.items():
            label_percentages[label] = (count / total_labels) * 100
        
        # Calculate imbalance ratio (most common / least common)
        if len(label_counts) > 1:
            max_count = max(label_counts.values())
            min_count = min(label_counts.values())
            imbalance_ratio = max_count / min_count if min_count > 0 else float('inf')
        else:
            imbalance_ratio = 1.0
        
        # Entity-specific analysis
        entity_analysis = {}
        if not entity_type:  # Overall analysis
            # Count entities by type
            entity_counts = defaultdict(int)
            for label_name in label_names:
                if label_name.startswith('B-'):
                    entity_type_name = label_name[2:]
                    entity_counts[entity_type_name] += 1
            
            entity_analysis = {
                "entity_counts": dict(entity_counts),
                "total_entities": sum(entity_counts.values()),
                "entity_types": len(entity_counts)
            }
        
        analysis = {
            "total_labels": total_labels,
            "unique_labels": len(label_counts),
            "label_counts": dict(label_counts),
            "label_percentages": label_percentages,
            "imbalance_ratio": imbalance_ratio,
            "most_common_label": label_counts.most_common(1)[0] if label_counts else None,
            "least_common_label": label_counts.most_common()[-1] if label_counts else None,
            "entity_type_filter": entity_type,
            **entity_analysis
        }
        
        # Log analysis results
        self.logger.logger.info(f"Label distribution analysis:")
        self.logger.logger.info(f"  Total labels: {total_labels}")
        self.logger.logger.info(f"  Unique labels: {len(label_counts)}")
        self.logger.logger.info(f"  Imbalance ratio: {imbalance_ratio:.2f}")
        
        for label, count in label_counts.most_common():
            percentage = label_percentages[label]
            self.logger.logger.info(f"  {label}: {count} ({percentage:.2f}%)")
        
        return analysis
    
    def create_class_weight_tensor(
        self,
        class_weights: Dict[str, float],
        label_list: List[str],
        device: Optional[torch.device] = None
    ) -> torch.Tensor:
        """
        Create PyTorch tensor of class weights for loss computation.
        
        Args:
            class_weights: Dictionary mapping label names to weights
            label_list: List of all labels in order
            device: Target device for tensor
            
        Returns:
            PyTorch tensor with weights in label order
        """
        weights = []
        for label in label_list:
            weight = class_weights.get(label, 1.0)
            weights.append(weight)
        
        weight_tensor = torch.tensor(weights, dtype=torch.float32)
        
        if device is not None:
            weight_tensor = weight_tensor.to(device)
        
        self.logger.logger.info(f"Created class weight tensor: shape={weight_tensor.shape}, device={weight_tensor.device}")
        
        return weight_tensor
    
    def save_class_weights(
        self,
        class_weights: Dict[str, Union[float, Dict[str, float]]],
        output_path: Union[str, Path],
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Save class weights to JSON file with metadata.
        
        Args:
            class_weights: Class weights dictionary (can be nested for per-head weights)
            output_path: Path to save weights JSON file
            metadata: Optional metadata to include
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Prepare data for saving
        save_data = {
            "class_weights": class_weights,
            "metadata": metadata or {},
            "created_at": str(np.datetime64('now'))
        }
        
        # Convert numpy types to native Python types for JSON serialization
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            else:
                return obj
        
        save_data = convert_numpy_types(save_data)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)
        
        self.logger.logger.info(f"Saved class weights to: {output_path}")
    
    def load_class_weights(self, weights_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Load class weights from JSON file.
        
        Args:
            weights_path: Path to weights JSON file
            
        Returns:
            Dictionary with class weights and metadata
        """
        weights_path = Path(weights_path)
        
        if not weights_path.exists():
            raise FileNotFoundError(f"Class weights file not found: {weights_path}")
        
        with open(weights_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.logger.logger.info(f"Loaded class weights from: {weights_path}")
        
        return data


def compute_class_weights(
    dataset: Union[Dataset, DatasetDict],
    label_list: List[str],
    method: str = "balanced",
    entity_type: Optional[str] = None
) -> Dict[str, float]:
    """
    Convenience function to compute class weights for balanced training.
    
    Args:
        dataset: Hugging Face dataset or dataset dict
        label_list: List of all labels in BIO format
        method: Weight computation method ("balanced" or "balanced_subsample")
        entity_type: Specific entity type for per-head computation
        
    Returns:
        Dictionary mapping label names to weight values
    """
    calculator = ClassWeightCalculator()
    return calculator.compute_class_weights(dataset, label_list, method, entity_type)


def compute_per_head_class_weights(
    dataset: Union[Dataset, DatasetDict],
    label_list: List[str],
    entity_types: Optional[List[str]] = None,
    method: str = "balanced"
) -> Dict[str, Dict[str, float]]:
    """
    Convenience function to compute per-head class weights.
    
    Args:
        dataset: Hugging Face dataset or dataset dict
        label_list: List of all labels in BIO format
        entity_types: List of entity types (auto-detected if None)
        method: Weight computation method
        
    Returns:
        Dictionary mapping entity types to their class weight dictionaries
    """
    calculator = ClassWeightCalculator()
    return calculator.compute_per_head_class_weights(dataset, label_list, entity_types, method)


def analyze_dataset_imbalance(
    dataset: Union[Dataset, DatasetDict],
    label_list: List[str],
    entity_type: Optional[str] = None
) -> Dict[str, Any]:
    """
    Convenience function to analyze dataset label distribution.
    
    Args:
        dataset: Hugging Face dataset or dataset dict
        label_list: List of all labels in BIO format
        entity_type: Specific entity type for analysis
        
    Returns:
        Dictionary with comprehensive imbalance analysis
    """
    calculator = ClassWeightCalculator()
    return calculator.analyze_label_distribution(dataset, label_list, entity_type)