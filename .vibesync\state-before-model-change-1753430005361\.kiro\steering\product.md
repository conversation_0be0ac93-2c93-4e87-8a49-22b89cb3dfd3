# Product Overview

## Multi-head BERTje for Dutch NLP

A production-ready Python project hosting a multi-head BERTje model for Dutch NLP tasks, featuring Named Entity Recognition (NER) and additional classification heads. The system is designed for on-premises deployment with CPU optimization and GDPR compliance capabilities.

### Core Features

- **Multi-task Architecture**: Supports NER, compliance, label, reason, and topic classification using a shared BERTje encoder
- **Dutch Language Focus**: Optimized for Dutch text processing using the pre-trained `wietsedv/bert-base-dutch-cased-finetuned-conll2002-ner` model
- **Production Ready**: FastAPI server, batch processing CLI, comprehensive configuration management
- **Cross-platform**: Linux/macOS/Windows support with Python 3.9-3.11
- **CPU Optimized**: Designed for efficient CPU inference without GPU requirements
- **GDPR Compliance**: Built-in compliance checking and redaction capabilities for Dutch privacy regulations

### Entity Types Supported

- **PER**: Person names (<PERSON>, <PERSON>)
- **LOC**: Locations (Amsterdam, Nederland) 
- **ORG**: Organizations (Google, Universiteit van Amsterdam)
- **MISC**: Miscellaneous entities (Dutch, Euro)

### Target Use Cases

- Dutch document processing and entity extraction
- GDPR/AVG compliance checking and redaction
- Multi-task Dutch NLP inference at scale
- On-premises deployment without cloud dependencies
- Research and development in Dutch language processing

### Performance Characteristics

- **NER Accuracy**: 90.24% F1 score on CoNLL-2002 Dutch NER dataset
- **CPU Friendly**: Optimized for CPU inference with quantization support
- **Scalable**: Supports batch processing and horizontal scaling
- **Memory Efficient**: Designed for production deployment constraints