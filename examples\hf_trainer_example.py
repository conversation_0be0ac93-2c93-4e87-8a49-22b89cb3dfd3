#!/usr/bin/env python3
"""
Example script demonstrating Hugging Face Trainer integration for RobBERT-2023 NER.

This script shows how to use the HFNERTrainer class and train_ner_model function
with various configuration options and demonstrates the complete training pipeline.
"""

import sys
import tempfile
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.training.hf_trainer import HFNERTrainer, train_ner_model
from src.training.hf_config import HFTrainingConfig, create_test_hf_config


def example_1_basic_training():
    """Example 1: Basic training with default configuration."""
    print("\n" + "="*60)
    print("Example 1: Basic Training with Default Configuration")
    print("="*60)
    
    # Use test data
    data_path = Path(__file__).parent.parent / "data_sets" / "test_ner_data.json"
    
    if not data_path.exists():
        print(f"❌ Test data not found: {data_path}")
        return
    
    # Create basic configuration
    config = create_test_hf_config()
    config.test_disable_wandb = True  # Disable WandB for example
    config.epochs = 1
    config.eval_steps = 2
    config.logging_steps = 1
    
    print(f"📊 Training data: {data_path}")
    print(f"⚙️  Configuration: {config.epochs} epochs, batch size {config.batch_size}")
    
    try:
        # Run training using convenience function
        results = train_ner_model(
            data_path=str(data_path),
            config=config
        )
        
        print("✅ Training completed!")
        print(f"📁 Model saved to: {results['output_dir']}")
        
        # Print metrics
        eval_result = results.get("eval_result", {})
        if eval_result:
            print("📈 Final metrics:")
            for metric, value in eval_result.items():
                if isinstance(value, (int, float)):
                    print(f"   - {metric}: {value:.4f}")
    
    except Exception as e:
        print(f"❌ Training failed: {e}")


def example_2_custom_configuration():
    """Example 2: Training with custom configuration."""
    print("\n" + "="*60)
    print("Example 2: Training with Custom Configuration")
    print("="*60)
    
    # Use test data
    data_path = Path(__file__).parent.parent / "data_sets" / "test_ner_data.json"
    
    if not data_path.exists():
        print(f"❌ Test data not found: {data_path}")
        return
    
    # Create custom configuration
    config = HFTrainingConfig(
        model_name="DTAI-KULeuven/robbert-2023-dutch-base",
        epochs=2,
        batch_size=4,
        learning_rate=3e-5,
        warmup_steps=100,
        eval_steps=5,
        logging_steps=2,
        save_steps=10,
        early_stopping_patience=2,
        test_mode=True,
        test_disable_wandb=True,
        run_version="v1.1",
        experiment_name="custom-example"
    )
    
    print(f"📊 Training data: {data_path}")
    print(f"⚙️  Custom configuration:")
    print(f"   - Model: {config.model_name}")
    print(f"   - Epochs: {config.epochs}")
    print(f"   - Batch size: {config.batch_size}")
    print(f"   - Learning rate: {config.learning_rate}")
    print(f"   - Early stopping patience: {config.early_stopping_patience}")
    
    try:
        # Create trainer instance for more control
        trainer = HFNERTrainer(config)
        
        # Run training
        results = trainer.train(data_path=str(data_path))
        
        print("✅ Training completed!")
        print(f"📁 Model saved to: {results['output_dir']}")
        
        # Print detailed results
        print("📈 Training results:")
        if "train_result" in results:
            train_result = results["train_result"]
            if hasattr(train_result, 'training_loss'):
                print(f"   - Final training loss: {train_result.training_loss:.4f}")
        
        eval_result = results.get("eval_result", {})
        if eval_result:
            print("📊 Evaluation metrics:")
            for metric, value in eval_result.items():
                if isinstance(value, (int, float)):
                    print(f"   - {metric}: {value:.4f}")
    
    except Exception as e:
        print(f"❌ Training failed: {e}")


def example_3_yaml_configuration():
    """Example 3: Training with YAML configuration file."""
    print("\n" + "="*60)
    print("Example 3: Training with YAML Configuration")
    print("="*60)
    
    # Use test data
    data_path = Path(__file__).parent.parent / "data_sets" / "test_ner_data.json"
    config_path = Path(__file__).parent / "hf_trainer_example.yaml"
    
    if not data_path.exists():
        print(f"❌ Test data not found: {data_path}")
        return
    
    if not config_path.exists():
        print(f"❌ Config file not found: {config_path}")
        return
    
    print(f"📊 Training data: {data_path}")
    print(f"⚙️  Configuration file: {config_path}")
    
    try:
        # Load configuration from YAML
        config = HFTrainingConfig.from_yaml(str(config_path))
        
        # Override for example (disable WandB, reduce epochs)
        config.test_mode = True
        config.test_disable_wandb = True
        config.epochs = 1
        config.eval_steps = 2
        config.logging_steps = 1
        
        print(f"📋 Loaded configuration:")
        print(f"   - Model: {config.model_name}")
        print(f"   - Epochs: {config.epochs} (overridden for example)")
        print(f"   - Batch size: {config.batch_size}")
        print(f"   - Learning rate: {config.learning_rate}")
        print(f"   - WandB project: {config.wandb_project}")
        
        # Run training
        results = train_ner_model(
            data_path=str(data_path),
            config=config
        )
        
        print("✅ Training completed!")
        print(f"📁 Model saved to: {results['output_dir']}")
        
        # Print metrics
        eval_result = results.get("eval_result", {})
        if eval_result:
            print("📈 Final metrics:")
            for metric, value in eval_result.items():
                if isinstance(value, (int, float)):
                    print(f"   - {metric}: {value:.4f}")
    
    except Exception as e:
        print(f"❌ Training failed: {e}")


def example_4_cli_usage():
    """Example 4: Demonstrate CLI usage."""
    print("\n" + "="*60)
    print("Example 4: CLI Usage Examples")
    print("="*60)
    
    data_path = Path(__file__).parent.parent / "data_sets" / "test_ner_data.json"
    config_path = Path(__file__).parent / "hf_trainer_example.yaml"
    
    print("🖥️  Command-line usage examples:")
    print()
    
    print("1. Basic training with test mode:")
    print(f"   python src/training/train_hf_ner.py {data_path} --test-mode --no-wandb")
    print()
    
    print("2. Training with custom parameters:")
    print(f"   python src/training/train_hf_ner.py {data_path} \\")
    print("      --epochs 3 --batch-size 16 --learning-rate 2e-5 --no-wandb")
    print()
    
    print("3. Training with configuration file:")
    print(f"   python src/training/train_hf_ner.py {data_path} --config {config_path} --no-wandb")
    print()
    
    print("4. Training with WandB (requires setup):")
    print(f"   python src/training/train_hf_ner.py {data_path} \\")
    print("      --wandb-project my-project --wandb-entity my-entity --run-name my-run")
    print()
    
    print("5. Resume from checkpoint:")
    print(f"   python src/training/train_hf_ner.py {data_path} \\")
    print("      --resume-from-checkpoint checkpoints/my-checkpoint/")
    print()


def main():
    """Run all examples."""
    print("🚀 Hugging Face Trainer Integration Examples")
    print("=" * 60)
    print("This script demonstrates various ways to use the HF Trainer integration")
    print("for RobBERT-2023 NER training.")
    
    try:
        # Run examples
        example_1_basic_training()
        example_2_custom_configuration()
        example_3_yaml_configuration()
        example_4_cli_usage()
        
        print("\n" + "="*60)
        print("🎉 All examples completed!")
        print("="*60)
        print()
        print("Key features demonstrated:")
        print("✅ Basic training with default configuration")
        print("✅ Custom configuration with HFTrainingConfig")
        print("✅ YAML configuration file loading")
        print("✅ CLI usage patterns")
        print("✅ WandB integration (disabled for examples)")
        print("✅ Early stopping and evaluation metrics")
        print("✅ Automatic checkpointing and model saving")
        print("✅ Mixed precision training support")
        print("✅ DataCollatorForTokenClassification usage")
        print()
        print("For production use:")
        print("- Enable WandB by setting appropriate project/entity")
        print("- Use larger datasets and more epochs")
        print("- Configure GPU settings appropriately")
        print("- Set up proper validation splits")
        
    except KeyboardInterrupt:
        print("\n⏹️  Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Examples failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()