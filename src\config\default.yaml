model:
  model_name: "${ROBBERT_MODEL_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}"
  tokenizer_name: "${ROBBERT_TOKENIZER_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}"
  checkpoint_dir: "${ROBBERT_CHECKPOINT_DIR:-models/robbert2023-per}"  # RobBERT checkpoint directory
  max_length: 512
  tokenizer_type: "byte_level_bpe"
  num_labels:
    ner: 3  # O, B-PER, I-PER (Person entities only)

training:
  epochs: 3
  batch_size: 8
  learning_rate: 2.0e-5
  warmup_steps: 500
  weight_decay: 0.01
  gradient_accumulation_steps: 1
  max_grad_norm: 1.0

  loss_weights:
    ner: 1.0  # Primary focus on Person entity recognition

inference:
  batch_size: 16
  thresholds:
    ner: 0.50  # Confidence threshold for Person entity recognition

  api:
    host: "0.0.0.0"
    port: 8000
    workers: 1

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"