"""
Tests for class weight calculation functionality.

This module tests the ClassWeightCalculator and related utilities for
computing balanced class weights for imbalanced NER datasets.
"""

import pytest
import json
import tempfile
from pathlib import Path
from collections import Counter
from unittest.mock import Mock, patch

import numpy as np
import torch
from datasets import Dataset, DatasetDict

from src.training.class_weights import (
    ClassWeightCalculator,
    compute_class_weights,
    compute_per_head_class_weights,
    analyze_dataset_imbalance
)
from src.training.hf_config import HFTrainingConfig
from src.exceptions import DataProcessingError


@pytest.fixture
def sample_dataset():
    """Create sample imbalanced NER dataset for testing."""
    # Create imbalanced dataset with more O labels and fewer entity labels
    examples = [
        # Example 1: Only O labels
        {
            'input_ids': [0, 1, 2, 3, 4],
            'attention_mask': [1, 1, 1, 1, 1],
            'labels': [0, 0, 0, 0, 0]  # All O
        },
        # Example 2: PER entity
        {
            'input_ids': [0, 5, 6, 7, 8],
            'attention_mask': [1, 1, 1, 1, 1],
            'labels': [0, 1, 2, 0, 0]  # O, B-PER, I-PER, O, O
        },
        # Example 3: LOC entity
        {
            'input_ids': [0, 9, 10, 11, 12],
            'attention_mask': [1, 1, 1, 1, 1],
            'labels': [0, 3, 4, 0, 0]  # O, B-LOC, I-LOC, O, O
        },
        # Example 4: Multiple O labels (imbalanced)
        {
            'input_ids': [0, 13, 14, 15, 16],
            'attention_mask': [1, 1, 1, 1, 1],
            'labels': [0, 0, 0, 0, 0]  # All O
        },
        # Example 5: PER entity again
        {
            'input_ids': [0, 17, 18, 19, 20],
            'attention_mask': [1, 1, 1, 1, 1],
            'labels': [0, 1, 2, 0, 0]  # O, B-PER, I-PER, O, O
        }
    ]
    
    return Dataset.from_list(examples)


@pytest.fixture
def sample_dataset_dict(sample_dataset):
    """Create DatasetDict from sample dataset."""
    # Split dataset for train/validation
    split_dataset = sample_dataset.train_test_split(test_size=0.4, seed=42)
    return DatasetDict({
        'train': split_dataset['train'],
        'validation': split_dataset['test']
    })


@pytest.fixture
def label_list():
    """Standard BIO label list for testing."""
    return ["O", "B-PER", "I-PER", "B-LOC", "I-LOC"]


@pytest.fixture
def class_weight_calculator():
    """Create ClassWeightCalculator instance."""
    return ClassWeightCalculator()


class TestClassWeightCalculator:
    """Test ClassWeightCalculator functionality."""
    
    def test_init(self, class_weight_calculator):
        """Test calculator initialization."""
        assert class_weight_calculator is not None
        assert hasattr(class_weight_calculator, 'logger')
    
    def test_compute_class_weights_basic(self, class_weight_calculator, sample_dataset, label_list):
        """Test basic class weight computation."""
        weights = class_weight_calculator.compute_class_weights(
            dataset=sample_dataset,
            label_list=label_list,
            method="balanced"
        )
        
        # Check that weights are computed for all labels
        assert isinstance(weights, dict)
        assert len(weights) == len(label_list)
        
        # Check that all weights are positive
        for label, weight in weights.items():
            assert weight > 0
            assert isinstance(weight, float)
        
        # O label should have lower weight (more frequent)
        # Entity labels should have higher weights (less frequent)
        assert weights["O"] < weights["B-PER"]
        assert weights["O"] < weights["I-PER"]
    
    def test_compute_class_weights_with_dataset_dict(self, class_weight_calculator, sample_dataset_dict, label_list):
        """Test class weight computation with DatasetDict."""
        weights = class_weight_calculator.compute_class_weights(
            dataset=sample_dataset_dict,
            label_list=label_list,
            method="balanced"
        )
        
        assert isinstance(weights, dict)
        assert len(weights) == len(label_list)
        
        # All weights should be positive
        for weight in weights.values():
            assert weight > 0
    
    def test_compute_class_weights_per_entity_type(self, class_weight_calculator, sample_dataset, label_list):
        """Test class weight computation for specific entity type."""
        # Test PER entity type
        per_weights = class_weight_calculator.compute_class_weights(
            dataset=sample_dataset,
            label_list=label_list,
            method="balanced",
            entity_type="PER"
        )
        
        # Should have 3 labels for PER head: O, B-PER, I-PER
        expected_labels = ["O", "B-PER", "I-PER"]
        assert len(per_weights) == len(expected_labels)
        
        for label in expected_labels:
            assert label in per_weights
            assert per_weights[label] > 0
    
    def test_compute_per_head_class_weights(self, class_weight_calculator, sample_dataset, label_list):
        """Test per-head class weight computation."""
        per_head_weights = class_weight_calculator.compute_per_head_class_weights(
            dataset=sample_dataset,
            label_list=label_list,
            method="balanced"
        )
        
        # Should have weights for PER and LOC entity types
        assert isinstance(per_head_weights, dict)
        assert "PER" in per_head_weights
        assert "LOC" in per_head_weights
        
        # Each head should have 3 labels
        for entity_type, weights in per_head_weights.items():
            assert len(weights) == 3  # O, B-ENTITY, I-ENTITY
            assert "O" in weights
            assert f"B-{entity_type}" in weights
            assert f"I-{entity_type}" in weights
    
    def test_compute_per_head_class_weights_with_specified_types(self, class_weight_calculator, sample_dataset, label_list):
        """Test per-head computation with specified entity types."""
        per_head_weights = class_weight_calculator.compute_per_head_class_weights(
            dataset=sample_dataset,
            label_list=label_list,
            entity_types=["PER"],  # Only PER
            method="balanced"
        )
        
        # Should only have PER weights
        assert len(per_head_weights) == 1
        assert "PER" in per_head_weights
        assert "LOC" not in per_head_weights
    
    def test_extract_labels_from_dataset(self, class_weight_calculator, sample_dataset):
        """Test label extraction from dataset."""
        labels = class_weight_calculator._extract_labels_from_dataset(sample_dataset)
        
        assert isinstance(labels, list)
        assert len(labels) > 0
        
        # Should contain all labels from all examples
        expected_total = sum(len(example['labels']) for example in sample_dataset)
        assert len(labels) == expected_total
        
        # Should not contain negative labels (special tokens)
        assert all(label >= 0 for label in labels)
    
    def test_filter_labels_for_entity_type(self, class_weight_calculator, label_list):
        """Test label filtering for specific entity type."""
        # Sample labels: [0, 1, 2, 3, 4] -> ["O", "B-PER", "I-PER", "B-LOC", "I-LOC"]
        all_labels = [0, 1, 2, 3, 4, 0, 1, 2]
        
        filtered_labels, filtered_label_list = class_weight_calculator._filter_labels_for_entity_type(
            all_labels, label_list, "PER"
        )
        
        # Should map to 3-label scheme: O, B-PER, I-PER
        assert filtered_label_list == ["O", "B-PER", "I-PER"]
        
        # Original labels should be mapped correctly
        # 0 -> 0 (O), 1 -> 1 (B-PER), 2 -> 2 (I-PER), 3 -> 0 (B-LOC->O), 4 -> 0 (I-LOC->O)
        expected_mapping = [0, 1, 2, 0, 0, 0, 1, 2]
        assert filtered_labels == expected_mapping
    
    def test_extract_entity_types_from_labels(self, class_weight_calculator, label_list):
        """Test entity type extraction from label list."""
        entity_types = class_weight_calculator._extract_entity_types_from_labels(label_list)
        
        assert isinstance(entity_types, list)
        assert "PER" in entity_types
        assert "LOC" in entity_types
        assert len(entity_types) == 2
    
    def test_analyze_label_distribution(self, class_weight_calculator, sample_dataset, label_list):
        """Test label distribution analysis."""
        analysis = class_weight_calculator.analyze_label_distribution(
            dataset=sample_dataset,
            label_list=label_list
        )
        
        # Check required fields
        required_fields = [
            'total_labels', 'unique_labels', 'label_counts', 'label_percentages',
            'imbalance_ratio', 'most_common_label', 'least_common_label'
        ]
        
        for field in required_fields:
            assert field in analysis
        
        # Check data types
        assert isinstance(analysis['total_labels'], int)
        assert isinstance(analysis['unique_labels'], int)
        assert isinstance(analysis['label_counts'], dict)
        assert isinstance(analysis['label_percentages'], dict)
        assert isinstance(analysis['imbalance_ratio'], (int, float))
        
        # Check that percentages sum to ~100%
        total_percentage = sum(analysis['label_percentages'].values())
        assert abs(total_percentage - 100.0) < 0.01
    
    def test_analyze_label_distribution_per_entity_type(self, class_weight_calculator, sample_dataset, label_list):
        """Test label distribution analysis for specific entity type."""
        analysis = class_weight_calculator.analyze_label_distribution(
            dataset=sample_dataset,
            label_list=label_list,
            entity_type="PER"
        )
        
        # Should have entity_type_filter field
        assert analysis['entity_type_filter'] == "PER"
        
        # Should only have 3 unique labels for PER head
        assert analysis['unique_labels'] <= 3
    
    def test_create_class_weight_tensor(self, class_weight_calculator, label_list):
        """Test PyTorch tensor creation from class weights."""
        class_weights = {
            "O": 0.5,
            "B-PER": 2.0,
            "I-PER": 2.0,
            "B-LOC": 3.0,
            "I-LOC": 3.0
        }
        
        tensor = class_weight_calculator.create_class_weight_tensor(
            class_weights, label_list
        )
        
        assert isinstance(tensor, torch.Tensor)
        assert tensor.shape == (len(label_list),)
        assert tensor.dtype == torch.float32
        
        # Check values match
        expected_values = [0.5, 2.0, 2.0, 3.0, 3.0]
        assert torch.allclose(tensor, torch.tensor(expected_values))
    
    def test_create_class_weight_tensor_with_device(self, class_weight_calculator, label_list):
        """Test tensor creation with specific device."""
        class_weights = {"O": 1.0, "B-PER": 2.0, "I-PER": 2.0, "B-LOC": 3.0, "I-LOC": 3.0}
        device = torch.device('cpu')
        
        tensor = class_weight_calculator.create_class_weight_tensor(
            class_weights, label_list, device
        )
        
        assert tensor.device == device
    
    def test_save_and_load_class_weights(self, class_weight_calculator):
        """Test saving and loading class weights."""
        class_weights = {
            "O": 0.5,
            "B-PER": 2.0,
            "I-PER": 2.0
        }
        
        metadata = {
            "method": "balanced",
            "entity_type": "PER"
        }
        
        with tempfile.TemporaryDirectory() as temp_dir:
            weights_path = Path(temp_dir) / "test_weights.json"
            
            # Save weights
            class_weight_calculator.save_class_weights(
                class_weights, weights_path, metadata
            )
            
            assert weights_path.exists()
            
            # Load weights
            loaded_data = class_weight_calculator.load_class_weights(weights_path)
            
            assert "class_weights" in loaded_data
            assert "metadata" in loaded_data
            assert loaded_data["class_weights"] == class_weights
            assert loaded_data["metadata"]["method"] == "balanced"
    
    def test_invalid_method_raises_error(self, class_weight_calculator, sample_dataset, label_list):
        """Test that invalid weight computation method raises error."""
        with pytest.raises(ValueError, match="Unsupported weight computation method"):
            class_weight_calculator.compute_class_weights(
                dataset=sample_dataset,
                label_list=label_list,
                method="invalid_method"
            )
    
    def test_empty_dataset_handling(self, class_weight_calculator, label_list):
        """Test handling of empty dataset."""
        empty_dataset = Dataset.from_list([])
        
        with pytest.raises(DataProcessingError):
            class_weight_calculator.compute_class_weights(
                dataset=empty_dataset,
                label_list=label_list,
                method="balanced"
            )


class TestConvenienceFunctions:
    """Test convenience functions for class weight computation."""
    
    def test_compute_class_weights_function(self, sample_dataset, label_list):
        """Test compute_class_weights convenience function."""
        weights = compute_class_weights(
            dataset=sample_dataset,
            label_list=label_list,
            method="balanced"
        )
        
        assert isinstance(weights, dict)
        assert len(weights) == len(label_list)
    
    def test_compute_per_head_class_weights_function(self, sample_dataset, label_list):
        """Test compute_per_head_class_weights convenience function."""
        per_head_weights = compute_per_head_class_weights(
            dataset=sample_dataset,
            label_list=label_list,
            method="balanced"
        )
        
        assert isinstance(per_head_weights, dict)
        assert "PER" in per_head_weights
        assert "LOC" in per_head_weights
    
    def test_analyze_dataset_imbalance_function(self, sample_dataset, label_list):
        """Test analyze_dataset_imbalance convenience function."""
        analysis = analyze_dataset_imbalance(
            dataset=sample_dataset,
            label_list=label_list
        )
        
        assert isinstance(analysis, dict)
        assert "total_labels" in analysis
        assert "imbalance_ratio" in analysis


class TestHFTrainingConfigIntegration:
    """Test integration with HFTrainingConfig."""
    
    def test_class_weight_config_fields(self):
        """Test that HFTrainingConfig has class weight fields."""
        config = HFTrainingConfig()
        
        # Check default values
        assert hasattr(config, 'use_class_weights')
        assert hasattr(config, 'class_weights')
        assert hasattr(config, 'class_weight_method')
        assert hasattr(config, 'per_head_class_weights')
        assert hasattr(config, 'class_weight_entity_types')
        assert hasattr(config, 'save_class_weights')
        
        # Check defaults
        assert config.use_class_weights is False
        assert config.class_weights is None
        assert config.class_weight_method == "balanced"
        assert config.per_head_class_weights is False
        assert config.class_weight_entity_types is None
        assert config.save_class_weights is True
    
    def test_class_weight_method_validation(self):
        """Test validation of class weight method."""
        # Valid method should work
        config = HFTrainingConfig(class_weight_method="balanced")
        assert config.class_weight_method == "balanced"
        
        # Invalid method should raise error
        with pytest.raises(ValueError, match="Invalid class_weight_method"):
            HFTrainingConfig(class_weight_method="invalid_method")
    
    @patch('src.training.class_weights.ClassWeightCalculator')
    def test_compute_and_set_class_weights(self, mock_calculator_class, sample_dataset, label_list):
        """Test compute_and_set_class_weights method."""
        # Setup mock
        mock_calculator = Mock()
        mock_calculator.compute_class_weights.return_value = {"O": 0.5, "B-PER": 2.0}
        mock_calculator_class.return_value = mock_calculator
        
        config = HFTrainingConfig(use_class_weights=True)
        
        weights = config.compute_and_set_class_weights(
            dataset=sample_dataset,
            label_list=label_list
        )
        
        # Check that weights were computed and set
        assert weights is not None
        assert config.class_weights == {"O": 0.5, "B-PER": 2.0}
        
        # Check that calculator was called correctly
        mock_calculator.compute_class_weights.assert_called_once_with(
            dataset=sample_dataset,
            label_list=label_list,
            method="balanced",
            entity_type=None
        )
    
    @patch('src.training.class_weights.ClassWeightCalculator')
    def test_compute_per_head_class_weights_method(self, mock_calculator_class, sample_dataset, label_list):
        """Test compute_per_head_class_weights method."""
        # Setup mock
        mock_calculator = Mock()
        mock_calculator.compute_per_head_class_weights.return_value = {
            "PER": {"O": 0.5, "B-PER": 2.0, "I-PER": 2.0}
        }
        mock_calculator_class.return_value = mock_calculator
        
        config = HFTrainingConfig(
            use_class_weights=True,
            per_head_class_weights=True
        )
        
        per_head_weights = config.compute_per_head_class_weights(
            dataset=sample_dataset,
            label_list=label_list
        )
        
        # Check that weights were computed
        assert per_head_weights is not None
        assert "PER" in per_head_weights
        
        # Check that calculator was called correctly
        mock_calculator.compute_per_head_class_weights.assert_called_once_with(
            dataset=sample_dataset,
            label_list=label_list,
            entity_types=None,
            method="balanced"
        )
    
    def test_disabled_class_weights(self, sample_dataset, label_list):
        """Test that disabled class weights return None."""
        config = HFTrainingConfig(use_class_weights=False)
        
        weights = config.compute_and_set_class_weights(
            dataset=sample_dataset,
            label_list=label_list
        )
        
        assert weights is None
        assert config.class_weights is None


class TestEdgeCases:
    """Test edge cases and error conditions."""
    
    def test_dataset_with_special_tokens(self, class_weight_calculator, label_list):
        """Test handling of dataset with special tokens (-100)."""
        examples = [
            {
                'input_ids': [0, 1, 2, 3],
                'attention_mask': [1, 1, 1, 1],
                'labels': [-100, 0, 1, -100]  # Special tokens should be ignored
            }
        ]
        
        dataset = Dataset.from_list(examples)
        
        weights = class_weight_calculator.compute_class_weights(
            dataset=dataset,
            label_list=label_list,
            method="balanced"
        )
        
        # Should work without errors
        assert isinstance(weights, dict)
    
    def test_single_label_dataset(self, class_weight_calculator):
        """Test dataset with only one label type."""
        examples = [
            {
                'input_ids': [0, 1, 2],
                'attention_mask': [1, 1, 1],
                'labels': [0, 0, 0]  # Only O labels
            }
        ]
        
        dataset = Dataset.from_list(examples)
        label_list = ["O"]
        
        weights = class_weight_calculator.compute_class_weights(
            dataset=dataset,
            label_list=label_list,
            method="balanced"
        )
        
        # Should handle single label case
        assert len(weights) == 1
        assert "O" in weights
    
    def test_missing_entity_type(self, class_weight_calculator, sample_dataset, label_list):
        """Test filtering for non-existent entity type."""
        # This should not crash but may return empty or default weights
        weights = class_weight_calculator.compute_class_weights(
            dataset=sample_dataset,
            label_list=label_list,
            method="balanced",
            entity_type="NONEXISTENT"
        )
        
        # Should return weights for the 3-label scheme even if no entities found
        assert isinstance(weights, dict)
        assert len(weights) == 3  # O, B-NONEXISTENT, I-NONEXISTENT