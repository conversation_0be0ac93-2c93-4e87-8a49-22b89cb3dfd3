#!/usr/bin/env python3
"""
Multi-task training script for BERTje model with compound name focus.
"""

import argparse
import os
import json
from datetime import datetime
from pathlib import Path
import torch
from torch.utils.data import DataLoader, Dataset
from transformers import get_linear_schedule_with_warmup
import logging
from sklearn.metrics import classification_report, f1_score
import numpy as np
from typing import List, Dict, Tuple

from ..models.multitask_bertje import MultiTaskBERTje
from ..data.dataset_builder import build_redaction_dataset
from ..config import load_config


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def train_multitask_model(args):
    """Train multi-task BERTje model."""
    logger = setup_logging()
    
    # Load configuration
    config = load_config(args.config)
    
    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Load model
    model = MultiTaskBERTje.from_pretrained()
    model.to(device)
    
    # Load dataset if provided
    if args.data:
        dataset = build_redaction_dataset(args.data)
        train_dataloader = DataLoader(
            dataset['train'], 
            batch_size=args.batch_size,
            shuffle=True
        )
        val_dataloader = DataLoader(
            dataset['validation'],
            batch_size=args.batch_size
        )
    else:
        logger.warning("No dataset provided, using dummy training loop")
        train_dataloader = None
        val_dataloader = None
    
    # Setup optimizer and scheduler
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=args.learning_rate,
        weight_decay=0.01
    )
    
    if train_dataloader:
        total_steps = len(train_dataloader) * args.epochs
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=500,
            num_training_steps=total_steps
        )
    
    # Training loop
    model.train()
    for epoch in range(args.epochs):
        logger.info(f"Epoch {epoch + 1}/{args.epochs}")
        
        if train_dataloader:
            total_loss = 0
            for batch_idx, batch in enumerate(train_dataloader):
                # Move batch to device
                input_ids = batch['input_ids'].to(device)
                attention_mask = batch['attention_mask'].to(device)
                
                # Forward pass
                outputs = model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    heads=args.heads
                )
                
                loss = outputs.get('loss', torch.tensor(0.0))
                total_loss += loss.item()
                
                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()
                scheduler.step()
                
                if batch_idx % 100 == 0:
                    logger.info(f"Batch {batch_idx}, Loss: {loss.item():.4f}")
            
            avg_loss = total_loss / len(train_dataloader)
            logger.info(f"Average training loss: {avg_loss:.4f}")
        else:
            logger.info("Dummy training step completed")
    
    # Save model
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path(args.output_dir) / f"checkpoint_{timestamp}"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    model.save_pretrained(str(output_dir))
    logger.info(f"Model saved to {output_dir}")
    
    # Save training info
    training_info = {
        'heads': args.heads,
        'epochs': args.epochs,
        'batch_size': args.batch_size,
        'learning_rate': args.learning_rate,
        'timestamp': timestamp
    }
    
    with open(output_dir / "training_info.json", 'w') as f:
        json.dump(training_info, f, indent=2)


def main():
    parser = argparse.ArgumentParser(description="Train multi-task BERTje model")
    parser.add_argument('--heads', nargs='+', default=['ner'], 
                       help='Heads to train (default: ner)')
    parser.add_argument('--epochs', type=int, default=3,
                       help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=8,
                       help='Training batch size')
    parser.add_argument('--learning-rate', type=float, default=2e-5,
                       help='Learning rate')
    parser.add_argument('--output-dir', default='src/models/checkpoints',
                       help='Output directory for saved models')
    parser.add_argument('--config', default='src/config/default.yaml',
                       help='Configuration file path')
    parser.add_argument('--data', help='Path to training data (JSONL)')
    
    args = parser.parse_args()
    train_multitask_model(args)


if __name__ == '__main__':
    main()