#!/usr/bin/env python3
"""
Multi-task training script for RobBERT-2023 model with compound name focus.
"""

import argparse
import os
import json
from datetime import datetime
from pathlib import Path
import torch
from torch.utils.data import DataLoader, Dataset
from transformers import get_linear_schedule_with_warmup
import logging
from sklearn.metrics import classification_report, f1_score
import numpy as np
from typing import List, Dict, Tuple

from ..models.multitask_robbert import MultiTaskRobBERT
from ..data.dataset_builder import build_redaction_dataset
from ..data.tokenizer_utils import RobBERTTokenizerWithAlignment
from ..config import load_config, load_wandb_config
from ..utils.checkpoint_manager import CheckpointManager, save_robbert_checkpoint
from ..utils.wandb_logger import create_wandb_logger
from ..utils.head_config_loader import load_head_configs, HeadConfigLoader


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def build_robbert_dataset(jsonl_path: str, tokenizer: RobBERTTokenizerWithAlignment, config, train_split: float = 0.8):
    """
    Build dataset from JSONL file for RobBERT-2023 multi-task training.
    
    Args:
        jsonl_path: Path to JSONL file
        tokenizer: RobBERT tokenizer with alignment
        config: Configuration object
        train_split: Fraction for training split
        
    Returns:
        DatasetDict with train/validation splits
    """
    from datasets import Dataset, DatasetDict
    
    # Load data
    data = []
    with open(jsonl_path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line.strip()))
    
    # Validate and preprocess
    processed_data = []
    max_length = config.model.max_length
    
    for item in data:
        if 'text' not in item:
            continue
            
        processed_item = {
            'text': item['text'],
        }
        
        # Add task labels if available
        for task in ['ner', 'compliance', 'label', 'reason', 'topic']:
            if task in item:
                processed_item[f'{task}_labels'] = item[task]
        
        processed_data.append(processed_item)
    
    # Create dataset
    dataset = Dataset.from_list(processed_data)
    
    # Split into train/validation
    split_idx = int(len(dataset) * train_split)
    train_dataset = dataset.select(range(split_idx))
    val_dataset = dataset.select(range(split_idx, len(dataset)))
    
    return DatasetDict({
        'train': train_dataset,
        'validation': val_dataset
    })


def collate_robbert_batch(batch: List[Dict], tokenizer: RobBERTTokenizerWithAlignment, device: torch.device):
    """
    Collate function for RobBERT training batches with proper tokenization and label alignment.
    
    Args:
        batch: List of data samples
        tokenizer: RobBERT tokenizer with alignment
        device: Target device for tensors
        
    Returns:
        Collated batch dictionary
    """
    texts = [item['text'] for item in batch]
    batch_size = len(texts)
    max_length = 512
    
    # Initialize batch tensors
    input_ids = torch.zeros(batch_size, max_length, dtype=torch.long)
    attention_mask = torch.zeros(batch_size, max_length, dtype=torch.long)
    
    # Initialize label tensors for different tasks
    ner_labels = torch.full((batch_size, max_length), -100, dtype=torch.long)  # -100 is ignore index
    compliance_labels = torch.zeros(batch_size, dtype=torch.long)
    label_labels = torch.zeros(batch_size, dtype=torch.long)
    reason_labels = torch.zeros(batch_size, dtype=torch.long)
    topic_labels = torch.zeros(batch_size, dtype=torch.long)
    
    for i, (text, item) in enumerate(zip(texts, batch)):
        # Tokenize text
        encoding = tokenizer.tokenizer(
            text,
            max_length=max_length,
            truncation=True,
            padding='max_length',
            return_tensors='pt'
        )
        
        input_ids[i] = encoding['input_ids'].squeeze()
        attention_mask[i] = encoding['attention_mask'].squeeze()
        
        # Handle NER labels with alignment if available
        if 'ner_labels' in item:
            # Assume NER labels are word-level, need to align to tokens
            words = text.split()  # Simple word splitting
            word_labels = item['ner_labels']
            
            if len(words) == len(word_labels):
                try:
                    alignment = tokenizer.tokenize_with_alignment(words, word_labels, max_length)
                    # Convert aligned labels to IDs
                    for j, label in enumerate(alignment.token_labels):
                        if j < max_length:
                            label_id = tokenizer.label2id.get(label, 0)
                            # Only set labels for non-padding tokens
                            if attention_mask[i][j] == 1:
                                ner_labels[i][j] = label_id
                except Exception as e:
                    # If alignment fails, skip NER labels for this sample
                    pass
        
        # Handle other task labels (sequence-level)
        if 'compliance_labels' in item:
            compliance_labels[i] = item['compliance_labels']
        if 'label_labels' in item:
            label_labels[i] = item['label_labels']
        if 'reason_labels' in item:
            reason_labels[i] = item['reason_labels']
        if 'topic_labels' in item:
            topic_labels[i] = item['topic_labels']
    
    # Move to device
    collated_batch = {
        'input_ids': input_ids.to(device),
        'attention_mask': attention_mask.to(device),
        'ner_labels': ner_labels.to(device),
        'compliance_labels': compliance_labels.to(device),
        'label_labels': label_labels.to(device),
        'reason_labels': reason_labels.to(device),
        'topic_labels': topic_labels.to(device),
    }
    
    return collated_batch


def train_multitask_model(args):
    """Train multi-task RobBERT-2023 model."""
    logger = setup_logging()
    
    # Load configuration
    config = load_config(args.config)
    
    # Load head-specific configurations
    head_configs = {}
    if hasattr(args, 'use_head_configs') and args.use_head_configs:
        try:
            head_configs = load_head_configs(args.heads)
            logger.info(f"Loaded head-specific configs for: {list(head_configs.keys())}")
            
            # Override training parameters with head-specific values if available
            if head_configs:
                head_config_loader = HeadConfigLoader()
                merged_config = head_config_loader.merge_training_configs(head_configs)
                
                # Update args with merged global training parameters
                global_training = merged_config.get('global_training', {})
                if not hasattr(args, 'epochs_override'):
                    args.epochs = global_training.get('epochs', args.epochs)
                if not hasattr(args, 'batch_size_override'):
                    args.batch_size = global_training.get('batch_size', args.batch_size)
                if not hasattr(args, 'learning_rate_override'):
                    args.learning_rate = global_training.get('learning_rate', args.learning_rate)
                
                logger.info(f"Updated training params - epochs: {args.epochs}, "
                          f"batch_size: {args.batch_size}, lr: {args.learning_rate}")
        except Exception as e:
            logger.warning(f"Failed to load head-specific configs: {e}")
            head_configs = {}
    
    # Load WandB configuration
    wandb_config = None
    wandb_logger = None
    if hasattr(args, 'wandb_config') and args.wandb_config:
        try:
            wandb_config_data = load_wandb_config(args.wandb_config)
            if wandb_config_data:
                # Store config dict in args for wandb logger
                args.config_dict = config.model_dump()
                args.head_configs = head_configs  # Pass head configs to wandb logger
                wandb_logger = create_wandb_logger(
                    wandb_config_data.model_dump(), 
                    args.heads, 
                    args
                )
                logger.info("Initialized WandB logging")
        except Exception as e:
            logger.warning(f"Failed to initialize WandB: {e}")
            wandb_logger = None
    
    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Initialize RobBERT tokenizer
    model_name = config.model.model_name
    tokenizer = RobBERTTokenizerWithAlignment(model_name)
    logger.info(f"Initialized RobBERT tokenizer: {model_name}")
    
    # Load model with RobBERT-2023
    model = MultiTaskRobBERT.from_pretrained()
    model.to(device)
    logger.info("Loaded RobBERT-2023 multi-task model")
    
    # Setup gradient logging if enabled
    if wandb_logger:
        wandb_logger.log_gradients(model)
    
    # Load dataset if provided
    if args.data:
        dataset = build_robbert_dataset(args.data, tokenizer, config)
        train_dataloader = DataLoader(
            dataset['train'], 
            batch_size=args.batch_size,
            shuffle=True,
            collate_fn=lambda batch: collate_robbert_batch(batch, tokenizer, device)
        )
        val_dataloader = DataLoader(
            dataset['validation'],
            batch_size=args.batch_size,
            collate_fn=lambda batch: collate_robbert_batch(batch, tokenizer, device)
        )
        logger.info(f"Loaded dataset: {len(dataset['train'])} train, {len(dataset['validation'])} val samples")
    else:
        logger.warning("No dataset provided, using dummy training loop")
        train_dataloader = None
        val_dataloader = None
    
    # Setup optimizer and scheduler with config values
    training_config = config.training
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=args.learning_rate,
        weight_decay=training_config.weight_decay
    )
    
    if train_dataloader:
        total_steps = len(train_dataloader) * args.epochs
        warmup_steps = training_config.warmup_steps
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=warmup_steps,
            num_training_steps=total_steps
        )
        logger.info(f"Training for {total_steps} steps with {warmup_steps} warmup steps")
    
    # Get loss weights from config
    loss_weights = training_config.loss_weights
    max_grad_norm = training_config.max_grad_norm
    
    # Training loop
    model.train()
    global_step = 0
    
    for epoch in range(args.epochs):
        logger.info(f"Epoch {epoch + 1}/{args.epochs}")
        
        if train_dataloader:
            total_loss = 0
            head_losses = {head: 0.0 for head in args.heads}
            epoch_metrics = {}
            
            for batch_idx, batch in enumerate(train_dataloader):
                global_step += 1
                # Prepare labels for active heads
                labels = {}
                for head in args.heads:
                    if f'{head}_labels' in batch:
                        labels[head] = batch[f'{head}_labels']
                
                # Forward pass with labels
                outputs = model(
                    input_ids=batch['input_ids'],
                    attention_mask=batch['attention_mask'],
                    heads=args.heads,
                    labels=labels if labels else None
                )
                
                # Calculate weighted loss
                total_batch_loss = 0.0
                for head in args.heads:
                    if head in outputs and 'loss' in outputs[head]:
                        head_loss = outputs[head]['loss']
                        weight = loss_weights.get(head, 1.0)
                        weighted_loss = head_loss * weight
                        total_batch_loss += weighted_loss
                        head_losses[head] += head_loss.item()
                
                # Use total loss if available, otherwise use calculated loss
                loss = outputs.get('loss', total_batch_loss)
                if loss.item() == 0.0 and total_batch_loss > 0.0:
                    loss = total_batch_loss
                
                total_loss += loss.item()
                
                # Backward pass
                optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
                optimizer.step()
                scheduler.step()
                
                # Log to WandB
                if wandb_logger and global_step % wandb_logger.config.get('log_freq', 100) == 0:
                    step_metrics = {
                        'train_loss': loss.item(),
                        'learning_rate': scheduler.get_last_lr()[0],
                        'epoch': epoch,
                        'global_step': global_step
                    }
                    
                    # Add head-specific losses
                    for head in args.heads:
                        if head in outputs and 'loss' in outputs[head]:
                            step_metrics[f'train_{head}_loss'] = outputs[head]['loss'].item()
                    
                    wandb_logger.log_metrics(step_metrics, step=global_step)
                
                if batch_idx % 100 == 0:
                    logger.info(f"Batch {batch_idx}, Total Loss: {loss.item():.4f}")
                    for head in args.heads:
                        if head in head_losses:
                            avg_head_loss = head_losses[head] / (batch_idx + 1)
                            logger.info(f"  {head} loss: {avg_head_loss:.4f}")
            
            avg_loss = total_loss / len(train_dataloader)
            logger.info(f"Average training loss: {avg_loss:.4f}")
            
            # Prepare epoch metrics
            epoch_metrics = {
                'train_avg_loss': avg_loss,
                'epoch': epoch
            }
            
            # Log per-head losses
            for head in args.heads:
                if head in head_losses:
                    avg_head_loss = head_losses[head] / len(train_dataloader)
                    logger.info(f"Average {head} loss: {avg_head_loss:.4f}")
                    epoch_metrics[f'train_avg_{head}_loss'] = avg_head_loss
            
            # Log epoch summary to WandB
            if wandb_logger:
                wandb_logger.log_epoch_summary(epoch, epoch_metrics)
        else:
            # Dummy training step for testing
            dummy_input = torch.randint(0, tokenizer.tokenizer.vocab_size, (1, 512)).to(device)
            dummy_mask = torch.ones(1, 512).to(device)
            
            outputs = model(
                input_ids=dummy_input,
                attention_mask=dummy_mask,
                heads=args.heads
            )
            logger.info("Dummy training step completed")
    
    # Save model using checkpoint manager
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    checkpoint_name = f"robbert2023_checkpoint_{timestamp}"
    
    # Prepare comprehensive metadata
    training_metadata = {
        'checkpoint_name': checkpoint_name,
        'model_name': model_name,
        'heads': args.heads,
        'epochs': args.epochs,
        'batch_size': args.batch_size,
        'learning_rate': args.learning_rate,
        'loss_weights': loss_weights,
        'timestamp': timestamp,
        'tokenizer_type': 'byte_level_bpe',
        'training_completed_at': datetime.now().isoformat(),
        'dataset_path': args.data if args.data else None,
        'config_path': args.config
    }
    
    # Use checkpoint manager for organized saving
    try:
        checkpoint_path = save_robbert_checkpoint(
            model=model,
            checkpoint_name=checkpoint_name,
            metadata=training_metadata,
            experiment_name=getattr(args, 'experiment', None),
            base_dir="models"
        )
        logger.info(f"RobBERT-2023 checkpoint saved to {checkpoint_path}")
        
        # Also save to legacy location for backward compatibility
        legacy_output_dir = Path(args.output_dir) / checkpoint_name
        legacy_output_dir.mkdir(parents=True, exist_ok=True)
        model.save_pretrained(str(legacy_output_dir))
        
        # Save training info in legacy format
        with open(legacy_output_dir / "training_info.json", 'w') as f:
            json.dump(training_metadata, f, indent=2)
        
        logger.info(f"Legacy checkpoint also saved to {legacy_output_dir}")
        
        # Log model artifact to WandB
        if wandb_logger:
            artifact_name = f"robbert2023-{'-'.join(args.heads)}-epoch-{args.epochs}"
            wandb_logger.log_model_artifact(
                str(legacy_output_dir),
                artifact_name,
                training_metadata
            )
        
    except Exception as e:
        logger.error(f"Failed to save checkpoint using checkpoint manager: {e}")
        # Fallback to legacy saving
        legacy_output_dir = Path(args.output_dir) / checkpoint_name
        legacy_output_dir.mkdir(parents=True, exist_ok=True)
        model.save_pretrained(str(legacy_output_dir))
        
        with open(legacy_output_dir / "training_info.json", 'w') as f:
            json.dump(training_metadata, f, indent=2)
        
        logger.info(f"Fallback: Model saved to {legacy_output_dir}")
        
        # Log model artifact to WandB
        if wandb_logger:
            artifact_name = f"robbert2023-{'-'.join(args.heads)}-epoch-{args.epochs}"
            wandb_logger.log_model_artifact(
                str(legacy_output_dir),
                artifact_name,
                training_metadata
            )
    
    # Finish WandB run
    if wandb_logger:
        wandb_logger.finish()


def main():
    parser = argparse.ArgumentParser(description="Train multi-task BERTje model")
    parser.add_argument('--heads', nargs='+', default=['ner'], 
                       help='Heads to train (default: ner)')
    parser.add_argument('--epochs', type=int, default=3,
                       help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=8,
                       help='Training batch size')
    parser.add_argument('--learning-rate', type=float, default=2e-5,
                       help='Learning rate')
    parser.add_argument('--output-dir', default='src/models/checkpoints',
                       help='Output directory for saved models')
    parser.add_argument('--config', default='src/config/default.yaml',
                       help='Configuration file path')
    parser.add_argument('--wandb-config', default='src/config/wandb.yaml',
                       help='WandB configuration file path')
    parser.add_argument('--use-head-configs', action='store_true',
                       help='Use head-specific configuration files')
    parser.add_argument('--head-config-dir', default='src/config/heads',
                       help='Directory containing head configuration files')
    parser.add_argument('--data', help='Path to training data (JSONL)')
    parser.add_argument('--experiment', help='Experiment name for organized checkpoint storage')
    
    # Override flags for head-specific configs
    parser.add_argument('--epochs-override', action='store_true',
                       help='Override epochs from head configs with command line value')
    parser.add_argument('--batch-size-override', action='store_true',
                       help='Override batch size from head configs with command line value')
    parser.add_argument('--learning-rate-override', action='store_true',
                       help='Override learning rate from head configs with command line value')
    
    args = parser.parse_args()
    train_multitask_model(args)


if __name__ == '__main__':
    main()