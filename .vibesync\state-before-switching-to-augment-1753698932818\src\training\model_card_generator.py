"""
Model card generator for trained NER models.

This module generates comprehensive model cards with training information,
dataset statistics, performance metrics, and usage examples.
"""

from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime

from .hf_config import HFTrainingConfig
from ..data.hf_dataset_preparation import DatasetStatistics


def generate_model_card(
    output_path: str,
    config: HFTrainingConfig,
    results: Dict[str, Any],
    dataset_statistics: Optional[DatasetStatistics] = None,
    label_list: Optional[List[str]] = None
) -> None:
    """
    Generate model card with training information.
    
    Args:
        output_path: Path to save model card
        config: Training configuration
        results: Training results
        dataset_statistics: Dataset statistics
        label_list: List of labels
    """
    # Extract metrics from results
    eval_result = results.get("eval_result", {})
    f1_score = eval_result.get("eval_f1", "N/A")
    precision = eval_result.get("eval_precision", "N/A")
    recall = eval_result.get("eval_recall", "N/A")
    accuracy = eval_result.get("eval_accuracy", "N/A")
    
    # Format metrics
    def format_metric(value):
        if isinstance(value, (int, float)):
            return f"{value:.4f}"
        return str(value)
    
    # Generate model card content
    model_card_content = f"""# RobBERT-2023 Dutch NER Model

## Model Description

This model is a fine-tuned version of [DTAI-KULeuven/robbert-2023-dutch-base](https://huggingface.co/DTAI-KULeuven/robbert-2023-dutch-base) for Named Entity Recognition (NER) in Dutch text.

- **Model Type**: Token Classification (NER)
- **Language**: Dutch
- **Base Model**: {config.model_name}
- **Training Framework**: Hugging Face Transformers
- **Training Date**: {datetime.now().strftime("%Y-%m-%d")}
- **Version**: {config.run_version}

## Performance

| Metric | Score |
|--------|-------|
| F1 Score | {format_metric(f1_score)} |
| Precision | {format_metric(precision)} |
| Recall | {format_metric(recall)} |
| Accuracy | {format_metric(accuracy)} |

## Labels

The model predicts the following entity types:

{_format_labels_section(label_list)}

## Training Configuration

- **Epochs**: {config.epochs}
- **Batch Size**: {config.batch_size}
- **Learning Rate**: {config.learning_rate}
- **Weight Decay**: {config.weight_decay}
- **Warmup Steps**: {config.warmup_steps}
- **Max Length**: {config.max_length}
- **Early Stopping Patience**: {config.early_stopping_patience}

## Dataset Information

{_format_dataset_section(dataset_statistics)}

## Usage

```python
from transformers import AutoTokenizer, AutoModelForTokenClassification
from transformers import pipeline

# Load model and tokenizer
tokenizer = AutoTokenizer.from_pretrained("{results.get('tokenizer_path', 'path/to/model')}")
model = AutoModelForTokenClassification.from_pretrained("{results.get('output_dir', 'path/to/model')}")

# Create NER pipeline
ner_pipeline = pipeline("ner", model=model, tokenizer=tokenizer, aggregation_strategy="simple")

# Example usage
text = "Jan Jansen woont in Amsterdam."
entities = ner_pipeline(text)
print(entities)
```

## Training Details

### Training Arguments

- **Output Directory**: {results.get('output_dir', 'N/A')}
- **Evaluation Strategy**: {config.evaluation_strategy}
- **Logging Steps**: {config.logging_steps}
- **Save Steps**: {config.save_steps}
- **Mixed Precision**: FP16={config.fp16}, BF16={config.bf16}
- **Hardware Optimization**: GPU={config.use_gpu}

### WandB Integration

- **Project**: {config.wandb_project}
- **Entity**: {config.wandb_entity}
- **Run Name**: {config.run_name or 'N/A'}

## Model Architecture

This model uses the RobBERT-2023 architecture with a token classification head:

- **Hidden Size**: 768
- **Number of Attention Heads**: 12
- **Number of Hidden Layers**: 12
- **Vocabulary Size**: ~50,000 tokens
- **Classification Head**: Linear layer with {len(label_list) if label_list else 'N/A'} output classes

## Limitations and Bias

- The model is trained specifically for Dutch text and may not perform well on other languages
- Performance may vary on text domains not represented in the training data
- The model inherits any biases present in the training data and base model

## Citation

If you use this model, please cite:

```bibtex
@misc{{robbert2023-ner-{config.run_version.replace('.', '-')},
  title={{RobBERT-2023 Dutch NER Model}},
  author={{Your Name}},
  year={{2025}},
  note={{Fine-tuned from DTAI-KULeuven/robbert-2023-dutch-base}}
}}
```

## License

This model inherits the license from the base RobBERT-2023 model. Please refer to the original model card for licensing information.

---

*Generated automatically on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""
    
    # Write model card to file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(model_card_content)


def _format_labels_section(label_list: Optional[List[str]]) -> str:
    """Format the labels section of the model card."""
    if not label_list:
        return "- Labels information not available"
    
    # Group labels by entity type
    entity_types = set()
    for label in label_list:
        if label != "O" and "-" in label:
            entity_type = label.split("-", 1)[1]
            entity_types.add(entity_type)
    
    if entity_types:
        labels_text = "\n".join([f"- **{entity_type}**: Person, Location, Organization, etc." for entity_type in sorted(entity_types)])
        labels_text += f"\n\nBIO Tagging Scheme: {', '.join(label_list)}"
    else:
        labels_text = f"BIO Tagging Scheme: {', '.join(label_list)}"
    
    return labels_text


def _format_dataset_section(dataset_statistics: Optional[DatasetStatistics]) -> str:
    """Format the dataset section of the model card."""
    if not dataset_statistics:
        return "Dataset statistics not available."
    
    return f"""- **Total Examples**: {dataset_statistics.total_examples:,}
- **Training Examples**: {dataset_statistics.train_size:,}
- **Validation Examples**: {dataset_statistics.val_size:,}
- **Total Entities**: {dataset_statistics.total_entities:,}
- **Entity Coverage**: {dataset_statistics.entity_coverage:.1f}% of examples contain entities
- **Average Sentence Length**: {dataset_statistics.avg_sentence_length:.1f} tokens
- **Average Entities per Sentence**: {dataset_statistics.avg_entities_per_sentence:.2f}

### Label Distribution

{_format_label_distribution(dataset_statistics.label_distribution)}"""


def _format_label_distribution(label_distribution: Dict[str, int]) -> str:
    """Format label distribution for the model card."""
    if not label_distribution:
        return "Label distribution not available."
    
    total_labels = sum(label_distribution.values())
    distribution_text = []
    
    for label, count in sorted(label_distribution.items()):
        percentage = (count / total_labels) * 100 if total_labels > 0 else 0
        distribution_text.append(f"- **{label}**: {count:,} ({percentage:.1f}%)")
    
    return "\n".join(distribution_text)


# Export main function
__all__ = ['generate_model_card']