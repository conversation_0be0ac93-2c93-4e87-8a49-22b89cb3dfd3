#!/usr/bin/env python3
"""
Command-line utility for RobBERT-2023 checkpoint management.

This script provides convenient CLI access to checkpoint management operations
including listing, validation, backup, and cleanup of RobBERT checkpoints.
"""

import argparse
import sys
import json
from pathlib import Path
from typing import Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.checkpoint_manager import CheckpointManager, load_robbert_checkpoint, list_robbert_checkpoints
from src.utils.logging_utils import get_logger


def setup_logging():
    """Setup logging for CLI operations."""
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def list_checkpoints_command(args):
    """List available checkpoints."""
    logger = setup_logging()
    
    try:
        checkpoints = list_robbert_checkpoints(
            experiment_name=args.experiment,
            base_dir=args.base_dir
        )
        
        if not checkpoints:
            print("No checkpoints found.")
            return
        
        print(f"\nFound {len(checkpoints)} checkpoints:")
        print("-" * 80)
        
        for checkpoint in checkpoints:
            print(f"Name: {checkpoint['name']}")
            print(f"Path: {checkpoint['path']}")
            print(f"Created: {checkpoint.get('created_at', 'Unknown')}")
            print(f"Size: {checkpoint.get('size_mb', 0):.1f} MB")
            print(f"Valid: {'✓' if checkpoint.get('valid', False) else '✗'}")
            
            # Show metadata if available
            if 'model_heads' in checkpoint:
                print(f"Heads: {', '.join(checkpoint['model_heads'])}")
            if 'epochs' in checkpoint:
                print(f"Epochs: {checkpoint['epochs']}")
            if 'learning_rate' in checkpoint:
                print(f"Learning Rate: {checkpoint['learning_rate']}")
            
            print("-" * 80)
            
    except Exception as e:
        logger.error(f"Failed to list checkpoints: {e}")
        sys.exit(1)


def validate_checkpoint_command(args):
    """Validate a specific checkpoint."""
    logger = setup_logging()
    
    try:
        manager = CheckpointManager(args.base_dir)
        
        checkpoint_path = manager._resolve_checkpoint_path(args.checkpoint)
        
        if not checkpoint_path.exists():
            print(f"❌ Checkpoint not found: {args.checkpoint}")
            sys.exit(1)
        
        print(f"Validating checkpoint: {checkpoint_path}")
        
        is_valid = manager.validate_checkpoint(checkpoint_path)
        
        if is_valid:
            print("✅ Checkpoint validation passed")
            
            # Show detailed info
            info = manager._get_checkpoint_info(checkpoint_path)
            if info:
                print(f"\nCheckpoint Details:")
                print(f"  Name: {info['name']}")
                print(f"  Size: {info['size_mb']:.1f} MB")
                print(f"  Created: {info['created_at']}")
                
                if 'model_heads' in info:
                    print(f"  Heads: {', '.join(info['model_heads'])}")
                if 'model_name' in info:
                    print(f"  Model: {info['model_name']}")
        else:
            print("❌ Checkpoint validation failed")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Failed to validate checkpoint: {e}")
        sys.exit(1)


def backup_checkpoint_command(args):
    """Create backup of a checkpoint."""
    logger = setup_logging()
    
    try:
        manager = CheckpointManager(args.base_dir)
        
        backup_path = manager.backup_checkpoint(
            checkpoint_path=args.checkpoint,
            backup_name=args.backup_name
        )
        
        print(f"✅ Backup created: {backup_path}")
        
    except Exception as e:
        logger.error(f"Failed to backup checkpoint: {e}")
        sys.exit(1)


def cleanup_checkpoints_command(args):
    """Clean up old checkpoints."""
    logger = setup_logging()
    
    try:
        manager = CheckpointManager(args.base_dir)
        
        # Show what will be cleaned up
        checkpoints = manager.list_checkpoints(args.experiment)
        
        if len(checkpoints) <= args.keep:
            print(f"No cleanup needed. Found {len(checkpoints)} checkpoints, keeping {args.keep}")
            return
        
        checkpoints_to_remove = checkpoints[args.keep:]
        
        print(f"Will remove {len(checkpoints_to_remove)} old checkpoints:")
        for checkpoint in checkpoints_to_remove:
            print(f"  - {checkpoint['name']} ({checkpoint.get('created_at', 'Unknown')})")
        
        if not args.force:
            response = input("\nProceed with cleanup? (y/N): ")
            if response.lower() != 'y':
                print("Cleanup cancelled.")
                return
        
        manager.cleanup_old_checkpoints(
            keep_count=args.keep,
            experiment_name=args.experiment
        )
        
        print("✅ Cleanup completed")
        
    except Exception as e:
        logger.error(f"Failed to cleanup checkpoints: {e}")
        sys.exit(1)


def load_checkpoint_command(args):
    """Test loading a checkpoint."""
    logger = setup_logging()
    
    try:
        print(f"Loading checkpoint: {args.checkpoint}")
        
        model = load_robbert_checkpoint(
            checkpoint_path=args.checkpoint,
            base_dir=args.base_dir
        )
        
        print("✅ Checkpoint loaded successfully")
        
        # Show model info
        num_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"\nModel Information:")
        print(f"  Total parameters: {num_params:,}")
        print(f"  Trainable parameters: {trainable_params:,}")
        print(f"  Model heads: {list(model.heads.keys())}")
        
        # Test basic inference
        if args.test_inference:
            print("\nTesting basic inference...")
            import torch
            
            # Create dummy input
            dummy_input = torch.randint(0, 1000, (1, 10))
            dummy_mask = torch.ones(1, 10)
            
            with torch.no_grad():
                outputs = model(
                    input_ids=dummy_input,
                    attention_mask=dummy_mask,
                    heads=['ner']
                )
            
            print("✅ Basic inference test passed")
            print(f"  Output shape: {outputs['ner']['logits'].shape}")
        
    except Exception as e:
        logger.error(f"Failed to load checkpoint: {e}")
        sys.exit(1)


def info_command(args):
    """Show detailed information about checkpoint structure."""
    logger = setup_logging()
    
    try:
        manager = CheckpointManager(args.base_dir)
        
        print("RobBERT-2023 Checkpoint Management")
        print("=" * 50)
        
        print(f"\nBase Directory: {manager.base_dir}")
        print(f"RobBERT Directory: {manager.robbert_dir}")
        
        print(f"\nDirectory Structure:")
        for subdir in ["fine-tuned", "base-models", "experiments", "backups"]:
            dir_path = manager.robbert_dir / subdir
            exists = "✓" if dir_path.exists() else "✗"
            print(f"  {exists} {subdir}/")
        
        print(f"\nSupported Models:")
        for model_name, config in manager.supported_models.items():
            print(f"  - {model_name}")
            print(f"    Tokenizer: {config['tokenizer_type']}")
            print(f"    Vocab Size: {config['vocab_size']:,}")
            print(f"    Hidden Size: {config['hidden_size']}")
        
        # Count checkpoints by type
        all_checkpoints = manager.list_checkpoints()
        fine_tuned = [c for c in all_checkpoints if 'fine-tuned' in c['path']]
        experiments = [c for c in all_checkpoints if 'experiments' in c['path']]
        backups = [c for c in all_checkpoints if 'backups' in c['path']]
        
        print(f"\nCheckpoint Summary:")
        print(f"  Fine-tuned: {len(fine_tuned)}")
        print(f"  Experiments: {len(experiments)}")
        print(f"  Backups: {len(backups)}")
        print(f"  Total: {len(all_checkpoints)}")
        
    except Exception as e:
        logger.error(f"Failed to show info: {e}")
        sys.exit(1)


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="RobBERT-2023 Checkpoint Management CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # List all checkpoints
  python scripts/checkpoint_management.py list
  
  # List checkpoints for specific experiment
  python scripts/checkpoint_management.py list --experiment my_experiment
  
  # Validate a checkpoint
  python scripts/checkpoint_management.py validate robbert2023_checkpoint_20250725_184935
  
  # Create backup
  python scripts/checkpoint_management.py backup robbert2023_checkpoint_20250725_184935
  
  # Clean up old checkpoints (keep 3 most recent)
  python scripts/checkpoint_management.py cleanup --keep 3
  
  # Test loading a checkpoint
  python scripts/checkpoint_management.py load robbert2023_checkpoint_20250725_184935 --test-inference
  
  # Show system info
  python scripts/checkpoint_management.py info
        """
    )
    
    parser.add_argument('--base-dir', default='models',
                       help='Base directory for model storage (default: models)')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List available checkpoints')
    list_parser.add_argument('--experiment', help='Filter by experiment name')
    list_parser.set_defaults(func=list_checkpoints_command)
    
    # Validate command
    validate_parser = subparsers.add_parser('validate', help='Validate checkpoint')
    validate_parser.add_argument('checkpoint', help='Checkpoint name or path')
    validate_parser.set_defaults(func=validate_checkpoint_command)
    
    # Backup command
    backup_parser = subparsers.add_parser('backup', help='Create checkpoint backup')
    backup_parser.add_argument('checkpoint', help='Checkpoint name or path')
    backup_parser.add_argument('--backup-name', help='Custom backup name')
    backup_parser.set_defaults(func=backup_checkpoint_command)
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser('cleanup', help='Clean up old checkpoints')
    cleanup_parser.add_argument('--keep', type=int, default=5,
                               help='Number of checkpoints to keep (default: 5)')
    cleanup_parser.add_argument('--experiment', help='Clean up specific experiment')
    cleanup_parser.add_argument('--force', action='store_true',
                               help='Skip confirmation prompt')
    cleanup_parser.set_defaults(func=cleanup_checkpoints_command)
    
    # Load command
    load_parser = subparsers.add_parser('load', help='Test loading checkpoint')
    load_parser.add_argument('checkpoint', help='Checkpoint name or path')
    load_parser.add_argument('--test-inference', action='store_true',
                            help='Run basic inference test')
    load_parser.set_defaults(func=load_checkpoint_command)
    
    # Info command
    info_parser = subparsers.add_parser('info', help='Show system information')
    info_parser.set_defaults(func=info_command)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    args.func(args)


if __name__ == '__main__':
    main()