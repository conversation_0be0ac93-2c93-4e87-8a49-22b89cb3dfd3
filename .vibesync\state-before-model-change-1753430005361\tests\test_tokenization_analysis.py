"""
Comprehensive analysis of tokenization issues with compound Dutch names.
"""

import pytest
import torch
from src.models.multitask_bertje import MultiTaskBERTje


class TestTokenizationAnalysis:
    """Analyze tokenization patterns and their impact on NER performance."""
    
    @pytest.fixture(scope="class")
    def model(self):
        """Load model for testing."""
        try:
            model = MultiTaskBERTje.from_pretrained()
            model.eval()
            return model
        except Exception as e:
            pytest.skip(f"Model not available: {e}")
    
    def analyze_tokenization(self, model, text):
        """Analyze tokenization patterns for given text."""
        # Basic tokenization
        tokens = model.tokenizer.tokenize(text)
        token_ids = model.tokenizer.encode(text, add_special_tokens=True)
        
        # Full tokenization with attention mask
        inputs = model.tokenizer(
            text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512,
            return_offsets_mapping=False
        )
        
        # Convert back to tokens for analysis
        full_tokens = model.tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])
        
        return {
            'text': text,
            'tokens': tokens,
            'token_count': len(tokens),
            'full_tokens': full_tokens,
            'token_ids': token_ids,
            'subword_tokens': [t for t in tokens if t.startswith('##')],
            'special_tokens': [t for t in full_tokens if t in ['[CLS]', '[SEP]', '[PAD]']],
            'inputs': inputs
        }
    
    def predict_with_analysis(self, model, text):
        """Predict entities and analyze tokenization alignment."""
        tokenization = self.analyze_tokenization(model, text)
        
        # Get predictions
        with torch.no_grad():
            outputs = model(
                input_ids=tokenization['inputs']['input_ids'],
                attention_mask=tokenization['inputs']['attention_mask'],
                heads=['ner']
            )
        
        logits = outputs['ner']['logits']
        predictions = torch.argmax(logits, dim=-1)
        confidences = torch.softmax(logits, dim=-1)
        
        # Map predictions to tokens
        label_names = ['O', 'B-PER', 'I-PER', 'B-LOC', 'I-LOC', 'B-ORG', 'I-ORG', 'B-MISC', 'I-MISC']
        
        token_predictions = []
        for i, (token, pred_id) in enumerate(zip(tokenization['full_tokens'], predictions[0])):
            if token not in ['[CLS]', '[SEP]', '[PAD]']:
                label = label_names[pred_id.item()] if pred_id.item() < len(label_names) else 'UNK'
                confidence = confidences[0][i][pred_id.item()].item()
                
                token_predictions.append({
                    'token': token,
                    'label': label,
                    'confidence': confidence,
                    'position': i
                })
        
        return {
            'tokenization': tokenization,
            'predictions': token_predictions,
            'issues': self.identify_tokenization_issues(tokenization, token_predictions)
        }
    
    def identify_tokenization_issues(self, tokenization, predictions):
        """Identify specific tokenization issues affecting NER performance."""
        issues = []
        
        # Check for subword splitting in names
        subword_count = len(tokenization['subword_tokens'])
        if subword_count > 0:
            issues.append(f"Subword splitting: {subword_count} subword tokens found")
        
        # Check for particle separation
        dutch_particles = ['van', 'de', 'der', 'den', 'ter', 'op', 'aan', 'bij', 'tot', 'uit']
        particles_found = [t for t in tokenization['tokens'] if t.lower() in dutch_particles]
        if particles_found:
            issues.append(f"Particle separation: {particles_found}")
        
        # Check for inconsistent BIO tagging across compound names
        bio_issues = []
        for i, pred in enumerate(predictions[:-1]):
            current_token = pred['token']
            next_pred = predictions[i + 1]
            
            # Check if particle should be I- instead of B- or O
            if current_token.lower() in dutch_particles:
                if pred['label'] != 'O' and not pred['label'].startswith('I-'):
                    bio_issues.append(f"Particle '{current_token}' tagged as {pred['label']} instead of I-PER")
        
        if bio_issues:
            issues.append(f"BIO tagging issues: {bio_issues}")
        
        # Check for entity boundary problems
        boundary_issues = []
        in_entity = False
        entity_type = None
        
        for pred in predictions:
            if pred['label'].startswith('B-'):
                if in_entity:
                    boundary_issues.append(f"Unexpected B- tag for '{pred['token']}' while in {entity_type} entity")
                in_entity = True
                entity_type = pred['label'][2:]
            elif pred['label'].startswith('I-'):
                if not in_entity:
                    boundary_issues.append(f"I- tag for '{pred['token']}' without preceding B- tag")
                elif entity_type != pred['label'][2:]:
                    boundary_issues.append(f"Entity type mismatch: {entity_type} -> {pred['label'][2:]}")
            else:  # O tag
                in_entity = False
                entity_type = None
        
        if boundary_issues:
            issues.append(f"Entity boundary issues: {boundary_issues}")
        
        return issues
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_compound_name_tokenization(self, model):
        """Test tokenization of compound Dutch names."""
        test_cases = [
            "Jan van der Berg",
            "Marie-Claire de Jong", 
            "Prof. dr. Willem van den Heuvel",
            "Drs. Anna-Maria ter Steege",
            "Mr. Pieter van de Meer",
            "Ir. Johannes op den Akker",
            "Dr. Elisabeth uit den Bogaard"
        ]
        
        print("\n=== COMPOUND NAME TOKENIZATION ANALYSIS ===")
        
        results = []
        for name in test_cases:
            analysis = self.predict_with_analysis(model, f"{name} woont in Amsterdam.")
            results.append(analysis)
            
            print(f"\nName: {name}")
            print(f"Tokens: {analysis['tokenization']['tokens']}")
            print(f"Subword tokens: {analysis['tokenization']['subword_tokens']}")
            
            # Show predictions for name tokens only
            name_tokens = analysis['tokenization']['tokens'][:len(name.split())]
            name_predictions = analysis['predictions'][:len(name_tokens)]
            
            print("Token predictions:")
            for pred in name_predictions:
                print(f"  {pred['token']:15} -> {pred['label']:8} (conf: {pred['confidence']:.3f})")
            
            if analysis['issues']:
                print(f"Issues: {analysis['issues']}")
        
        return results
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_long_entity_tokenization(self, model):
        """Test tokenization of long entity names."""
        test_cases = [
            "Koninklijke Nederlandse Voetbal Bond",
            "Universitair Medisch Centrum Utrecht",
            "Nederlandse Organisatie voor Wetenschappelijk Onderzoek",
            "Centraal Bureau voor de Statistiek",
            "Koninklijke Luchtvaart Maatschappij"
        ]
        
        print("\n=== LONG ENTITY TOKENIZATION ANALYSIS ===")
        
        results = []
        for entity in test_cases:
            analysis = self.predict_with_analysis(model, f"De {entity} is een belangrijke organisatie.")
            results.append(analysis)
            
            print(f"\nEntity: {entity}")
            print(f"Token count: {len(entity.split())} words -> {analysis['tokenization']['token_count']} tokens")
            print(f"Tokens: {analysis['tokenization']['tokens']}")
            
            # Show predictions for entity tokens
            entity_start = 1  # Skip "De"
            entity_tokens = analysis['tokenization']['tokens'][entity_start:entity_start + len(entity.split())]
            entity_predictions = analysis['predictions'][entity_start:entity_start + len(entity_tokens)]
            
            print("Token predictions:")
            for pred in entity_predictions:
                print(f"  {pred['token']:20} -> {pred['label']:8} (conf: {pred['confidence']:.3f})")
            
            if analysis['issues']:
                print(f"Issues: {analysis['issues']}")
        
        return results
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_particle_handling(self, model):
        """Analyze how Dutch particles are handled in tokenization."""
        particles = ['van', 'de', 'der', 'den', 'ter', 'op', 'aan', 'bij', 'tot', 'uit', 'van der', 'van den', 'van de']
        
        print("\n=== DUTCH PARTICLE HANDLING ANALYSIS ===")
        
        results = []
        for particle in particles:
            test_name = f"Jan {particle} Berg"
            analysis = self.predict_with_analysis(model, f"{test_name} is een persoon.")
            results.append(analysis)
            
            print(f"\nParticle pattern: '{particle}'")
            print(f"Full name: {test_name}")
            print(f"Tokenization: {analysis['tokenization']['tokens'][:3]}")  # First 3 tokens
            
            # Show how particle is predicted
            particle_predictions = analysis['predictions'][:3]
            print("Predictions:")
            for pred in particle_predictions:
                print(f"  {pred['token']:10} -> {pred['label']:8} (conf: {pred['confidence']:.3f})")
        
        return results
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_subword_impact(self, model):
        """Analyze impact of subword tokenization on entity recognition."""
        test_cases = [
            "Steege",      # Gets split into Ste + ##e + ##ge
            "Heuvel",      # Usually stays as one token
            "Jansen",      # Usually stays as one token
            "Bogaard",     # May get split
            "Akker",       # Usually stays as one token
            "Meer"         # Usually stays as one token
        ]
        
        print("\n=== SUBWORD TOKENIZATION IMPACT ANALYSIS ===")
        
        results = []
        for surname in test_cases:
            full_name = f"Jan {surname}"
            analysis = self.predict_with_analysis(model, f"{full_name} woont hier.")
            results.append(analysis)
            
            print(f"\nSurname: {surname}")
            surname_tokens = [t for t in analysis['tokenization']['tokens'] if surname.lower() in t.lower() or t.startswith('##')]
            print(f"Tokenization: {surname_tokens}")
            
            # Find predictions for surname tokens
            surname_predictions = [p for p in analysis['predictions'] if any(surname.lower() in p['token'].lower() or p['token'].startswith('##') for _ in [None])]
            
            if surname_predictions:
                print("Predictions:")
                for pred in surname_predictions:
                    if surname.lower() in pred['token'].lower() or pred['token'].startswith('##'):
                        print(f"  {pred['token']:10} -> {pred['label']:8} (conf: {pred['confidence']:.3f})")
        
        return results