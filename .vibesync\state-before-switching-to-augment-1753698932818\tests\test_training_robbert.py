"""
Integration tests for RobBERT-2023 training pipeline.
"""

import pytest
import torch
import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from src.training.train_multitask import (
    train_multitask_model, 
    build_robbert_dataset, 
    collate_robbert_batch,
    setup_logging
)
from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment
from src.models.multitask_robbert import MultiTaskRobBERT


class TestRobBERTTrainingPipeline:
    """Test suite for RobBERT-2023 training pipeline."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration for testing."""
        config = Mock()
        config.model = {
            'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'max_length': 512
        }
        config.training = {
            'weight_decay': 0.01,
            'warmup_steps': 100,
            'loss_weights': {'ner': 1.0, 'compliance': 2.0},
            'max_grad_norm': 1.0
        }
        return config
    
    @pytest.fixture
    def sample_training_data(self):
        """Sample training data in JSONL format."""
        return [
            {
                'text': '<PERSON> woon<PERSON> in Amsterdam.',
                'ner_labels': ['B-PER', 'I-PER', 'O', 'O', 'B-LOC', 'O'],
                'compliance_labels': 1
            },
            {
                'text': 'Marie de Wit werkt bij Google.',
                'ner_labels': ['B-PER', 'I-PER', 'I-PER', 'O', 'O', 'B-ORG', 'O'],
                'compliance_labels': 0
            },
            {
                'text': 'Dit is een test zonder entiteiten.',
                'ner_labels': ['O', 'O', 'O', 'O', 'O', 'O', 'O'],
                'compliance_labels': 0
            }
        ]
    
    @pytest.fixture
    def temp_jsonl_file(self, sample_training_data):
        """Create temporary JSONL file with sample data."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
            for item in sample_training_data:
                f.write(json.dumps(item) + '\n')
            return f.name
    
    @pytest.fixture
    def mock_tokenizer(self):
        """Mock RobBERT tokenizer for testing."""
        tokenizer = Mock(spec=RobBERTTokenizerWithAlignment)
        tokenizer.tokenizer = Mock()
        tokenizer.tokenizer.vocab_size = 50000
        tokenizer.label2id = {"O": 0, "B-PER": 1, "I-PER": 2}
        tokenizer.id2label = {0: "O", 1: "B-PER", 2: "I-PER"}
        
        # Mock tokenization
        def mock_tokenize(text, **kwargs):
            # Simple mock: return fixed-size tensors
            return {
                'input_ids': torch.randint(0, 1000, (1, 512)),
                'attention_mask': torch.ones(1, 512)
            }
        
        tokenizer.tokenizer.side_effect = mock_tokenize
        
        # Mock alignment
        mock_alignment = Mock()
        def alignment_func(words, labels, max_length):
            alignment = Mock()
            alignment.token_labels = labels[:max_length] if labels else ['O'] * len(words)
            return alignment
        
        mock_alignment.side_effect = alignment_func
        tokenizer.tokenize_with_alignment = mock_alignment
        return tokenizer
    
    def test_setup_logging(self):
        """Test logging setup."""
        logger = setup_logging()
        assert logger is not None
        assert logger.name == 'src.training.train_multitask'
    
    def test_build_robbert_dataset(self, temp_jsonl_file, mock_tokenizer, mock_config):
        """Test dataset building for RobBERT training."""
        dataset = build_robbert_dataset(temp_jsonl_file, mock_tokenizer, mock_config)
        
        assert 'train' in dataset
        assert 'validation' in dataset
        assert len(dataset['train']) > 0
        assert len(dataset['validation']) > 0
        
        # Check data structure
        sample = dataset['train'][0]
        assert 'text' in sample
        # Note: labels are preserved from original data
        if 'ner_labels' in sample:
            assert 'ner_labels' in sample
        if 'compliance_labels' in sample:
            assert 'compliance_labels' in sample
    
    def test_collate_robbert_batch(self, sample_training_data):
        """Test batch collation for RobBERT training."""
        device = torch.device('cpu')
        batch = sample_training_data[:2]  # Use first 2 samples
        
        # Create a proper mock tokenizer
        mock_tokenizer = Mock()
        mock_tokenizer.label2id = {"O": 0, "B-PER": 1, "I-PER": 2}
        
        # Mock the tokenizer call to return proper tensors
        def mock_tokenize_call(text, **kwargs):
            return {
                'input_ids': torch.randint(0, 1000, (1, 512)),
                'attention_mask': torch.ones(1, 512)
            }
        mock_tokenizer.tokenizer = mock_tokenize_call
        
        # Mock alignment function
        def mock_alignment(words, labels, max_length):
            alignment = Mock()
            alignment.token_labels = labels[:max_length] if labels else ['O'] * min(len(words), max_length)
            return alignment
        mock_tokenizer.tokenize_with_alignment = mock_alignment
        
        collated = collate_robbert_batch(batch, mock_tokenizer, device)
        
        # Check batch structure
        assert 'input_ids' in collated
        assert 'attention_mask' in collated
        assert 'ner_labels' in collated
        assert 'compliance_labels' in collated
        
        # Check tensor shapes
        assert collated['input_ids'].shape == (2, 512)
        assert collated['attention_mask'].shape == (2, 512)
        assert collated['ner_labels'].shape == (2, 512)
        assert collated['compliance_labels'].shape == (2,)
        
        # Check device
        assert collated['input_ids'].device == device
    
    @patch('src.training.train_multitask.MultiTaskRobBERT')
    @patch('src.training.train_multitask.load_config')
    def test_train_multitask_model_dummy(self, mock_load_config, mock_model_class):
        """Test training pipeline with dummy data (no dataset provided)."""
        # Setup mocks
        mock_load_config.return_value = Mock()
        mock_load_config.return_value.model = {
            'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base'
        }
        mock_load_config.return_value.training = {
            'weight_decay': 0.01,
            'warmup_steps': 100,
            'loss_weights': {'ner': 1.0},
            'max_grad_norm': 1.0
        }
        
        mock_model = Mock()
        mock_model.parameters.return_value = [torch.tensor([1.0], requires_grad=True)]
        mock_model.train = Mock()
        mock_model.save_pretrained = Mock()
        mock_model_class.from_pretrained.return_value = mock_model
        
        # Mock model forward pass
        def mock_forward(*args, **kwargs):
            return {'ner': {'logits': torch.randn(1, 512, 3)}}
        mock_model.side_effect = mock_forward
        
        # Create args
        args = Mock()
        args.config = 'test_config.yaml'
        args.data = None  # No dataset
        args.heads = ['ner']
        args.epochs = 1
        args.batch_size = 2
        args.learning_rate = 2e-5
        args.output_dir = tempfile.mkdtemp()
        
        # Run training
        with patch('src.training.train_multitask.RobBERTTokenizerWithAlignment'):
            train_multitask_model(args)
        
        # Verify model was loaded and training completed
        mock_model_class.from_pretrained.assert_called_once()
        mock_model.save_pretrained.assert_called_once()
    
    @patch('src.training.train_multitask.MultiTaskRobBERT')
    @patch('src.training.train_multitask.load_config')
    @patch('src.training.train_multitask.build_robbert_dataset')
    def test_train_multitask_model_with_data(self, mock_build_dataset, mock_load_config, mock_model_class):
        """Test training pipeline with actual dataset."""
        # Setup mocks
        mock_load_config.return_value = Mock()
        mock_load_config.return_value.model = {
            'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base'
        }
        mock_load_config.return_value.training = {
            'weight_decay': 0.01,
            'warmup_steps': 100,
            'loss_weights': {'ner': 1.0},
            'max_grad_norm': 1.0
        }
        
        # Mock dataset
        from datasets import Dataset, DatasetDict
        mock_dataset = DatasetDict({
            'train': Dataset.from_list([{'text': 'test', 'ner_labels': ['O']}]),
            'validation': Dataset.from_list([{'text': 'test', 'ner_labels': ['O']}])
        })
        mock_build_dataset.return_value = mock_dataset
        
        # Mock model
        mock_model = Mock()
        mock_model.parameters.return_value = [torch.tensor([1.0], requires_grad=True)]
        mock_model.train = Mock()
        mock_model.save_pretrained = Mock()
        mock_model_class.from_pretrained.return_value = mock_model
        
        # Mock model forward pass with loss
        def mock_forward(*args, **kwargs):
            return {
                'ner': {
                    'logits': torch.randn(1, 512, 3),
                    'loss': torch.tensor(0.5, requires_grad=True)
                },
                'loss': torch.tensor(0.5, requires_grad=True)
            }
        mock_model.side_effect = mock_forward
        
        # Create args
        args = Mock()
        args.config = 'test_config.yaml'
        args.data = 'test_data.jsonl'
        args.heads = ['ner']
        args.epochs = 1
        args.batch_size = 1
        args.learning_rate = 2e-5
        args.output_dir = tempfile.mkdtemp()
        
        # Run training
        with patch('src.training.train_multitask.RobBERTTokenizerWithAlignment'):
            with patch('torch.utils.data.DataLoader') as mock_dataloader:
                # Mock dataloader to return one batch
                mock_batch = {
                    'input_ids': torch.randint(0, 1000, (1, 512)),
                    'attention_mask': torch.ones(1, 512),
                    'ner_labels': torch.zeros(1, 512, dtype=torch.long)
                }
                mock_dataloader.return_value = [mock_batch]
                
                train_multitask_model(args)
        
        # Verify dataset was built and model was trained
        mock_build_dataset.assert_called_once()
        mock_model_class.from_pretrained.assert_called_once()
        mock_model.save_pretrained.assert_called_once()
    
    def test_collate_batch_error_handling(self, mock_tokenizer):
        """Test error handling in batch collation."""
        device = torch.device('cpu')
        
        # Test with malformed data
        bad_batch = [
            {'text': 'Valid text'},
            {'no_text_field': 'Invalid'}
        ]
        
        # Should handle missing text gracefully
        with pytest.raises(KeyError):
            collate_robbert_batch(bad_batch, mock_tokenizer, device)
    
    def test_label_alignment_in_collation(self):
        """Test label alignment during batch collation."""
        device = torch.device('cpu')
        
        # Create a proper mock tokenizer
        mock_tokenizer = Mock()
        mock_tokenizer.label2id = {"O": 0, "B-PER": 1, "I-PER": 2}
        
        # Mock the tokenizer call to return proper tensors
        def mock_tokenize_call(text, **kwargs):
            return {
                'input_ids': torch.randint(0, 1000, (1, 512)),
                'attention_mask': torch.ones(1, 512)
            }
        mock_tokenizer.tokenizer = mock_tokenize_call
        
        # Mock alignment to return specific labels
        mock_alignment = Mock()
        def alignment_func(words, labels, max_length):
            alignment = Mock()
            alignment.token_labels = ['O', 'B-PER', 'I-PER'] + ['O'] * (max_length - 3)
            return alignment
        mock_alignment.side_effect = alignment_func
        mock_tokenizer.tokenize_with_alignment = mock_alignment
        
        batch = [{
            'text': 'Jan Jansen test',
            'ner_labels': ['B-PER', 'I-PER', 'O']
        }]
        
        collated = collate_robbert_batch(batch, mock_tokenizer, device)
        
        # Verify alignment was called
        mock_tokenizer.tokenize_with_alignment.assert_called()
        
        # Check that NER labels were processed
        assert collated['ner_labels'].shape == (1, 512)
    
    @patch('src.training.train_multitask.torch.save')
    def test_model_saving(self, mock_torch_save):
        """Test model saving functionality."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a mock model
            mock_model = Mock()
            mock_model.state_dict.return_value = {'test': torch.tensor([1.0])}
            mock_model.save_pretrained = Mock()
            
            # Test save_pretrained call
            mock_model.save_pretrained(temp_dir)
            mock_model.save_pretrained.assert_called_once_with(temp_dir)
    
    def test_training_info_generation(self, temp_jsonl_file):
        """Test training info JSON generation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_info = {
                'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
                'heads': ['ner'],
                'epochs': 3,
                'batch_size': 8,
                'learning_rate': 2e-5,
                'loss_weights': {'ner': 1.0},
                'timestamp': '20240101_120000',
                'tokenizer_type': 'byte_level_bpe'
            }
            
            info_path = Path(temp_dir) / "training_info.json"
            with open(info_path, 'w') as f:
                json.dump(training_info, f, indent=2)
            
            # Verify file was created and contains expected data
            assert info_path.exists()
            
            with open(info_path, 'r') as f:
                loaded_info = json.load(f)
            
            assert loaded_info['model_name'] == 'DTAI-KULeuven/robbert-2023-dutch-base'
            assert loaded_info['tokenizer_type'] == 'byte_level_bpe'
            assert loaded_info['heads'] == ['ner']


class TestRobBERTTrainingIntegration:
    """Integration tests for complete training workflow."""
    
    @pytest.mark.slow
    @patch('src.training.train_multitask.MultiTaskRobBERT')
    def test_end_to_end_training_workflow(self, mock_model_class):
        """Test complete training workflow from data loading to model saving."""
        # This test would require actual model loading, so we mock it
        # In a real scenario, this would test with a small subset of real data
        
        mock_model = Mock()
        mock_model.parameters.return_value = [torch.tensor([1.0], requires_grad=True)]
        mock_model.train = Mock()
        mock_model.save_pretrained = Mock()
        mock_model_class.from_pretrained.return_value = mock_model
        
        # Mock forward pass
        def mock_forward(*args, **kwargs):
            return {
                'ner': {
                    'logits': torch.randn(2, 512, 3),
                    'loss': torch.tensor(0.5, requires_grad=True)
                },
                'loss': torch.tensor(0.5, requires_grad=True)
            }
        mock_model.side_effect = mock_forward
        
        # Create test data
        test_data = [
            {
                'text': 'Jan Jansen woont in Amsterdam.',
                'ner_labels': ['B-PER', 'I-PER', 'O', 'O', 'B-LOC', 'O']
            }
        ] * 10  # Repeat to have enough data
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
            for item in test_data:
                f.write(json.dumps(item) + '\n')
            temp_file = f.name
        
        try:
            with tempfile.TemporaryDirectory() as output_dir:
                # Create args for training
                args = Mock()
                args.config = 'src/config/default.yaml'
                args.data = temp_file
                args.heads = ['ner']
                args.epochs = 1
                args.batch_size = 2
                args.learning_rate = 2e-5
                args.output_dir = output_dir
                
                # Run training with mocked components
                with patch('src.training.train_multitask.load_config') as mock_config:
                    mock_config.return_value = Mock()
                    mock_config.return_value.model = {
                        'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
                        'max_length': 512
                    }
                    mock_config.return_value.training = {
                        'weight_decay': 0.01,
                        'warmup_steps': 100,
                        'loss_weights': {'ner': 1.0},
                        'max_grad_norm': 1.0
                    }
                    
                    with patch('src.training.train_multitask.RobBERTTokenizerWithAlignment'):
                        train_multitask_model(args)
                
                # Verify training completed
                mock_model.save_pretrained.assert_called_once()
                
        finally:
            # Clean up temp file
            Path(temp_file).unlink()
    
    def test_loss_calculation_and_weighting(self):
        """Test loss calculation with multiple heads and weighting."""
        # Test that loss weights are properly applied
        loss_weights = {'ner': 1.0, 'compliance': 2.0, 'topic': 0.5}
        
        # Mock head losses
        head_losses = {
            'ner': torch.tensor(0.5, requires_grad=True),
            'compliance': torch.tensor(0.3, requires_grad=True),
            'topic': torch.tensor(0.8, requires_grad=True)
        }
        
        # Calculate expected weighted loss
        expected_loss = (
            head_losses['ner'] * loss_weights['ner'] +
            head_losses['compliance'] * loss_weights['compliance'] +
            head_losses['topic'] * loss_weights['topic']
        )
        
        # Verify calculation
        calculated_loss = sum(
            loss * loss_weights.get(head, 1.0) 
            for head, loss in head_losses.items()
        )
        
        assert torch.allclose(expected_loss, calculated_loss)
