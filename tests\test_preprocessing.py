"""
Unit tests for data preprocessing utilities.

Tests the sentence + entities preprocessing functionality with various Dutch text examples.
"""

import json
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from src.data.preprocessing import (
    SentenceEntityPreprocessor,
    EntitySpan,
    PreprocessedExample,
    preprocess_raw_data
)
from src.exceptions import TokenizationError


class TestEntitySpan:
    """Test EntitySpan dataclass."""
    
    def test_valid_entity_span(self):
        """Test creating valid entity span."""
        span = EntitySpan(text="<PERSON>", label="PER", start=0, end=10)
        assert span.text == "<PERSON>"
        assert span.label == "PER"
        assert span.start == 0
        assert span.end == 10
    
    def test_invalid_span_positions(self):
        """Test validation of invalid span positions."""
        with pytest.raises(ValueError, match="Invalid span"):
            EntitySpan(text="Jan", label="PER", start=5, end=3)  # end < start
        
        with pytest.raises(ValueError, match="Invalid span"):
            EntitySpan(text="Jan", label="PER", start=-1, end=3)  # negative start
        
        with pytest.raises(ValueError, match="Invalid span"):
            EntitySpan(text="Jan", label="PER", start=0, end=0)  # start == end
    
    def test_text_length_mismatch(self):
        """Test validation of text length vs span length."""
        with pytest.raises(ValueError, match="Text length"):
            EntitySpan(text="Jan Jansen", label="PER", start=0, end=5)  # text too long for span


class TestSentenceEntityPreprocessor:
    """Test SentenceEntityPreprocessor class."""
    
    @pytest.fixture
    def preprocessor(self):
        """Create preprocessor instance with mocked tokenizer."""
        with patch('src.data.preprocessing.AutoTokenizer') as mock_tokenizer_class:
            # Mock tokenizer instance
            mock_tokenizer = Mock()
            mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
            
            # Configure tokenizer methods
            mock_tokenizer.return_value = {
                "input_ids": [0, 2335, 8554, 2],  # <s> Jan Jansen </s>
                "attention_mask": [1, 1, 1, 1],
                "offset_mapping": [(0, 0), (0, 3), (4, 10), (0, 0)]  # Special, Jan, Jansen, Special
            }
            mock_tokenizer.convert_ids_to_tokens.return_value = ["<s>", "ĠJan", "ĠJansen", "</s>"]
            
            preprocessor = SentenceEntityPreprocessor("test-model")
            return preprocessor
    
    def test_initialization(self, preprocessor):
        """Test preprocessor initialization."""
        assert preprocessor.model_name == "test-model"
        assert preprocessor.label_list == ["O", "B-PER", "I-PER"]
        assert preprocessor.label2id == {"O": 0, "B-PER": 1, "I-PER": 2}
        assert preprocessor.id2label == {0: "O", 1: "B-PER", 2: "I-PER"}
    
    def test_validate_span_consistency_exact_match(self, preprocessor):
        """Test span consistency validation with exact match."""
        sentence = "Jan Jansen woont in Amsterdam."
        result = preprocessor._validate_span_consistency(sentence, "Jan Jansen", 0, 10)
        assert result is True
    
    def test_validate_span_consistency_case_insensitive(self, preprocessor):
        """Test span consistency validation with case differences."""
        sentence = "JAN JANSEN woont in Amsterdam."
        result = preprocessor._validate_span_consistency(sentence, "jan jansen", 0, 10)
        assert result is True
    
    def test_validate_span_consistency_whitespace_normalized(self, preprocessor):
        """Test span consistency validation with whitespace differences."""
        sentence = " Jan Jansen  woont in Amsterdam."
        result = preprocessor._validate_span_consistency(sentence, "Jan Jansen", 1, 11)
        assert result is True
    
    def test_validate_span_consistency_invalid(self, preprocessor):
        """Test span consistency validation with invalid spans."""
        sentence = "Jan Jansen woont in Amsterdam."
        
        # Wrong text
        result = preprocessor._validate_span_consistency(sentence, "Wrong Name", 0, 10)
        assert result is False
        
        # Out of bounds
        result = preprocessor._validate_span_consistency(sentence, "Jan", -1, 3)
        assert result is False
        
        result = preprocessor._validate_span_consistency(sentence, "Jan", 0, 100)
        assert result is False
    
    def test_auto_detect_span_exact_match(self, preprocessor):
        """Test auto-detection with exact match."""
        sentence = "Jan Jansen woont in Amsterdam."
        span = preprocessor._auto_detect_span(sentence, "Jan Jansen", "PER")
        
        assert span is not None
        assert span.text == "Jan Jansen"
        assert span.label == "PER"
        assert span.start == 0
        assert span.end == 10
    
    def test_auto_detect_span_case_insensitive(self, preprocessor):
        """Test auto-detection with case differences."""
        sentence = "JAN JANSEN woont in Amsterdam."
        span = preprocessor._auto_detect_span(sentence, "jan jansen", "PER")
        
        assert span is not None
        assert span.text == "JAN JANSEN"  # Should use actual text from sentence
        assert span.label == "PER"
        assert span.start == 0
        assert span.end == 10
    
    def test_auto_detect_span_word_boundaries(self, preprocessor):
        """Test auto-detection with word boundaries."""
        sentence = "De heer Jan woont hier."
        span = preprocessor._auto_detect_span(sentence, "Jan", "PER")
        
        assert span is not None
        assert span.text == "Jan"
        assert span.label == "PER"
        assert span.start == 8
        assert span.end == 11
    
    def test_auto_detect_span_not_found(self, preprocessor):
        """Test auto-detection when entity is not found."""
        sentence = "Jan Jansen woont in Amsterdam."
        span = preprocessor._auto_detect_span(sentence, "Piet Pietersen", "PER")
        
        assert span is None
    
    def test_auto_detect_span_special_characters(self, preprocessor):
        """Test auto-detection with special regex characters."""
        sentence = "Mr. (Jan) woont hier."
        span = preprocessor._auto_detect_span(sentence, "(Jan)", "PER")
        
        assert span is not None
        assert span.text == "(Jan)"
        assert span.label == "PER"
    
    def test_get_entity_spans_explicit_spans(self, preprocessor):
        """Test getting entity spans with explicit start/end positions."""
        sentence = "Jan Jansen woont in Amsterdam."
        entities = [
            {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10},
            {"text": "Amsterdam", "label": "LOC", "start": 20, "end": 29}
        ]
        
        spans = preprocessor.get_entity_spans(sentence, entities)
        
        assert len(spans) == 2
        assert spans[0].text == "Jan Jansen"
        assert spans[0].label == "PER"
        assert spans[0].start == 0
        assert spans[0].end == 10
        
        assert spans[1].text == "Amsterdam"
        assert spans[1].label == "LOC"
        assert spans[1].start == 20
        assert spans[1].end == 29
    
    def test_get_entity_spans_auto_detection(self, preprocessor):
        """Test getting entity spans with auto-detection."""
        sentence = "Jan Jansen woont in Amsterdam."
        entities = [
            {"text": "Jan Jansen", "label": "PER"},
            {"text": "Amsterdam", "label": "LOC"}
        ]
        
        spans = preprocessor.get_entity_spans(sentence, entities)
        
        assert len(spans) == 2
        assert spans[0].text == "Jan Jansen"
        assert spans[0].start == 0
        assert spans[0].end == 10
        
        assert spans[1].text == "Amsterdam"
        assert spans[1].start == 20
        assert spans[1].end == 29
    
    def test_get_entity_spans_mixed_modes(self, preprocessor):
        """Test getting entity spans with mixed explicit and auto-detection."""
        sentence = "Jan Jansen woont in Amsterdam."
        entities = [
            {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10},  # Explicit
            {"text": "Amsterdam", "label": "LOC"}  # Auto-detect
        ]
        
        spans = preprocessor.get_entity_spans(sentence, entities)
        
        assert len(spans) == 2
        assert spans[0].start == 0  # Explicit
        assert spans[1].start == 20  # Auto-detected
    
    def test_get_entity_spans_inconsistent_explicit(self, preprocessor):
        """Test handling of inconsistent explicit spans with fallback to auto-detection."""
        sentence = "Jan Jansen woont in Amsterdam."
        entities = [
            {"text": "Jan Jansen", "label": "PER", "start": 5, "end": 10}  # Wrong span
        ]
        
        spans = preprocessor.get_entity_spans(sentence, entities)
        
        # Should fall back to auto-detection
        assert len(spans) == 1
        assert spans[0].start == 0  # Correct auto-detected position
        assert spans[0].end == 10
    
    def test_get_entity_spans_empty_text(self, preprocessor):
        """Test handling of empty entity text."""
        sentence = "Jan Jansen woont in Amsterdam."
        entities = [
            {"text": "", "label": "PER"},
            {"text": "Jan Jansen", "label": "PER"}
        ]
        
        spans = preprocessor.get_entity_spans(sentence, entities)
        
        # Should skip empty text and process valid entity
        assert len(spans) == 1
        assert spans[0].text == "Jan Jansen"
    
    def test_get_entity_spans_not_found(self, preprocessor):
        """Test handling of entities not found in sentence."""
        sentence = "Jan Jansen woont in Amsterdam."
        entities = [
            {"text": "Piet Pietersen", "label": "PER"},  # Not in sentence
            {"text": "Jan Jansen", "label": "PER"}  # In sentence
        ]
        
        spans = preprocessor.get_entity_spans(sentence, entities)
        
        # Should only find the valid entity
        assert len(spans) == 1
        assert spans[0].text == "Jan Jansen"


class TestDutchTextExamples:
    """Test preprocessing with various Dutch text examples."""
    
    @pytest.fixture
    def real_preprocessor(self):
        """Create preprocessor with real tokenizer for integration tests."""
        # Skip if transformers not available
        pytest.importorskip("transformers")
        
        try:
            return SentenceEntityPreprocessor("DTAI-KULeuven/robbert-2023-dutch-base")
        except Exception:
            pytest.skip("RobBERT model not available")
    
    def test_dutch_compound_names(self, real_preprocessor):
        """Test Dutch compound names with particles."""
        examples = [
            {
                "sentence": "Mevrouw van der Laan woont hier.",
                "entities": [{"text": "van der Laan", "label": "PER"}]
            },
            {
                "sentence": "Jan de Wit en Marie van den Berg.",
                "entities": [
                    {"text": "Jan de Wit", "label": "PER"},
                    {"text": "Marie van den Berg", "label": "PER"}
                ]
            }
        ]
        
        for example in examples:
            processed = real_preprocessor.preprocess_example(example)
            
            # Should have valid tokens and labels
            assert len(processed.tokens) > 0
            assert len(processed.labels) == len(processed.tokens)
            assert len(processed.input_ids) == len(processed.tokens)
            
            # Should have B-PER and I-PER labels for entities
            has_b_per = any(label == "B-PER" for label in processed.labels)
            assert has_b_per, f"No B-PER found in labels: {processed.labels}"
    
    def test_dutch_diacritics(self, real_preprocessor):
        """Test Dutch names with diacritics."""
        examples = [
            {
                "sentence": "François Müller woont in Zürich.",
                "entities": [
                    {"text": "François Müller", "label": "PER"},
                    {"text": "Zürich", "label": "LOC"}
                ]
            },
            {
                "sentence": "José van Dijk en Björn Andersson.",
                "entities": [
                    {"text": "José van Dijk", "label": "PER"},
                    {"text": "Björn Andersson", "label": "PER"}
                ]
            }
        ]
        
        for example in examples:
            processed = real_preprocessor.preprocess_example(example)
            
            # Should handle diacritics correctly
            assert len(processed.entity_spans) > 0
            assert all(span.text in example["sentence"] for span in processed.entity_spans)
    
    def test_dutch_formal_titles(self, real_preprocessor):
        """Test Dutch formal titles and prefixes."""
        examples = [
            {
                "sentence": "Dhr. J. van der Berg en Mevr. A. de Vries.",
                "entities": [
                    {"text": "J. van der Berg", "label": "PER"},
                    {"text": "A. de Vries", "label": "PER"}
                ]
            },
            {
                "sentence": "Prof. dr. Willem Jansen spreekt vandaag.",
                "entities": [{"text": "Willem Jansen", "label": "PER"}]
            }
        ]
        
        for example in examples:
            processed = real_preprocessor.preprocess_example(example)
            
            # Should correctly identify names without titles
            assert len(processed.entity_spans) > 0
            for span in processed.entity_spans:
                # Names should not include titles
                assert not span.text.startswith(("Dhr.", "Mevr.", "Prof.", "dr."))
    
    def test_dutch_hyphenated_names(self, real_preprocessor):
        """Test Dutch hyphenated names."""
        examples = [
            {
                "sentence": "Jan-Willem van der Berg-Smit woont hier.",
                "entities": [{"text": "Jan-Willem van der Berg-Smit", "label": "PER"}]
            },
            {
                "sentence": "Marie-Claire de Jong en Anne-Sophie Müller.",
                "entities": [
                    {"text": "Marie-Claire de Jong", "label": "PER"},
                    {"text": "Anne-Sophie Müller", "label": "PER"}
                ]
            }
        ]
        
        for example in examples:
            processed = real_preprocessor.preprocess_example(example)
            
            # Should handle hyphenated names correctly
            assert len(processed.entity_spans) > 0
            for span in processed.entity_spans:
                assert "-" in span.text  # Should preserve hyphens
    
    def test_dutch_locations(self, real_preprocessor):
        """Test Dutch location names."""
        examples = [
            {
                "sentence": "Jan woont in Amsterdam en werkt in Den Haag.",
                "entities": [
                    {"text": "Jan", "label": "PER"},
                    {"text": "Amsterdam", "label": "LOC"},
                    {"text": "Den Haag", "label": "LOC"}
                ]
            },
            {
                "sentence": "Van Utrecht naar 's-Hertogenbosch.",
                "entities": [
                    {"text": "Utrecht", "label": "LOC"},
                    {"text": "'s-Hertogenbosch", "label": "LOC"}
                ]
            }
        ]
        
        for example in examples:
            processed = real_preprocessor.preprocess_example(example)
            
            # Should handle location names correctly
            assert len(processed.entity_spans) > 0
            loc_spans = [span for span in processed.entity_spans if span.label == "LOC"]
            assert len(loc_spans) > 0
    
    def test_edge_cases(self, real_preprocessor):
        """Test edge cases and challenging examples."""
        examples = [
            # Single character names
            {
                "sentence": "A. B. de Vries en C. van Dijk.",
                "entities": [
                    {"text": "A. B. de Vries", "label": "PER"},
                    {"text": "C. van Dijk", "label": "PER"}
                ]
            },
            # Names at sentence boundaries
            {
                "sentence": "Jan. Dat is alles.",
                "entities": [{"text": "Jan", "label": "PER"}]
            },
            # Multiple spaces
            {
                "sentence": "Jan  Jansen   woont    hier.",
                "entities": [{"text": "Jan Jansen", "label": "PER"}]
            }
        ]
        
        for example in examples:
            processed = real_preprocessor.preprocess_example(example)
            
            # Should handle edge cases without errors
            assert isinstance(processed, PreprocessedExample)
            assert len(processed.tokens) > 0
            assert len(processed.labels) == len(processed.tokens)


class TestPreprocessRawData:
    """Test the main preprocessing function."""
    
    def test_preprocess_json_file(self):
        """Test preprocessing a JSON file."""
        # Create temporary input file
        input_data = [
            {
                "id": 1,
                "sentence": "Jan Jansen woont in Amsterdam.",
                "entities": [
                    {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10}
                ]
            },
            {
                "id": 2,
                "sentence": "Marie de Vries werkt hier.",
                "entities": [
                    {"text": "Marie de Vries", "label": "PER"}
                ]
            }
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(input_data, f)
            input_path = f.name
        
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                output_path = f.name
            
            # Mock the tokenizer to avoid downloading
            with patch('src.data.preprocessing.AutoTokenizer') as mock_tokenizer_class:
                mock_tokenizer = Mock()
                mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
                
                # Configure tokenizer for first example
                mock_tokenizer.side_effect = [
                    {
                        "input_ids": [0, 2335, 8554, 23, 4, 812, 5, 2],
                        "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1],
                        "offset_mapping": [(0, 0), (0, 3), (4, 10), (11, 16), (17, 19), (20, 29), (29, 30), (0, 0)]
                    },
                    {
                        "input_ids": [0, 1234, 567, 890, 23, 45, 5, 2],
                        "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1],
                        "offset_mapping": [(0, 0), (0, 5), (6, 8), (9, 14), (15, 20), (21, 25), (25, 26), (0, 0)]
                    }
                ]
                
                mock_tokenizer.convert_ids_to_tokens.side_effect = [
                    ["<s>", "ĠJan", "ĠJansen", "Ġwoont", "Ġin", "ĠAmsterdam", ".", "</s>"],
                    ["<s>", "ĠMarie", "Ġde", "ĠVries", "Ġwerkt", "Ġhier", ".", "</s>"]
                ]
                
                # Process the data
                result_path = preprocess_raw_data(input_path, output_path)
                
                assert result_path == output_path
                
                # Check output file exists and has correct format
                with open(output_path, 'r', encoding='utf-8') as f:
                    output_data = json.load(f)
                
                assert len(output_data) == 2
                
                # Check first example
                example1 = output_data[0]
                assert example1["id"] == 1
                assert example1["sentence"] == "Jan Jansen woont in Amsterdam."
                assert "tokens" in example1
                assert "labels" in example1
                assert "input_ids" in example1
                assert "attention_mask" in example1
                
                # Check that labels align with tokens
                assert len(example1["tokens"]) == len(example1["labels"])
                assert len(example1["tokens"]) == len(example1["input_ids"])
                assert len(example1["tokens"]) == len(example1["attention_mask"])
        
        finally:
            # Clean up temporary files
            Path(input_path).unlink(missing_ok=True)
            Path(output_path).unlink(missing_ok=True)
    
    def test_preprocess_jsonl_file(self):
        """Test preprocessing a JSONL file."""
        input_data = [
            {"id": 1, "sentence": "Jan woont hier.", "entities": [{"text": "Jan", "label": "PER"}]},
            {"id": 2, "sentence": "Marie werkt daar.", "entities": [{"text": "Marie", "label": "PER"}]}
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
            for item in input_data:
                f.write(json.dumps(item) + '\n')
            input_path = f.name
        
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                output_path = f.name
            
            # Mock the tokenizer
            with patch('src.data.preprocessing.AutoTokenizer') as mock_tokenizer_class:
                mock_tokenizer = Mock()
                mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
                
                mock_tokenizer.side_effect = [
                    {
                        "input_ids": [0, 2335, 23, 45, 5, 2],
                        "attention_mask": [1, 1, 1, 1, 1, 1],
                        "offset_mapping": [(0, 0), (0, 3), (4, 9), (10, 14), (14, 15), (0, 0)]
                    },
                    {
                        "input_ids": [0, 1234, 567, 890, 5, 2],
                        "attention_mask": [1, 1, 1, 1, 1, 1],
                        "offset_mapping": [(0, 0), (0, 5), (6, 11), (12, 16), (16, 17), (0, 0)]
                    }
                ]
                
                mock_tokenizer.convert_ids_to_tokens.side_effect = [
                    ["<s>", "ĠJan", "Ġwoont", "Ġhier", ".", "</s>"],
                    ["<s>", "ĠMarie", "Ġwerkt", "Ġdaar", ".", "</s>"]
                ]
                
                result_path = preprocess_raw_data(input_path, output_path)
                
                assert result_path == output_path
                
                # Check output
                with open(output_path, 'r', encoding='utf-8') as f:
                    output_data = json.load(f)
                
                assert len(output_data) == 2
        
        finally:
            Path(input_path).unlink(missing_ok=True)
            Path(output_path).unlink(missing_ok=True)
    
    def test_preprocess_file_not_found(self):
        """Test handling of missing input file."""
        with pytest.raises(RuntimeError, match="Failed to preprocess raw data"):
            preprocess_raw_data("nonexistent.json", "output.json")
    
    def test_preprocess_invalid_json(self):
        """Test handling of invalid JSON in input file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
            f.write('{"valid": "json"}\n')
            f.write('invalid json line\n')  # Invalid JSON
            f.write('{"another": "valid"}\n')
            input_path = f.name
        
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                output_path = f.name
            
            # Mock the tokenizer
            with patch('src.data.preprocessing.AutoTokenizer') as mock_tokenizer_class:
                mock_tokenizer = Mock()
                mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
                
                mock_tokenizer.return_value = {
                    "input_ids": [0, 1, 2],
                    "attention_mask": [1, 1, 1],
                    "offset_mapping": [(0, 0), (0, 1), (0, 0)]
                }
                mock_tokenizer.convert_ids_to_tokens.return_value = ["<s>", "test", "</s>"]
                
                # Should process valid lines and skip invalid ones
                result_path = preprocess_raw_data(input_path, output_path)
                
                with open(output_path, 'r', encoding='utf-8') as f:
                    output_data = json.load(f)
                
                # Should have processed 2 valid examples (invalid line skipped)
                assert len(output_data) >= 0  # At least doesn't crash
        
        finally:
            Path(input_path).unlink(missing_ok=True)
            Path(output_path).unlink(missing_ok=True)


if __name__ == "__main__":
    pytest.main([__file__])