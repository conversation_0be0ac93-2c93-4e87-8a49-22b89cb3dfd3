#!/usr/bin/env python3
"""
Dutch NER Class Weights Example.

This example demonstrates how to use class weights for training
imbalanced Dutch NER datasets with the Hugging Face Trainer integration.
"""

import sys
import json
from pathlib import Path
from typing import Dict, List, Any

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from datasets import Dataset, DatasetDict
from src.training.class_weights import ClassWeightCalculator, compute_per_head_class_weights
from src.training.hf_config import HFTrainingConfig


def create_realistic_dutch_ner_dataset() -> List[Dict[str, Any]]:
    """
    Create a realistic Dutch NER dataset with typical imbalance patterns.
    
    In real Dutch NER datasets:
    - O labels: ~85-90% (very common)
    - PER labels: ~5-8% (moderately common)
    - LOC labels: ~2-4% (less common)
    - ORG labels: ~1-2% (rare)
    """
    
    examples = []
    
    # Simulate realistic Dutch sentences with entities
    dutch_sentences = [
        # Many sentences without entities (realistic scenario)
        {
            'sentence': "De vergadering begint om negen uur 's ochtends.",
            'entities': [],
            'labels': [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]  # All O
        },
        {
            'sentence': "Het weer is vandaag erg mooi en zonnig.",
            'entities': [],
            'labels': [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        },
        {
            'sentence': "We gaan morgen naar de winkel voor boodschappen.",
            'entities': [],
            'labels': [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        },
        {
            'sentence': "De kinderen spelen buiten in de tuin.",
            'entities': [],
            'labels': [0, 0, 0, 0, 0, 0, 0, 0]
        },
        {
            'sentence': "Het boek ligt op de tafel naast de lamp.",
            'entities': [],
            'labels': [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        },
        
        # Sentences with PER entities (moderately common)
        {
            'sentence': "Jan Jansen werkt bij een groot bedrijf in de stad.",
            'entities': [{'text': 'Jan Jansen', 'label': 'PER', 'start': 0, 'end': 10}],
            'labels': [1, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0]  # B-PER, I-PER, O, ...
        },
        {
            'sentence': "Marie van der Berg is een bekende Nederlandse schrijfster.",
            'entities': [{'text': 'Marie van der Berg', 'label': 'PER', 'start': 0, 'end': 18}],
            'labels': [1, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0]  # B-PER, I-PER, I-PER, I-PER, O, ...
        },
        {
            'sentence': "Piet de Vries heeft gisteren een nieuwe auto gekocht.",
            'entities': [{'text': 'Piet de Vries', 'label': 'PER', 'start': 0, 'end': 13}],
            'labels': [1, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0]  # B-PER, I-PER, I-PER, O, ...
        },
        {
            'sentence': "Anna Bakker woont al tien jaar in deze buurt.",
            'entities': [{'text': 'Anna Bakker', 'label': 'PER', 'start': 0, 'end': 11}],
            'labels': [1, 2, 0, 0, 0, 0, 0, 0, 0, 0]  # B-PER, I-PER, O, ...
        },
        
        # Sentences with LOC entities (less common)
        {
            'sentence': "Amsterdam is de hoofdstad van Nederland en zeer populair.",
            'entities': [
                {'text': 'Amsterdam', 'label': 'LOC', 'start': 0, 'end': 9},
                {'text': 'Nederland', 'label': 'LOC', 'start': 31, 'end': 40}
            ],
            'labels': [3, 0, 0, 0, 0, 3, 0, 0, 0, 0]  # B-LOC, O, O, O, O, B-LOC, O, ...
        },
        {
            'sentence': "Rotterdam heeft de grootste haven van Europa.",
            'entities': [
                {'text': 'Rotterdam', 'label': 'LOC', 'start': 0, 'end': 9},
                {'text': 'Europa', 'label': 'LOC', 'start': 39, 'end': 45}
            ],
            'labels': [3, 0, 0, 0, 0, 0, 3, 0]  # B-LOC, O, O, O, O, O, B-LOC, O
        },
        {
            'sentence': "Utrecht ligt in het midden van het land.",
            'entities': [{'text': 'Utrecht', 'label': 'LOC', 'start': 0, 'end': 7}],
            'labels': [3, 0, 0, 0, 0, 0, 0, 0, 0]  # B-LOC, O, ...
        },
        
        # Sentences with ORG entities (rare)
        {
            'sentence': "KLM is de nationale luchtvaartmaatschappij van Nederland.",
            'entities': [
                {'text': 'KLM', 'label': 'ORG', 'start': 0, 'end': 3},
                {'text': 'Nederland', 'label': 'LOC', 'start': 48, 'end': 57}
            ],
            'labels': [5, 0, 0, 0, 0, 0, 3, 0]  # B-ORG, O, O, O, O, O, B-LOC, O
        },
        {
            'sentence': "Philips heeft zijn hoofdkantoor in Eindhoven.",
            'entities': [
                {'text': 'Philips', 'label': 'ORG', 'start': 0, 'end': 7},
                {'text': 'Eindhoven', 'label': 'LOC', 'start': 36, 'end': 45}
            ],
            'labels': [5, 0, 0, 0, 0, 3, 0]  # B-ORG, O, O, O, O, B-LOC, O
        },
        
        # More sentences without entities to maintain realistic imbalance
        {
            'sentence': "De trein vertrekt precies op tijd vanaf het perron.",
            'entities': [],
            'labels': [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        },
        {
            'sentence': "Het restaurant is vandaag gesloten wegens renovatie.",
            'entities': [],
            'labels': [0, 0, 0, 0, 0, 0, 0, 0]
        },
        {
            'sentence': "De auto staat geparkeerd voor het grote gebouw.",
            'entities': [],
            'labels': [0, 0, 0, 0, 0, 0, 0, 0, 0]
        },
        {
            'sentence': "We hebben gisteren een interessante film gekeken.",
            'entities': [],
            'labels': [0, 0, 0, 0, 0, 0, 0, 0]
        },
        {
            'sentence': "De kat slaapt rustig op de warme vensterbank.",
            'entities': [],
            'labels': [0, 0, 0, 0, 0, 0, 0, 0, 0]
        }
    ]
    
    # Add IDs to examples
    for i, example in enumerate(dutch_sentences):
        example['id'] = i
        examples.append(example)
    
    return examples


def demonstrate_dutch_ner_class_weights():
    """Demonstrate class weight calculation for Dutch NER data."""
    
    print("🇳🇱 Dutch NER Class Weights Example")
    print("=" * 50)
    
    # Create realistic Dutch NER dataset
    print("\n📊 Creating realistic Dutch NER dataset...")
    dutch_data = create_realistic_dutch_ner_dataset()
    dataset = Dataset.from_list(dutch_data)
    
    # Define Dutch NER label scheme
    label_list = ["O", "B-PER", "I-PER", "B-LOC", "I-LOC", "B-ORG", "I-ORG"]
    
    print(f"Dataset size: {len(dataset)} examples")
    print(f"Label scheme: {label_list}")
    
    # Split dataset
    split_dataset = dataset.train_test_split(test_size=0.2, seed=42)
    dataset_dict = DatasetDict({
        'train': split_dataset['train'],
        'validation': split_dataset['test']
    })
    
    print(f"Train size: {len(dataset_dict['train'])}")
    print(f"Validation size: {len(dataset_dict['validation'])}")
    
    # Initialize calculator
    calculator = ClassWeightCalculator()
    
    # 1. Analyze Dutch NER imbalance patterns
    print("\n📈 Analyzing Dutch NER label distribution...")
    analysis = calculator.analyze_label_distribution(dataset_dict, label_list)
    
    print(f"Total labels: {analysis['total_labels']}")
    print(f"Imbalance ratio: {analysis['imbalance_ratio']:.2f}")
    print("\nDutch NER label distribution:")
    for label, count in analysis['label_counts'].items():
        percentage = analysis['label_percentages'][label]
        print(f"  {label}: {count:3d} ({percentage:5.1f}%)")
    
    # 2. Compute per-head class weights for Dutch entities
    print("\n🎯 Computing per-head class weights for Dutch entities...")
    per_head_weights = calculator.compute_per_head_class_weights(
        dataset=dataset_dict,
        label_list=label_list,
        method="balanced"
    )
    
    print("\nPer-head weights for Dutch entity types:")
    for entity_type in ["PER", "LOC", "ORG"]:
        if entity_type in per_head_weights:
            print(f"\n{entity_type} (Dutch {entity_type.lower()} entities):")
            weights = per_head_weights[entity_type]
            for label, weight in weights.items():
                print(f"  {label}: {weight:8.4f}")
    
    # 3. Analyze per-entity imbalance
    print("\n🔍 Per-entity imbalance analysis:")
    entity_analyses = {}
    for entity_type in ["PER", "LOC", "ORG"]:
        analysis = calculator.analyze_label_distribution(
            dataset_dict, label_list, entity_type=entity_type
        )
        entity_analyses[entity_type] = analysis
        
        print(f"\n{entity_type} entities:")
        print(f"  Imbalance ratio: {analysis['imbalance_ratio']:.2f}")
        print("  Distribution:")
        for label, count in analysis['label_counts'].items():
            percentage = analysis['label_percentages'][label]
            print(f"    {label}: {count:3d} ({percentage:5.1f}%)")
    
    # 4. Create training configuration with class weights
    print("\n⚙️ Creating training configuration with class weights...")
    config = HFTrainingConfig(
        # Model settings
        model_name="DTAI-KULeuven/robbert-2023-dutch-base",
        
        # Training settings
        epochs=3,
        batch_size=8,
        learning_rate=2e-5,
        
        # Class weight settings
        use_class_weights=True,
        class_weight_method="balanced",
        per_head_class_weights=True,
        class_weight_entity_types=["PER", "LOC", "ORG"],
        save_class_weights=True,
        
        # WandB settings
        wandb_project="dutch-ner-class-weights",
        run_name="imbalanced-dutch-ner",
        wandb_tags=["dutch-nlp", "ner", "class-weights", "robbert-2023"],
        
        # Output settings
        output_dir="checkpoints/dutch_ner_class_weights",
        save_predictions=True,
        generate_model_card=True
    )
    
    print("Configuration created with class weights enabled:")
    print(f"  Use class weights: {config.use_class_weights}")
    print(f"  Method: {config.class_weight_method}")
    print(f"  Per-head weights: {config.per_head_class_weights}")
    print(f"  Entity types: {config.class_weight_entity_types}")
    
    # 5. Compute weights using configuration
    print("\n⚖️ Computing class weights via configuration...")
    
    # Create output directory
    output_dir = Path("examples/output/dutch_ner")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Compute overall weights
    overall_weights = config.compute_and_set_class_weights(
        dataset=dataset_dict,
        label_list=label_list,
        output_dir=str(output_dir)
    )
    
    # Compute per-head weights
    per_head_computed = config.compute_per_head_class_weights(
        dataset=dataset_dict,
        label_list=label_list,
        output_dir=str(output_dir)
    )
    
    print("✅ Class weights computed and saved")
    
    # 6. Demonstrate effectiveness for Dutch NER
    print("\n📊 Class weight effectiveness for Dutch NER:")
    
    print("\nWithout class weights:")
    print("  - O labels dominate (85-90% of tokens)")
    print("  - Dutch person names get insufficient attention")
    print("  - Location names (Amsterdam, Rotterdam) underrepresented")
    print("  - Organization names (KLM, Philips) severely underrepresented")
    print("  - Model biased toward predicting O for everything")
    
    print("\nWith balanced class weights:")
    print("  - Rare Dutch entities get proportionally higher importance")
    print("  - Person names (Jan Jansen, Marie van der Berg) better detected")
    print("  - Dutch locations (Amsterdam, Utrecht) properly weighted")
    print("  - Organizations (KLM, Philips) get maximum attention")
    print("  - Better balance between precision and recall")
    
    # Show specific weight ratios
    if overall_weights:
        o_weight = overall_weights.get("O", 1.0)
        per_weight = overall_weights.get("B-PER", 1.0)
        loc_weight = overall_weights.get("B-LOC", 1.0)
        org_weight = overall_weights.get("B-ORG", 1.0)
        
        print(f"\nDutch NER weight ratios:")
        print(f"  Person names are {per_weight/o_weight:.1f}x more important than O")
        print(f"  Location names are {loc_weight/o_weight:.1f}x more important than O")
        print(f"  Organization names are {org_weight/o_weight:.1f}x more important than O")
    
    # 7. Training recommendations
    print("\n💡 Training recommendations for Dutch NER:")
    print("  1. Use per-head class weights for multi-entity training")
    print("  2. Monitor per-entity F1 scores during training")
    print("  3. Consider data augmentation for rare entities (ORG)")
    print("  4. Use early stopping based on macro-averaged F1")
    print("  5. Validate on balanced test set with all entity types")
    
    # 8. Save example configuration
    config_path = output_dir / "dutch_ner_config.yaml"
    config.save_yaml(str(config_path))
    print(f"\n💾 Example configuration saved to: {config_path}")
    
    print("\n✨ Dutch NER class weights example completed!")
    
    return config, overall_weights, per_head_computed


def show_training_example():
    """Show how to use the computed weights in actual training."""
    
    print("\n🚀 Training Integration Example")
    print("=" * 40)
    
    print("""
# Example training script with class weights:

from src.training.hf_config import HFTrainingConfig
from src.data.hf_dataset_preparation import prepare_ner_dataset
from src.training.hf_trainer import train_ner_model

# 1. Load configuration with class weights
config = HFTrainingConfig.from_yaml('dutch_ner_config.yaml')

# 2. Prepare dataset
dataset_dict, label_list, label2id, id2label, stats = prepare_ner_dataset(
    data_path='data/dutch_ner.json',
    model_name=config.model_name,
    train_split=0.8
)

# 3. Compute class weights automatically
if config.use_class_weights:
    if config.per_head_class_weights:
        # For per-head training
        per_head_weights = config.compute_per_head_class_weights(
            dataset=dataset_dict,
            label_list=label_list,
            output_dir=config.output_dir
        )
        
        # Train each head separately with its specific weights
        for entity_type, weights in per_head_weights.items():
            print(f"Training {entity_type} head with weights: {weights}")
            
            # Filter dataset for this entity type
            # Train with computed weights
            
    else:
        # For joint training
        class_weights = config.compute_and_set_class_weights(
            dataset=dataset_dict,
            label_list=label_list,
            output_dir=config.output_dir
        )
        
        # Train with overall weights
        print(f"Training with class weights: {class_weights}")

# 4. Train model with weights
model, tokenizer, trainer = train_ner_model(
    dataset_dict=dataset_dict,
    label_list=label_list,
    config=config
)

print("Training completed with class weights!")
""")


if __name__ == "__main__":
    try:
        # Run the main demonstration
        config, overall_weights, per_head_weights = demonstrate_dutch_ner_class_weights()
        
        # Show training integration
        show_training_example()
        
    except Exception as e:
        print(f"\n❌ Error during example: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)