# RobBERT-2023 Dutch NER Project Structure

## Overview
This project implements Person Named Entity Recognition for Dutch text using RobBERT-2023-dutch-base model.

## Directory Structure

```
bertjeNER/
├── src/                          # Source code
│   ├── config/                   # Configuration management
│   │   ├── __init__.py          # Config loading and validation
│   │   ├── default.yaml         # Main configuration
│   │   ├── wandb.yaml           # WandB experiment tracking config
│   │   └── heads/               # Head-specific configurations
│   │       └── ner.yaml         # Person NER head configuration
│   ├── data/                    # Data processing utilities
│   │   ├── dataset_builder.py  # Dataset construction
│   │   └── tokenizer_utils.py   # RobBERT tokenizer with alignment
│   ├── heads/                   # Task-specific model heads
│   │   └── ner_head.py         # Person NER head implementation
│   ├── inference/               # Inference interfaces
│   │   ├── api_fastapi.py      # FastAPI REST server
│   │   └── cli_batch.py        # Batch processing CLI
│   ├── models/                  # Model definitions
│   │   ├── __init__.py
│   │   ├── multitask_robbert.py # Main RobBERT-2023 model
│   │   └── checkpoints/         # Training checkpoints
│   ├── training/                # Training pipeline
│   │   └── train_multitask.py  # Multi-head training script
│   ├── utils/                   # Utility modules
│   │   ├── checkpoint_manager.py # Model checkpoint management
│   │   ├── head_config_loader.py # Head configuration loading
│   │   ├── logging_utils.py     # Logging utilities
│   │   └── wandb_logger.py      # WandB integration
│   └── exceptions.py            # Custom exception classes
├── scripts/                     # Utility scripts
│   ├── checkpoint_management.py # Checkpoint management CLI
│   ├── enhanced_ner_test.py    # Enhanced NER testing
│   ├── oov_analysis_and_tokenization_verification.py # Tokenization analysis
│   ├── performance_baseline.py # Performance benchmarking
│   ├── robbert_sanity_check.py # Model validation
│   ├── smoke_test.py           # Basic functionality test
│   ├── test_end_to_end_pipeline.py # End-to-end testing
│   ├── test_enhanced_logging.py # Logging system test
│   ├── test_performance_baseline.py # Performance testing
│   ├── test_robbert_tokenizer.py # Tokenizer testing
│   ├── train_with_wandb.py     # WandB training examples
│   ├── validate_robbert_environment.py # Environment validation
│   └── validate_wandb_config.py # WandB configuration validation
├── tests/                       # Test suite
│   ├── conftest.py             # Pytest configuration
│   ├── test_api_robbert.py     # API testing
│   ├── test_api_startup.py     # API startup testing
│   ├── test_checkpoint_manager.py # Checkpoint management tests
│   ├── test_compound_names.py  # Compound name handling tests
│   ├── test_config.py          # Configuration tests
│   ├── test_end_to_end_pipeline.py # Pipeline integration tests
│   ├── test_error_handling.py  # Error handling tests
│   ├── test_inference.py       # Inference tests
│   ├── test_logging.py         # Logging tests
│   ├── test_models.py          # Model tests
│   ├── test_oov_analysis.py    # OOV analysis tests
│   ├── test_robbert_tokenizer.py # Tokenizer tests
│   ├── test_tokenization_analysis.py # Tokenization analysis tests
│   └── test_training_robbert.py # Training tests
├── models/                      # Model artifacts
│   ├── README.md               # Model documentation
│   └── robbert2023-per/        # Person NER model checkpoints
│       └── README.md           # Model-specific documentation
├── docs/                        # Documentation
│   ├── checkpoint_management.md # Checkpoint management guide
│   ├── end_to_end_pipeline_validation.md # Pipeline validation
│   ├── performance_baseline.md # Performance documentation
│   ├── README_API_ROBBERT.md   # API documentation
│   ├── README_WANDB_INTEGRATION.md # WandB integration guide
│   └── robbert_tokenization_guide.md # Tokenization guide
├── data_sets/                   # Training and test data
├── process_data/                # Data processing workspace
├── notebooks/                   # Jupyter notebooks
├── .kiro/                       # Kiro IDE configuration
├── requirements.txt             # Python dependencies
├── README.md                    # Main project documentation
└── PROJECT_STRUCTURE.md         # This file
```

## Key Components

### Core Model (`src/models/multitask_robbert.py`)
- RobBERT-2023-dutch-base implementation
- Multi-head architecture supporting extensible task heads
- Optimized for Person entity recognition

### Tokenizer (`src/data/tokenizer_utils.py`)
- RobBERT tokenizer with label alignment
- Byte-level BPE tokenization for better Dutch text handling
- Proper subword-to-label alignment for NER tasks

### Training Pipeline (`src/training/train_multitask.py`)
- Multi-head training with WandB integration
- Head-specific configuration support
- Checkpoint management and model artifacts

### API Server (`src/inference/api_fastapi.py`)
- FastAPI-based REST API
- Person entity recognition endpoints
- Production-ready inference serving

### Configuration System
- YAML-based configuration with environment variable support
- Head-specific configurations for extensibility
- WandB experiment tracking configuration

## Usage

### Training
```bash
python -m src.training.train_multitask --heads ner --use-head-configs --wandb-config src/config/wandb.yaml
```

### API Server
```bash
uvicorn src.inference.api_fastapi:app --host 0.0.0.0 --port 8000
```

### Testing
```bash
pytest tests/ -v
python scripts/robbert_sanity_check.py
```

### Validation
```bash
python scripts/validate_wandb_config.py
python scripts/validate_robbert_environment.py
```

## Model Focus

Currently focused on **Person Named Entity Recognition** with:
- O (Outside) - Not a person entity
- B-PER (Beginning) - Start of person entity
- I-PER (Inside) - Continuation of person entity

The architecture supports adding additional heads for other entity types or classification tasks as needed.