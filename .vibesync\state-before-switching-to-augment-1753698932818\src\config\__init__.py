"""
Configuration management with Pydantic validation and environment variable substitution.
"""

import os
import re
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field, field_validator


class ModelConfig(BaseModel):
    """Model configuration."""
    model_name: str = Field(..., description="Hugging Face model identifier")
    tokenizer_name: str = Field(..., description="Hugging Face tokenizer identifier")
    encoder_weights: Optional[str] = Field(None, description="Path to encoder weights (legacy)")
    max_length: int = Field(512, description="Maximum sequence length")
    tokenizer_type: str = Field("byte_level_bpe", description="Type of tokenizer (byte_level_bpe or wordpiece)")
    num_labels: Dict[str, int] = Field(..., description="Number of labels per task")

    @field_validator('tokenizer_type')
    @classmethod
    def validate_tokenizer_type(cls, v):
        valid_types = ['byte_level_bpe', 'wordpiece']
        if v not in valid_types:
            raise ValueError(f"Invalid tokenizer type: {v}. Must be one of {valid_types}")
        return v

    @field_validator('model_name')
    @classmethod
    def validate_model_name(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError("model_name must be a non-empty string")
        return v

    @field_validator('tokenizer_name')
    @classmethod
    def validate_tokenizer_name(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError("tokenizer_name must be a non-empty string")
        return v


class TrainingConfig(BaseModel):
    """Training configuration."""
    epochs: int = Field(3, description="Number of training epochs")
    batch_size: int = Field(8, description="Training batch size")
    learning_rate: float = Field(2e-5, description="Learning rate")
    warmup_steps: int = Field(500, description="Warmup steps")
    weight_decay: float = Field(0.01, description="Weight decay")
    gradient_accumulation_steps: int = Field(1, description="Gradient accumulation steps")
    max_grad_norm: float = Field(1.0, description="Maximum gradient norm")
    loss_weights: Dict[str, float] = Field(default_factory=dict, description="Loss weights per task")


class APIConfig(BaseModel):
    """API server configuration."""
    host: str = Field("0.0.0.0", description="API host")
    port: int = Field(8000, description="API port")
    workers: int = Field(1, description="Number of workers")


class InferenceConfig(BaseModel):
    """Inference configuration."""
    batch_size: int = Field(16, description="Inference batch size")
    thresholds: Dict[str, float] = Field(default_factory=dict, description="Classification thresholds")
    api: APIConfig = Field(default_factory=APIConfig, description="API configuration")


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = Field("INFO", description="Logging level")
    format: str = Field(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format string"
    )

    @field_validator('level')
    @classmethod
    def validate_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level: {v}. Must be one of {valid_levels}")
        return v.upper()


class WandBConfig(BaseModel):
    """Weights & Biases configuration."""
    entity: str = Field(..., description="WandB entity name")
    project: str = Field(..., description="WandB project name")
    api_key: Optional[str] = Field(None, description="WandB API key")
    enabled: bool = Field(True, description="Enable WandB logging")
    log_model: bool = Field(True, description="Log model artifacts")
    log_gradients: bool = Field(False, description="Log gradients")
    log_parameters: bool = Field(True, description="Log parameters")
    log_metrics: bool = Field(True, description="Log metrics")
    log_freq: int = Field(100, description="Logging frequency")
    save_freq: int = Field(1000, description="Model save frequency")
    run_name: Optional[str] = Field(None, description="Run name")
    tags: list = Field(default_factory=list, description="Run tags")
    notes: str = Field("", description="Run notes")
    heads: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="Head-specific config")
    multitask: Dict[str, Any] = Field(default_factory=dict, description="Multi-task config")
    artifacts: Dict[str, Any] = Field(default_factory=dict, description="Artifact config")


class Config(BaseModel):
    """Main configuration class."""
    model: ModelConfig
    training: TrainingConfig
    inference: InferenceConfig
    logging: LoggingConfig
    wandb: Optional[WandBConfig] = Field(None, description="WandB configuration")


def substitute_env_vars(data: Any) -> Any:
    """Recursively substitute environment variables in configuration data."""
    if isinstance(data, dict):
        return {key: substitute_env_vars(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [substitute_env_vars(item) for item in data]
    elif isinstance(data, str):
        # Pattern to match ${VAR} or ${VAR:-default}
        pattern = r'\$\{([^}]+)\}'

        def replace_var(match):
            var_expr = match.group(1)
            if ':-' in var_expr:
                var_name, default_value = var_expr.split(':-', 1)
                return os.getenv(var_name, default_value)
            else:
                return os.getenv(var_expr, match.group(0))  # Return original if not found

        return re.sub(pattern, replace_var, data)
    else:
        return data


def load_wandb_config(wandb_config_path: str = "src/config/wandb.yaml") -> Optional[WandBConfig]:
    """
    Load WandB configuration from YAML file.
    
    Args:
        wandb_config_path: Path to WandB YAML configuration file
        
    Returns:
        WandB configuration object or None if file doesn't exist
    """
    config_file = Path(wandb_config_path)
    
    if not config_file.exists():
        return None
        
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            raw_config = yaml.safe_load(f)
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"Failed to parse WandB YAML configuration: {e}")
    
    # Substitute environment variables
    processed_config = substitute_env_vars(raw_config)
    
    # Extract wandb section
    wandb_config = processed_config.get('wandb', {})
    
    try:
        return WandBConfig(**wandb_config)
    except Exception as e:
        raise ValueError(f"WandB configuration validation failed: {e}")


def load_config(config_path: str, wandb_config_path: str = "src/config/wandb.yaml") -> Config:
    """
    Load configuration from YAML file with environment variable substitution.

    Args:
        config_path: Path to YAML configuration file

    Returns:
        Validated configuration object

    Raises:
        FileNotFoundError: If config file doesn't exist
        yaml.YAMLError: If YAML parsing fails
        ValidationError: If configuration validation fails
    """
    config_file = Path(config_path)

    if not config_file.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            raw_config = yaml.safe_load(f)
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"Failed to parse YAML configuration: {e}")

    # Substitute environment variables
    processed_config = substitute_env_vars(raw_config)

    # Load WandB config if available
    wandb_config = load_wandb_config(wandb_config_path)
    if wandb_config:
        processed_config['wandb'] = wandb_config.model_dump()

    # Validate with Pydantic
    try:
        return Config(**processed_config)
    except Exception as e:
        raise ValueError(f"Configuration validation failed: {e}")


def get_default_config() -> Config:
    """Get default configuration."""
    return load_config('src/config/default.yaml')


# Export main classes and functions
__all__ = [
    'Config',
    'ModelConfig',
    'TrainingConfig',
    'InferenceConfig',
    'LoggingConfig',
    'APIConfig',
    'WandBConfig',
    'load_config',
    'load_wandb_config',
    'get_default_config'
]