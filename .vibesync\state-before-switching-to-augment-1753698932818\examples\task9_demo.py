#!/usr/bin/env python3
"""
Demonstration of Task 9: Early Stopping and Learning Rate Scheduling

This script demonstrates the enhanced early stopping and learning rate scheduling
functionality implemented for the Hugging Face Trainer integration.
"""

import sys
import os
sys.path.append('.')

from src.training.hf_config import HFTrainingConfig
from src.training.hf_callbacks import create_enhanced_callbacks
import torch


def demo_early_stopping_and_lr_scheduling():
    """Demonstrate early stopping and learning rate scheduling configuration."""
    
    print("=" * 70)
    print("TASK 9 DEMO: Early Stopping and Learning Rate Scheduling")
    print("=" * 70)
    print()
    
    # 1. Configuration Examples
    print("1. Configuration Examples")
    print("-" * 30)
    
    # Default configuration
    print("📋 Default Configuration:")
    default_config = HFTrainingConfig.from_yaml("src/config/hf_training_default.yaml")
    print(f"   - Early stopping patience: {default_config.early_stopping_patience}")
    print(f"   - Early stopping threshold: {default_config.early_stopping_threshold}")
    print(f"   - LR scheduler type: {default_config.lr_scheduler_type}")
    print(f"   - Warmup steps: {default_config.warmup_steps}")
    print(f"   - Monitor metrics: {default_config.early_stopping_monitor_metrics}")
    print()
    
    # Test configuration
    print("🧪 Test Configuration:")
    test_config = HFTrainingConfig.from_yaml("src/config/hf_training_test.yaml")
    print(f"   - Early stopping patience: {test_config.early_stopping_patience}")
    print(f"   - Early stopping threshold: {test_config.early_stopping_threshold}")
    print(f"   - LR scheduler type: {test_config.lr_scheduler_type}")
    print(f"   - Warmup steps: {test_config.warmup_steps}")
    print(f"   - Test mode: {test_config.test_mode}")
    print()
    
    # 2. Advanced Configuration
    print("2. Advanced Configuration Options")
    print("-" * 35)
    
    advanced_config = HFTrainingConfig(
        # Early stopping configuration
        early_stopping_patience=5,
        early_stopping_threshold=0.001,
        early_stopping_threshold_type="absolute",
        early_stopping_monitor_metrics=["eval_f1", "eval_loss", "eval_precision"],
        early_stopping_min_delta=0.0001,
        early_stopping_restore_best_weights=True,
        
        # Learning rate scheduling
        lr_scheduler_type="cosine_with_restarts",
        warmup_steps=1000,
        warmup_schedule_type="linear",
        cosine_schedule_num_cycles=0.5,
        
        # Training parameters
        epochs=10,
        learning_rate=2e-5,
        batch_size=16,
        
        # Monitoring
        logging_steps=25,
        eval_steps=100,
    )
    
    print("🔧 Advanced Configuration:")
    print(f"   - Multiple metrics monitoring: {advanced_config.early_stopping_monitor_metrics}")
    print(f"   - Threshold type: {advanced_config.early_stopping_threshold_type}")
    print(f"   - LR scheduler: {advanced_config.lr_scheduler_type}")
    print(f"   - Cosine cycles: {advanced_config.cosine_schedule_num_cycles}")
    print(f"   - Warmup type: {advanced_config.warmup_schedule_type}")
    print()
    
    # 3. Callback Creation
    print("3. Callback Creation and Integration")
    print("-" * 38)
    
    callbacks = create_enhanced_callbacks(
        config=advanced_config,
        label_list=["O", "B-PER", "I-PER", "B-LOC", "I-LOC", "B-ORG", "I-ORG"],
        num_training_steps=5000
    )
    
    print(f"📦 Created {len(callbacks)} enhanced callbacks:")
    for i, callback in enumerate(callbacks, 1):
        callback_name = type(callback).__name__
        print(f"   {i}. {callback_name}")
        
        # Show specific configuration for each callback
        if "EarlyStoppingCallback" in callback_name:
            print(f"      - Patience: {callback.early_stopping_patience}")
            print(f"      - Threshold: {callback.early_stopping_threshold}")
            print(f"      - Monitor metrics: {callback.monitor_metrics}")
        elif "LearningRateSchedulerCallback" in callback_name:
            print(f"      - Scheduler type: {callback.scheduler_type}")
            print(f"      - Warmup steps: {callback.num_warmup_steps}")
            print(f"      - Total steps: {callback.num_training_steps}")
        elif "TrainingMonitorCallback" in callback_name:
            print(f"      - Log interval: {callback.log_interval}")
            print(f"      - Track gradients: {callback.track_gradients}")
            print(f"      - Track memory: {callback.track_memory}")
    print()
    
    # 4. Scheduler Types Demonstration
    print("4. Available Learning Rate Schedulers")
    print("-" * 40)
    
    scheduler_configs = [
        ("linear", "Linear decay with warmup"),
        ("cosine", "Cosine annealing"),
        ("cosine_with_restarts", "Cosine with warm restarts"),
        ("polynomial", "Polynomial decay"),
        ("constant", "Constant learning rate"),
        ("constant_with_warmup", "Constant with warmup"),
    ]
    
    for scheduler_type, description in scheduler_configs:
        config = HFTrainingConfig(
            lr_scheduler_type=scheduler_type,
            warmup_steps=500 if scheduler_type != "constant" else 0
        )
        
        callbacks = create_enhanced_callbacks(
            config=config,
            num_training_steps=2000
        )
        
        lr_callback = next((cb for cb in callbacks if "LearningRateSchedulerCallback" in type(cb).__name__), None)
        status = "✅" if lr_callback else "⏭️"
        print(f"   {status} {scheduler_type:20} - {description}")
    print()
    
    # 5. Early Stopping Strategies
    print("5. Early Stopping Strategies")
    print("-" * 30)
    
    strategies = [
        {
            "name": "Conservative (High Patience)",
            "patience": 10,
            "threshold": 0.0001,
            "metrics": ["eval_f1"]
        },
        {
            "name": "Aggressive (Low Patience)",
            "patience": 2,
            "threshold": 0.01,
            "metrics": ["eval_loss"]
        },
        {
            "name": "Multi-Metric Monitoring",
            "patience": 5,
            "threshold": 0.001,
            "metrics": ["eval_f1", "eval_loss", "eval_precision", "eval_recall"]
        }
    ]
    
    for strategy in strategies:
        print(f"📊 {strategy['name']}:")
        print(f"   - Patience: {strategy['patience']} evaluations")
        print(f"   - Threshold: {strategy['threshold']}")
        print(f"   - Metrics: {', '.join(strategy['metrics'])}")
        print()
    
    # 6. Integration with TrainingArguments
    print("6. Integration with Hugging Face TrainingArguments")
    print("-" * 50)
    
    training_args = advanced_config.to_training_args("demo_output")
    
    print("🔗 TrainingArguments Integration:")
    print(f"   - Load best model at end: {training_args.load_best_model_at_end}")
    print(f"   - Metric for best model: {training_args.metric_for_best_model}")
    print(f"   - Greater is better: {training_args.greater_is_better}")
    print(f"   - LR scheduler type: {training_args.lr_scheduler_type}")
    print(f"   - Warmup steps: {training_args.warmup_steps}")
    print(f"   - Evaluation strategy: {training_args.eval_strategy}")
    print(f"   - Save total limit: {training_args.save_total_limit}")
    print()
    
    # 7. Usage Example
    print("7. Usage Example")
    print("-" * 16)
    
    print("💡 Example usage in training script:")
    print("""
    from src.training.hf_config import HFTrainingConfig
    from src.training.hf_trainer import HFNERTrainer
    
    # Load configuration
    config = HFTrainingConfig.from_yaml("src/config/hf_training_default.yaml")
    
    # Create trainer with enhanced callbacks
    trainer = HFNERTrainer(config)
    
    # Train with automatic early stopping and LR scheduling
    results = trainer.train(
        data_path="data/dutch_ner.json",
        output_dir="checkpoints/enhanced_training"
    )
    
    # The trainer will automatically:
    # - Monitor eval_f1 and eval_loss
    # - Stop early if no improvement for 3 evaluations
    # - Use linear LR schedule with 500 warmup steps
    # - Log detailed training progress
    # - Save best model based on F1 score
    """)
    
    print("=" * 70)
    print("✅ Task 9 implementation provides comprehensive early stopping")
    print("   and learning rate scheduling with easy configuration!")
    print("=" * 70)


if __name__ == "__main__":
    demo_early_stopping_and_lr_scheduling()