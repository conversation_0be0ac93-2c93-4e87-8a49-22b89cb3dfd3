"""
Compatibility layer for ensuring backward compatibility with existing API endpoints
and inference scripts when using the new Hugging Face Trainer integration.
"""

import os
import json
import warnings
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from functools import wraps

from ..utils.logging_utils import get_logger
from ..exceptions import ModelLoadingError, InferenceError


class BackwardCompatibilityManager:
    """
    Manages backward compatibility for model loading and API responses.
    """
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self._compatibility_warnings = []
    
    def ensure_model_compatibility(
        self,
        model_path: Union[str, Path],
        expected_format: str = "auto"
    ) -> Dict[str, Any]:
        """
        Ensure model is compatible with current inference pipeline.
        
        Args:
            model_path: Path to model checkpoint
            expected_format: Expected model format ("auto", "hf_trainer", "legacy")
            
        Returns:
            Compatibility information
        """
        self.logger.logger.info(f"Checking model compatibility: {model_path}")
        
        model_path = Path(model_path)
        
        if not model_path.exists():
            raise ModelLoadingError(f"Model path not found: {model_path}")
        
        compatibility_info = {
            "model_path": str(model_path),
            "format_detected": None,
            "is_compatible": False,
            "requires_migration": False,
            "compatibility_issues": [],
            "recommendations": []
        }
        
        try:
            # Detect model format
            format_detected = self._detect_model_format(model_path)
            compatibility_info["format_detected"] = format_detected
            
            # Check compatibility based on format
            if format_detected == "hf_trainer":
                compatibility_info.update(self._check_hf_trainer_compatibility(model_path))
            elif format_detected == "legacy":
                compatibility_info.update(self._check_legacy_compatibility(model_path))
            else:
                compatibility_info["compatibility_issues"].append(f"Unknown model format: {format_detected}")
            
            # Determine overall compatibility
            compatibility_info["is_compatible"] = len(compatibility_info["compatibility_issues"]) == 0
            
            if not compatibility_info["is_compatible"]:
                compatibility_info["recommendations"].append(
                    "Consider migrating model to current format using migration utilities"
                )
            
            self.logger.logger.info(f"Compatibility check: {'✓' if compatibility_info['is_compatible'] else '✗'}")
            
            return compatibility_info
            
        except Exception as e:
            error_msg = f"Failed to check model compatibility: {e}"
            self.logger.logger.error(error_msg)
            raise ModelLoadingError(error_msg) from e
    
    def _detect_model_format(self, model_path: Path) -> str:
        """Detect the format of the model checkpoint."""
        # Check for Hugging Face Trainer format
        if (model_path / "training_args.bin").exists():
            return "hf_trainer"
        
        # Check for standard Hugging Face format
        if (model_path / "config.json").exists() and (model_path / "pytorch_model.bin").exists():
            return "huggingface"
        
        # Check for legacy format
        if (model_path / "pytorch_model.bin").exists():
            return "legacy"
        
        return "unknown"
    
    def _check_hf_trainer_compatibility(self, model_path: Path) -> Dict[str, Any]:
        """Check compatibility of Hugging Face Trainer format model."""
        issues = []
        recommendations = []
        
        # Check required files
        required_files = ["config.json", "pytorch_model.bin"]
        for file_name in required_files:
            if not (model_path / file_name).exists():
                issues.append(f"Missing required file: {file_name}")
        
        # Check config compatibility
        config_path = model_path / "config.json"
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                
                # Check model type
                model_type = config.get("model_type", "")
                if model_type != "roberta":
                    issues.append(f"Unexpected model type: {model_type} (expected: roberta)")
                
                # Check label configuration
                num_labels = config.get("num_labels", 0)
                if num_labels != 3:
                    issues.append(f"Unexpected number of labels: {num_labels} (expected: 3 for NER)")
                
                # Check architecture name
                architectures = config.get("architectures", [])
                if "RobertaForTokenClassification" not in architectures:
                    issues.append("Model not configured for token classification")
                
            except Exception as e:
                issues.append(f"Error reading config.json: {e}")
        
        return {
            "compatibility_issues": issues,
            "recommendations": recommendations
        }
    
    def _check_legacy_compatibility(self, model_path: Path) -> Dict[str, Any]:
        """Check compatibility of legacy format model."""
        issues = []
        recommendations = []
        
        # Legacy models require migration
        recommendations.append("Legacy model detected - consider migrating to Hugging Face format")
        
        # Check if basic files exist
        if not (model_path / "pytorch_model.bin").exists():
            issues.append("Missing model weights file")
        
        return {
            "compatibility_issues": issues,
            "recommendations": recommendations,
            "requires_migration": True
        }


def adapt_response_for_compatibility(response_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Adapt API response to maintain backward compatibility.
    
    Args:
        response_data: Original response data
        
    Returns:
        Adapted response data
    """
    # Ensure response has expected structure
    adapted_response = {
        "text": response_data.get("text", ""),
        "results": response_data.get("results", {})
    }
    
    # Ensure NER results have expected format
    if "ner" in adapted_response["results"]:
        ner_results = adapted_response["results"]["ner"]
        
        # Convert to list format if needed
        if isinstance(ner_results, dict):
            adapted_response["results"]["ner"] = ner_results.get("entities", [])
        
        # Ensure each entity has required fields
        if isinstance(adapted_response["results"]["ner"], list):
            for entity in adapted_response["results"]["ner"]:
                if isinstance(entity, dict):
                    # Ensure required fields exist
                    entity.setdefault("text", "")
                    entity.setdefault("label", "PER")
                    entity.setdefault("start", 0)
                    entity.setdefault("end", 0)
                    entity.setdefault("confidence", 0.5)
    
    return adapted_response


def validate_request_for_compatibility(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and adapt API request for compatibility.
    
    Args:
        request_data: Original request data
        
    Returns:
        Validated request data
    """
    # Ensure request has expected structure
    validated_request = {
        "text": request_data.get("text", ""),
        "tasks": request_data.get("tasks", None)
    }
    
    # Handle legacy task names
    if validated_request["tasks"]:
        task_mapping = {
            "person_extraction": "ner",
            "entity_recognition": "ner",
            "named_entity": "ner"
        }
        
        if isinstance(validated_request["tasks"], list):
            validated_request["tasks"] = [
                task_mapping.get(task, task) for task in validated_request["tasks"]
            ]
        elif isinstance(validated_request["tasks"], str):
            validated_request["tasks"] = task_mapping.get(validated_request["tasks"], validated_request["tasks"])
    
    return validated_request


def compatibility_warning(message: str):
    """Issue a compatibility warning."""
    warnings.warn(f"Compatibility Warning: {message}", UserWarning, stacklevel=2)


def deprecated_function(replacement: str = None):
    """Decorator to mark functions as deprecated."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            warning_msg = f"Function {func.__name__} is deprecated"
            if replacement:
                warning_msg += f". Use {replacement} instead"
            compatibility_warning(warning_msg)
            return func(*args, **kwargs)
        return wrapper
    return decorator


class LegacyModelLoader:
    """
    Provides backward-compatible model loading for legacy checkpoints.
    """
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    @deprecated_function("MultiTaskRobBERT.from_pretrained")
    def load_legacy_model(
        self,
        model_path: Union[str, Path],
        device: str = "auto"
    ) -> Any:
        """
        Load legacy model format with compatibility layer.
        
        Args:
            model_path: Path to legacy model
            device: Target device ("auto", "cpu", "cuda")
            
        Returns:
            Loaded model with compatibility wrapper
        """
        self.logger.logger.info(f"Loading legacy model: {model_path}")
        
        try:
            # Import here to avoid circular imports
            from ..models.multitask_robbert import MultiTaskRobBERT
            
            # Load model using current implementation
            model = MultiTaskRobBERT.from_pretrained(str(model_path))
            
            # Add compatibility wrapper
            model = self._add_compatibility_wrapper(model)
            
            self.logger.logger.info("Legacy model loaded with compatibility wrapper")
            
            return model
            
        except Exception as e:
            error_msg = f"Failed to load legacy model: {e}"
            self.logger.logger.error(error_msg)
            raise ModelLoadingError(error_msg) from e
    
    def _add_compatibility_wrapper(self, model: Any) -> Any:
        """Add compatibility methods to model."""
        # Add legacy method aliases
        if not hasattr(model, 'predict'):
            model.predict = self._create_predict_method(model)
        
        if not hasattr(model, 'extract_entities'):
            model.extract_entities = self._create_extract_entities_method(model)
        
        return model
    
    def _create_predict_method(self, model):
        """Create legacy predict method."""
        def predict(text: str, task: str = "ner") -> Dict[str, Any]:
            """Legacy predict method for backward compatibility."""
            try:
                # Tokenize input
                inputs = model.tokenizer.tokenizer(
                    text,
                    return_tensors="pt",
                    truncation=True,
                    padding=True,
                    max_length=512
                )
                
                # Run inference
                with torch.no_grad():
                    outputs = model(
                        input_ids=inputs['input_ids'],
                        attention_mask=inputs['attention_mask'],
                        heads=[task]
                    )
                
                # Process results
                if task == "ner" and task in outputs:
                    logits = outputs[task]['logits']
                    predictions = torch.argmax(logits, dim=-1)
                    
                    # Extract entities (simplified)
                    entities = []
                    # This would need the full entity extraction logic
                    
                    return {"entities": entities}
                
                return {"results": outputs}
                
            except Exception as e:
                raise InferenceError(f"Legacy prediction failed: {e}")
        
        return predict
    
    def _create_extract_entities_method(self, model):
        """Create legacy extract_entities method."""
        def extract_entities(text: str) -> List[Dict[str, Any]]:
            """Legacy entity extraction method."""
            result = model.predict(text, "ner")
            return result.get("entities", [])
        
        return extract_entities


class APICompatibilityLayer:
    """
    Provides compatibility layer for API endpoints.
    """
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    def wrap_endpoint(self, endpoint_func):
        """Wrap API endpoint with compatibility layer."""
        @wraps(endpoint_func)
        async def wrapper(*args, **kwargs):
            try:
                # Call original endpoint
                result = await endpoint_func(*args, **kwargs)
                
                # Apply compatibility adaptations
                if hasattr(result, 'model_dump'):
                    result_data = result.model_dump()
                else:
                    result_data = result
                
                adapted_result = adapt_response_for_compatibility(result_data)
                
                # Return in same format as original
                if hasattr(result, 'model_dump'):
                    # Pydantic model - reconstruct
                    result_class = type(result)
                    return result_class(**adapted_result)
                else:
                    return adapted_result
                
            except Exception as e:
                self.logger.logger.error(f"Compatibility layer error: {e}")
                raise
        
        return wrapper


def ensure_checkpoint_compatibility(checkpoint_path: Union[str, Path]) -> bool:
    """
    Ensure checkpoint is compatible with current system.
    
    Args:
        checkpoint_path: Path to checkpoint
        
    Returns:
        True if compatible, False otherwise
    """
    try:
        compatibility_manager = BackwardCompatibilityManager()
        compatibility_info = compatibility_manager.ensure_model_compatibility(checkpoint_path)
        
        if not compatibility_info["is_compatible"]:
            # Log compatibility issues
            logger = get_logger("checkpoint_compatibility")
            logger.logger.warning(f"Checkpoint compatibility issues found:")
            for issue in compatibility_info["compatibility_issues"]:
                logger.logger.warning(f"  - {issue}")
            
            # Log recommendations
            for recommendation in compatibility_info["recommendations"]:
                logger.logger.info(f"  Recommendation: {recommendation}")
        
        return compatibility_info["is_compatible"]
        
    except Exception as e:
        logger = get_logger("checkpoint_compatibility")
        logger.logger.error(f"Failed to check checkpoint compatibility: {e}")
        return False


def create_compatibility_test_suite() -> Dict[str, Any]:
    """
    Create test suite for compatibility validation.
    
    Returns:
        Test suite configuration
    """
    return {
        "model_loading_tests": [
            {
                "name": "load_hf_trainer_checkpoint",
                "description": "Test loading Hugging Face Trainer checkpoint",
                "test_function": "test_load_hf_trainer_model"
            },
            {
                "name": "load_legacy_checkpoint", 
                "description": "Test loading legacy checkpoint with compatibility layer",
                "test_function": "test_load_legacy_model"
            }
        ],
        "api_compatibility_tests": [
            {
                "name": "predict_endpoint_compatibility",
                "description": "Test /predict endpoint backward compatibility",
                "test_function": "test_predict_endpoint"
            },
            {
                "name": "annotate_endpoint_compatibility",
                "description": "Test /annotate endpoint backward compatibility", 
                "test_function": "test_annotate_endpoint"
            }
        ],
        "data_format_tests": [
            {
                "name": "jsonl_to_sentence_entities",
                "description": "Test JSONL to sentence+entities conversion",
                "test_function": "test_data_format_conversion"
            }
        ]
    }


# Export main classes and functions
__all__ = [
    'BackwardCompatibilityManager',
    'LegacyModelLoader',
    'APICompatibilityLayer',
    'adapt_response_for_compatibility',
    'validate_request_for_compatibility',
    'ensure_checkpoint_compatibility',
    'compatibility_warning',
    'deprecated_function',
    'create_compatibility_test_suite'
]