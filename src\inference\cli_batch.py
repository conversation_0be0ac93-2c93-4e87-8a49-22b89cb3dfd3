#!/usr/bin/env python3
"""
Batch inference CLI for multi-task RobBERT model.
"""

import argparse
import json
import csv
import sys
from pathlib import Path
from typing import List, Dict, Any, Iterator
import torch
from tqdm import tqdm
import logging

from ..models.multitask_robbert import MultiTaskRobBERT
from ..config import load_config


def setup_logging(level: str = "INFO"):
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def read_input_file(file_path: str, format_type: str = None) -> Iterator[Dict[str, Any]]:
    """Read input file in various formats."""
    path = Path(file_path)

    if format_type is None:
        # Auto-detect format from extension
        if path.suffix.lower() == '.jsonl':
            format_type = 'jsonl'
        elif path.suffix.lower() == '.csv':
            format_type = 'csv'
        elif path.suffix.lower() == '.txt':
            format_type = 'txt'
        else:
            raise ValueError(f"Cannot auto-detect format for {file_path}")

    if format_type == 'jsonl':
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    if 'text' not in data:
                        raise ValueError("Missing 'text' field")
                    yield {'id': data.get('id', line_num), 'text': data['text']}
                except json.JSONDecodeError as e:
                    logging.error(f"JSON decode error on line {line_num}: {e}")
                    continue
                except ValueError as e:
                    logging.error(f"Data error on line {line_num}: {e}")
                    continue

    elif format_type == 'csv':
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row_num, row in enumerate(reader, 1):
                if 'text' not in row:
                    logging.error(f"Missing 'text' column in row {row_num}")
                    continue
                yield {'id': row.get('id', row_num), 'text': row['text']}

    elif format_type == 'txt':
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                text = line.strip()
                if text:
                    yield {'id': line_num, 'text': text}

    else:
        raise ValueError(f"Unsupported format: {format_type}")


def write_output_file(results: List[Dict[str, Any]], file_path: str, format_type: str = None):
    """Write results to output file."""
    path = Path(file_path)

    if format_type is None:
        # Auto-detect format from extension
        if path.suffix.lower() == '.jsonl':
            format_type = 'jsonl'
        elif path.suffix.lower() == '.csv':
            format_type = 'csv'
        else:
            format_type = 'jsonl'  # Default to JSONL

    if format_type == 'jsonl':
        with open(file_path, 'w', encoding='utf-8') as f:
            for result in results:
                f.write(json.dumps(result, ensure_ascii=False) + '\n')

    elif format_type == 'csv':
        if not results:
            return

        # Flatten results for CSV
        flattened_results = []
        for result in results:
            flat_result = {'id': result['id'], 'text': result['text']}
            for task, task_result in result['results'].items():
                if isinstance(task_result, dict):
                    for key, value in task_result.items():
                        flat_result[f'{task}_{key}'] = value
                else:
                    flat_result[task] = task_result
            flattened_results.append(flat_result)

        with open(file_path, 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=flattened_results[0].keys())
            writer.writeheader()
            writer.writerows(flattened_results)

    else:
        raise ValueError(f"Unsupported output format: {format_type}")


def process_batch(model: MultiTaskRobBERT, texts: List[str], tasks: List[str],
                 batch_size: int = 16) -> List[Dict[str, Any]]:
    """Process a batch of texts."""
    results = []

    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i + batch_size]

        # Tokenize batch
        inputs = model.tokenizer(
            batch_texts,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        )

        # Move to device
        device = next(model.parameters()).device
        inputs = {k: v.to(device) for k, v in inputs.items()}

        # Run inference
        with torch.no_grad():
            outputs = model(
                input_ids=inputs['input_ids'],
                attention_mask=inputs['attention_mask'],
                heads=tasks
            )

        # Process results for each text in batch
        for j, text in enumerate(batch_texts):
            text_results = {}

            for task_name, task_output in outputs.items():
                if task_name == 'loss':
                    continue

                logits = task_output['logits'][j]

                if task_name == 'ner':
                    # Simple NER processing (token-level)
                    predictions = torch.argmax(logits, dim=-1)
                    text_results[task_name] = {
                        'predictions': predictions.tolist(),
                        'num_entities': (predictions > 0).sum().item()
                    }
                else:
                    # Classification processing
                    probabilities = torch.softmax(logits, dim=-1)
                    predicted_class = torch.argmax(probabilities, dim=-1)
                    confidence = probabilities.max().item()

                    text_results[task_name] = {
                        'predicted_class': predicted_class.item(),
                        'confidence': confidence
                    }

            results.append(text_results)

    return results


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(description="Batch inference for multi-task BERTje")
    parser.add_argument('--tasks', nargs='+', default=['ner'],
                       help='Tasks to run (default: ner)')
    parser.add_argument('--input', required=True,
                       help='Input file path')
    parser.add_argument('--output', required=True,
                       help='Output file path')
    parser.add_argument('--input-format', choices=['jsonl', 'csv', 'txt'],
                       help='Input format (auto-detected if not specified)')
    parser.add_argument('--output-format', choices=['jsonl', 'csv'],
                       help='Output format (auto-detected if not specified)')
    parser.add_argument('--batch-size', type=int, default=16,
                       help='Batch size for processing')
    parser.add_argument('--model-path',
                       help='Path to model (uses BERTJE_WEIGHTS_DIR env var if not specified)')
    parser.add_argument('--config', default='src/config/default.yaml',
                       help='Configuration file path')
    parser.add_argument('--log-level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')

    args = parser.parse_args()

    # Setup logging
    logger = setup_logging(args.log_level)

    try:
        # Load configuration
        config = load_config(args.config)
        logger.info(f"Loaded configuration from {args.config}")

        # Load model
        logger.info("Loading multi-task RobBERT model...")
        if args.model_path:
            model = MultiTaskRobBERT.from_pretrained(args.model_path)
        else:
            model = MultiTaskRobBERT.from_pretrained()

        model.eval()

        # Setup device
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model.to(device)
        logger.info(f"Model loaded on {device}")

        # Read input data
        logger.info(f"Reading input from {args.input}")
        input_data = list(read_input_file(args.input, args.input_format))
        logger.info(f"Loaded {len(input_data)} items")

        if not input_data:
            logger.error("No valid input data found")
            sys.exit(1)

        # Process data
        logger.info(f"Processing with tasks: {args.tasks}")
        results = []

        # Process in batches with progress bar
        texts = [item['text'] for item in input_data]
        batch_results = []

        for i in tqdm(range(0, len(texts), args.batch_size), desc="Processing batches"):
            batch_texts = texts[i:i + args.batch_size]
            batch_output = process_batch(model, batch_texts, args.tasks, args.batch_size)
            batch_results.extend(batch_output)

        # Combine with original data
        for i, (input_item, result) in enumerate(zip(input_data, batch_results)):
            results.append({
                'id': input_item['id'],
                'text': input_item['text'],
                'results': result
            })

        # Write output
        logger.info(f"Writing results to {args.output}")
        write_output_file(results, args.output, args.output_format)
        logger.info(f"Successfully processed {len(results)} items")

    except Exception as e:
        logger.error(f"Error during processing: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()