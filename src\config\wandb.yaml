# Weights & Biases Configuration for RobBERT-2023 Training
wandb:
  # Core wandb settings
  entity: "slippydongle"
  project: "robbert2023"
  api_key: "${WANDB_API_KEY:-****************************************}"
  
  # Experiment tracking settings
  enabled: true
  log_model: true  # Save model artifacts to wandb
  log_gradients: false  # Set to true for gradient tracking (can be expensive)
  log_parameters: true
  log_metrics: true
  
  # Logging frequency
  log_freq: 100  # Log metrics every N steps
  save_freq: 1000  # Save model checkpoints every N steps
  
  # Run configuration
  run_name: null  # Will be auto-generated if null
  tags: ["robbert-2023", "dutch-nlp", "multi-task"]
  notes: "Multi-head RobBERT training for Dutch NLP tasks"
  
  # Head-specific configurations
  heads:
    ner:
      project_suffix: "-ner"  # Creates project: robbert2023-ner
      tags: ["named-entity-recognition", "token-classification", "person-entities"]
      notes: "Person Named Entity Recognition with RobBERT-2023"
      metrics:
        - "ner_loss"
        - "ner_f1"
        - "ner_precision" 
        - "ner_recall"
        - "ner_accuracy"
        - "ner_entity_f1"
  
  # Multi-task training configuration
  multitask:
    project_suffix: "-multitask"  # Creates project: robbert2023-multitask
    tags: ["multi-task", "joint-training"]
    notes: "Multi-head joint training"
    log_head_losses: true
    log_combined_metrics: true
    
  # Model artifact settings
  artifacts:
    save_best_model: true
    save_final_model: true
    model_name_template: "robbert2023-{heads}-epoch-{epoch}"
    metadata_keys:
      - "model_name"
      - "heads"
      - "epochs"
      - "batch_size"
      - "learning_rate"
      - "loss_weights"
      - "dataset_path"
      - "config_path"