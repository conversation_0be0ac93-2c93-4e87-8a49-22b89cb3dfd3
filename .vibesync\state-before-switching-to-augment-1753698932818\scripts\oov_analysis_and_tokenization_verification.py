#!/usr/bin/env python3
"""
OOV Analysis and Tokenization Verification Script for RobBERT-2023 Transition

This script implements comprehensive analysis of out-of-vocabulary token rates
and tokenization quality verification for the RobBERT-2023 transition.

Task 10 Implementation:
- Create script to analyze out-of-vocabulary token rates in current dataset
- Implement tokenization quality checker for emails, diacritics, and compound names
- Generate examples of tokenized sentences for manual verification
- Write validation functions to demonstrate correct token splits and alignment
"""

import json
import sys
import argparse
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from collections import Counter, defaultdict
import re
from dataclasses import dataclass
import unicodedata

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment
from src.utils.logging_utils import get_logger


@dataclass
class OOVAnalysisResult:
    """Results from OOV analysis."""
    total_tokens: int
    oov_tokens: int
    oov_rate: float
    oov_examples: List[str]
    vocabulary_coverage: float
    unique_tokens: int
    unique_oov_tokens: int


@dataclass
class TokenizationExample:
    """Example of tokenization for verification."""
    original_text: str
    words: List[str]
    tokens: List[str]
    word_ids: List[Optional[int]]
    labels: Optional[List[str]] = None
    token_labels: Optional[List[str]] = None


class OOVAnalyzer:
    """Analyzer for out-of-vocabulary tokens and tokenization quality."""
    
    def __init__(self, model_name: str = "DTAI-KULeuven/robbert-2023-dutch-base"):
        """Initialize the OOV analyzer."""
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.tokenizer = RobBERTTokenizerWithAlignment(model_name)
        
        # Get vocabulary for OOV analysis
        self.vocabulary = set(self.tokenizer.tokenizer.get_vocab().keys())
        self.logger.logger.info(f"Loaded vocabulary with {len(self.vocabulary)} tokens")
        
        # Patterns for special text types
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.compound_pattern = re.compile(r'\b\w+[-]\w+\b')  # Hyphenated compounds
        self.diacritic_pattern = re.compile(r'[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]', re.IGNORECASE)
        
    def analyze_dataset_oov(self, dataset_path: str) -> OOVAnalysisResult:
        """
        Analyze out-of-vocabulary token rates in the dataset.
        
        Args:
            dataset_path: Path to the JSON dataset file
            
        Returns:
            OOVAnalysisResult with analysis statistics
        """
        self.logger.logger.info(f"Analyzing OOV rates in dataset: {dataset_path}")
        
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                dataset = json.load(f)
        except Exception as e:
            self.logger.logger.error(f"Failed to load dataset: {e}")
            raise
        
        all_tokens = []
        oov_tokens = []
        token_counter = Counter()
        
        # Process each example in the dataset
        for i, example in enumerate(dataset):
            if i % 1000 == 0:
                self.logger.logger.info(f"Processed {i}/{len(dataset)} examples")
            
            words = example.get('tokens', [])
            if not words:
                continue
            
            # Tokenize the words
            try:
                alignment = self.tokenizer.tokenize_with_alignment(words)
                tokens = alignment.tokens
                
                for token in tokens:
                    all_tokens.append(token)
                    token_counter[token] += 1
                    
                    # Check if token is in vocabulary
                    if token not in self.vocabulary:
                        oov_tokens.append(token)
                        
            except Exception as e:
                self.logger.logger.warning(f"Failed to tokenize example {i}: {e}")
                continue
        
        # Calculate statistics
        total_tokens = len(all_tokens)
        oov_count = len(oov_tokens)
        oov_rate = oov_count / total_tokens if total_tokens > 0 else 0.0
        
        # Get unique counts
        unique_tokens = len(set(all_tokens))
        unique_oov_tokens = len(set(oov_tokens))
        
        # Get most common OOV examples
        oov_counter = Counter(oov_tokens)
        oov_examples = [token for token, count in oov_counter.most_common(20)]
        
        # Calculate vocabulary coverage
        vocab_coverage = (unique_tokens - unique_oov_tokens) / unique_tokens if unique_tokens > 0 else 0.0
        
        result = OOVAnalysisResult(
            total_tokens=total_tokens,
            oov_tokens=oov_count,
            oov_rate=oov_rate,
            oov_examples=oov_examples,
            vocabulary_coverage=vocab_coverage,
            unique_tokens=unique_tokens,
            unique_oov_tokens=unique_oov_tokens
        )
        
        self.logger.logger.info(f"OOV analysis complete: {oov_rate:.4f} OOV rate")
        return result
    
    def check_tokenization_quality(self, test_cases: List[str]) -> Dict[str, Any]:
        """
        Check tokenization quality for specific text types.
        
        Args:
            test_cases: List of test strings to analyze
            
        Returns:
            Dictionary with quality analysis results
        """
        self.logger.logger.info(f"Checking tokenization quality for {len(test_cases)} test cases")
        
        results = {
            'email_handling': [],
            'diacritic_handling': [],
            'compound_handling': [],
            'general_quality': [],
            'statistics': {
                'total_cases': len(test_cases),
                'avg_tokens_per_word': 0.0,
                'subword_split_rate': 0.0,
                'special_char_preservation': 0.0
            }
        }
        
        total_words = 0
        total_tokens = 0
        subword_splits = 0
        special_chars_preserved = 0
        total_special_chars = 0
        
        for text in test_cases:
            try:
                # Tokenize the text
                words = text.split()
                alignment = self.tokenizer.tokenize_with_alignment(words)
                
                # Basic statistics
                total_words += len(words)
                total_tokens += len(alignment.tokens)
                
                # Count subword splits
                word_token_counts = defaultdict(int)
                for word_id in alignment.word_ids:
                    if word_id is not None:
                        word_token_counts[word_id] += 1
                
                splits_in_text = sum(1 for count in word_token_counts.values() if count > 1)
                subword_splits += splits_in_text
                
                # Check special character preservation
                for char in text:
                    if not char.isalnum() and not char.isspace():
                        total_special_chars += 1
                        if char in ''.join(alignment.tokens):
                            special_chars_preserved += 1
                
                # Categorize by text type
                example = {
                    'text': text,
                    'words': words,
                    'tokens': alignment.tokens,
                    'word_ids': alignment.word_ids,
                    'subword_splits': splits_in_text,
                    'tokens_per_word': len(alignment.tokens) / len(words) if words else 0
                }
                
                if self.email_pattern.search(text):
                    results['email_handling'].append(example)
                elif self.diacritic_pattern.search(text):
                    results['diacritic_handling'].append(example)
                elif self.compound_pattern.search(text):
                    results['compound_handling'].append(example)
                else:
                    results['general_quality'].append(example)
                    
            except Exception as e:
                self.logger.logger.warning(f"Failed to analyze text '{text[:50]}...': {e}")
                continue
        
        # Calculate final statistics
        if total_words > 0:
            results['statistics']['avg_tokens_per_word'] = total_tokens / total_words
            results['statistics']['subword_split_rate'] = subword_splits / total_words
        
        if total_special_chars > 0:
            results['statistics']['special_char_preservation'] = special_chars_preserved / total_special_chars
        
        self.logger.logger.info("Tokenization quality analysis complete")
        return results
    
    def generate_verification_examples(self, dataset_path: str, num_examples: int = 50) -> List[TokenizationExample]:
        """
        Generate examples of tokenized sentences for manual verification.
        
        Args:
            dataset_path: Path to the dataset
            num_examples: Number of examples to generate
            
        Returns:
            List of TokenizationExample objects
        """
        self.logger.logger.info(f"Generating {num_examples} verification examples")
        
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                dataset = json.load(f)
        except Exception as e:
            self.logger.logger.error(f"Failed to load dataset: {e}")
            raise
        
        examples = []
        
        # Select diverse examples
        step = max(1, len(dataset) // num_examples)
        selected_indices = list(range(0, len(dataset), step))[:num_examples]
        
        for i in selected_indices:
            example_data = dataset[i]
            words = example_data.get('tokens', [])
            labels = example_data.get('ner_tags', [])
            
            if not words:
                continue
            
            try:
                # Get tokenization with alignment
                alignment = self.tokenizer.tokenize_with_alignment(words, labels)
                
                example = TokenizationExample(
                    original_text=' '.join(words),
                    words=words,
                    tokens=alignment.tokens,
                    word_ids=alignment.word_ids,
                    labels=labels,
                    token_labels=alignment.token_labels
                )
                
                examples.append(example)
                
            except Exception as e:
                self.logger.logger.warning(f"Failed to create example {i}: {e}")
                continue
        
        self.logger.logger.info(f"Generated {len(examples)} verification examples")
        return examples
    
    def validate_token_splits_and_alignment(self, examples: List[TokenizationExample]) -> Dict[str, Any]:
        """
        Validate correct token splits and label alignment.
        
        Args:
            examples: List of tokenization examples to validate
            
        Returns:
            Dictionary with validation results
        """
        self.logger.logger.info(f"Validating token splits and alignment for {len(examples)} examples")
        
        validation_results = {
            'total_examples': len(examples),
            'valid_alignments': 0,
            'alignment_errors': [],
            'split_statistics': {
                'single_token_words': 0,
                'multi_token_words': 0,
                'max_tokens_per_word': 0,
                'avg_tokens_per_word': 0.0
            },
            'label_alignment_stats': {
                'correct_b_to_i_transitions': 0,
                'incorrect_transitions': 0,
                'special_token_labels': 0
            }
        }
        
        total_words = 0
        total_tokens = 0
        
        for example in examples:
            try:
                # Validate basic alignment
                if len(example.word_ids) != len(example.tokens):
                    validation_results['alignment_errors'].append({
                        'text': example.original_text[:50],
                        'error': 'Word IDs length mismatch with tokens',
                        'word_ids_len': len(example.word_ids),
                        'tokens_len': len(example.tokens)
                    })
                    continue
                
                if example.token_labels and len(example.token_labels) != len(example.tokens):
                    validation_results['alignment_errors'].append({
                        'text': example.original_text[:50],
                        'error': 'Token labels length mismatch with tokens',
                        'labels_len': len(example.token_labels),
                        'tokens_len': len(example.tokens)
                    })
                    continue
                
                # Analyze token splits
                word_token_counts = defaultdict(int)
                for word_id in example.word_ids:
                    if word_id is not None:
                        word_token_counts[word_id] += 1
                
                single_token_words = sum(1 for count in word_token_counts.values() if count == 1)
                multi_token_words = sum(1 for count in word_token_counts.values() if count > 1)
                max_tokens = max(word_token_counts.values()) if word_token_counts else 0
                
                validation_results['split_statistics']['single_token_words'] += single_token_words
                validation_results['split_statistics']['multi_token_words'] += multi_token_words
                validation_results['split_statistics']['max_tokens_per_word'] = max(
                    validation_results['split_statistics']['max_tokens_per_word'], max_tokens
                )
                
                total_words += len(example.words)
                total_tokens += len(example.tokens)
                
                # Validate label alignment if available
                if example.token_labels and example.labels:
                    self._validate_label_alignment(example, validation_results)
                
                validation_results['valid_alignments'] += 1
                
            except Exception as e:
                validation_results['alignment_errors'].append({
                    'text': example.original_text[:50],
                    'error': f'Validation failed: {e}'
                })
        
        # Calculate averages
        if total_words > 0:
            validation_results['split_statistics']['avg_tokens_per_word'] = total_tokens / total_words
        
        self.logger.logger.info(f"Validation complete: {validation_results['valid_alignments']}/{len(examples)} valid")
        return validation_results
    
    def _validate_label_alignment(self, example: TokenizationExample, results: Dict[str, Any]):
        """Validate label alignment for a single example."""
        previous_word_id = None
        
        for i, (word_id, token_label) in enumerate(zip(example.word_ids, example.token_labels)):
            if word_id is None:
                # Special token should have "O" label
                if token_label == "O":
                    results['label_alignment_stats']['special_token_labels'] += 1
            elif word_id != previous_word_id:
                # First token of a word - should match original label
                if word_id < len(example.labels):
                    original_label = example.labels[word_id]
                    if original_label in ["B-PER", "I-PER", "O"] and token_label == original_label:
                        results['label_alignment_stats']['correct_b_to_i_transitions'] += 1
            else:
                # Continuation token - should be I-PER if original was B-PER
                if word_id < len(example.labels):
                    original_label = example.labels[word_id]
                    if original_label == "B-PER" and token_label == "I-PER":
                        results['label_alignment_stats']['correct_b_to_i_transitions'] += 1
                    elif original_label == "I-PER" and token_label == "I-PER":
                        results['label_alignment_stats']['correct_b_to_i_transitions'] += 1
                    elif original_label == "O" and token_label == "O":
                        results['label_alignment_stats']['correct_b_to_i_transitions'] += 1
                    else:
                        results['label_alignment_stats']['incorrect_transitions'] += 1
            
            previous_word_id = word_id


def create_test_cases() -> List[str]:
    """Create test cases for tokenization quality checking."""
    return [
        # Email addresses
        "<EMAIL> werkt bij de gemeente",
        "Contact <NAME_EMAIL> voor meer informatie",
        "Het e-mailadres <EMAIL> is geldig",
        
        # Diacritics and special characters
        "Café Müller in Düsseldorf is gesloten",
        "François Mitterrand bezocht Straßburg",
        "Naïve benadering van het probleem",
        "Coördinatie tussen afdelingen is essentieel",
        "Zoë en Chloë gingen naar de bioscoop",
        
        # Compound names and hyphenated words
        "Jan-Willem van der Berg-Jansen",
        "Anne-Marie Koster-de Vries",
        "De Vos-van Steenwijk familie",
        "Mevrouw Thomassen-à-Thuessink",
        "Hendriks genaamd Modderkolk",
        "Vennegoor of Hesselink speelde goed",
        
        # Complex Dutch names
        "Jonkheer van der Capellen tot den Marsch",
        "Graaf Schimmelpenninck van der Oije",
        "Baron de Loë-van der Veen",
        "Prins Bernhard van Oranje-Nassau",
        
        # OCR-like noisy text
        "J4n J4nsen w0nt in Amst3rd4m",
        "De tekst bevat veel typ0's en f0uten",
        "Sc4nning resulteerde in vreemde k4rakters",
        
        # Mixed content
        "Stuur een <NAME_EMAIL> voor vragen",
        "De coördinator François werkt bij café De Kroon",
        "Jan-Willem's e-<NAME_EMAIL>",
        
        # Long compound words (typical Dutch)
        "Arbeidsongeschiktheidsverzekering",
        "Kinderopvangtoeslag",
        "Studiefinancieringsaanvraag",
        "Hypotheekrenteaftrek",
        
        # Numbers and dates
        "Geboren op 15-03-1985 in Amsterdam",
        "Telefoonnummer: 06-12345678",
        "Postcode 1234 AB Amsterdam",
        "€ 1.234,56 werd overgemaakt",
        
        # Mixed languages (common in Dutch text)
        "De CEO van het bedrijf sprak over de business case",
        "Het team werkte aan een state-of-the-art oplossing",
        "De workshop over machine learning was interessant"
    ]


def print_oov_analysis_report(result: OOVAnalysisResult):
    """Print a formatted OOV analysis report."""
    print("\n" + "="*60)
    print("OUT-OF-VOCABULARY (OOV) ANALYSIS REPORT")
    print("="*60)
    
    print(f"Total tokens processed: {result.total_tokens:,}")
    print(f"OOV tokens found: {result.oov_tokens:,}")
    print(f"OOV rate: {result.oov_rate:.4f} ({result.oov_rate*100:.2f}%)")
    print(f"Unique tokens: {result.unique_tokens:,}")
    print(f"Unique OOV tokens: {result.unique_oov_tokens:,}")
    print(f"Vocabulary coverage: {result.vocabulary_coverage:.4f} ({result.vocabulary_coverage*100:.2f}%)")
    
    if result.oov_examples:
        print(f"\nMost common OOV tokens:")
        for i, token in enumerate(result.oov_examples[:10], 1):
            print(f"  {i:2d}. '{token}'")


def print_quality_analysis_report(results: Dict[str, Any]):
    """Print a formatted tokenization quality report."""
    print("\n" + "="*60)
    print("TOKENIZATION QUALITY ANALYSIS REPORT")
    print("="*60)
    
    stats = results['statistics']
    print(f"Total test cases: {stats['total_cases']}")
    print(f"Average tokens per word: {stats['avg_tokens_per_word']:.2f}")
    print(f"Subword split rate: {stats['subword_split_rate']:.4f} ({stats['subword_split_rate']*100:.2f}%)")
    print(f"Special character preservation: {stats['special_char_preservation']:.4f} ({stats['special_char_preservation']*100:.2f}%)")
    
    categories = [
        ('email_handling', 'Email Handling'),
        ('diacritic_handling', 'Diacritic Handling'),
        ('compound_handling', 'Compound Name Handling'),
        ('general_quality', 'General Quality')
    ]
    
    for key, title in categories:
        examples = results[key]
        if examples:
            print(f"\n{title} ({len(examples)} examples):")
            for example in examples[:3]:  # Show first 3 examples
                print(f"  Text: {example['text']}")
                print(f"  Tokens: {example['tokens']}")
                print(f"  Splits: {example['subword_splits']}, Ratio: {example['tokens_per_word']:.2f}")
                print()


def print_verification_examples(examples: List[TokenizationExample], num_to_show: int = 10):
    """Print verification examples for manual inspection."""
    print("\n" + "="*60)
    print("TOKENIZATION VERIFICATION EXAMPLES")
    print("="*60)
    
    for i, example in enumerate(examples[:num_to_show], 1):
        print(f"\nExample {i}:")
        print(f"Original: {example.original_text}")
        print(f"Words: {example.words}")
        print(f"Tokens: {example.tokens}")
        print(f"Word IDs: {example.word_ids}")
        
        if example.labels and example.token_labels:
            print(f"Original labels: {example.labels}")
            print(f"Token labels: {example.token_labels}")
        
        # Show alignment
        print("Alignment:")
        for j, (token, word_id) in enumerate(zip(example.tokens, example.word_ids)):
            if word_id is not None and word_id < len(example.words):
                word = example.words[word_id]
                label_info = ""
                if example.token_labels:
                    label_info = f" [{example.token_labels[j]}]"
                print(f"  {token} -> '{word}'{label_info}")
            else:
                label_info = ""
                if example.token_labels:
                    label_info = f" [{example.token_labels[j]}]"
                print(f"  {token} -> [SPECIAL]{label_info}")


def print_validation_report(results: Dict[str, Any]):
    """Print validation results report."""
    print("\n" + "="*60)
    print("TOKEN SPLITS AND ALIGNMENT VALIDATION REPORT")
    print("="*60)
    
    print(f"Total examples validated: {results['total_examples']}")
    print(f"Valid alignments: {results['valid_alignments']}")
    print(f"Alignment errors: {len(results['alignment_errors'])}")
    
    split_stats = results['split_statistics']
    print(f"\nToken Split Statistics:")
    print(f"  Single-token words: {split_stats['single_token_words']:,}")
    print(f"  Multi-token words: {split_stats['multi_token_words']:,}")
    print(f"  Max tokens per word: {split_stats['max_tokens_per_word']}")
    print(f"  Average tokens per word: {split_stats['avg_tokens_per_word']:.2f}")
    
    label_stats = results['label_alignment_stats']
    print(f"\nLabel Alignment Statistics:")
    print(f"  Correct B-/I- transitions: {label_stats['correct_b_to_i_transitions']}")
    print(f"  Incorrect transitions: {label_stats['incorrect_transitions']}")
    print(f"  Special token labels: {label_stats['special_token_labels']}")
    
    if results['alignment_errors']:
        print(f"\nAlignment Errors (first 5):")
        for error in results['alignment_errors'][:5]:
            print(f"  Text: {error['text']}")
            print(f"  Error: {error['error']}")


def main():
    """Main function to run OOV analysis and tokenization verification."""
    parser = argparse.ArgumentParser(description="OOV Analysis and Tokenization Verification")
    parser.add_argument("--dataset", default="data_sets/combined_training.json",
                       help="Path to dataset JSON file")
    parser.add_argument("--model", default="DTAI-KULeuven/robbert-2023-dutch-base",
                       help="RobBERT model name")
    parser.add_argument("--examples", type=int, default=20,
                       help="Number of verification examples to generate")
    parser.add_argument("--output", help="Output file for detailed results (JSON)")
    parser.add_argument("--verbose", action="store_true",
                       help="Enable verbose output")
    
    args = parser.parse_args()
    
    # Initialize analyzer
    print("Initializing OOV Analyzer...")
    analyzer = OOVAnalyzer(args.model)
    
    # 1. Analyze OOV rates in dataset
    print(f"\n1. Analyzing OOV rates in dataset: {args.dataset}")
    oov_result = analyzer.analyze_dataset_oov(args.dataset)
    print_oov_analysis_report(oov_result)
    
    # 2. Check tokenization quality for special text types
    print(f"\n2. Checking tokenization quality for special text types...")
    test_cases = create_test_cases()
    quality_results = analyzer.check_tokenization_quality(test_cases)
    print_quality_analysis_report(quality_results)
    
    # 3. Generate verification examples
    print(f"\n3. Generating {args.examples} verification examples...")
    verification_examples = analyzer.generate_verification_examples(args.dataset, args.examples)
    print_verification_examples(verification_examples, min(10, args.examples))
    
    # 4. Validate token splits and alignment
    print(f"\n4. Validating token splits and alignment...")
    validation_results = analyzer.validate_token_splits_and_alignment(verification_examples)
    print_validation_report(validation_results)
    
    # Save detailed results if requested
    if args.output:
        detailed_results = {
            'oov_analysis': {
                'total_tokens': oov_result.total_tokens,
                'oov_tokens': oov_result.oov_tokens,
                'oov_rate': oov_result.oov_rate,
                'oov_examples': oov_result.oov_examples,
                'vocabulary_coverage': oov_result.vocabulary_coverage,
                'unique_tokens': oov_result.unique_tokens,
                'unique_oov_tokens': oov_result.unique_oov_tokens
            },
            'quality_analysis': quality_results,
            'validation_results': validation_results,
            'verification_examples': [
                {
                    'original_text': ex.original_text,
                    'words': ex.words,
                    'tokens': ex.tokens,
                    'word_ids': ex.word_ids,
                    'labels': ex.labels,
                    'token_labels': ex.token_labels
                }
                for ex in verification_examples
            ]
        }
        
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, indent=2, ensure_ascii=False)
        print(f"\nDetailed results saved to: {args.output}")
    
    print("\n" + "="*60)
    print("ANALYSIS COMPLETE")
    print("="*60)
    print(f"Key findings:")
    print(f"- OOV rate: {oov_result.oov_rate:.4f} ({oov_result.oov_rate*100:.2f}%)")
    print(f"- Average tokens per word: {quality_results['statistics']['avg_tokens_per_word']:.2f}")
    print(f"- Valid alignments: {validation_results['valid_alignments']}/{validation_results['total_examples']}")
    print(f"- Subword split rate: {quality_results['statistics']['subword_split_rate']:.4f}")


if __name__ == "__main__":
    main()