"""
Pytest configuration and fixtures for BERTje multi-task model tests.
"""

import pytest
import torch
import tempfile
import shutil
import json
from pathlib import Path
from unittest.mock import Mock, patch

from src.models.multitask_robbert import MultiTaskRobBERT
from src.config import load_config


@pytest.fixture(scope="session")
def test_config():
    """Load test configuration."""
    return load_config('src/config/default.yaml')


@pytest.fixture(scope="session")
def device():
    """Get test device (CPU for consistent testing)."""
    return torch.device('cpu')


@pytest.fixture(scope="session")
def sample_text():
    """Sample Dutch text for testing."""
    return "<PERSON> woon<PERSON> in Amsterdam en werkt bij Google."


@pytest.fixture(scope="session")
def sample_texts():
    """Multiple sample Dutch texts for batch testing."""
    return [
        "<PERSON> woon<PERSON> in Amsterdam.",
        "Google is een technologiebedrijf in Mountain View.",
        "De Universiteit van Amsterdam is een bekende instelling.",
        "Jan de Wit werkt bij Microsoft in Nederland."
    ]


@pytest.fixture(scope="session")
def sample_ner_labels():
    """Sample NER labels for testing."""
    return {
        "<PERSON> <PERSON>sen woont in Amsterdam.": ["B-PER", "I-PER", "O", "O", "B-LOC", "O"],
        "Google is een technologiebedrijf.": ["B-ORG", "O", "O", "O", "O"]
    }


@pytest.fixture(scope="session")
def mock_tokenizer():
    """Mock tokenizer for testing."""
    tokenizer = Mock()
    tokenizer.encode.return_value = [101, 1234, 5678, 102]  # [CLS] token token [SEP]
    tokenizer.convert_ids_to_tokens.return_value = ["[CLS]", "test", "token", "[SEP]"]
    tokenizer.return_value = {
        'input_ids': torch.tensor([[101, 1234, 5678, 102]]),
        'attention_mask': torch.tensor([[1, 1, 1, 1]])
    }
    return tokenizer


@pytest.fixture(scope="function")
def temp_dir():
    """Create temporary directory for test files."""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path)


@pytest.fixture(scope="function")
def sample_jsonl_file(temp_dir):
    """Create sample JSONL file for testing."""
    jsonl_path = temp_dir / "test_data.jsonl"
    
    sample_data = [
        {"text": "Linda Jansen woont in Amsterdam.", "ner": ["B-PER", "I-PER", "O", "O", "B-LOC"], "compliance": 0},
        {"text": "Google is een technologiebedrijf.", "ner": ["B-ORG", "O", "O", "O"], "compliance": 1},
        {"text": "De minister gaat naar Brussel.", "ner": ["O", "B-PER", "O", "O", "B-LOC"], "compliance": 0}
    ]
    
    with open(jsonl_path, 'w', encoding='utf-8') as f:
        for item in sample_data:
            f.write(f"{json.dumps(item)}\n")
    
    return jsonl_path


@pytest.fixture(scope="function")
def sample_csv_file(temp_dir):
    """Create sample CSV file for testing."""
    csv_path = temp_dir / "test_data.csv"
    
    with open(csv_path, 'w', encoding='utf-8') as f:
        f.write("id,text\n")
        f.write("1,Linda Jansen woont in Amsterdam.\n")
        f.write("2,Google is een technologiebedrijf.\n")
        f.write("3,De minister gaat naar Brussel.\n")
    
    return csv_path


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Setup test environment."""
    # Ensure we're using CPU for consistent testing
    torch.set_num_threads(1)
    
    # Set random seeds for reproducibility
    torch.manual_seed(42)
    
    yield
    
    # Cleanup after tests
    torch.set_num_threads(torch.get_num_threads())


@pytest.fixture(scope="function")
def mock_model_loading():
    """Mock model loading to avoid downloading during tests."""
    with patch('src.models.multitask_robbert.AutoModel') as mock_auto_model, \
         patch('src.models.multitask_robbert.AutoConfig') as mock_auto_config, \
         patch('src.models.multitask_robbert.AutoTokenizer') as mock_auto_tokenizer:
        
        # Mock config
        mock_config = Mock()
        mock_config.hidden_size = 768
        mock_config.num_labels = 9
        mock_auto_config.from_pretrained.return_value = mock_config
        
        # Mock model
        mock_model = Mock()
        mock_auto_model.from_pretrained.return_value = mock_model
        
        # Mock tokenizer
        mock_tokenizer = Mock()
        mock_tokenizer.return_value = {
            'input_ids': torch.tensor([[101, 1234, 5678, 102]]),
            'attention_mask': torch.tensor([[1, 1, 1, 1]])
        }
        mock_auto_tokenizer.from_pretrained.return_value = mock_tokenizer
        
        yield {
            'model': mock_model,
            'config': mock_config,
            'tokenizer': mock_tokenizer
        }


# Test markers
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )


# Skip tests if model files are not available
def pytest_collection_modifyitems(config, items):
    """Modify test collection to skip tests requiring model files if not available."""
    model_path = Path("src/models/weights/bertje_conll")
    
    if not model_path.exists():
        skip_model = pytest.mark.skip(reason="Model files not available")
        for item in items:
            if "model" in item.keywords:
                item.add_marker(skip_model)
