"""
Tests for error handling and logging functionality.
"""

import pytest
import torch
import tempfile
import logging
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from src.exceptions import (
    RobBERTError, TokenizationError, ModelLoadingError, InferenceError,
    DimensionMismatchError, LabelAlignmentError, ConfigurationError,
    MemoryError, ValidationError
)
from src.utils.logging_utils import (
    RobBERTLogger, get_logger, log_execution_time, log_memory_usage,
    safe_execute, validate_tensor_shape, log_tensor_stats
)
from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment
from src.models.multitask_robbert import MultiTaskRobBERT


class TestCustomExceptions:
    """Test custom exception classes."""
    
    def test_robbert_error_base(self):
        """Test base RobBERTError exception."""
        error = RobBERTError("Test error", {"key": "value"})
        
        assert str(error) == "Test error | Details: {'key': 'value'}"
        assert error.message == "Test error"
        assert error.details == {"key": "value"}
    
    def test_robbert_error_no_details(self):
        """Test RobBERTError without details."""
        error = RobBERTError("Test error")
        
        assert str(error) == "Test error"
        assert error.message == "Test error"
        assert error.details == {}
    
    def test_tokenization_error(self):
        """Test TokenizationError with specific fields."""
        error = TokenizationError(
            "Tokenization failed",
            text="Sample text",
            tokenizer_name="robbert-2023"
        )
        
        assert "Tokenization failed" in str(error)
        assert error.details["text_preview"] == "Sample text"
        assert error.details["tokenizer_name"] == "robbert-2023"
        assert error.details["text_length"] == 11
    
    def test_model_loading_error(self):
        """Test ModelLoadingError with specific fields."""
        error = ModelLoadingError(
            "Model loading failed",
            model_name="robbert-2023",
            checkpoint_path="/path/to/model"
        )
        
        assert "Model loading failed" in str(error)
        assert error.details["model_name"] == "robbert-2023"
        assert error.details["checkpoint_path"] == "/path/to/model"
    
    def test_inference_error(self):
        """Test InferenceError with specific fields."""
        error = InferenceError(
            "Inference failed",
            input_shape=(1, 512),
            model_name="robbert-2023",
            heads=["ner"]
        )
        
        assert "Inference failed" in str(error)
        assert error.details["input_shape"] == (1, 512)
        assert error.details["model_name"] == "robbert-2023"
        assert error.details["heads"] == ["ner"]
    
    def test_dimension_mismatch_error(self):
        """Test DimensionMismatchError with specific fields."""
        error = DimensionMismatchError(
            "Shape mismatch",
            expected_shape=(1, 512, 768),
            actual_shape=(1, 256, 768),
            tensor_name="hidden_states"
        )
        
        assert "Shape mismatch" in str(error)
        assert error.details["expected_shape"] == (1, 512, 768)
        assert error.details["actual_shape"] == (1, 256, 768)
        assert error.details["tensor_name"] == "hidden_states"
    
    def test_label_alignment_error(self):
        """Test LabelAlignmentError with specific fields."""
        error = LabelAlignmentError(
            "Label alignment failed",
            num_words=10,
            num_labels=8,
            num_tokens=15
        )
        
        assert "Label alignment failed" in str(error)
        assert error.details["num_words"] == 10
        assert error.details["num_labels"] == 8
        assert error.details["num_tokens"] == 15


class TestRobBERTLogger:
    """Test RobBERTLogger functionality."""
    
    def test_logger_initialization(self):
        """Test logger initialization."""
        logger = RobBERTLogger("test_logger", level="DEBUG")
        
        assert logger.logger.name == "test_logger"
        assert logger.logger.level == logging.DEBUG
        assert len(logger.logger.handlers) >= 1
    
    def test_logger_with_file(self, temp_dir):
        """Test logger with file output."""
        log_file = temp_dir / "test.log"
        logger = RobBERTLogger("test_logger", log_file=str(log_file))
        
        logger.logger.info("Test message")
        
        assert log_file.exists()
        content = log_file.read_text()
        assert "Test message" in content
    
    def test_log_system_info(self):
        """Test system info logging."""
        logger = RobBERTLogger("test_logger")
        
        # Should not raise exception
        logger.log_system_info()
    
    def test_log_model_info(self):
        """Test model info logging."""
        logger = RobBERTLogger("test_logger")
        
        # Should not raise exception
        logger.log_model_info("robbert-2023", num_parameters=125000000, model_size_mb=500.0)
    
    def test_log_tokenization_stats(self):
        """Test tokenization stats logging."""
        logger = RobBERTLogger("test_logger")
        
        text = "Test text for tokenization"
        tokens = ["Test", "text", "for", "token", "ization"]
        stats = {"words": 4, "labels": 0}
        
        # Should not raise exception
        logger.log_tokenization_stats(text, tokens, stats)
    
    def test_log_inference_stats(self):
        """Test inference stats logging."""
        logger = RobBERTLogger("test_logger")
        
        # Should not raise exception
        logger.log_inference_stats(
            input_shape=(1, 512),
            output_shape=(1, 512, 3),
            inference_time=0.5,
            heads=["ner"]
        )
    
    def test_log_memory_usage(self):
        """Test memory usage logging."""
        logger = RobBERTLogger("test_logger")
        
        # Should not raise exception
        logger.log_memory_usage("test operation")
    
    def test_log_error(self):
        """Test error logging."""
        logger = RobBERTLogger("test_logger")
        
        error = TokenizationError("Test error", text="sample")
        context = {"function": "test_function"}
        
        # Should not raise exception
        logger.log_error(error, context)


class TestLoggingDecorators:
    """Test logging decorators."""
    
    def test_log_execution_time_decorator(self):
        """Test execution time logging decorator."""
        logger = RobBERTLogger("test_logger")
        
        @log_execution_time(logger=logger, operation_name="test_operation")
        def test_function():
            return "success"
        
        result = test_function()
        assert result == "success"
    
    def test_log_memory_usage_decorator(self):
        """Test memory usage logging decorator."""
        logger = RobBERTLogger("test_logger")
        
        @log_memory_usage(logger=logger, operation_name="test_operation")
        def test_function():
            return "success"
        
        result = test_function()
        assert result == "success"
    
    def test_safe_execute_decorator_success(self):
        """Test safe execute decorator with successful execution."""
        logger = RobBERTLogger("test_logger")
        
        @safe_execute(logger=logger, reraise=False)
        def test_function():
            return "success"
        
        result = test_function()
        assert result == "success"
    
    def test_safe_execute_decorator_failure(self):
        """Test safe execute decorator with failure."""
        logger = RobBERTLogger("test_logger")
        
        @safe_execute(logger=logger, default_return="default", reraise=False)
        def test_function():
            raise ValueError("Test error")
        
        result = test_function()
        assert result == "default"
    
    def test_safe_execute_decorator_reraise(self):
        """Test safe execute decorator with reraise=True."""
        logger = RobBERTLogger("test_logger")
        
        @safe_execute(logger=logger, reraise=True)
        def test_function():
            raise ValueError("Test error")
        
        with pytest.raises(ValueError, match="Test error"):
            test_function()


class TestTensorValidation:
    """Test tensor validation utilities."""
    
    def test_validate_tensor_shape_success(self):
        """Test successful tensor shape validation."""
        logger = RobBERTLogger("test_logger")
        tensor = torch.randn(1, 512, 768)
        
        # Should not raise exception
        validate_tensor_shape(tensor, (1, 512, 768), "test_tensor", logger)
    
    def test_validate_tensor_shape_flexible(self):
        """Test tensor shape validation with flexible dimensions."""
        logger = RobBERTLogger("test_logger")
        tensor = torch.randn(2, 256, 768)
        
        # Should not raise exception (-1 means flexible)
        validate_tensor_shape(tensor, (-1, -1, 768), "test_tensor", logger)
    
    def test_validate_tensor_shape_failure(self):
        """Test tensor shape validation failure."""
        logger = RobBERTLogger("test_logger")
        tensor = torch.randn(1, 256, 768)
        
        with pytest.raises(DimensionMismatchError):
            validate_tensor_shape(tensor, (1, 512, 768), "test_tensor", logger)
    
    def test_log_tensor_stats(self):
        """Test tensor statistics logging."""
        logger = RobBERTLogger("test_logger")
        tensor = torch.randn(2, 10, 5)
        
        # Should not raise exception
        log_tensor_stats(tensor, "test_tensor", logger)
    
    def test_log_tensor_stats_empty(self):
        """Test tensor statistics logging with empty tensor."""
        logger = RobBERTLogger("test_logger")
        tensor = torch.empty(0)
        
        # Should not raise exception
        log_tensor_stats(tensor, "empty_tensor", logger)


class TestTokenizerErrorHandling:
    """Test tokenizer error handling."""
    
    @pytest.mark.unit
    def test_tokenizer_initialization_failure(self):
        """Test tokenizer initialization with invalid model."""
        with pytest.raises(ModelLoadingError):
            RobBERTTokenizerWithAlignment("invalid-model-name")
    
    @pytest.mark.unit
    def test_tokenize_with_alignment_empty_words(self):
        """Test tokenization with empty word list."""
        with patch('src.data.tokenizer_utils.AutoTokenizer') as mock_tokenizer:
            mock_tokenizer_instance = Mock()
            mock_tokenizer_instance.__len__ = Mock(return_value=50000)  # Mock vocab size
            mock_tokenizer_instance.special_tokens_map = {}
            mock_tokenizer.from_pretrained.return_value = mock_tokenizer_instance
            
            tokenizer = RobBERTTokenizerWithAlignment()
            
            with pytest.raises(TokenizationError, match="Empty word list"):
                tokenizer.tokenize_with_alignment([])
    
    @pytest.mark.unit
    def test_tokenize_with_alignment_mismatched_labels(self):
        """Test tokenization with mismatched word and label counts."""
        with patch('src.data.tokenizer_utils.AutoTokenizer') as mock_tokenizer:
            mock_tokenizer.from_pretrained.return_value = Mock()
            
            tokenizer = RobBERTTokenizerWithAlignment()
            
            with pytest.raises(LabelAlignmentError):
                tokenizer.tokenize_with_alignment(
                    words=["word1", "word2"],
                    labels=["B-PER"]  # Mismatched count
                )
    
    @pytest.mark.unit
    def test_encode_for_model_tokenization_failure(self):
        """Test model encoding with tokenization failure."""
        with patch('src.data.tokenizer_utils.AutoTokenizer') as mock_tokenizer:
            mock_tokenizer_instance = Mock()
            mock_tokenizer_instance.side_effect = Exception("Tokenization failed")
            mock_tokenizer.from_pretrained.return_value = mock_tokenizer_instance
            
            tokenizer = RobBERTTokenizerWithAlignment()
            
            with pytest.raises(TokenizationError):
                tokenizer.encode_for_model(["test", "words"])
    
    @pytest.mark.unit
    def test_decode_predictions_empty_input(self):
        """Test prediction decoding with empty input."""
        with patch('src.data.tokenizer_utils.AutoTokenizer') as mock_tokenizer:
            mock_tokenizer.from_pretrained.return_value = Mock()
            
            tokenizer = RobBERTTokenizerWithAlignment()
            
            with pytest.raises(TokenizationError, match="Empty input_ids"):
                tokenizer.decode_predictions(
                    torch.empty(0),
                    torch.empty(0)
                )
    
    @pytest.mark.unit
    def test_decode_predictions_shape_mismatch(self):
        """Test prediction decoding with shape mismatch."""
        with patch('src.data.tokenizer_utils.AutoTokenizer') as mock_tokenizer:
            mock_tokenizer.from_pretrained.return_value = Mock()
            
            tokenizer = RobBERTTokenizerWithAlignment()
            
            with pytest.raises(TokenizationError, match="doesn't match predictions length"):
                tokenizer.decode_predictions(
                    torch.tensor([1, 2, 3]),
                    torch.tensor([1, 2])  # Different length
                )


class TestModelErrorHandling:
    """Test model error handling."""
    
    @pytest.mark.unit
    def test_model_initialization_config_failure(self):
        """Test model initialization with config loading failure."""
        with patch('src.models.multitask_robbert.AutoConfig') as mock_config:
            mock_config.from_pretrained.side_effect = Exception("Config loading failed")
            
            with pytest.raises(ModelLoadingError, match="Failed to load RobBERT configuration"):
                MultiTaskRobBERT("test_path")
    
    @pytest.mark.unit
    def test_model_initialization_tokenizer_failure(self):
        """Test model initialization with tokenizer failure."""
        with patch('src.models.multitask_robbert.AutoConfig') as mock_config, \
             patch('src.models.multitask_robbert.RobBERTTokenizerWithAlignment') as mock_tokenizer:
            
            mock_config.from_pretrained.return_value = Mock(hidden_size=768)
            mock_tokenizer.side_effect = Exception("Tokenizer loading failed")
            
            with pytest.raises(ModelLoadingError, match="Failed to initialize RobBERT tokenizer"):
                MultiTaskRobBERT("test_path")
    
    @pytest.mark.unit
    def test_model_initialization_model_failure(self):
        """Test model initialization with model loading failure."""
        with patch('src.models.multitask_robbert.AutoConfig') as mock_config, \
             patch('src.models.multitask_robbert.RobBERTTokenizerWithAlignment') as mock_tokenizer, \
             patch('src.models.multitask_robbert.AutoModelForTokenClassification') as mock_model:
            
            mock_config.from_pretrained.return_value = Mock(hidden_size=768)
            mock_tokenizer.return_value = Mock()
            mock_model.from_pretrained.side_effect = Exception("Model loading failed")
            
            with pytest.raises(ModelLoadingError, match="Failed to load RobBERT model"):
                MultiTaskRobBERT("test_path")
    
    @pytest.mark.unit
    def test_model_forward_empty_input(self):
        """Test model forward with empty input."""
        with patch('src.models.multitask_robbert.AutoConfig') as mock_config, \
             patch('src.models.multitask_robbert.RobBERTTokenizerWithAlignment') as mock_tokenizer, \
             patch('src.models.multitask_robbert.AutoModelForTokenClassification') as mock_model:
            
            # Setup mocks
            mock_config.from_pretrained.return_value = Mock(hidden_size=768)
            mock_tokenizer.return_value = Mock()
            mock_model_instance = Mock()
            mock_model_instance.roberta = Mock()
            mock_model_instance.classifier = Mock()
            mock_model.from_pretrained.return_value = mock_model_instance
            
            model = MultiTaskRobBERT("test_path")
            
            with pytest.raises(InferenceError, match="Empty or None input_ids"):
                model.forward(torch.empty(0))
    
    @pytest.mark.unit
    def test_model_forward_attention_mask_mismatch(self):
        """Test model forward with mismatched attention mask shape."""
        with patch('src.models.multitask_robbert.AutoConfig') as mock_config, \
             patch('src.models.multitask_robbert.RobBERTTokenizerWithAlignment') as mock_tokenizer, \
             patch('src.models.multitask_robbert.AutoModelForTokenClassification') as mock_model:
            
            # Setup mocks
            mock_config.from_pretrained.return_value = Mock(hidden_size=768)
            mock_tokenizer.return_value = Mock()
            mock_model_instance = Mock()
            mock_model_instance.roberta = Mock()
            mock_model_instance.classifier = Mock()
            mock_model.from_pretrained.return_value = mock_model_instance
            
            model = MultiTaskRobBERT("test_path")
            
            input_ids = torch.randint(0, 1000, (1, 10))
            attention_mask = torch.ones(1, 5)  # Different shape
            
            with pytest.raises(DimensionMismatchError):
                model.forward(input_ids, attention_mask)
    
    @pytest.mark.unit
    def test_model_forward_invalid_heads(self):
        """Test model forward with invalid heads."""
        with patch('src.models.multitask_robbert.AutoConfig') as mock_config, \
             patch('src.models.multitask_robbert.RobBERTTokenizerWithAlignment') as mock_tokenizer, \
             patch('src.models.multitask_robbert.AutoModelForTokenClassification') as mock_model:
            
            # Setup mocks
            mock_config.from_pretrained.return_value = Mock(hidden_size=768)
            mock_tokenizer.return_value = Mock()
            mock_model_instance = Mock()
            mock_model_instance.roberta = Mock()
            mock_model_instance.classifier = Mock()
            mock_model.from_pretrained.return_value = mock_model_instance
            
            model = MultiTaskRobBERT("test_path")
            
            input_ids = torch.randint(0, 1000, (1, 10))
            
            with pytest.raises(InferenceError, match="No valid heads specified"):
                model.forward(input_ids, heads=["invalid_head"])


class TestAPIErrorHandling:
    """Test API error handling."""
    
    @pytest.mark.unit
    def test_api_error_handlers(self):
        """Test that error handlers are properly defined."""
        from src.inference.api_fastapi import app
        
        # Check that error handlers are registered
        exception_handlers = app.exception_handlers
        
        assert RobBERTError in exception_handlers
        assert ModelLoadingError in exception_handlers
        assert InferenceError in exception_handlers
        assert TokenizationError in exception_handlers


class TestIntegrationErrorScenarios:
    """Test integration error scenarios."""
    
    @pytest.mark.integration
    def test_end_to_end_error_handling(self):
        """Test end-to-end error handling in a realistic scenario."""
        # This test would require actual model loading, so we'll mock it
        with patch('src.models.multitask_robbert.AutoConfig') as mock_config, \
             patch('src.models.multitask_robbert.RobBERTTokenizerWithAlignment') as mock_tokenizer, \
             patch('src.models.multitask_robbert.AutoModelForTokenClassification') as mock_model:
            
            # Setup mocks to simulate various failure points
            mock_config.from_pretrained.return_value = Mock(hidden_size=768)
            mock_tokenizer.return_value = Mock()
            mock_model_instance = Mock()
            mock_model_instance.roberta = Mock()
            mock_model_instance.classifier = Mock()
            mock_model.from_pretrained.return_value = mock_model_instance
            
            # Test successful initialization
            model = MultiTaskRobBERT("test_path")
            assert model is not None
            
            # Test that logger was initialized
            assert hasattr(model, 'logger')
            assert model.logger is not None


@pytest.mark.slow
class TestMemoryErrorHandling:
    """Test memory-related error handling."""
    
    def test_memory_monitoring(self):
        """Test memory monitoring functionality."""
        logger = RobBERTLogger("test_logger")
        
        # Should not raise exception
        logger.log_memory_usage("test operation")
    
    def test_low_memory_warning(self):
        """Test low memory warning detection."""
        # This would require mocking psutil to simulate low memory
        with patch('psutil.virtual_memory') as mock_memory:
            mock_memory.return_value = Mock(available=1e9)  # 1GB available
            
            logger = RobBERTLogger("test_logger")
            # Should log warning but not raise exception
            logger.log_memory_usage("test operation")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
