# Design Document

## Overview

This design outlines the transition from BERTje (GroNLP/bert-base-dutch-cased) to RobBERT-2023-base (DTAI-KULeuven/robbert-2023-dutch-base) in the Dutch NER pipeline. The transition involves replacing the tokenizer, encoder, and classification heads while maintaining API compatibility and improving tokenization quality for Dutch text.

The key architectural change is moving from WordPiece tokenization (30k vocabulary) to byte-level BPE tokenization (50k vocabulary), which should significantly reduce out-of-vocabulary tokens and improve handling of compound Dutch names, diacritics, and OCR-noisy text.

## Architecture

### Current Architecture (BERTje-based)
```
Input Text → BERTje Tokenizer (WordPiece) → BERTje Encoder → NER Head → Predictions
```

### New Architecture (RobBERT-2023-based)
```
Input Text → RobBERT Tokenizer (Byte-level BPE) → RobBERT Encoder → NER Head → Predictions
```

### Key Changes
1. **Tokenizer**: Replace `AutoTokenizer` loading BERTje with RobBERT-2023 tokenizer
2. **Encoder**: Replace `AutoModel` loading BERTje with RobBERT-2023 encoder
3. **Classification Head**: Update to use `AutoModelForTokenClassification` pattern
4. **Label Alignment**: Implement proper subword-to-label alignment for byte-level BPE

## Components and Interfaces

### 1. Tokenizer Component

**Current Implementation:**
```python
self.tokenizer = AutoTokenizer.from_pretrained("wietsedv/bert-base-dutch-cased-finetuned-conll2002-ner")
```

**New Implementation:**
```python
self.tokenizer = AutoTokenizer.from_pretrained("DTAI-KULeuven/robbert-2023-dutch-base")
```

**Label Alignment Logic:**
- For multi-subword tokens, assign original label to first subword
- Assign "I-PER" to continuation subwords (or "O" if original was "O")
- Implement alignment validation to ensure correct mapping

### 2. Model Architecture Component

**Current Implementation:**
```python
# Mixed approach with BertForTokenClassification and custom heads
if hasattr(self.config, 'architectures') and 'BertForTokenClassification' in self.config.architectures:
    self.ner_model = BertForTokenClassification.from_pretrained(model_path)
    self.encoder = self.ner_model.bert
```

**New Implementation:**
```python
# Clean AutoModelForTokenClassification approach
from transformers import AutoModelForTokenClassification

id2label = {0: "O", 1: "B-PER", 2: "I-PER"}
label2id = {v: k for k, v in id2label.items()}

self.model = AutoModelForTokenClassification.from_pretrained(
    "DTAI-KULeuven/robbert-2023-dutch-base",
    num_labels=3,
    id2label=id2label,
    label2id=label2id
)
```

### 3. Configuration Updates

**Model Configuration Changes:**
```yaml
model:
  encoder_weights: "DTAI-KULeuven/robbert-2023-dutch-base"
  tokenizer_type: "byte_level_bpe"  # New field to track tokenizer type
  max_length: 512
  num_labels:
    ner: 3  # Simplified to O, B-PER, I-PER for initial implementation
```

### 4. Inference Pipeline Updates

**Key Changes:**
- Update tokenization calls to use RobBERT tokenizer
- Implement proper token-to-word alignment for output processing
- Update entity extraction logic for byte-level BPE tokens
- Maintain backward compatibility in API responses

## Data Models

### 1. Token Alignment Model
```python
@dataclass
class TokenAlignment:
    """Represents alignment between original words and subword tokens."""
    word_ids: List[int]  # Maps each token to original word index
    token_labels: List[str]  # Labels aligned to subword tokens
    original_labels: List[str]  # Original word-level labels
    
    def align_labels(self, word_labels: List[str]) -> List[str]:
        """Align word-level labels to subword tokens."""
        pass
```

### 2. Entity Extraction Model
```python
@dataclass
class ExtractedEntity:
    """Represents an extracted named entity."""
    text: str
    label: str
    start_char: int
    end_char: int
    confidence: float
    tokens: List[str]  # Subword tokens that form this entity
```

### 3. Model Configuration
```python
@dataclass
class RobBERTConfig:
    """Configuration for RobBERT-2023 model."""
    model_name: str = "DTAI-KULeuven/robbert-2023-dutch-base"
    num_labels: int = 3
    max_length: int = 512
    id2label: Dict[int, str] = field(default_factory=lambda: {0: "O", 1: "B-PER", 2: "I-PER"})
    label2id: Dict[str, int] = field(default_factory=lambda: {"O": 0, "B-PER": 1, "I-PER": 2})
```

## Error Handling

### 1. Tokenization Errors
- **OOV Handling**: Byte-level BPE should eliminate OOV tokens, but implement fallback for edge cases
- **Length Overflow**: Implement proper truncation with warning logging
- **Encoding Issues**: Handle character encoding problems gracefully

### 2. Model Loading Errors
- **Checkpoint Compatibility**: Detect and handle incompatible model checkpoints
- **Memory Issues**: Implement graceful degradation for memory constraints
- **Network Issues**: Handle model download failures with local fallbacks

### 3. Alignment Errors
- **Misaligned Labels**: Validate label alignment and log warnings for mismatches
- **Token Count Mismatch**: Handle cases where tokenization produces unexpected token counts
- **Label Mapping**: Ensure proper mapping between old and new label schemes

## Testing Strategy

### 1. Unit Tests

**Tokenizer Tests:**
```python
def test_robbert_tokenizer_basic():
    """Test basic tokenization functionality."""
    
def test_label_alignment():
    """Test word-to-subword label alignment."""
    
def test_oov_rate():
    """Test out-of-vocabulary token rate."""
```

**Model Tests:**
```python
def test_model_initialization():
    """Test RobBERT model initialization."""
    
def test_forward_pass():
    """Test model forward pass with sample input."""
    
def test_prediction_format():
    """Test prediction output format compatibility."""
```

### 2. Integration Tests

**Pipeline Tests:**
```python
def test_end_to_end_inference():
    """Test complete inference pipeline."""
    
def test_api_compatibility():
    """Test API endpoint compatibility."""
    
def test_batch_processing():
    """Test batch processing functionality."""
```

### 3. Performance Tests

**Tokenization Quality:**
- Compare OOV rates between BERTje and RobBERT
- Test handling of compound names, diacritics, and special characters
- Validate tokenization consistency across different text types

**Model Performance:**
- Benchmark inference speed on CPU
- Compare memory usage between models
- Test accuracy on existing test cases

### 4. Validation Tests

**Data Validation:**
```python
def test_tokenization_examples():
    """Test tokenization on diverse Dutch text examples."""
    
def test_compound_name_handling():
    """Test handling of compound Dutch names."""
    
def test_special_character_handling():
    """Test handling of diacritics and special characters."""
```

## Implementation Phases

### Phase 1: Core Model Replacement
1. Update `MultiTaskBERTje` class to use RobBERT-2023
2. Implement new tokenizer integration
3. Create simplified NER head (O, B-PER, I-PER)
4. Update configuration files

### Phase 2: Label Alignment System
1. Implement token alignment logic
2. Create alignment validation functions
3. Update entity extraction methods
4. Add comprehensive alignment tests

### Phase 3: Pipeline Integration
1. Update inference scripts
2. Modify API endpoints
3. Update training scripts
4. Ensure backward compatibility

### Phase 4: Testing and Validation
1. Create comprehensive test suite
2. Implement OOV analysis tools
3. Validate performance improvements
4. Document changes and migration guide

## Migration Strategy

### 1. Backward Compatibility
- Maintain existing API endpoints and response formats
- Preserve configuration file structure where possible
- Ensure existing test cases continue to pass

### 2. Gradual Rollout
- Implement feature flags for model selection
- Allow side-by-side comparison during transition
- Provide rollback mechanism if issues arise

### 3. Performance Monitoring
- Track tokenization quality metrics
- Monitor inference performance
- Compare accuracy on existing benchmarks

### 4. Documentation Updates
- Update API documentation
- Create migration guide for users
- Document new tokenization behavior

## Risk Mitigation

### 1. Model Performance Risk
- **Risk**: RobBERT may perform differently on existing test cases
- **Mitigation**: Comprehensive testing before deployment, gradual rollout

### 2. Tokenization Compatibility Risk
- **Risk**: Byte-level BPE may tokenize differently than expected
- **Mitigation**: Extensive tokenization testing, alignment validation

### 3. Memory and Performance Risk
- **Risk**: RobBERT may have different resource requirements
- **Mitigation**: Performance benchmarking, resource monitoring

### 4. Integration Risk
- **Risk**: Changes may break existing integrations
- **Mitigation**: Comprehensive integration testing, backward compatibility measures