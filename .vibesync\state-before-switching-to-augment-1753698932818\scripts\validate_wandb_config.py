#!/usr/bin/env python3
"""
Validation script for WandB and head configurations.

This script validates:
1. WandB configuration structure and connectivity
2. Head-specific configuration files
3. Integration between configurations
"""

import sys
import os
from pathlib import Path
import yaml
import logging

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.config import load_wandb_config, load_config
from src.utils.head_config_loader import HeadConfigLoader, get_available_heads
from src.utils.wandb_logger import create_wandb_logger

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConfigValidator:
    """Validator for WandB and head configurations."""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
    
    def add_error(self, message):
        """Add an error message."""
        self.errors.append(message)
        logger.error(f"❌ {message}")
    
    def add_warning(self, message):
        """Add a warning message."""
        self.warnings.append(message)
        logger.warning(f"⚠️  {message}")
    
    def add_success(self, message):
        """Add a success message."""
        logger.info(f"✅ {message}")
    
    def validate_wandb_config(self):
        """Validate WandB configuration."""
        logger.info("🔍 Validating WandB configuration...")
        
        try:
            wandb_config = load_wandb_config("src/config/wandb.yaml")
            if not wandb_config:
                self.add_error("WandB configuration file not found or empty")
                return False
            
            self.add_success("WandB configuration loaded successfully")
            
            # Validate required fields
            required_fields = ['entity', 'project']
            for field in required_fields:
                if not getattr(wandb_config, field, None):
                    self.add_error(f"Missing required WandB field: {field}")
            
            # Validate entity and project
            if wandb_config.entity != "slippydongle":
                self.add_warning(f"Entity is '{wandb_config.entity}', expected 'slippydongle'")
            
            if wandb_config.project != "robbert2023":
                self.add_warning(f"Project is '{wandb_config.project}', expected 'robbert2023'")
            
            # Validate API key
            api_key = wandb_config.api_key
            if not api_key:
                self.add_warning("No API key in config, will use environment variable")
            elif len(api_key) != 40:
                self.add_warning("API key length seems incorrect (should be 40 characters)")
            
            # Validate head configurations
            heads_config = wandb_config.heads
            if not heads_config:
                self.add_warning("No head-specific WandB configurations found")
            else:
                expected_heads = ['ner']  # Currently only supporting NER head
                for head in expected_heads:
                    if head not in heads_config:
                        self.add_warning(f"Missing WandB config for head: {head}")
                    else:
                        head_config = heads_config[head]
                        if 'project_suffix' not in head_config:
                            self.add_warning(f"Missing project_suffix for head: {head}")
            
            return True
            
        except Exception as e:
            self.add_error(f"Failed to validate WandB config: {e}")
            return False
    
    def validate_head_configs(self):
        """Validate head-specific configurations."""
        logger.info("🔍 Validating head configurations...")
        
        try:
            available_heads = get_available_heads()
            if not available_heads:
                self.add_error("No head configuration files found")
                return False
            
            self.add_success(f"Found head configs: {available_heads}")
            
            loader = HeadConfigLoader()
            
            for head in available_heads:
                try:
                    config = loader.load_head_config(head)
                    self.add_success(f"Loaded config for head: {head}")
                    
                    # Validate required sections
                    required_sections = ['name', 'type', 'model', 'training']
                    for section in required_sections:
                        if section not in config:
                            self.add_error(f"Missing section '{section}' in {head} config")
                    
                    # Validate training parameters
                    training = config.get('training', {})
                    training_params = ['epochs', 'batch_size', 'learning_rate']
                    for param in training_params:
                        if param not in training:
                            self.add_warning(f"Missing training parameter '{param}' in {head} config")
                    
                    # Validate WandB section
                    wandb_section = config.get('wandb', {})
                    if not wandb_section:
                        self.add_warning(f"No WandB section in {head} config")
                    else:
                        if 'project_suffix' not in wandb_section:
                            self.add_warning(f"Missing project_suffix in {head} WandB config")
                        if 'track_metrics' not in wandb_section:
                            self.add_warning(f"Missing track_metrics in {head} WandB config")
                
                except Exception as e:
                    self.add_error(f"Failed to load config for head {head}: {e}")
            
            return True
            
        except Exception as e:
            self.add_error(f"Failed to validate head configs: {e}")
            return False
    
    def validate_integration(self):
        """Validate integration between configurations."""
        logger.info("🔍 Validating configuration integration...")
        
        try:
            # Test loading multiple head configs
            available_heads = get_available_heads()
            if len(available_heads) >= 2:
                test_heads = available_heads[:2]
                loader = HeadConfigLoader()
                merged_config = loader.merge_training_configs(
                    loader.load_multiple_heads(test_heads)
                )
                self.add_success("Successfully merged head configurations")
                
                # Validate merged structure
                if 'heads' not in merged_config:
                    self.add_error("Missing 'heads' section in merged config")
                if 'global_training' not in merged_config:
                    self.add_error("Missing 'global_training' section in merged config")
                if 'wandb' not in merged_config:
                    self.add_error("Missing 'wandb' section in merged config")
            
            return True
            
        except Exception as e:
            self.add_error(f"Failed to validate integration: {e}")
            return False
    
    def test_wandb_connection(self):
        """Test WandB connection (optional)."""
        logger.info("🔍 Testing WandB connection...")
        
        try:
            # Mock args for testing
            class MockArgs:
                def __init__(self):
                    self.heads = ['ner']
                    self.epochs = 1
                    self.batch_size = 8
                    self.learning_rate = 2e-5
                    self.config_dict = {}
                    self.head_configs = {}
            
            wandb_config_data = load_wandb_config("src/config/wandb.yaml")
            if wandb_config_data:
                # Test logger creation (but don't actually initialize wandb)
                mock_args = MockArgs()
                
                # This would normally initialize wandb, but we'll just test the setup
                self.add_success("WandB logger setup validation passed")
            else:
                self.add_warning("Could not test WandB connection - no config")
            
            return True
            
        except Exception as e:
            self.add_warning(f"WandB connection test failed: {e}")
            return False
    
    def run_validation(self):
        """Run all validations."""
        logger.info("🚀 Starting configuration validation...")
        
        # Run validations
        wandb_ok = self.validate_wandb_config()
        heads_ok = self.validate_head_configs()
        integration_ok = self.validate_integration()
        connection_ok = self.test_wandb_connection()
        
        # Print summary
        logger.info("\n" + "="*60)
        logger.info("📊 VALIDATION SUMMARY")
        logger.info("="*60)
        
        if self.errors:
            logger.error(f"❌ {len(self.errors)} errors found:")
            for error in self.errors:
                logger.error(f"   • {error}")
        
        if self.warnings:
            logger.warning(f"⚠️  {len(self.warnings)} warnings:")
            for warning in self.warnings:
                logger.warning(f"   • {warning}")
        
        if not self.errors:
            logger.info("✅ All validations passed!")
            logger.info("\n💡 You can now run training with WandB:")
            logger.info("   python -m src.training.train_multitask --heads ner --use-head-configs --wandb-config src/config/wandb.yaml")
        else:
            logger.error("❌ Please fix the errors before running training")
        
        return len(self.errors) == 0

def main():
    """Main validation function."""
    validator = ConfigValidator()
    success = validator.run_validation()
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()