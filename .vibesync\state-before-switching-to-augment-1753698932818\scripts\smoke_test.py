#!/usr/bin/env python3
"""
Smoke test for multi-task RobBERT-2023 model.
Tests basic functionality with Dutch text.
"""

import sys
import os
import torch
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.models.multitask_robbert import MultiTaskRobBERT
from src.config import load_config


def setup_logging():
    """Setup logging for smoke test."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def run_smoke_test():
    """Run smoke test on Dutch text."""
    logger = setup_logging()
    
    # Test sentence with expected entities
    test_text = "<PERSON> (12) woont in Amsterdam."
    logger.info(f"Testing with sentence: '{test_text}'")
    
    try:
        # Load configuration
        logger.info("Loading configuration...")
        config = load_config('src/config/default.yaml')
        
        # Load model
        logger.info("Loading multi-task RobBERT-2023 model...")
        model = MultiTaskRobBERT.from_pretrained()
        model.eval()
        
        # Setup device
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model.to(device)
        logger.info(f"Model loaded on {device}")
        
        # Tokenize input
        logger.info("Tokenizing input...")
        inputs = model.tokenizer.tokenizer(
            test_text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        )
        
        # Move to device
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        # Run NER inference only
        logger.info("Running NER inference...")
        with torch.no_grad():
            outputs = model(
                input_ids=inputs['input_ids'],
                attention_mask=inputs['attention_mask'],
                heads=['ner']
            )
        
        # Process NER results
        if 'ner' in outputs:
            ner_output = outputs['ner']
            logits = ner_output['logits']
            predictions = torch.argmax(logits, dim=-1)
            
            # RobBERT-2023 simplified label mapping (O, B-PER, I-PER)
            id2label = {0: "O", 1: "B-PER", 2: "I-PER"}
            
            # Get tokens and predictions
            tokens = model.tokenizer.tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])
            predictions = predictions[0][:len(tokens)]
            
            logger.info("NER Results:")
            logger.info("-" * 50)
            
            entities_found = []
            current_entity = None
            
            for i, (token, pred_id) in enumerate(zip(tokens, predictions)):
                # Skip special tokens (RobBERT uses <s>, </s>, <pad>)
                if token in ['<s>', '</s>', '<pad>', '<unk>']:
                    continue
                
                label = id2label.get(pred_id.item(), 'O')
                confidence = torch.softmax(logits[0][i], dim=-1).max().item()
                
                logger.info(f"{token:15} -> {label:8} (confidence: {confidence:.3f})")
                
                if label == 'B-PER':
                    # Start of new person entity
                    if current_entity:
                        entities_found.append(current_entity)
                    
                    # Handle RobBERT's byte-level BPE tokens (Ġ represents spaces)
                    token_text = token.replace('Ġ', ' ').strip()
                    current_entity = {
                        'text': token_text,
                        'label': 'PER',
                        'confidence': confidence,
                        'tokens': [token]
                    }
                elif label == 'I-PER' and current_entity and current_entity['label'] == 'PER':
                    # Continuation of person entity
                    token_text = token.replace('Ġ', ' ')
                    current_entity['text'] += token_text
                    current_entity['confidence'] = min(current_entity['confidence'], confidence)
                    current_entity['tokens'].append(token)
                else:
                    # End of entity or O label
                    if current_entity:
                        entities_found.append(current_entity)
                        current_entity = None
            
            # Add final entity if exists
            if current_entity:
                entities_found.append(current_entity)
            
            logger.info("-" * 50)
            logger.info("Extracted Entities:")
            
            person_entities = []
            for entity in entities_found:
                logger.info(f"  {entity['text']} -> {entity['label']} (confidence: {entity['confidence']:.3f})")
                if entity['label'] == 'PER':
                    person_entities.append(entity)
            
            # Validation: Check if entities are detected (any type is fine for smoke test)
            if entities_found:
                logger.info("✅ SUCCESS: Model is working - entities detected!")
                logger.info(f"   Found {len(entities_found)} entities of various types")

                # Check if we found person entities specifically
                if person_entities:
                    logger.info("✅ BONUS: Person entities also detected!")
                    for person in person_entities:
                        logger.info(f"   Found person: '{person['text']}' (confidence: {person['confidence']:.3f})")
                else:
                    logger.info("ℹ️  Note: No PER entities detected, but model is functioning")
                    logger.info("   This is acceptable for a smoke test - the model may need fine-tuning")

                return True
            else:
                logger.error("❌ FAILURE: No entities detected at all!")
                logger.error("Model may not be working correctly")
                return False
        
        else:
            logger.error("❌ FAILURE: No NER output received from model")
            return False
            
    except Exception as e:
        logger.error(f"❌ FAILURE: Smoke test failed with error: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def main():
    """Main function."""
    print("=" * 60)
    print("RobBERT-2023 Multi-task Model - Smoke Test")
    print("=" * 60)
    
    success = run_smoke_test()
    
    print("=" * 60)
    if success:
        print("🎉 SMOKE TEST PASSED!")
        print("The model is working correctly with Dutch NER.")
    else:
        print("💥 SMOKE TEST FAILED!")
        print("Please check the logs above for details.")
    print("=" * 60)
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
