"""
Migration utilities for converting between data formats and ensuring compatibility.

This module provides utilities for migrating from existing JSONL format to the new
sentence + entities format used by the Hugging Face Trainer integration.
"""

import json
import re
import os
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime
import shutil
import logging

from ..utils.logging_utils import get_logger
from ..exceptions import DataProcessingError, ModelLoadingError


class DataFormatMigrator:
    """
    Handles migration between different data formats.
    
    Supports conversion from:
    - JSONL format (one JSON object per line)
    - Legacy tokenized format (tokens + labels arrays)
    - To new sentence + entities format
    """
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    def convert_jsonl_to_sentence_entities(
        self,
        input_path: Union[str, Path],
        output_path: Union[str, Path],
        format_type: str = "auto"
    ) -> Dict[str, Any]:
        """
        Convert JSONL format to sentence + entities format.
        
        Args:
            input_path: Path to input JSONL file
            output_path: Path to output JSON file
            format_type: Input format type ("auto", "tokenized", "conll")
            
        Returns:
            Conversion statistics
        """
        self.logger.logger.info(f"Converting JSONL to sentence+entities format")
        self.logger.logger.info(f"  Input: {input_path}")
        self.logger.logger.info(f"  Output: {output_path}")
        
        input_path = Path(input_path)
        output_path = Path(output_path)
        
        if not input_path.exists():
            raise DataProcessingError(f"Input file not found: {input_path}")
        
        # Create output directory
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        converted_examples = []
        conversion_stats = {
            "total_lines": 0,
            "converted_examples": 0,
            "skipped_lines": 0,
            "errors": 0,
            "entity_count": 0,
            "format_detected": None
        }
        
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    conversion_stats["total_lines"] += 1
                    
                    try:
                        line = line.strip()
                        if not line:
                            conversion_stats["skipped_lines"] += 1
                            continue
                        
                        data = json.loads(line)
                        
                        # Auto-detect format on first valid line
                        if conversion_stats["format_detected"] is None:
                            conversion_stats["format_detected"] = self._detect_format(data)
                            self.logger.logger.info(f"Detected format: {conversion_stats['format_detected']}")
                        
                        # Convert based on detected format
                        if conversion_stats["format_detected"] == "tokenized":
                            example = self._convert_tokenized_format(data, line_num)
                        elif conversion_stats["format_detected"] == "conll":
                            example = self._convert_conll_format(data, line_num)
                        elif conversion_stats["format_detected"] == "sentence_entities":
                            # Already in correct format
                            example = self._validate_sentence_entities_format(data, line_num)
                        else:
                            self.logger.logger.warning(f"Unknown format at line {line_num}, skipping")
                            conversion_stats["skipped_lines"] += 1
                            continue
                        
                        if example:
                            converted_examples.append(example)
                            conversion_stats["converted_examples"] += 1
                            conversion_stats["entity_count"] += len(example.get("entities", []))
                        else:
                            conversion_stats["skipped_lines"] += 1
                    
                    except json.JSONDecodeError as e:
                        self.logger.logger.warning(f"Invalid JSON at line {line_num}: {e}")
                        conversion_stats["errors"] += 1
                    except Exception as e:
                        self.logger.logger.warning(f"Error processing line {line_num}: {e}")
                        conversion_stats["errors"] += 1
            
            # Save converted data
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(converted_examples, f, indent=2, ensure_ascii=False)
            
            # Log conversion statistics
            self.logger.logger.info("Conversion completed:")
            self.logger.logger.info(f"  - Total lines processed: {conversion_stats['total_lines']}")
            self.logger.logger.info(f"  - Examples converted: {conversion_stats['converted_examples']}")
            self.logger.logger.info(f"  - Lines skipped: {conversion_stats['skipped_lines']}")
            self.logger.logger.info(f"  - Errors: {conversion_stats['errors']}")
            self.logger.logger.info(f"  - Total entities: {conversion_stats['entity_count']}")
            
            return conversion_stats
            
        except Exception as e:
            error_msg = f"Failed to convert JSONL file: {e}"
            self.logger.logger.error(error_msg)
            raise DataProcessingError(error_msg) from e
    
    def _detect_format(self, data: Dict[str, Any]) -> str:
        """
        Detect the format of input data.
        
        Args:
            data: Sample data record
            
        Returns:
            Detected format type
        """
        # Check for sentence + entities format
        if "sentence" in data and "entities" in data:
            return "sentence_entities"
        
        # Check for tokenized format (tokens + labels arrays)
        if "tokens" in data and "labels" in data:
            return "tokenized"
        
        # Check for CoNLL-style format
        if "words" in data and "ner_tags" in data:
            return "conll"
        
        # Check for alternative tokenized format
        if "input_ids" in data and "labels" in data:
            return "tokenized"
        
        return "unknown"
    
    def _convert_tokenized_format(self, data: Dict[str, Any], line_num: int) -> Optional[Dict[str, Any]]:
        """
        Convert tokenized format to sentence + entities format.
        
        Args:
            data: Tokenized data record
            line_num: Line number for error reporting
            
        Returns:
            Converted example or None if conversion failed
        """
        try:
            tokens = data.get("tokens", [])
            labels = data.get("labels", [])
            
            if not tokens or not labels:
                self.logger.logger.warning(f"Empty tokens or labels at line {line_num}")
                return None
            
            if len(tokens) != len(labels):
                self.logger.logger.warning(f"Token/label length mismatch at line {line_num}")
                return None
            
            # Reconstruct sentence from tokens
            sentence = self._reconstruct_sentence_from_tokens(tokens)
            
            # Extract entities from BIO labels
            entities = self._extract_entities_from_bio_labels(tokens, labels, sentence)
            
            return {
                "id": data.get("id", line_num),
                "sentence": sentence,
                "entities": entities
            }
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to convert tokenized format at line {line_num}: {e}")
            return None
    
    def _convert_conll_format(self, data: Dict[str, Any], line_num: int) -> Optional[Dict[str, Any]]:
        """
        Convert CoNLL format to sentence + entities format.
        
        Args:
            data: CoNLL data record
            line_num: Line number for error reporting
            
        Returns:
            Converted example or None if conversion failed
        """
        try:
            words = data.get("words", [])
            ner_tags = data.get("ner_tags", [])
            
            if not words or not ner_tags:
                return None
            
            if len(words) != len(ner_tags):
                self.logger.logger.warning(f"Words/tags length mismatch at line {line_num}")
                return None
            
            # Reconstruct sentence
            sentence = " ".join(words)
            
            # Convert numeric tags to string labels if needed
            if isinstance(ner_tags[0], int):
                # Assume standard BIO mapping: 0=O, 1=B-PER, 2=I-PER
                label_map = {0: "O", 1: "B-PER", 2: "I-PER"}
                string_labels = [label_map.get(tag, "O") for tag in ner_tags]
            else:
                string_labels = ner_tags
            
            # Extract entities
            entities = self._extract_entities_from_bio_labels(words, string_labels, sentence)
            
            return {
                "id": data.get("id", line_num),
                "sentence": sentence,
                "entities": entities
            }
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to convert CoNLL format at line {line_num}: {e}")
            return None
    
    def _validate_sentence_entities_format(self, data: Dict[str, Any], line_num: int) -> Optional[Dict[str, Any]]:
        """
        Validate and normalize sentence + entities format.
        
        Args:
            data: Data in sentence + entities format
            line_num: Line number for error reporting
            
        Returns:
            Validated example or None if validation failed
        """
        try:
            sentence = data.get("sentence", "")
            entities = data.get("entities", [])
            
            if not sentence:
                return None
            
            # Validate and normalize entities
            normalized_entities = []
            for entity in entities:
                if not isinstance(entity, dict):
                    continue
                
                text = entity.get("text", "")
                label = entity.get("label", "PER")
                
                if not text:
                    continue
                
                # Add start/end positions if missing
                if "start" not in entity or "end" not in entity:
                    # Try to find entity in sentence
                    match = re.search(re.escape(text), sentence)
                    if match:
                        entity["start"] = match.start()
                        entity["end"] = match.end()
                    else:
                        self.logger.logger.warning(f"Entity '{text}' not found in sentence at line {line_num}")
                        continue
                
                normalized_entities.append({
                    "text": text,
                    "label": label,
                    "start": entity.get("start"),
                    "end": entity.get("end")
                })
            
            return {
                "id": data.get("id", line_num),
                "sentence": sentence,
                "entities": normalized_entities
            }
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to validate sentence+entities format at line {line_num}: {e}")
            return None
    
    def _reconstruct_sentence_from_tokens(self, tokens: List[str]) -> str:
        """
        Reconstruct sentence from tokenized format.
        
        Args:
            tokens: List of tokens
            
        Returns:
            Reconstructed sentence
        """
        # Handle RobBERT/RoBERTa tokenization
        sentence_parts = []
        
        for token in tokens:
            # Skip special tokens
            if token in ["<s>", "</s>", "<pad>", "<unk>", "<mask>"]:
                continue
            
            # Handle RobBERT byte-level BPE tokens
            if token.startswith("Ġ"):
                # Ġ indicates start of word in RobBERT
                sentence_parts.append(" " + token[1:])
            elif token.startswith("##"):
                # WordPiece continuation
                if sentence_parts:
                    sentence_parts[-1] += token[2:]
                else:
                    sentence_parts.append(token[2:])
            else:
                # Regular token
                if sentence_parts and not sentence_parts[-1].endswith(" "):
                    sentence_parts[-1] += token
                else:
                    sentence_parts.append(token)
        
        return "".join(sentence_parts).strip()
    
    def _extract_entities_from_bio_labels(
        self,
        tokens: List[str],
        labels: List[str],
        sentence: str
    ) -> List[Dict[str, Any]]:
        """
        Extract entities from BIO-tagged tokens.
        
        Args:
            tokens: List of tokens
            labels: List of BIO labels
            sentence: Original sentence text
            
        Returns:
            List of entity dictionaries
        """
        entities = []
        current_entity = None
        
        for i, (token, label) in enumerate(zip(tokens, labels)):
            # Skip special tokens
            if token in ["<s>", "</s>", "<pad>", "<unk>", "<mask>"]:
                continue
            
            if label.startswith("B-"):
                # Start of new entity
                if current_entity:
                    entities.append(self._finalize_entity(current_entity, sentence))
                
                current_entity = {
                    "label": label[2:],  # Remove B- prefix
                    "tokens": [token],
                    "token_indices": [i]
                }
                
            elif label.startswith("I-") and current_entity and current_entity["label"] == label[2:]:
                # Continuation of current entity
                current_entity["tokens"].append(token)
                current_entity["token_indices"].append(i)
                
            else:
                # End of entity or O label
                if current_entity:
                    entities.append(self._finalize_entity(current_entity, sentence))
                    current_entity = None
        
        # Add final entity if exists
        if current_entity:
            entities.append(self._finalize_entity(current_entity, sentence))
        
        return entities
    
    def _finalize_entity(self, entity_data: Dict[str, Any], sentence: str) -> Dict[str, Any]:
        """
        Finalize entity by reconstructing text and finding positions.
        
        Args:
            entity_data: Partial entity data
            sentence: Original sentence
            
        Returns:
            Complete entity dictionary
        """
        # Reconstruct entity text from tokens
        entity_text = self._reconstruct_sentence_from_tokens(entity_data["tokens"])
        
        # Find entity position in sentence
        start_pos = sentence.find(entity_text)
        if start_pos == -1:
            # Try fuzzy matching for tokenization differences
            entity_text_clean = re.sub(r'\s+', ' ', entity_text).strip()
            start_pos = sentence.find(entity_text_clean)
        
        if start_pos == -1:
            # Use approximate position
            start_pos = 0
            end_pos = len(entity_text)
        else:
            end_pos = start_pos + len(entity_text)
        
        return {
            "text": entity_text,
            "label": entity_data["label"],
            "start": start_pos,
            "end": end_pos
        }


class CheckpointCompatibilityValidator:
    """
    Validates compatibility between different checkpoint formats and model versions.
    """
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    def validate_checkpoint_compatibility(
        self,
        checkpoint_path: Union[str, Path],
        target_model_name: str = "DTAI-KULeuven/robbert-2023-dutch-base"
    ) -> Dict[str, Any]:
        """
        Validate checkpoint compatibility with target model.
        
        Args:
            checkpoint_path: Path to checkpoint directory
            target_model_name: Target model name for compatibility check
            
        Returns:
            Compatibility report
        """
        self.logger.logger.info(f"Validating checkpoint compatibility: {checkpoint_path}")
        
        checkpoint_path = Path(checkpoint_path)
        
        if not checkpoint_path.exists():
            raise ModelLoadingError(f"Checkpoint path not found: {checkpoint_path}")
        
        compatibility_report = {
            "checkpoint_path": str(checkpoint_path),
            "target_model": target_model_name,
            "is_compatible": False,
            "issues": [],
            "warnings": [],
            "recommendations": [],
            "files_found": {},
            "model_info": {}
        }
        
        try:
            # Check for required files
            required_files = ["config.json", "pytorch_model.bin"]
            optional_files = ["tokenizer.json", "tokenizer_config.json", "vocab.json", "merges.txt"]
            
            for file_name in required_files:
                file_path = checkpoint_path / file_name
                compatibility_report["files_found"][file_name] = file_path.exists()
                
                if not file_path.exists():
                    compatibility_report["issues"].append(f"Missing required file: {file_name}")
            
            for file_name in optional_files:
                file_path = checkpoint_path / file_name
                compatibility_report["files_found"][file_name] = file_path.exists()
            
            # Load and validate config
            config_path = checkpoint_path / "config.json"
            if config_path.exists():
                try:
                    with open(config_path, 'r') as f:
                        config = json.load(f)
                    
                    compatibility_report["model_info"]["config"] = config
                    
                    # Check model architecture
                    model_type = config.get("model_type", "unknown")
                    if model_type not in ["roberta", "bert"]:
                        compatibility_report["warnings"].append(f"Unexpected model type: {model_type}")
                    
                    # Check label configuration
                    num_labels = config.get("num_labels")
                    id2label = config.get("id2label", {})
                    label2id = config.get("label2id", {})
                    
                    if num_labels:
                        compatibility_report["model_info"]["num_labels"] = num_labels
                        
                        # Check for NER-compatible labels
                        if num_labels == 3 and "B-PER" in str(id2label.values()):
                            compatibility_report["model_info"]["ner_compatible"] = True
                        else:
                            compatibility_report["warnings"].append(
                                f"Label configuration may not be NER-compatible (num_labels={num_labels})"
                            )
                    
                    # Check hidden size compatibility
                    hidden_size = config.get("hidden_size")
                    if hidden_size and hidden_size != 768:
                        compatibility_report["warnings"].append(
                            f"Non-standard hidden size: {hidden_size} (expected 768 for RobBERT)"
                        )
                    
                except json.JSONDecodeError as e:
                    compatibility_report["issues"].append(f"Invalid config.json: {e}")
                except Exception as e:
                    compatibility_report["issues"].append(f"Error reading config.json: {e}")
            
            # Check model weights
            model_path = checkpoint_path / "pytorch_model.bin"
            if model_path.exists():
                try:
                    import torch
                    state_dict = torch.load(model_path, map_location='cpu')
                    
                    compatibility_report["model_info"]["state_dict_keys"] = list(state_dict.keys())[:10]  # First 10 keys
                    compatibility_report["model_info"]["total_parameters"] = sum(
                        p.numel() for p in state_dict.values() if hasattr(p, 'numel')
                    )
                    
                    # Check for expected RobBERT keys
                    expected_keys = ["roberta.embeddings.word_embeddings.weight", "classifier.weight"]
                    missing_keys = [key for key in expected_keys if key not in state_dict]
                    
                    if missing_keys:
                        compatibility_report["warnings"].append(f"Missing expected keys: {missing_keys}")
                    
                except Exception as e:
                    compatibility_report["issues"].append(f"Error loading model weights: {e}")
            
            # Determine overall compatibility
            if not compatibility_report["issues"]:
                compatibility_report["is_compatible"] = True
                compatibility_report["recommendations"].append("Checkpoint appears compatible")
            else:
                compatibility_report["recommendations"].append("Address issues before using checkpoint")
            
            # Add migration recommendations
            if compatibility_report["warnings"]:
                compatibility_report["recommendations"].append(
                    "Consider retraining with current Hugging Face Trainer for optimal compatibility"
                )
            
            self.logger.logger.info(f"Compatibility check completed: {'✓' if compatibility_report['is_compatible'] else '✗'}")
            
            return compatibility_report
            
        except Exception as e:
            error_msg = f"Failed to validate checkpoint compatibility: {e}"
            self.logger.logger.error(error_msg)
            raise ModelLoadingError(error_msg) from e
    
    def migrate_checkpoint_format(
        self,
        source_path: Union[str, Path],
        target_path: Union[str, Path],
        target_format: str = "hf_trainer"
    ) -> Dict[str, Any]:
        """
        Migrate checkpoint to target format.
        
        Args:
            source_path: Source checkpoint path
            target_path: Target checkpoint path
            target_format: Target format ("hf_trainer", "legacy")
            
        Returns:
            Migration report
        """
        self.logger.logger.info(f"Migrating checkpoint format")
        self.logger.logger.info(f"  Source: {source_path}")
        self.logger.logger.info(f"  Target: {target_path}")
        self.logger.logger.info(f"  Format: {target_format}")
        
        source_path = Path(source_path)
        target_path = Path(target_path)
        
        if not source_path.exists():
            raise ModelLoadingError(f"Source checkpoint not found: {source_path}")
        
        # Create target directory
        target_path.mkdir(parents=True, exist_ok=True)
        
        migration_report = {
            "source_path": str(source_path),
            "target_path": str(target_path),
            "target_format": target_format,
            "files_migrated": [],
            "files_created": [],
            "warnings": [],
            "success": False
        }
        
        try:
            if target_format == "hf_trainer":
                # Migrate to Hugging Face Trainer format
                self._migrate_to_hf_format(source_path, target_path, migration_report)
            elif target_format == "legacy":
                # Migrate to legacy format
                self._migrate_to_legacy_format(source_path, target_path, migration_report)
            else:
                raise ValueError(f"Unsupported target format: {target_format}")
            
            migration_report["success"] = True
            self.logger.logger.info("Checkpoint migration completed successfully")
            
            return migration_report
            
        except Exception as e:
            error_msg = f"Failed to migrate checkpoint: {e}"
            self.logger.logger.error(error_msg)
            raise ModelLoadingError(error_msg) from e
    
    def _migrate_to_hf_format(
        self,
        source_path: Path,
        target_path: Path,
        report: Dict[str, Any]
    ) -> None:
        """Migrate checkpoint to Hugging Face Trainer format."""
        # Copy standard files
        standard_files = ["config.json", "pytorch_model.bin", "tokenizer.json", "tokenizer_config.json"]
        
        for file_name in standard_files:
            source_file = source_path / file_name
            target_file = target_path / file_name
            
            if source_file.exists():
                shutil.copy2(source_file, target_file)
                report["files_migrated"].append(file_name)
        
        # Create training_args.bin if not exists (for Trainer compatibility)
        training_args_path = target_path / "training_args.bin"
        if not training_args_path.exists():
            # Create minimal training args
            import torch
            from transformers import TrainingArguments
            
            dummy_args = TrainingArguments(output_dir=str(target_path))
            torch.save(dummy_args, training_args_path)
            report["files_created"].append("training_args.bin")
        
        # Create README.md with usage instructions
        readme_path = target_path / "README.md"
        if not readme_path.exists():
            readme_content = self._generate_checkpoint_readme(target_path)
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            report["files_created"].append("README.md")
    
    def _migrate_to_legacy_format(
        self,
        source_path: Path,
        target_path: Path,
        report: Dict[str, Any]
    ) -> None:
        """Migrate checkpoint to legacy format."""
        # Copy essential files
        essential_files = ["config.json", "pytorch_model.bin"]
        
        for file_name in essential_files:
            source_file = source_path / file_name
            target_file = target_path / file_name
            
            if source_file.exists():
                shutil.copy2(source_file, target_file)
                report["files_migrated"].append(file_name)
    
    def _generate_checkpoint_readme(self, checkpoint_path: Path) -> str:
        """Generate README for migrated checkpoint."""
        return f"""# RobBERT-2023 NER Checkpoint

This checkpoint has been migrated to Hugging Face Trainer format.

## Usage

### Loading the model

```python
from transformers import RobertaForTokenClassification, AutoTokenizer

model = RobertaForTokenClassification.from_pretrained("{checkpoint_path}")
tokenizer = AutoTokenizer.from_pretrained("{checkpoint_path}")
```

### Using with Hugging Face Trainer

```python
from src.training.hf_trainer import HFNERTrainer
from src.training.hf_config import HFTrainingConfig

config = HFTrainingConfig()
trainer = HFNERTrainer(config)

# Resume training
trainer.train(
    data_path="path/to/data.json",
    resume_from_checkpoint="{checkpoint_path}"
)
```

## Migration Information

- Migrated on: {datetime.now().isoformat()}
- Format: Hugging Face Trainer compatible
- Model: RobBERT-2023 for Dutch NER
- Labels: O, B-PER, I-PER

## Compatibility

This checkpoint is compatible with:
- Hugging Face Transformers library
- RobBERT-2023 tokenizer
- Current inference API endpoints
"""


class ConfigurationMigrator:
    """
    Handles migration of training configurations between different formats.
    """
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    def migrate_training_config(
        self,
        source_config_path: Union[str, Path],
        target_config_path: Union[str, Path],
        target_format: str = "hf_trainer"
    ) -> Dict[str, Any]:
        """
        Migrate training configuration to target format.
        
        Args:
            source_config_path: Source configuration file
            target_config_path: Target configuration file
            target_format: Target format ("hf_trainer", "legacy")
            
        Returns:
            Migration report
        """
        self.logger.logger.info(f"Migrating configuration to {target_format} format")
        self.logger.logger.info(f"  Source: {source_config_path}")
        self.logger.logger.info(f"  Target: {target_config_path}")
        
        source_path = Path(source_config_path)
        target_path = Path(target_config_path)
        
        if not source_path.exists():
            raise DataProcessingError(f"Source config file not found: {source_path}")
        
        # Create target directory
        target_path.parent.mkdir(parents=True, exist_ok=True)
        
        migration_report = {
            "source_path": str(source_path),
            "target_path": str(target_path),
            "target_format": target_format,
            "success": False,
            "warnings": [],
            "converted_parameters": {},
            "unmapped_parameters": []
        }
        
        try:
            # Load source configuration
            import yaml
            with open(source_path, 'r', encoding='utf-8') as f:
                source_config = yaml.safe_load(f)
            
            if target_format == "hf_trainer":
                target_config = self._convert_to_hf_trainer_config(source_config, migration_report)
            else:
                raise ValueError(f"Unsupported target format: {target_format}")
            
            # Save target configuration
            with open(target_path, 'w', encoding='utf-8') as f:
                yaml.dump(target_config, f, default_flow_style=False, indent=2)
            
            migration_report["success"] = True
            self.logger.logger.info("Configuration migration completed successfully")
            
            return migration_report
            
        except Exception as e:
            error_msg = f"Failed to migrate configuration: {e}"
            self.logger.logger.error(error_msg)
            raise DataProcessingError(error_msg) from e
    
    def _convert_to_hf_trainer_config(
        self,
        source_config: Dict[str, Any],
        report: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Convert legacy config to HF Trainer format."""
        target_config = {
            "hf_training": {
                "model_name": "DTAI-KULeuven/robbert-2023-dutch-base",
                "epochs": 3,
                "batch_size": 8,
                "eval_batch_size": 8,
                "learning_rate": 5e-5,
                "weight_decay": 0.01,
                "warmup_steps": 500,
                "eval_steps": 100,
                "logging_steps": 50,
                "save_steps": 500,
                "early_stopping_patience": 3,
                "early_stopping_threshold": 0.001,
                "load_best_model_at_end": True,
                "metric_for_best_model": "eval_f1",
                "use_class_weights": False,
                "wandb_project": "robbert2023-ner",
                "wandb_entity": "slippydongle",
                "use_gpu": True,
                "fp16": True,
                "dataloader_num_workers": 2,
                "dataloader_pin_memory": True,
                "save_total_limit": 3,
                "push_to_hub": False,
                "run_version": "v1.0",
                "test_mode": False,
                "test_sample_limit": 50,
                "test_epochs": 1,
                "use_cache": True,
                "cache_dir": "data/cache",
                "save_predictions": True,
                "generate_model_card": True
            }
        }
        
        # Map legacy parameters to HF Trainer format
        parameter_mapping = {
            # Training parameters
            ("training", "epochs"): ("hf_training", "epochs"),
            ("training", "batch_size"): ("hf_training", "batch_size"),
            ("training", "learning_rate"): ("hf_training", "learning_rate"),
            ("training", "weight_decay"): ("hf_training", "weight_decay"),
            ("training", "warmup_steps"): ("hf_training", "warmup_steps"),
            ("training", "max_grad_norm"): None,  # Handled by Trainer
            
            # Model parameters
            ("model", "model_name"): ("hf_training", "model_name"),
            ("model", "max_length"): None,  # Handled by tokenizer
            
            # WandB parameters
            ("wandb", "project"): ("hf_training", "wandb_project"),
            ("wandb", "entity"): ("hf_training", "wandb_entity"),
            
            # Evaluation parameters
            ("evaluation", "eval_steps"): ("hf_training", "eval_steps"),
            ("evaluation", "logging_steps"): ("hf_training", "logging_steps"),
            ("evaluation", "save_steps"): ("hf_training", "save_steps"),
        }
        
        # Apply parameter mapping
        for source_path, target_path in parameter_mapping.items():
            if target_path is None:
                continue
                
            source_section, source_key = source_path
            target_section, target_key = target_path
            
            if (source_section in source_config and 
                source_key in source_config[source_section]):
                
                source_value = source_config[source_section][source_key]
                target_config[target_section][target_key] = source_value
                
                report["converted_parameters"][f"{source_section}.{source_key}"] = {
                    "source_value": source_value,
                    "target_path": f"{target_section}.{target_key}"
                }
        
        # Handle unmapped parameters
        for section_name, section_data in source_config.items():
            if isinstance(section_data, dict):
                for key, value in section_data.items():
                    source_param = f"{section_name}.{key}"
                    if source_param not in report["converted_parameters"]:
                        report["unmapped_parameters"].append({
                            "parameter": source_param,
                            "value": value,
                            "reason": "No mapping defined"
                        })
        
        # Add warnings for significant changes
        if "loss_weights" in source_config.get("training", {}):
            report["warnings"].append(
                "Loss weights configuration needs manual review - "
                "HF Trainer uses different loss weighting approach"
            )
        
        if "heads" in source_config.get("model", {}):
            report["warnings"].append(
                "Multi-head configuration detected - "
                "HF Trainer integration focuses on single-head NER training"
            )
        
        return target_config


# Export main classes
__all__ = [
    'DataFormatMigrator',
    'CheckpointCompatibilityValidator', 
    'ConfigurationMigrator'
]