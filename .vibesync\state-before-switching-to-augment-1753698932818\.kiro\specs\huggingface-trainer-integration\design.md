# Design Document

## Overview

This design outlines the integration of Hugging Face Datasets and Trainer classes into the existing RobBERT-2023 Dutch NER training pipeline. The current system uses custom PyTorch training loops with manual WandB integration, but we want to leverage the Hugging Face ecosystem for more streamlined training, better evaluation metrics, and enhanced experiment tracking.

The key architectural change is replacing custom training loops with the standardized Hugging Face Trainer API while maintaining compatibility with the existing RobBERT-2023 model, WandB integration, and multi-head architecture.

## Architecture

### Current Architecture (Custom Training)
```
JSON Data → Custom Dataset → Custom DataLoader → Custom Training Loop → Manual WandB Logging → Custom Checkpointing
```

### New Architecture (Hugging Face Trainer)
```
JSON Data → datasets.Dataset → DataCollatorForTokenClassification → Trainer → Built-in WandB Integration → Automatic Checkpointing
```

### Key Changes
1. **Data Loading**: Replace custom dataset building with `datasets.load_dataset()`
2. **Training Loop**: Replace custom PyTorch training with `Trainer` class
3. **Data Collation**: Use `DataCollatorForTokenClassification` for proper padding
4. **Evaluation**: Integrate `seqeval` metrics for proper NER evaluation
5. **WandB Integration**: Use built-in `report_to="wandb"` instead of custom logger
6. **Model Compatibility**: Maintain `RobertaForTokenClassification` compatibility

## Components and Interfaces

### 1. Data Processing Component

**Current Implementation:**
```python
def build_robbert_dataset(jsonl_path: str, tokenizer, config, train_split: float = 0.8):
    # Custom dataset building with manual tokenization
    data = []
    with open(jsonl_path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line.strip()))
    # ... manual processing
```

**New Implementation:**
```python
from datasets import load_dataset, Dataset

def prepare_ner_dataset(data_path: str, tokenizer, train_split: float = 0.8):
    """
    Load and prepare dataset using Hugging Face datasets.
    
    Expected JSON format (sentence + entities):
    [
        {
            "id": 1,
            "sentence": "Jan Jansen woont in Amsterdam.",
            "entities": [
                {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10}
            ]
        }
    ]
    
    Or simplified format (auto-detect spans):
    [
        {
            "id": 1,
            "sentence": "Jan Jansen woont in Amsterdam.",
            "entities": [
                {"text": "Jan Jansen", "label": "PER"}
            ]
        }
    ]
    """
    # Load dataset
    dataset = load_dataset("json", data_files=data_path, split="train")
    
    # Dynamic label discovery or use default
    all_labels = set()
    for example in dataset:
        for entity in example.get("entities", []):
            all_labels.add(entity.get("label", "PER"))
    
    # Create label scheme with BIO tagging
    entity_labels = sorted(all_labels) if all_labels else ["PER"]
    label_list = ["O"] + [f"B-{label}" for label in entity_labels] + [f"I-{label}" for label in entity_labels]
    label2id = {label: i for i, label in enumerate(label_list)}
    
    def get_entity_spans(sentence, entities):
        """Convert entities to character spans."""
        spans = []
        for entity in entities:
            text = entity["text"]
            label = entity.get("label", "PER")
            
            # Use provided spans if available
            if "start" in entity and "end" in entity:
                spans.append({"start": entity["start"], "end": entity["end"], "label": label})
            else:
                # Auto-detect spans using regex
                import re
                match = re.search(re.escape(text), sentence)
                if match:
                    spans.append({"start": match.start(), "end": match.end(), "label": label})
                else:
                    print(f"Warning: Entity '{text}' not found in sentence: {sentence}")
        return spans
    
    def tokenize_and_align_labels(examples):
        """Tokenize sentences and align entity labels with subword tokens."""
        tokenized_inputs = tokenizer(
            examples["sentence"],
            truncation=True,
            padding=False,  # Padding handled by data collator
            return_offsets_mapping=True
        )
        
        labels = []
        for i, (sentence, entities) in enumerate(zip(examples["sentence"], examples["entities"])):
            # Get entity spans
            entity_spans = get_entity_spans(sentence, entities)
            
            # Initialize labels for all tokens
            token_labels = ["O"] * len(tokenized_inputs.input_ids[i])
            offsets = tokenized_inputs.offset_mapping[i]
            
            # Assign labels based on character spans
            for span in entity_spans:
                start_char, end_char, label = span["start"], span["end"], span["label"]
                inside_entity = False
                
                for token_idx, (token_start, token_end) in enumerate(offsets):
                    if token_start is None or token_end is None:  # Special tokens
                        continue
                    
                    # Check if token overlaps with entity span
                    if token_start >= end_char:
                        break
                    if token_end > start_char and token_start < end_char:
                        if not inside_entity:
                            token_labels[token_idx] = f"B-{label}"
                            inside_entity = True
                        else:
                            token_labels[token_idx] = f"I-{label}"
            
            # Convert labels to IDs
            label_ids = [label2id[label] for label in token_labels]
            labels.append(label_ids)
        
        # Remove offset_mapping as it's not needed for training
        tokenized_inputs.pop("offset_mapping")
        tokenized_inputs["labels"] = labels
        return tokenized_inputs
    
    # Apply tokenization
    tokenized_dataset = dataset.map(tokenize_and_align_labels, batched=True)
    
    # Split dataset
    train_test_split = tokenized_dataset.train_test_split(test_size=1-train_split)
    return train_test_split["train"], train_test_split["test"], label_list
```

### 2. Model Architecture Component

**Current Implementation:**
```python
# Custom multi-task model with manual head management
model = MultiTaskRobBERT.from_pretrained()
```

**New Implementation:**
```python
from transformers import RobertaForTokenClassification, AutoTokenizer

def create_ner_model():
    """Create RobBERT model for token classification."""
    model_name = "DTAI-KULeuven/robbert-2023-dutch-base"
    
    # Label configuration
    label_list = ["O", "B-PER", "I-PER"]
    id2label = {i: label for i, label in enumerate(label_list)}
    label2id = {label: i for label, i in id2label.items()}
    
    # Load model
    model = RobertaForTokenClassification.from_pretrained(
        model_name,
        num_labels=len(label_list),
        id2label=id2label,
        label2id=label2id
    )
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    
    return model, tokenizer
```

### 3. Training Configuration Component

**New Implementation:**
```python
from transformers import TrainingArguments

def create_training_args(output_dir: str, **kwargs):
    """Create training arguments with WandB integration."""
    return TrainingArguments(
        output_dir=output_dir,
        
        # Training parameters
        num_train_epochs=kwargs.get('epochs', 3),
        per_device_train_batch_size=kwargs.get('batch_size', 8),
        per_device_eval_batch_size=kwargs.get('eval_batch_size', 8),
        learning_rate=kwargs.get('learning_rate', 5e-5),
        weight_decay=kwargs.get('weight_decay', 0.01),
        
        # Evaluation and logging
        evaluation_strategy="steps",
        eval_steps=kwargs.get('eval_steps', 100),
        logging_steps=kwargs.get('logging_steps', 50),
        save_steps=kwargs.get('save_steps', 500),
        
        # WandB integration
        report_to="wandb",
        run_name=kwargs.get('run_name'),
        
        # Optimization
        warmup_steps=kwargs.get('warmup_steps', 500),
        lr_scheduler_type="linear",
        
        # Early stopping and checkpointing
        load_best_model_at_end=True,
        metric_for_best_model="eval_f1",
        greater_is_better=True,
        save_total_limit=3,
        
        # Hardware optimization
        fp16=torch.cuda.is_available(),
        dataloader_pin_memory=True,
        dataloader_num_workers=2,
    )
```

### 4. Evaluation Metrics Component

**New Implementation:**
```python
from datasets import load_metric
import numpy as np

def compute_metrics(eval_pred):
    """Compute NER metrics using seqeval."""
    predictions, labels = eval_pred
    predictions = np.argmax(predictions, axis=2)
    
    # Remove ignored index (special tokens)
    true_predictions = [
        [label_list[p] for (p, l) in zip(prediction, label) if l != -100]
        for prediction, label in zip(predictions, labels)
    ]
    true_labels = [
        [label_list[l] for (p, l) in zip(prediction, label) if l != -100]
        for prediction, label in zip(predictions, labels)
    ]
    
    # Load seqeval metric
    metric = load_metric("seqeval")
    results = metric.compute(predictions=true_predictions, references=true_labels)
    
    return {
        "precision": results["overall_precision"],
        "recall": results["overall_recall"],
        "f1": results["overall_f1"],
        "accuracy": results["overall_accuracy"],
    }
```

### 5. Data Preprocessing Component

**New Implementation:**
```python
import json
import re
from pathlib import Path
from transformers import RobertaTokenizerFast

def preprocess_raw_data(input_path: str, output_path: str, model_name: str = "DTAI-KULeuven/robbert-2023-dutch-base"):
    """
    Convert raw sentence + entities format to tokenized format.
    
    Input format:
    {"id": 1, "sentence": "Jan Jansen woont in Amsterdam.", "entities": [{"text": "Jan Jansen", "label": "PER"}]}
    
    Output format:
    {"id": 1, "tokens": ["<s>", "ĠJan", "ĠJansen", ...], "labels": ["O", "B-PER", "I-PER", ...], ...}
    """
    tokenizer = RobertaTokenizerFast.from_pretrained(model_name)
    
    def get_entity_spans(sentence, entities):
        spans = []
        for entity in entities:
            text = entity["text"]
            label = entity.get("label", "PER")
            
            if "start" in entity and "end" in entity:
                spans.append({"start": entity["start"], "end": entity["end"], "label": label})
            else:
                match = re.search(re.escape(text), sentence)
                if match:
                    spans.append({"start": match.start(), "end": match.end(), "label": label})
                else:
                    print(f"Warning: Entity '{text}' not found in sentence: {sentence}")
        return spans
    
    def encode_labeled_sentence(example):
        sentence = example["sentence"]
        entities = get_entity_spans(sentence, example.get("entities", []))
        
        tokenized = tokenizer(sentence, return_offsets_mapping=True, truncation=True)
        labels = ["O"] * len(tokenized.input_ids)
        offsets = tokenized.offset_mapping
        
        for ent in entities:
            start, end, label = ent["start"], ent["end"], ent["label"]
            inside = False
            for i, (token_start, token_end) in enumerate(offsets):
                if token_start is None or token_end is None:
                    continue
                if token_start >= end:
                    break
                if token_start >= start and token_end <= end:
                    labels[i] = f"B-{label}" if not inside else f"I-{label}"
                    inside = True
        
        return {
            "id": example.get("id"),
            "tokens": tokenizer.convert_ids_to_tokens(tokenized.input_ids),
            "labels": labels,
            "input_ids": tokenized.input_ids,
            "attention_mask": tokenized.attention_mask
        }
    
    # Process file
    output_data = []
    with open(input_path, "r", encoding="utf-8") as f:
        for line in f:
            try:
                example = json.loads(line.strip())
                encoded = encode_labeled_sentence(example)
                output_data.append(encoded)
            except Exception as e:
                print(f"Skipping record due to error: {e}")
    
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(output_data, f, indent=2)
    
    print(f"✅ Processed {len(output_data)} examples to {output_path}")
    return output_path
```

### 6. Data Collation Component

**New Implementation:**
```python
from transformers import DataCollatorForTokenClassification

def create_data_collator(tokenizer):
    """Create data collator for token classification."""
    return DataCollatorForTokenClassification(
        tokenizer=tokenizer,
        padding=True,
        max_length=512,
        pad_to_multiple_of=8 if torch.cuda.is_available() else None,
        return_tensors="pt"
    )
```

## Enhanced Data Organization Structure

```
data/
├── raw/                          # Raw datasets in sentence + entities format
│   ├── dutch_ner_full.json      # Complete dataset with all entity types
│   ├── per_specific.json        # PER-only dataset (optional)
│   └── loc_specific.json        # LOC-only dataset (optional)
├── cache/                        # Cached processed datasets
│   ├── {hash1}/                  # Hash of (PER, dutch_ner_full.json, strict, robbert-2023-v1)
│   │   ├── train.json
│   │   ├── val.json
│   │   └── cache_info.json
│   └── {hash2}/                  # Different configuration hash
│       ├── train.json
│       ├── val.json
│       └── cache_info.json
├── processed/                    # Current processed datasets (symlinks to cache)
│   ├── PER/
│   │   ├── train.json -> ../cache/{hash1}/train.json
│   │   ├── val.json -> ../cache/{hash1}/val.json
│   │   └── metadata.json
│   └── LOC/
│       ├── train.json -> ../cache/{hash2}/train.json
│       ├── val.json -> ../cache/{hash2}/val.json
│       └── metadata.json
└── completed/                    # Completed training runs
    ├── PER_20250126_143022_v1.2/
    │   ├── dataset_report.json
    │   ├── training_metrics.json
    │   ├── model_performance.json
    │   └── predictions.jsonl
    └── LOC_20250126_150145_v1.0/
        ├── dataset_report.json
        ├── training_metrics.json
        ├── model_performance.json
        └── predictions.jsonl

checkpoints/
├── PER_20250126_143022_v1.2/
│   ├── model/                    # Trained model files
│   ├── logs/                     # Training logs
│   ├── metrics/                  # Evaluation metrics
│   ├── config.yaml              # Training configuration used
│   ├── model_card.md            # Auto-generated model card
│   ├── README.md                # Usage instructions
│   ├── predictions.jsonl        # Validation predictions
│   └── dataset_info.json        # Dataset statistics and filtering info
└── LOC_20250126_150145_v1.0/
    ├── model/
    ├── logs/
    ├── metrics/
    ├── config.yaml
    ├── model_card.md
    ├── README.md
    ├── predictions.jsonl
    └── dataset_info.json
```

## Data Models

### 1. Dataset Schema
```python
from typing import List, Optional
from pydantic import BaseModel

class EntitySpan(BaseModel):
    """Single entity annotation."""
    text: str
    label: str = "PER"
    start: Optional[int] = None  # Character start position (auto-detected if None)
    end: Optional[int] = None    # Character end position (auto-detected if None)

class NERExample(BaseModel):
    """Single NER training example."""
    id: Optional[int] = None
    sentence: str  # Raw sentence text
    entities: List[EntitySpan] = []  # Entity annotations

class NERDataset(BaseModel):
    """Complete NER dataset."""
    examples: List[NERExample]
    label_list: Optional[List[str]] = None  # Auto-discovered from data if None
    
    def __post_init__(self):
        """Auto-discover labels if not provided."""
        if self.label_list is None:
            entity_labels = set()
            for example in self.examples:
                for entity in example.entities:
                    entity_labels.add(entity.label)
            
            # Create BIO tagging scheme
            sorted_labels = sorted(entity_labels) if entity_labels else ["PER"]
            self.label_list = ["O"] + [f"B-{label}" for label in sorted_labels] + [f"I-{label}" for label in sorted_labels]
    
    @property
    def label2id(self) -> dict:
        return {label: i for i, label in enumerate(self.label_list)}
    
    @property
    def id2label(self) -> dict:
        return {i: label for i, label in enumerate(self.label_list)}
    
    @property
    def entity_types(self) -> List[str]:
        """Get unique entity types (without BIO prefixes)."""
        return sorted({label.split('-')[1] for label in self.label_list if label != "O"})
```

### 2. Training Configuration
```python
@dataclass
class CacheInfo(BaseModel):
    """Dataset cache metadata."""
    cache_hash: str
    label: str
    input_dataset_path: str
    filtering_strategy: str
    tokenizer_version: str
    created_at: datetime
    dataset_size: int
    entity_count: int
    
    def is_valid(self, current_hash: str) -> bool:
        """Check if cache is still valid."""
        return self.cache_hash == current_hash

@dataclass
class TrainingVersion(BaseModel):
    """Training run version information."""
    version: str = "v1.0"
    description: Optional[str] = None
    parent_version: Optional[str] = None
    changes: List[str] = field(default_factory=list)
    
    def format_checkpoint_name(self, base_name: str) -> str:
        """Format checkpoint name with version."""
        return f"{base_name}_{self.version}"

@dataclass
class HFTrainingConfig:
    """Hugging Face Trainer configuration."""
    model_name: str = "DTAI-KULeuven/robbert-2023-dutch-base"
    
    # Training parameters
    epochs: int = 3
    batch_size: int = 8
    eval_batch_size: int = 8
    learning_rate: float = 5e-5
    weight_decay: float = 0.01
    warmup_steps: int = 500
    
    # Evaluation
    eval_steps: int = 100
    logging_steps: int = 50
    save_steps: int = 500
    
    # Early stopping
    early_stopping_patience: int = 3
    early_stopping_threshold: float = 0.001
    load_best_model_at_end: bool = True
    metric_for_best_model: str = "eval_f1"
    
    # Class balancing (optional)
    use_class_weights: bool = False
    class_weights: Optional[Dict[str, float]] = None
    
    # WandB
    wandb_project: str = "robbert2023-ner"
    wandb_entity: str = "slippydongle"
    run_name: Optional[str] = None
    
    # Hardware
    use_gpu: bool = True
    fp16: bool = True
    dataloader_num_workers: int = 2
    dataloader_pin_memory: bool = True
    
    # Checkpointing
    save_total_limit: int = 3
    push_to_hub: bool = False
    hub_model_id: Optional[str] = None
    
    # Versioning and experiment tracking
    run_version: str = "v1.0"
    version_description: Optional[str] = None
    
    # Test mode settings
    test_mode: bool = False
    test_sample_limit: int = 50
    test_epochs: int = 1
    
    # Caching
    use_cache: bool = True
    cache_dir: str = "data/cache"
    
    # Output settings
    save_predictions: bool = True
    generate_model_card: bool = True
    
    @classmethod
    def from_yaml(cls, yaml_path: str) -> 'HFTrainingConfig':
        """Load configuration from YAML file."""
        import yaml
        with open(yaml_path, 'r') as f:
            config_dict = yaml.safe_load(f)
        return cls(**config_dict.get('hf_training', {}))
    
    def to_training_args(self, output_dir: str) -> 'TrainingArguments':
        """Convert to Hugging Face TrainingArguments."""
        from transformers import TrainingArguments
        
        return TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=self.epochs,
            per_device_train_batch_size=self.batch_size,
            per_device_eval_batch_size=self.eval_batch_size,
            learning_rate=self.learning_rate,
            weight_decay=self.weight_decay,
            warmup_steps=self.warmup_steps,
            
            evaluation_strategy="steps",
            eval_steps=self.eval_steps,
            logging_steps=self.logging_steps,
            save_steps=self.save_steps,
            
            load_best_model_at_end=self.load_best_model_at_end,
            metric_for_best_model=self.metric_for_best_model,
            greater_is_better=True,
            save_total_limit=self.save_total_limit,
            
            report_to="wandb" if self.wandb_project else None,
            run_name=self.run_name,
            
            fp16=self.fp16 and torch.cuda.is_available(),
            dataloader_pin_memory=self.dataloader_pin_memory,
            dataloader_num_workers=self.dataloader_num_workers,
            
            push_to_hub=self.push_to_hub,
            hub_model_id=self.hub_model_id,
        )
```

### 3. Evaluation Results
```python
@dataclass
class NERMetrics:
    """NER evaluation metrics."""
    precision: float
    recall: float
    f1: float
    accuracy: float
    
    # Per-entity metrics
    per_entity_precision: Dict[str, float] = field(default_factory=dict)
    per_entity_recall: Dict[str, float] = field(default_factory=dict)
    per_entity_f1: Dict[str, float] = field(default_factory=dict)
    
    # Additional metrics
    loss: Optional[float] = None
    runtime: Optional[float] = None
    samples_per_second: Optional[float] = None
```

## Error Handling

### 1. Data Loading Errors
- **Invalid JSON Format**: Validate JSON structure and provide clear error messages
- **Missing Required Fields**: Check for required fields (tokens, labels) and handle gracefully
- **Label Alignment Issues**: Detect and handle misaligned tokens and labels
- **Empty Dataset**: Handle empty or invalid datasets with appropriate fallbacks

### 2. Training Errors
- **CUDA Out of Memory**: Implement automatic batch size reduction and mixed precision
- **Model Loading Failures**: Handle network issues and provide local fallbacks
- **Checkpoint Corruption**: Implement checkpoint validation and recovery
- **WandB Connection Issues**: Graceful degradation when WandB is unavailable

### 3. Evaluation Errors
- **Metric Calculation Failures**: Handle edge cases in metric computation
- **Invalid Predictions**: Validate prediction format and handle malformed outputs
- **Missing Evaluation Data**: Handle cases where validation data is unavailable

## Testing Strategy

### 1. Unit Tests

**Data Processing Tests:**
```python
def test_dataset_loading():
    """Test Hugging Face dataset loading."""
    
def test_label_alignment():
    """Test token-label alignment with RobBERT tokenizer."""
    
def test_data_collation():
    """Test DataCollatorForTokenClassification."""
```

**Model Tests:**
```python
def test_model_initialization():
    """Test RobertaForTokenClassification initialization."""
    
def test_trainer_setup():
    """Test Trainer configuration and setup."""
    
def test_metrics_computation():
    """Test NER metrics calculation."""
```

### 2. Integration Tests

**Training Pipeline Tests:**
```python
def test_end_to_end_training():
    """Test complete training pipeline with small dataset."""
    
def test_wandb_integration():
    """Test WandB logging and artifact saving."""
    
def test_checkpoint_loading():
    """Test model checkpoint saving and loading."""
```

### 3. Performance Tests

**Training Performance:**
- Benchmark training speed with different batch sizes
- Test memory usage optimization
- Compare CPU vs GPU performance
- Validate mixed precision training

**Evaluation Quality:**
- Test metric accuracy against known benchmarks
- Validate early stopping behavior
- Test learning rate scheduling effectiveness

## Implementation Phases

### Phase 1: Core Trainer Integration
1. Create new training script using Hugging Face Trainer
2. Implement dataset loading with `datasets.load_dataset()`
3. Set up `DataCollatorForTokenClassification`
4. Configure basic `TrainingArguments`

### Phase 2: Evaluation and Metrics
1. Implement `compute_metrics` function with seqeval
2. Add early stopping configuration
3. Set up proper evaluation strategy
4. Test metric calculation accuracy

### Phase 3: WandB Integration
1. Configure `report_to="wandb"` in TrainingArguments
2. Set up automatic model artifact logging
3. Implement custom metric logging if needed
4. Test experiment tracking functionality

### Phase 4: Compatibility and Migration
1. Ensure compatibility with existing model checkpoints
2. Create migration utilities for existing training data
3. Update configuration system integration
4. Maintain backward compatibility with inference pipeline

## Migration Strategy

### 1. Gradual Migration
- **Parallel Implementation**: Keep existing training script while developing new one
- **Feature Flags**: Allow switching between old and new training methods
- **Validation**: Compare results between old and new training approaches
- **Rollback Plan**: Maintain ability to revert to custom training if needed

### 2. Data Format Compatibility
- **Input Format**: Support existing JSON data format
- **Label Mapping**: Maintain existing label scheme (O, B-PER, I-PER)
- **Tokenization**: Ensure consistent tokenization with existing inference pipeline
- **Output Format**: Maintain checkpoint compatibility

### 3. Configuration Integration
- **YAML Configuration**: Integrate with existing `src/config/default.yaml`
- **WandB Settings**: Use existing `src/config/wandb.yaml`
- **Environment Variables**: Support existing environment variable patterns
- **Command Line Interface**: Maintain familiar CLI interface

### 4. Performance Monitoring
- **Benchmark Comparison**: Compare training speed and memory usage
- **Accuracy Validation**: Ensure no regression in model performance
- **Resource Usage**: Monitor GPU/CPU utilization changes
- **Training Stability**: Validate convergence behavior

## Risk Mitigation

### 1. Performance Risk
- **Risk**: Trainer may be slower than custom training loop
- **Mitigation**: Benchmark performance, optimize batch sizes and data loading

### 2. Compatibility Risk
- **Risk**: Changes may break existing model loading or inference
- **Mitigation**: Extensive compatibility testing, maintain model format standards

### 3. Dependency Risk
- **Risk**: Additional dependencies may cause conflicts
- **Mitigation**: Careful version management, comprehensive testing

### 4. Learning Curve Risk
- **Risk**: Team unfamiliarity with Hugging Face Trainer patterns
- **Mitigation**: Documentation, examples, gradual migration approach

## Optional Enhancements

### 1. Advanced Callbacks
```python
from transformers import EarlyStoppingCallback, TrainerCallback
from transformers.integrations import WandbCallback

class CustomWandbCallback(WandbCallback):
    """Enhanced WandB callback with confusion matrix and per-class metrics."""
    
    def on_evaluate(self, args, state, control, model, tokenizer, eval_dataloader, **kwargs):
        super().on_evaluate(args, state, control, model, tokenizer, eval_dataloader, **kwargs)
        
        # Log confusion matrix
        if hasattr(state, 'log_history') and state.log_history:
            latest_metrics = state.log_history[-1]
            if 'eval_predictions' in latest_metrics:
                self.log_confusion_matrix(latest_metrics['eval_predictions'])
    
    def log_confusion_matrix(self, predictions):
        """Log confusion matrix to WandB."""
        import wandb
        from sklearn.metrics import confusion_matrix
        
        # Create confusion matrix visualization
        cm = confusion_matrix(predictions['true_labels'], predictions['pred_labels'])
        wandb.log({"confusion_matrix": wandb.plot.confusion_matrix(
            probs=None,
            y_true=predictions['true_labels'],
            preds=predictions['pred_labels'],
            class_names=self.label_list
        )})

def create_callbacks(config: HFTrainingConfig):
    """Create training callbacks."""
    callbacks = []
    
    # Early stopping
    if config.early_stopping_patience > 0:
        callbacks.append(EarlyStoppingCallback(
            early_stopping_patience=config.early_stopping_patience,
            early_stopping_threshold=config.early_stopping_threshold
        ))
    
    # Enhanced WandB logging
    if config.wandb_project:
        callbacks.append(CustomWandbCallback())
    
    return callbacks
```

### 2. Class Weight Calculation
```python
from sklearn.utils.class_weight import compute_class_weight
import numpy as np

def compute_class_weights(dataset, label_list):
    """Compute class weights for imbalanced datasets."""
    all_labels = []
    for example in dataset:
        all_labels.extend([label for label in example['labels'] if label != -100])
    
    # Convert to label names
    label_names = [label_list[label_id] for label_id in all_labels]
    
    # Compute weights
    class_weights = compute_class_weight(
        'balanced',
        classes=np.unique(label_names),
        y=label_names
    )
    
    return {label: weight for label, weight in zip(np.unique(label_names), class_weights)}
```

### 3. CLI Interface
```python
import typer
from pathlib import Path
from typing import Optional

app = typer.Typer()

@app.command()
def train(
    data_path: Path = typer.Argument(..., help="Path to training data"),
    config_path: Optional[Path] = typer.Option("src/config/hf_training.yaml", help="Training config path"),
    output_dir: Path = typer.Option("./checkpoints", help="Output directory"),
    wandb_project: Optional[str] = typer.Option(None, help="WandB project name"),
    run_name: Optional[str] = typer.Option(None, help="WandB run name"),
    epochs: Optional[int] = typer.Option(None, help="Number of epochs"),
    batch_size: Optional[int] = typer.Option(None, help="Batch size"),
    learning_rate: Optional[float] = typer.Option(None, help="Learning rate"),
):
    """Train RobBERT NER model using Hugging Face Trainer."""
    
    # Load config
    config = HFTrainingConfig.from_yaml(config_path) if config_path.exists() else HFTrainingConfig()
    
    # Override with CLI arguments
    if epochs is not None:
        config.epochs = epochs
    if batch_size is not None:
        config.batch_size = batch_size
    if learning_rate is not None:
        config.learning_rate = learning_rate
    if wandb_project is not None:
        config.wandb_project = wandb_project
    if run_name is not None:
        config.run_name = run_name
    
    # Run training
    train_ner_model(data_path, config, output_dir)

if __name__ == "__main__":
    app()
```

## Benefits and Trade-offs

### Benefits
- **Reduced Boilerplate**: Eliminate custom training loop code
- **Built-in Features**: Automatic checkpointing, evaluation, early stopping
- **Better Integration**: Native WandB support, metric logging
- **Community Standards**: Follow established patterns and best practices
- **Maintenance**: Easier to maintain and extend

### Trade-offs
- **Less Control**: Reduced flexibility compared to custom training loops
- **Abstraction**: May hide some training details
- **Dependencies**: Additional dependency on Hugging Face ecosystem
- **Migration Effort**: Initial effort required to migrate existing code

## Success Metrics

### 1. Functional Metrics
- All existing training functionality preserved
- WandB integration working correctly
- Model checkpoints compatible with inference pipeline
- Evaluation metrics accurate and comprehensive

### 2. Performance Metrics
- Training speed within 10% of current implementation
- Memory usage optimized for available hardware
- Model accuracy maintained or improved
- Stable convergence behavior

### 3. Developer Experience Metrics
- Reduced lines of training code
- Simplified configuration and setup
- Better error messages and debugging
- Improved experiment tracking and reproducibility