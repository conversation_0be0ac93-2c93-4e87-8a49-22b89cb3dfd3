#!/usr/bin/env python3
"""
Example demonstrating enhanced WandB integration with Hugging Face Trainer.

This example shows how to use the CustomWandbCallback for advanced logging
including confusion matrices, per-class metrics, and model artifacts.
"""

import os
import sys
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.training.hf_trainer import HFNERTrainer
from src.training.hf_config import HFTrainingConfig
from src.training.hf_wandb_integration import CustomWandbCallback, setup_wandb_for_hf_trainer


def main():
    """Demonstrate enhanced WandB integration."""
    print("Enhanced WandB Integration Example")
    print("=" * 50)
    
    # Create configuration with enhanced WandB settings
    config = HFTrainingConfig(
        # Basic training settings
        model_name="DTAI-KULeuven/robbert-2023-dutch-base",
        epochs=2,
        batch_size=4,
        eval_batch_size=4,
        learning_rate=5e-5,
        
        # WandB configuration
        wandb_project="robbert2023-ner-enhanced",
        wandb_entity="your-entity",  # Replace with your WandB entity
        run_name="enhanced-wandb-demo",
        wandb_tags=["demo", "enhanced-logging", "robbert-2023"],
        wandb_notes="Demonstration of enhanced WandB integration with confusion matrix and per-class metrics",
        
        # Enhanced WandB logging options
        wandb_log_confusion_matrix=True,
        wandb_log_per_class_metrics=True,
        wandb_log_model_artifacts=True,
        wandb_log_evaluation_tables=True,
        wandb_confusion_matrix_frequency="epoch",
        wandb_confusion_matrix_steps=100,
        
        # Evaluation settings for frequent logging
        evaluation_strategy="steps",
        eval_steps=50,
        logging_steps=25,
        save_steps=100,
        
        # Test mode for quick demonstration
        test_mode=True,
        test_epochs=2,
        test_sample_limit=100,
        test_disable_wandb=False,  # Enable WandB for this demo
        
        # Output settings
        output_dir="checkpoints/enhanced_wandb_demo",
        save_predictions=True,
        generate_model_card=True
    )
    
    print(f"Configuration created:")
    print(f"  - Model: {config.model_name}")
    print(f"  - Epochs: {config.epochs}")
    print(f"  - Batch size: {config.batch_size}")
    print(f"  - WandB project: {config.wandb_project}")
    print(f"  - Enhanced logging: {config.wandb_log_confusion_matrix}")
    print()
    
    # Check if sample data exists
    sample_data_path = Path("data_sets/synthetic1.json")
    if not sample_data_path.exists():
        print(f"Sample data not found at {sample_data_path}")
        print("Please ensure you have sample NER data available.")
        print("You can create synthetic data or use your own NER dataset.")
        return
    
    try:
        # Create trainer
        print("Creating HF NER Trainer with enhanced WandB integration...")
        trainer = HFNERTrainer(config)
        
        # Setup model and tokenizer
        print("Setting up model and tokenizer...")
        model, tokenizer, label2id, id2label = trainer.setup_model_and_tokenizer()
        
        print(f"Model setup completed:")
        print(f"  - Labels: {list(label2id.keys())}")
        print(f"  - Model class: {model.__class__.__name__}")
        print()
        
        # Demonstrate enhanced WandB callback creation
        print("Enhanced WandB features enabled:")
        wandb_config = config.get_wandb_config()
        enhanced_logging = wandb_config.get('enhanced_logging', {})
        
        for feature, enabled in enhanced_logging.items():
            status = "✓" if enabled else "✗"
            print(f"  {status} {feature.replace('_', ' ').title()}")
        print()
        
        # Note about training
        print("Training Configuration:")
        print(f"  - Test mode: {config.test_mode}")
        print(f"  - Sample limit: {config.test_sample_limit}")
        print(f"  - WandB disabled: {config.test_disable_wandb}")
        print()
        
        if config.test_disable_wandb:
            print("Note: WandB is disabled in test mode.")
            print("To see enhanced logging in action:")
            print("1. Set test_disable_wandb=False in the config")
            print("2. Ensure you have a valid WandB account and API key")
            print("3. Update the wandb_entity to your WandB username/team")
        else:
            print("WandB logging is enabled. Enhanced features will be logged to:")
            print(f"  - Project: {config.wandb_project}")
            print(f"  - Entity: {config.wandb_entity}")
            print(f"  - Run: {config.run_name}")
        
        print()
        print("Enhanced WandB Integration Features:")
        print("=" * 40)
        print("1. Confusion Matrix Visualization")
        print("   - Logged at each epoch or specified steps")
        print("   - Interactive confusion matrix in WandB UI")
        print("   - Supports multi-class NER evaluation")
        print()
        print("2. Per-Class Metrics")
        print("   - Precision, recall, F1 for each entity type")
        print("   - Organized in WandB tables for easy comparison")
        print("   - Tracks performance across different entity types")
        print()
        print("3. Model Artifacts")
        print("   - Automatic model checkpoint logging")
        print("   - Includes metadata and training configuration")
        print("   - Version tracking for model iterations")
        print()
        print("4. Detailed Evaluation Tables")
        print("   - Comprehensive evaluation results")
        print("   - Historical performance tracking")
        print("   - Exportable data for further analysis")
        print()
        print("5. Training Progress Visualization")
        print("   - Real-time metrics tracking")
        print("   - Learning curves and training dynamics")
        print("   - Best metrics summary")
        
        # Uncomment the following lines to run actual training
        # print("\nStarting training with enhanced WandB logging...")
        # results = trainer.train(data_path=sample_data_path)
        # print(f"Training completed! Results saved to: {results['output_dir']}")
        
    except Exception as e:
        print(f"Error during demonstration: {e}")
        print("This is a demonstration script. Actual training requires:")
        print("1. Valid NER training data")
        print("2. WandB account and API key (if WandB logging is enabled)")
        print("3. Sufficient computational resources")


def demonstrate_callback_features():
    """Demonstrate specific callback features."""
    print("\nCustomWandbCallback Features:")
    print("=" * 30)
    
    # Example label list
    label_list = ["O", "B-PER", "I-PER", "B-LOC", "I-LOC", "B-ORG", "I-ORG"]
    
    # Configuration options
    callback_config = {
        'log_confusion_matrix': True,
        'log_per_class_metrics': True,
        'log_model_artifacts': True,
        'log_evaluation_tables': True,
        'confusion_matrix_frequency': 'epoch',
        'confusion_matrix_steps': 500
    }
    
    print(f"Label list: {label_list}")
    print(f"Entity types detected: {len(set(l.split('-')[1] for l in label_list if '-' in l))}")
    print()
    
    print("Callback Configuration:")
    for key, value in callback_config.items():
        print(f"  - {key}: {value}")
    print()
    
    print("What gets logged to WandB:")
    print("1. Model Information:")
    print("   - Architecture details")
    print("   - Parameter counts")
    print("   - Model size estimation")
    print()
    print("2. Training Configuration:")
    print("   - All hyperparameters")
    print("   - Hardware settings")
    print("   - Optimization details")
    print()
    print("3. Label Information:")
    print("   - Entity types and counts")
    print("   - Label scheme (BIO/IO)")
    print("   - Label distribution")
    print()
    print("4. Enhanced Metrics:")
    print("   - Standard metrics with improvements")
    print("   - Metric trends and comparisons")
    print("   - Best performance tracking")


if __name__ == "__main__":
    main()
    demonstrate_callback_features()