# WandB Integration for RobBERT-2023

This document describes the Weights & Biases (WandB) integration for the RobBERT-2023 multi-task training pipeline.

## Overview

The WandB integration provides comprehensive experiment tracking, model artifact management, and visualization for training RobBERT-2023 with multiple task-specific heads.

### Key Features

- **Multi-Project Organization**: Separate WandB projects for each head and multi-task training
- **Head-Specific Configurations**: Individual configuration files for each task head
- **Comprehensive Metrics Tracking**: Loss, accuracy, F1, and task-specific metrics
- **Model Artifact Management**: Automatic model checkpoint logging
- **Flexible Training Modes**: Single-head, multi-task, and parameter override support

## Configuration

### WandB Settings (`src/config/wandb.yaml`)

```yaml
wandb:
  entity: "slippydongle"
  project: "robbert2023"
  api_key: "${WANDB_API_KEY:-****************************************}"
  enabled: true
  log_model: true
  log_freq: 100
```

### Head-Specific Configurations

Each head has its own configuration file in `src/config/heads/`:

- `ner.yaml` - Named Entity Recognition
- `compliance.yaml` - GDPR Compliance Classification  
- `label.yaml` - Document Label Classification
- `reason.yaml` - Reason Classification
- `topic.yaml` - Topic Classification

## Project Organization

### WandB Projects

The integration creates separate projects for different training scenarios:

- **`robbert2023-ner`** - NER head training
- **`robbert2023-compliance`** - Compliance head training
- **`robbert2023-label`** - Label classification training
- **`robbert2023-reason`** - Reason classification training
- **`robbert2023-topic`** - Topic classification training
- **`robbert2023-multitask`** - Multi-task joint training

### Why Separate Projects?

1. **Clear Organization**: Each head has distinct metrics and requirements
2. **Focused Analysis**: Easy to compare experiments within the same task
3. **Different Stakeholders**: Different teams may focus on different heads
4. **Metric Clarity**: Avoid confusion between similar metrics from different heads

## Usage Examples

### 1. Single Head Training

Train only the NER head with head-specific configuration:

```bash
python -m src.training.train_multitask \
    --heads ner \
    --use-head-configs \
    --wandb-config src/config/wandb.yaml \
    --epochs 5 \
    --data data_sets/synthetic1_cleaned.json
```

### 2. Multi-Task Training

Train multiple heads jointly:

```bash
python -m src.training.train_multitask \
    --heads ner compliance topic \
    --use-head-configs \
    --wandb-config src/config/wandb.yaml \
    --epochs 3 \
    --batch-size 8
```

### 3. Parameter Overrides

Override head-specific parameters:

```bash
python -m src.training.train_multitask \
    --heads compliance \
    --use-head-configs \
    --wandb-config src/config/wandb.yaml \
    --epochs 10 \
    --epochs-override \
    --learning-rate 1e-5 \
    --learning-rate-override
```

### 4. Training Without WandB

Fallback mode without WandB logging:

```bash
python -m src.training.train_multitask \
    --heads ner \
    --epochs 3 \
    --batch-size 8
```

## Tracked Metrics

### Common Metrics (All Heads)
- Training loss
- Learning rate
- Epoch progress
- Global step

### Head-Specific Metrics

#### NER Head
- `ner_loss`, `ner_f1`, `ner_precision`, `ner_recall`
- `ner_accuracy`, `ner_entity_f1`

#### Compliance Head
- `compliance_loss`, `compliance_accuracy`, `compliance_f1`
- `compliance_auc`, `compliance_fpr`, `compliance_fnr`

#### Label Head
- `label_loss`, `label_accuracy`, `label_f1`
- `label_top3_accuracy`, `label_macro_f1`

#### Reason Head
- `reason_loss`, `reason_accuracy`, `reason_f1`
- `reason_weighted_f1`, `reason_cohen_kappa`

#### Topic Head
- `topic_loss`, `topic_accuracy`, `topic_f1`
- `topic_coherence_score`, `topic_diversity_score`

## Model Artifacts

### Automatic Artifact Logging

The integration automatically logs model checkpoints as WandB artifacts:

- **Artifact Name**: `robbert2023-{heads}-epoch-{epoch}`
- **Type**: `model`
- **Metadata**: Training parameters, dataset info, performance metrics

### Artifact Organization

```
robbert2023-ner/
├── robbert2023-ner-epoch-5:v0
├── robbert2023-ner-epoch-5:v1
└── ...

robbert2023-multitask/
├── robbert2023-ner-compliance-topic-epoch-3:v0
└── ...
```

## Configuration Files Structure

### Head Configuration Template

```yaml
head_config:
  name: "head_name"
  type: "token_classification" | "sequence_classification"
  
  model:
    hidden_size: 768
    num_labels: N
    dropout: 0.1
    
  training:
    epochs: N
    batch_size: N
    learning_rate: Ne-5
    weight_decay: 0.01
    loss_weight: 1.0
    
  wandb:
    project_suffix: "-head_name"
    tags: ["tag1", "tag2"]
    notes: "Description"
    track_metrics: ["metric1", "metric2"]
```

## Validation and Testing

### Configuration Validation

Validate your configuration setup:

```bash
python scripts/validate_wandb_config.py
```

### Training Examples

Run comprehensive training examples:

```bash
python scripts/train_with_wandb.py
```

## Best Practices

### 1. Configuration Management

- Keep head-specific parameters in separate files
- Use environment variables for sensitive data (API keys)
- Validate configurations before training

### 2. Experiment Organization

- Use descriptive experiment names
- Tag experiments consistently
- Document experiment goals in notes

### 3. Metric Tracking

- Focus on task-relevant metrics
- Use appropriate averaging for multi-class problems
- Track both training and validation metrics

### 4. Model Management

- Save models at appropriate intervals
- Use meaningful artifact names
- Include comprehensive metadata

## Troubleshooting

### Common Issues

1. **WandB Authentication**
   ```bash
   export WANDB_API_KEY=your_api_key_here
   ```

2. **Missing Head Configurations**
   ```bash
   # Check available heads
   ls src/config/heads/
   ```

3. **Configuration Validation Errors**
   ```bash
   python scripts/validate_wandb_config.py
   ```

### Error Messages

- **"WandB configuration file not found"**: Check `src/config/wandb.yaml` exists
- **"Head config file not found"**: Ensure head config exists in `src/config/heads/`
- **"Failed to initialize WandB"**: Check API key and network connection

## Advanced Usage

### Custom Metrics

Add custom metrics in head configurations:

```yaml
wandb:
  track_metrics:
    - "custom_metric_name"
    - "another_custom_metric"
```

### Multi-Experiment Sweeps

Use WandB sweeps for hyperparameter optimization:

```yaml
# sweep.yaml
program: src/training/train_multitask.py
method: bayes
parameters:
  learning_rate:
    min: 1e-6
    max: 1e-4
  batch_size:
    values: [8, 16, 32]
```

### Integration with CI/CD

```bash
# In your CI pipeline
python scripts/validate_wandb_config.py
python -m src.training.train_multitask --heads ner --use-head-configs --epochs 1
```

## Support

For issues with the WandB integration:

1. Check the validation script output
2. Review the training logs
3. Verify WandB dashboard for experiment status
4. Check network connectivity and API key validity

## References

- [WandB Documentation](https://docs.wandb.ai/)
- [RobBERT Model](https://huggingface.co/DTAI-KULeuven/robbert-2023-dutch-base)
- [Project Structure Documentation](docs/structure.md)