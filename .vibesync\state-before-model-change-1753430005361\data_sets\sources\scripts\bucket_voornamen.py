#!/usr/bin/env python
"""
json2bucket_csv.py

• reads  voornamen.json  (list of [name, freq1, freq2])
• converts unicode escapes → real chars
• normalises each freq column:
      "698"  → 698
      "< 5"  → 3
      "-"    → 0
• sums the two columns → total freq
• ranks by freq → assigns bucket:
      top   5 %  → common
      5-30 %     → mid
      rest       → tail
• writes voornamen_bucketed.csv  in same folder
"""

import json, csv, os, sys
from pathlib import Path

# ---------- helper ----------------------------------------------------------
def norm(x: str) -> int:
    x = str(x).strip()
    if x.startswith("<"):    # "< 5"
        return 3
    if x == "-" or x == "":
        return 0
    return int(x)

def bucket(percentile: float) -> str:
    if percentile < 5:
        return "common"
    if percentile < 30:
        return "mid"
    return "tail"

# ---------- paths -----------------------------------------------------------
base_dir   = Path(os.path.dirname(__file__)).resolve()
json_path  = base_dir / "voornamen.json"
csv_path   = base_dir / "voornamen_bucketed.csv"

if not json_path.exists():
    sys.exit(f"[ERROR] {json_path} not found.")

# ---------- load & process --------------------------------------------------
with json_path.open(encoding="utf-8") as f:
    raw = json.load(f)                  # list[list]

records = []
for row in raw:
    if not row:       # empty row guard
        continue
    name = str(row[0])
    f1   = norm(row[1] if len(row) > 1 else 0)
    f2   = norm(row[2] if len(row) > 2 else 0)
    freq = f2
    records.append((name, freq))

# sort by descending freq
records.sort(key=lambda x: x[1], reverse=True)
n = len(records)

# ---------- write CSV with bucket ------------------------------------------
with csv_path.open("w", encoding="utf-8", newline="") as f:
    w = csv.writer(f)
    w.writerow(["name", "freq", "bucket"])
    for idx, (name, freq) in enumerate(records):
        pct = (idx / n) * 100           # rank percentile
        w.writerow([name, freq, bucket(pct)])

print(f"[OK] processed {n:,} rows → {csv_path}")
