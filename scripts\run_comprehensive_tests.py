#!/usr/bin/env python3
"""
Comprehensive test runner for HF Trainer integration.

This script implements the --test-mode flag for CI and provides
different test execution modes for development and production.
"""

import argparse
import sys
import subprocess
import os
from pathlib import Path


def run_tests(test_mode=False, test_type="all", verbose=False, coverage=False):
    """
    Run comprehensive tests with different modes.
    
    Args:
        test_mode: Enable test mode (small dataset, no WandB, fast execution)
        test_type: Type of tests to run ("unit", "integration", "performance", "all")
        verbose: Enable verbose output
        coverage: Enable coverage reporting
    """
    
    # Base pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Add verbosity
    if verbose:
        cmd.extend(["-v", "-s"])
    else:
        cmd.append("-v")
    
    # Add coverage if requested
    if coverage:
        cmd.extend([
            "--cov=src",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-report=xml"
        ])
    
    # Set environment variables for test mode
    if test_mode:
        os.environ["TEST_MODE"] = "true"
        os.environ["TEST_EPOCHS"] = "1"
        os.environ["TEST_SAMPLE_LIMIT"] = "50"
        os.environ["WANDB_MODE"] = "disabled"
        os.environ["CUDA_VISIBLE_DEVICES"] = ""  # Force CPU
        print("🧪 Running in TEST MODE - reduced resources, no external dependencies")
    
    # Select test files based on test type
    if test_type == "unit":
        cmd.extend([
            "tests/test_comprehensive_hf_integration.py::TestDataPreprocessingAndTokenization",
            "tests/test_comprehensive_hf_integration.py::TestEvaluationMetrics",
            "tests/test_hf_test_mode.py::TestModeConfiguration",
            "-m", "unit"
        ])
        print("🔬 Running UNIT tests")
        
    elif test_type == "integration":
        cmd.extend([
            "tests/test_comprehensive_hf_integration.py::TestEndToEndTrainingPipeline",
            "tests/test_comprehensive_hf_integration.py::TestWandBIntegrationAndMetrics",
            "tests/test_comprehensive_hf_integration.py::TestModelCompatibilityAndCheckpoints",
            "tests/test_hf_test_mode.py::TestModeIntegrationWithTraining",
            "-m", "integration"
        ])
        print("🔗 Running INTEGRATION tests")
        
    elif test_type == "performance":
        if test_mode:
            print("⚠️  Performance tests are limited in test mode")
        cmd.extend([
            "tests/test_hf_performance_comparison.py",
            "tests/test_comprehensive_hf_integration.py::TestPerformanceComparison",
            "-m", "slow"
        ])
        print("🚀 Running PERFORMANCE tests")
        
    elif test_type == "wandb":
        if test_mode:
            print("⚠️  WandB tests are disabled in test mode")
            return 0
        cmd.extend([
            "tests/test_comprehensive_hf_integration.py::TestWandBIntegrationAndMetrics",
            "tests/test_hf_wandb_integration.py",
            "-m", "wandb"
        ])
        print("📊 Running WANDB tests")
        
    elif test_type == "all":
        if test_mode:
            # In test mode, run only essential tests
            cmd.extend([
                "tests/test_comprehensive_hf_integration.py",
                "tests/test_hf_test_mode.py",
                "-m", "not slow and not wandb"
            ])
            print("🧪 Running ALL tests (test mode - excluding slow and WandB tests)")
        else:
            cmd.extend([
                "tests/test_comprehensive_hf_integration.py",
                "tests/test_hf_test_mode.py",
                "tests/test_hf_performance_comparison.py"
            ])
            print("🧪 Running ALL tests")
    
    else:
        print(f"❌ Unknown test type: {test_type}")
        return 1
    
    # Add test mode specific options
    if test_mode:
        cmd.extend([
            "--tb=short",
            "--disable-warnings",
            "--timeout=60"  # 1 minute timeout for test mode
        ])
    else:
        cmd.extend([
            "--tb=long",
            "--timeout=300"  # 5 minute timeout for full tests
        ])
    
    print(f"🏃 Executing: {' '.join(cmd)}")
    print("=" * 60)
    
    # Run the tests
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\n❌ Tests interrupted by user")
        return 130
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return 1


def main():
    """Main entry point for test runner."""
    parser = argparse.ArgumentParser(
        description="Comprehensive test runner for HF Trainer integration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run all tests in test mode (CI-friendly)
  python scripts/run_comprehensive_tests.py --test-mode
  
  # Run only unit tests with coverage
  python scripts/run_comprehensive_tests.py --type unit --coverage
  
  # Run performance tests (full mode)
  python scripts/run_comprehensive_tests.py --type performance
  
  # Run integration tests with verbose output
  python scripts/run_comprehensive_tests.py --type integration --verbose
        """
    )
    
    parser.add_argument(
        "--test-mode",
        action="store_true",
        help="Enable test mode (small dataset, no WandB, CPU-only, fast execution)"
    )
    
    parser.add_argument(
        "--type",
        choices=["unit", "integration", "performance", "wandb", "all"],
        default="all",
        help="Type of tests to run (default: all)"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose output"
    )
    
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Enable coverage reporting"
    )
    
    parser.add_argument(
        "--list-tests",
        action="store_true",
        help="List available tests without running them"
    )
    
    args = parser.parse_args()
    
    # Change to project root directory
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    if args.list_tests:
        print("Available test categories:")
        print("  unit        - Fast, isolated unit tests")
        print("  integration - Multi-component integration tests")
        print("  performance - Performance and benchmarking tests")
        print("  wandb       - WandB integration tests")
        print("  all         - All tests (default)")
        print()
        print("Test files:")
        print("  tests/test_comprehensive_hf_integration.py - Main comprehensive test suite")
        print("  tests/test_hf_test_mode.py                - Test mode implementation tests")
        print("  tests/test_hf_performance_comparison.py   - Performance comparison tests")
        return 0
    
    # Print configuration
    print("🧪 HF Trainer Integration - Comprehensive Test Suite")
    print("=" * 60)
    print(f"Test Mode:    {'ENABLED' if args.test_mode else 'DISABLED'}")
    print(f"Test Type:    {args.type.upper()}")
    print(f"Verbose:      {'ENABLED' if args.verbose else 'DISABLED'}")
    print(f"Coverage:     {'ENABLED' if args.coverage else 'DISABLED'}")
    print(f"Working Dir:  {os.getcwd()}")
    print("=" * 60)
    
    # Run tests
    exit_code = run_tests(
        test_mode=args.test_mode,
        test_type=args.type,
        verbose=args.verbose,
        coverage=args.coverage
    )
    
    # Print summary
    print("=" * 60)
    if exit_code == 0:
        print("✅ All tests passed!")
    else:
        print(f"❌ Tests failed with exit code: {exit_code}")
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())