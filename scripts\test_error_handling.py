#!/usr/bin/env python3
"""
Test script for comprehensive error handling implementation.

This script tests various error scenarios to ensure the error handling
system works correctly and provides meaningful feedback.
"""

import sys
import json
import tempfile
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.training.error_handling import (
    TrainingErrorHandler,
    CUDAMemoryManager,
    DataProcessingErrorHandler,
    TokenizationErrorHandler,
    WandBErrorHandler
)
from src.utils.logging_utils import get_logger
from src.exceptions import (
    DataProcessingError,
    TokenizationError,
    LabelAlignmentError,
    TrainingError
)


def test_data_processing_error_handler():
    """Test data processing error handling."""
    print("Testing Data Processing Error Handler...")
    
    handler = DataProcessingErrorHandler()
    
    # Test 1: Invalid JSON file
    try:
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('{"invalid": json}')  # Invalid JSON
            invalid_json_path = f.name
        
        handler.safe_json_load(invalid_json_path)
        print("❌ Should have raised DataProcessingError for invalid JSON")
    except DataProcessingError as e:
        print(f"✅ Correctly caught invalid JSON: {e}")
    finally:
        Path(invalid_json_path).unlink(missing_ok=True)
    
    # Test 2: Valid JSON data
    try:
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            test_data = [
                {
                    "id": 1,
                    "sentence": "Jan Jansen woont in Amsterdam.",
                    "entities": [
                        {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10},
                        {"text": "Amsterdam", "label": "LOC", "start": 20, "end": 29}
                    ]
                }
            ]
            json.dump(test_data, f)
            valid_json_path = f.name
        
        data = handler.safe_json_load(valid_json_path)
        print(f"✅ Successfully loaded {len(data)} records")
    except Exception as e:
        print(f"❌ Failed to load valid JSON: {e}")
    finally:
        Path(valid_json_path).unlink(missing_ok=True)
    
    # Test 3: Entity validation
    entities = [
        {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10},
        {"text": "NonExistent", "label": "PER"},  # This should be filtered out
        {"text": "Amsterdam", "label": "LOC", "start": 20, "end": 29}
    ]
    sentence = "Jan Jansen woont in Amsterdam."
    
    valid_entities = handler.validate_entity_format(entities, sentence, "test_record")
    print(f"✅ Validated entities: {len(valid_entities)}/3 entities kept")
    
    print("Data Processing Error Handler tests completed.\n")


def test_tokenization_error_handler():
    """Test tokenization error handling."""
    print("Testing Tokenization Error Handler...")
    
    handler = TokenizationErrorHandler()
    
    # Mock tokenizer for testing
    class MockTokenizer:
        def __init__(self):
            self.name_or_path = "mock-tokenizer"
        
        def __call__(self, text, **kwargs):
            if "fail" in text.lower():
                raise ValueError("Mock tokenization failure")
            
            # Simple mock tokenization
            tokens = text.split()
            return {
                "input_ids": list(range(len(tokens))),
                "attention_mask": [1] * len(tokens),
                "offset_mapping": [(i*5, (i+1)*5) for i in range(len(tokens))]
            }
    
    tokenizer = MockTokenizer()
    
    # Test 1: Successful tokenization
    try:
        result = handler.safe_tokenize(tokenizer, "This is a test sentence")
        print(f"✅ Successful tokenization: {len(result['input_ids'])} tokens")
    except Exception as e:
        print(f"❌ Unexpected error in successful tokenization: {e}")
    
    # Test 2: Failed tokenization
    try:
        handler.safe_tokenize(tokenizer, "This will fail tokenization")
        print("❌ Should have raised TokenizationError")
    except TokenizationError as e:
        print(f"✅ Correctly caught tokenization error: {e}")
    
    # Test 3: Label alignment
    tokenized_inputs = {
        "input_ids": [0, 1, 2, 3, 4],
        "offset_mapping": [(0, 3), (4, 6), (7, 12), (13, 15), (16, 25)]
    }
    entities = [
        {"text": "Jan", "label": "PER", "start": 0, "end": 3},
        {"text": "Amsterdam", "label": "LOC", "start": 16, "end": 25}
    ]
    label2id = {"O": 0, "B-PER": 1, "I-PER": 2, "B-LOC": 3, "I-LOC": 4}
    sentence = "Jan is woont in Amsterdam"
    
    try:
        labels = handler.align_labels_with_tokens(
            tokenized_inputs, entities, label2id, sentence
        )
        print(f"✅ Label alignment successful: {len(labels)} labels")
    except Exception as e:
        print(f"❌ Label alignment failed: {e}")
    
    print("Tokenization Error Handler tests completed.\n")


def test_cuda_memory_manager():
    """Test CUDA memory management."""
    print("Testing CUDA Memory Manager...")
    
    manager = CUDAMemoryManager()
    
    # Test 1: Memory info
    try:
        memory_info = manager.get_memory_info()
        print(f"✅ Memory info retrieved: CUDA available = {memory_info['cuda_available']}")
    except Exception as e:
        print(f"❌ Failed to get memory info: {e}")
    
    # Test 2: Clear cache (should not fail even without CUDA)
    try:
        manager.clear_cuda_cache()
        print("✅ CUDA cache clear completed (or skipped if no CUDA)")
    except Exception as e:
        print(f"❌ Failed to clear CUDA cache: {e}")
    
    print("CUDA Memory Manager tests completed.\n")


def test_wandb_error_handler():
    """Test WandB error handling."""
    print("Testing WandB Error Handler...")
    
    handler = WandBErrorHandler()
    
    # Test 1: Check availability
    try:
        available = handler.check_wandb_availability()
        print(f"✅ WandB availability check: {available}")
    except Exception as e:
        print(f"❌ WandB availability check failed: {e}")
    
    # Test 2: Safe metric logging (should work even if WandB unavailable)
    try:
        handler.safe_log_metrics({"test_metric": 0.95, "epoch": 1}, step=100)
        print("✅ Safe metric logging completed")
    except Exception as e:
        print(f"❌ Safe metric logging failed: {e}")
    
    # Test 3: Get fallback metrics
    try:
        fallback_metrics = handler.get_fallback_metrics()
        print(f"✅ Retrieved {len(fallback_metrics)} fallback metrics")
    except Exception as e:
        print(f"❌ Failed to get fallback metrics: {e}")
    
    print("WandB Error Handler tests completed.\n")


def test_comprehensive_error_handler():
    """Test the comprehensive training error handler."""
    print("Testing Comprehensive Training Error Handler...")
    
    handler = TrainingErrorHandler()
    
    # Test 1: Error summary
    try:
        summary = handler.get_error_summary()
        print(f"✅ Error summary retrieved: {summary}")
    except Exception as e:
        print(f"❌ Failed to get error summary: {e}")
    
    # Test 2: Handle different error types
    test_errors = [
        DataProcessingError("Test data error", data_path="test.json"),
        TokenizationError("Test tokenization error", text="test text"),
        TrainingError("Test training error", epoch=1, step=100)
    ]
    
    for error in test_errors:
        try:
            handled = handler.handle_training_error(error, step=100, epoch=1)
            print(f"✅ Handled {type(error).__name__}: recovery = {handled}")
        except Exception as e:
            print(f"❌ Failed to handle {type(error).__name__}: {e}")
    
    # Test 3: Save error report
    try:
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            report_path = f.name
        
        handler.save_error_report(report_path)
        
        # Check if report was created
        if Path(report_path).exists():
            print("✅ Error report saved successfully")
            Path(report_path).unlink()
        else:
            print("❌ Error report was not created")
    except Exception as e:
        print(f"❌ Failed to save error report: {e}")
    
    print("Comprehensive Training Error Handler tests completed.\n")


def main():
    """Run all error handling tests."""
    print("🧪 Starting Error Handling Tests\n")
    print("=" * 50)
    
    try:
        test_data_processing_error_handler()
        test_tokenization_error_handler()
        test_cuda_memory_manager()
        test_wandb_error_handler()
        test_comprehensive_error_handler()
        
        print("=" * 50)
        print("✅ All error handling tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()