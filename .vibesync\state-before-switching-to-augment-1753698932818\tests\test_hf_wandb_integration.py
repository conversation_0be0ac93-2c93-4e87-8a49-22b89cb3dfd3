"""
Tests for enhanced WandB integration with Hugging Face Trainer.

This module tests the CustomWandbCallback and enhanced logging features
including confusion matrix, per-class metrics, and model artifacts.
"""

import pytest
import numpy as np
import torch
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from src.training.hf_wandb_integration import (
    CustomWandbCallback,
    create_enhanced_wandb_callback,
    setup_wandb_for_hf_trainer
)
from transformers import TrainingArguments, TrainerState, TrainerControl


class TestCustomWandbCallback:
    """Test CustomWandbCallback functionality."""
    
    @pytest.fixture
    def label_list(self):
        """Sample label list for testing."""
        return ["O", "B-PER", "I-PER", "B-LOC", "I-LOC"]
    
    @pytest.fixture
    def callback_config(self):
        """Sample callback configuration."""
        return {
            'log_confusion_matrix': True,
            'log_per_class_metrics': True,
            'log_model_artifacts': True,
            'log_evaluation_tables': True,
            'confusion_matrix_frequency': 'epoch',
            'confusion_matrix_steps': 500
        }
    
    @pytest.fixture
    def mock_wandb(self):
        """Mock WandB for testing."""
        with patch('src.training.hf_wandb_integration.wandb') as mock_wandb:
            mock_run = Mock()
            mock_wandb.run = mock_run
            mock_wandb.init.return_value = mock_run
            mock_wandb.plot.confusion_matrix.return_value = Mock()
            mock_wandb.Table = Mock()
            mock_wandb.Artifact = Mock()
            yield mock_wandb
    
    def test_callback_initialization(self, label_list, callback_config):
        """Test callback initialization."""
        callback = CustomWandbCallback(label_list, callback_config)
        
        assert callback.label_list == label_list
        assert callback.config == callback_config
        assert callback.log_confusion_matrix == True
        assert callback.log_per_class_metrics == True
        assert callback.log_model_artifacts == True
        assert callback.log_evaluation_tables == True
        assert callback.confusion_matrix_frequency == 'epoch'
        
        # Check tracking variables are initialized
        assert callback.best_metrics == {}
        assert callback.metrics_history == []
        assert callback.confusion_matrices == []
        assert callback.evaluation_tables == []
    
    def test_callback_initialization_with_defaults(self, label_list):
        """Test callback initialization with default config."""
        callback = CustomWandbCallback(label_list)
        
        assert callback.label_list == label_list
        assert callback.config == {}
        assert callback.log_confusion_matrix == True  # Default
        assert callback.log_per_class_metrics == True  # Default
        assert callback.log_model_artifacts == True  # Default
        assert callback.log_evaluation_tables == True  # Default
    
    @patch('src.training.hf_wandb_integration.wandb')
    def test_setup_method(self, mock_wandb, label_list, callback_config):
        """Test setup method."""
        callback = CustomWandbCallback(label_list, callback_config)
        callback._wandb = mock_wandb
        
        # Mock training arguments and model
        args = Mock(spec=TrainingArguments)
        args.to_dict.return_value = {
            'learning_rate': 5e-5,
            'per_device_train_batch_size': 8,
            'num_train_epochs': 3
        }
        args.learning_rate = 5e-5
        args.per_device_train_batch_size = 8
        args.num_train_epochs = 3
        
        state = Mock(spec=TrainerState)
        state.is_world_process_zero = True
        
        model = Mock()
        model.__class__.__name__ = "RobertaForTokenClassification"
        model.parameters.return_value = [torch.randn(100), torch.randn(50)]
        model.config.to_dict.return_value = {
            'model_type': 'roberta',
            'num_labels': len(label_list)
        }
        
        # Call setup
        callback.setup(args, state, model)
        
        # Verify WandB logging calls were made
        assert mock_wandb.log.call_count >= 2  # model_info and label_info
        assert mock_wandb.config.update.called
    
    def test_model_summary_logging(self, label_list, callback_config, mock_wandb):
        """Test model summary logging."""
        callback = CustomWandbCallback(label_list, callback_config)
        callback._wandb = mock_wandb
        
        # Create mock model
        model = Mock()
        model.__class__.__name__ = "RobertaForTokenClassification"
        
        # Mock parameters
        param1 = Mock()
        param1.numel.return_value = 1000
        param1.requires_grad = True
        
        param2 = Mock()
        param2.numel.return_value = 500
        param2.requires_grad = False
        
        model.parameters.return_value = [param1, param2]
        
        # Call method
        callback._log_model_summary(model)
        
        # Verify logging
        mock_wandb.log.assert_called()
        call_args = mock_wandb.log.call_args[0][0]
        assert "model_info" in call_args
        
        model_info = call_args["model_info"]
        assert model_info["model_architecture"] == "RobertaForTokenClassification"
        assert model_info["total_parameters"] == 1500
        assert model_info["trainable_parameters"] == 1000
        assert model_info["num_labels"] == len(label_list)
        assert model_info["labels"] == label_list
    
    def test_label_info_logging(self, label_list, callback_config, mock_wandb):
        """Test label information logging."""
        callback = CustomWandbCallback(label_list, callback_config)
        callback._wandb = mock_wandb
        
        # Call method
        callback._log_label_info()
        
        # Verify logging
        mock_wandb.log.assert_called()
        call_args = mock_wandb.log.call_args[0][0]
        assert "label_info" in call_args
        
        label_info = call_args["label_info"]
        assert label_info["total_labels"] == len(label_list)
        assert label_info["entity_types"] == ["LOC", "PER"]  # Sorted
        assert label_info["label_scheme"] == "BIO"
        assert label_info["all_labels"] == label_list
    
    def test_enhanced_metrics_logging(self, label_list, callback_config, mock_wandb):
        """Test enhanced metrics logging."""
        callback = CustomWandbCallback(label_list, callback_config)
        callback._wandb = mock_wandb
        
        # Mock state
        state = Mock(spec=TrainerState)
        state.epoch = 1
        state.global_step = 100
        
        # Sample evaluation metrics
        eval_metrics = {
            "eval_f1": 0.85,
            "eval_precision": 0.87,
            "eval_recall": 0.83,
            "eval_loss": 0.25
        }
        
        # Call method
        callback._log_enhanced_metrics(eval_metrics, state)
        
        # Verify metrics were stored
        assert len(callback.metrics_history) == 1
        stored_metrics = callback.metrics_history[0]
        assert stored_metrics["epoch"] == 1
        assert stored_metrics["global_step"] == 100
        assert stored_metrics["eval_f1"] == 0.85
        
        # Verify WandB logging
        mock_wandb.log.assert_called()
        call_args = mock_wandb.log.call_args
        logged_metrics = call_args[0][0]
        assert logged_metrics["epoch"] == 1
        assert logged_metrics["eval_f1"] == 0.85
        assert call_args[1]["step"] == 100
    
    def test_confusion_matrix_logging(self, label_list, callback_config, mock_wandb):
        """Test confusion matrix logging."""
        callback = CustomWandbCallback(label_list, callback_config)
        callback._wandb = mock_wandb
        
        # Mock state
        state = Mock(spec=TrainerState)
        state.epoch = 1
        state.global_step = 100
        
        # Sample predictions and labels
        predictions = [["O", "B-PER", "I-PER"], ["B-LOC", "O"]]
        labels = [["O", "B-PER", "I-PER"], ["B-LOC", "O"]]
        
        # Call method
        callback._log_confusion_matrix(predictions, labels, state)
        
        # Verify confusion matrix was stored
        assert len(callback.confusion_matrices) == 1
        stored_cm = callback.confusion_matrices[0]
        assert stored_cm["epoch"] == 1
        assert stored_cm["step"] == 100
        assert stored_cm["labels"] == label_list
        
        # Verify WandB logging
        mock_wandb.log.assert_called()
        mock_wandb.plot.confusion_matrix.assert_called()
    
    def test_per_class_metrics_logging(self, label_list, callback_config, mock_wandb):
        """Test per-class metrics logging."""
        callback = CustomWandbCallback(label_list, callback_config)
        callback._wandb = mock_wandb
        
        # Mock state
        state = Mock(spec=TrainerState)
        state.epoch = 1
        state.global_step = 100
        
        # Sample evaluation metrics with per-class metrics
        eval_metrics = {
            "eval_f1": 0.85,
            "eval_PER_precision": 0.90,
            "eval_PER_recall": 0.88,
            "eval_PER_f1": 0.89,
            "eval_LOC_precision": 0.82,
            "eval_LOC_recall": 0.80,
            "eval_LOC_f1": 0.81
        }
        
        # Call method
        callback._log_per_class_metrics(eval_metrics, state)
        
        # Verify WandB logging
        mock_wandb.log.assert_called()
        call_args = mock_wandb.log.call_args[0][0]
        
        # Check that per-class metrics were logged
        assert "eval_PER_precision" in call_args
        assert "eval_LOC_f1" in call_args
        
        # Check that table was created
        mock_wandb.Table.assert_called()
    
    def test_evaluation_table_entry(self, label_list, callback_config):
        """Test evaluation table entry creation."""
        callback = CustomWandbCallback(label_list, callback_config)
        
        # Mock state
        state = Mock(spec=TrainerState)
        state.epoch = 1
        state.global_step = 100
        
        # Sample evaluation metrics
        eval_metrics = {
            "eval_f1": 0.85,
            "eval_precision": 0.87,
            "eval_recall": 0.83
        }
        
        # Call method
        callback._add_evaluation_table_entry(eval_metrics, state)
        
        # Verify table entry was added
        assert len(callback.evaluation_tables) == 1
        entry = callback.evaluation_tables[0]
        assert entry["epoch"] == 1
        assert entry["step"] == 100
        assert entry["f1"] == 0.85  # eval_ prefix removed
        assert entry["precision"] == 0.87
        assert "timestamp" in entry
    
    def test_best_metrics_tracking(self, label_list, callback_config):
        """Test best metrics tracking."""
        callback = CustomWandbCallback(label_list, callback_config)
        
        # Mock state
        state1 = Mock(spec=TrainerState)
        state1.epoch = 1
        state1.global_step = 100
        
        state2 = Mock(spec=TrainerState)
        state2.epoch = 2
        state2.global_step = 200
        
        # First evaluation (lower scores)
        eval_metrics1 = {"eval_f1": 0.75, "eval_precision": 0.77}
        callback._update_best_metrics(eval_metrics1, state1)
        
        # Second evaluation (higher F1, lower precision)
        eval_metrics2 = {"eval_f1": 0.85, "eval_precision": 0.70}
        callback._update_best_metrics(eval_metrics2, state2)
        
        # Verify best metrics tracking
        assert callback.best_metrics["eval_f1"]["value"] == 0.85
        assert callback.best_metrics["eval_f1"]["epoch"] == 2
        assert callback.best_metrics["eval_precision"]["value"] == 0.77
        assert callback.best_metrics["eval_precision"]["epoch"] == 1
    
    def test_should_log_confusion_matrix(self, label_list, callback_config):
        """Test confusion matrix logging frequency."""
        # Test epoch frequency
        callback = CustomWandbCallback(label_list, callback_config)
        state = Mock(spec=TrainerState)
        state.global_step = 100
        
        assert callback._should_log_confusion_matrix(state) == True  # Always true for epoch
        
        # Test step frequency
        callback.confusion_matrix_frequency = 'step'
        callback.config['confusion_matrix_steps'] = 50
        
        state.global_step = 50
        assert callback._should_log_confusion_matrix(state) == True
        
        state.global_step = 75
        assert callback._should_log_confusion_matrix(state) == False
        
        state.global_step = 100
        assert callback._should_log_confusion_matrix(state) == True


class TestFactoryFunctions:
    """Test factory functions."""
    
    def test_create_enhanced_wandb_callback(self):
        """Test enhanced WandB callback factory."""
        label_list = ["O", "B-PER", "I-PER"]
        config = {'log_confusion_matrix': False}
        
        callback = create_enhanced_wandb_callback(label_list, config)
        
        assert isinstance(callback, CustomWandbCallback)
        assert callback.label_list == label_list
        assert callback.config == config
        assert callback.log_confusion_matrix == False
    
    @patch('src.training.hf_wandb_integration.wandb')
    def test_setup_wandb_for_hf_trainer(self, mock_wandb):
        """Test WandB setup for HF trainer."""
        setup_wandb_for_hf_trainer(
            project="test-project",
            entity="test-entity",
            name="test-run",
            tags=["test"],
            notes="Test run",
            config={"test": True}
        )
        
        mock_wandb.init.assert_called_once_with(
            project="test-project",
            entity="test-entity",
            name="test-run",
            tags=["test"],
            notes="Test run",
            config={"test": True},
            reinit=True
        )


class TestIntegrationWithTrainer:
    """Test integration with Hugging Face Trainer."""
    
    @pytest.fixture
    def mock_trainer_components(self):
        """Mock trainer components for testing."""
        args = Mock(spec=TrainingArguments)
        args.output_dir = "/tmp/test"
        args.learning_rate = 5e-5
        args.per_device_train_batch_size = 8
        args.to_dict.return_value = {
            'output_dir': "/tmp/test",
            'learning_rate': 5e-5,
            'per_device_train_batch_size': 8
        }
        
        state = Mock(spec=TrainerState)
        state.epoch = 1
        state.global_step = 100
        state.log_history = [{"eval_f1": 0.85, "eval_loss": 0.25}]
        state.is_world_process_zero = True
        
        control = Mock(spec=TrainerControl)
        
        model = Mock()
        model.__class__.__name__ = "RobertaForTokenClassification"
        model.device = torch.device("cpu")
        model.config.to_dict.return_value = {
            'model_type': 'roberta',
            'num_labels': 3
        }
        
        tokenizer = Mock()
        
        return args, state, control, model, tokenizer
    
    @patch('src.training.hf_wandb_integration.wandb')
    def test_on_evaluate_integration(self, mock_wandb, mock_trainer_components):
        """Test on_evaluate method integration."""
        args, state, control, model, tokenizer = mock_trainer_components
        
        label_list = ["O", "B-PER", "I-PER"]
        callback = CustomWandbCallback(label_list)
        callback._wandb = mock_wandb
        
        # Call on_evaluate
        callback.on_evaluate(
            args, state, control, model=model, tokenizer=tokenizer, eval_dataloader=Mock()
        )
        
        # Verify metrics were processed
        assert len(callback.metrics_history) == 1
        assert callback.metrics_history[0]["eval_f1"] == 0.85
        
        # Verify WandB logging
        mock_wandb.log.assert_called()
    
    @patch('src.training.hf_wandb_integration.wandb')
    def test_on_train_end_integration(self, mock_wandb, mock_trainer_components):
        """Test on_train_end method integration."""
        args, state, control, model, tokenizer = mock_trainer_components
        
        label_list = ["O", "B-PER", "I-PER"]
        callback = CustomWandbCallback(label_list)
        callback._wandb = mock_wandb
        
        # Add some metrics history
        callback.metrics_history = [
            {"epoch": 1, "eval_f1": 0.80},
            {"epoch": 2, "eval_f1": 0.85}
        ]
        callback.best_metrics = {"eval_f1": {"value": 0.85, "epoch": 2, "step": 200}}
        
        # Call on_train_end
        callback.on_train_end(args, state, control, model, tokenizer)
        
        # Verify final logging
        assert mock_wandb.log.call_count >= 2  # training_summary and others
        mock_wandb.log_artifact.assert_called()  # Model artifact
        mock_wandb.run.summary.update.assert_called()  # Best metrics summary


if __name__ == "__main__":
    pytest.main([__file__])