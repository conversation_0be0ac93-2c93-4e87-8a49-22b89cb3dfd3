import csv
from transformers import RobertaTokenizerFast

# Load RobBERT 2023 tokenizer
tokenizer = RobertaTokenizerFast.from_pretrained("DTAI-KULeuven/robbert-2023-dutch-base")

# Get vocab dictionary: token -> ID
vocab = tokenizer.get_vocab()

# Sort by token ID (not alphabetically)
sorted_vocab = sorted(vocab.items(), key=lambda x: x[1])

def unicode_info(text):
    if not text:
        return "<EMPTY>"
    return " ".join(f"U+{ord(c):04X}" for c in text)

# Output path
output_file = "robbert_vocab.tsv"

with open(output_file, mode="w", newline="", encoding="utf-8") as tsvfile:
    writer = csv.writer(tsvfile, delimiter="\t")
    writer.writerow([
        "Token (raw)", 
        "Token ID", 
        "Decoded Text", 
        "Unicode Codepoints", 
        "Is Special Token", 
        "Is Added Token"
    ])

    for token, token_id in sorted_vocab:
        try:
            decoded = tokenizer.convert_tokens_to_string([token]).strip()
        except Exception:
            decoded = "<ERROR>"

        decoded_display = decoded if decoded else "<EMPTY>"
        codepoints = unicode_info(decoded)

        is_special = token in tokenizer.all_special_tokens
        is_added = token in tokenizer.added_tokens_encoder

        writer.writerow([
            repr(token),
            token_id,
            repr(decoded_display),
            codepoints,
            is_special,
            is_added
        ])

print(f"✅ Exported {len(sorted_vocab)} tokens to {output_file}")
