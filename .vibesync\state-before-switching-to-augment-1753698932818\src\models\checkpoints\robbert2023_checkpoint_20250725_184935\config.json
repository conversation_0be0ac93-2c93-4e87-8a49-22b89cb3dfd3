{"_name_or_path": "DTAI-KULeuven/robbert-2023-dutch-base", "additional_special_tokens_ids": [], "architectures": ["RobertaForMaskedLM"], "attention_probs_dropout_prob": 0.1, "bos_token_id": 0, "classifier_dropout": null, "cls_token_id": 0, "eos_token_id": 3, "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "hidden_size": 768, "initializer_range": 0.02, "intermediate_size": 3072, "layer_norm_eps": 1e-05, "mask_token_id": 4, "max_position_embeddings": 514, "model_type": "roberta", "num_attention_heads": 12, "num_hidden_layers": 12, "pad_token_id": 1, "position_embedding_type": "absolute", "sep_token_id": 3, "tokenizer_class": "RobertaTokenizerFast", "torch_dtype": "float32", "transformers_version": "4.41.2", "type_vocab_size": 1, "unk_token_id": 2, "use_cache": true, "vocab_size": 50000}