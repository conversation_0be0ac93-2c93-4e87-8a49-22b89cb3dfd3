# HFTrainingConfig Documentation

The `HFTrainingConfig` class provides comprehensive configuration management for Hugging Face Trainer-based training with YAML support, environment variable substitution, and seamless integration with the existing RobBERT-2023 training pipeline.

## Features

- **YAML Configuration**: Load configuration from YAML files with environment variable substitution
- **TrainingArguments Conversion**: Seamless conversion to Hugging Face TrainingArguments
- **Class Weights Support**: Built-in support for handling imbalanced datasets
- **WandB Integration**: Comprehensive WandB experiment tracking configuration
- **Hardware Optimization**: Automatic GPU/CPU detection and optimization
- **Test Mode**: Special configuration for testing and CI/CD pipelines
- **Validation**: Comprehensive validation of configuration parameters
- **Template System**: Pre-built configuration templates for common scenarios

## Quick Start

### Basic Usage

```python
from src.training.hf_config import HFTrainingConfig, create_default_hf_config

# Create default configuration
config = create_default_hf_config()

# Create custom configuration
config = HFTrainingConfig(
    epochs=5,
    batch_size=16,
    learning_rate=3e-5,
    use_class_weights=True,
    class_weights={"O": 0.5, "B-PER": 2.0, "I-PER": 1.5}
)

# Convert to TrainingArguments
training_args = config.to_training_args(output_dir="./checkpoints")
```

### YAML Configuration

```python
# Load from YAML file
config = HFTrainingConfig.from_yaml("src/config/hf_training_default.yaml")

# Save configuration to YAML
config.save_yaml("my_config.yaml")
```

## Configuration Parameters

### Core Training Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `model_name` | str | `"DTAI-KULeuven/robbert-2023-dutch-base"` | Hugging Face model identifier |
| `epochs` | int | `3` | Number of training epochs |
| `batch_size` | int | `8` | Training batch size |
| `eval_batch_size` | int | `8` | Evaluation batch size |
| `learning_rate` | float | `5e-5` | Learning rate |
| `weight_decay` | float | `0.01` | Weight decay |
| `warmup_steps` | int | `500` | Warmup steps |

### Evaluation and Logging

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `eval_steps` | int | `100` | Evaluation frequency (steps) |
| `logging_steps` | int | `50` | Logging frequency (steps) |
| `save_steps` | int | `500` | Checkpoint saving frequency (steps) |
| `evaluation_strategy` | str | `"steps"` | Evaluation strategy ("no", "steps", "epoch") |

### Early Stopping

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `early_stopping_patience` | int | `3` | Early stopping patience |
| `early_stopping_threshold` | float | `0.001` | Early stopping threshold |
| `load_best_model_at_end` | bool | `True` | Load best model at end |
| `metric_for_best_model` | str | `"eval_f1"` | Metric for best model selection |

### Class Weights (Imbalanced Data)

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `use_class_weights` | bool | `False` | Enable class weighting |
| `class_weights` | Dict[str, float] | `None` | Class weight mapping |

### WandB Integration

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `wandb_project` | str | `"robbert2023-ner"` | WandB project name |
| `wandb_entity` | str | `"slippydongle"` | WandB entity name |
| `run_name` | str | `None` | WandB run name (auto-generated if None) |
| `wandb_tags` | List[str] | `["robbert-2023", "dutch-nlp", "ner"]` | WandB tags |
| `report_to` | str | `"wandb"` | Reporting integration |

### Hardware Optimization

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `use_gpu` | bool | `True` | Use GPU if available |
| `fp16` | bool | `True` | Mixed precision training |
| `bf16` | bool | `False` | Brain float 16 |
| `dataloader_num_workers` | int | `2` | Data loader workers |
| `gradient_accumulation_steps` | int | `1` | Gradient accumulation |

### Test Mode

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `test_mode` | bool | `False` | Enable test mode |
| `test_epochs` | int | `1` | Epochs in test mode |
| `test_sample_limit` | int | `50` | Sample limit in test mode |
| `test_disable_wandb` | bool | `True` | Disable WandB in test mode |

## Configuration Templates

### Default Configuration

```yaml
# src/config/hf_training_default.yaml
hf_training:
  model_name: "DTAI-KULeuven/robbert-2023-dutch-base"
  epochs: 3
  batch_size: 8
  learning_rate: 5.0e-5
  use_class_weights: false
  wandb_project: "robbert2023-ner"
  # ... more parameters
```

### Test Configuration

```yaml
# src/config/hf_training_test.yaml
hf_training:
  test_mode: true
  test_epochs: 1
  test_sample_limit: 50
  use_gpu: false
  report_to: null
  # ... optimized for testing
```

### Class Weights Configuration

```yaml
# src/config/hf_training_class_weights.yaml
hf_training:
  use_class_weights: true
  class_weights:
    "O": 0.3
    "B-PER": 3.0
    "I-PER": 2.5
  # ... optimized for imbalanced data
```

## Environment Variables

The configuration supports environment variable substitution using the `${VAR:-default}` syntax:

```yaml
hf_training:
  model_name: "${ROBBERT_MODEL_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}"
  wandb_project: "${WANDB_PROJECT:-robbert2023-ner}"
  wandb_entity: "${WANDB_ENTITY:-slippydongle}"
```

## Advanced Usage

### Class Weights for Imbalanced Data

```python
# Configure class weights for imbalanced NER dataset
config = HFTrainingConfig(
    use_class_weights=True,
    class_weights={
        "O": 0.3,      # Reduce weight for majority class
        "B-PER": 3.0,  # Increase weight for minority class
        "I-PER": 2.5   # Increase weight for minority class
    }
)

# Validate class weights against label list
label_list = ["O", "B-PER", "I-PER"]
config.validate_class_weights(label_list)
```

### WandB Configuration

```python
# Extract WandB configuration for experiment tracking
wandb_config = config.get_wandb_config()

# Initialize WandB with extracted configuration
import wandb
wandb.init(**wandb_config)
```

### Output Directory Management

```python
# Create timestamped output directory
output_dir = config.create_output_dir("training")
# Creates: checkpoints/training_20250127_143022_v1.0/

# With experiment name
config.experiment_name = "ner_experiment"
output_dir = config.create_output_dir()
# Creates: checkpoints/ner_experiment_20250127_143022_v1.0/
```

### Learning Rate Scheduling

```python
# Configure advanced learning rate scheduling
config = HFTrainingConfig(
    lr_scheduler_type="cosine_with_restarts",
    cosine_schedule_num_cycles=0.5,
    warmup_steps=1000
)

# Polynomial decay
config = HFTrainingConfig(
    lr_scheduler_type="polynomial",
    polynomial_decay_power=2.0
)
```

## Integration with Hugging Face Trainer

```python
from transformers import Trainer, RobertaForTokenClassification
from src.training.hf_config import HFTrainingConfig

# Load configuration
config = HFTrainingConfig.from_yaml("config.yaml")

# Convert to TrainingArguments
training_args = config.to_training_args(output_dir="./checkpoints")

# Initialize model
model = RobertaForTokenClassification.from_pretrained(config.model_name)

# Create trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    # ... other trainer parameters
)

# Start training
trainer.train()
```

## Validation and Error Handling

The configuration includes comprehensive validation:

- **Type Validation**: Ensures all parameters have correct types
- **Value Validation**: Validates parameter ranges and constraints
- **Class Weights Validation**: Ensures class weights match label lists
- **Hardware Detection**: Automatically adjusts settings based on available hardware

```python
try:
    config = HFTrainingConfig.from_yaml("config.yaml")
    config.validate_class_weights(["O", "B-PER", "I-PER"])
except ValueError as e:
    print(f"Configuration error: {e}")
```

## Factory Functions

```python
from src.training.hf_config import (
    create_default_hf_config,
    create_test_hf_config
)

# Create default configuration
default_config = create_default_hf_config()

# Create test configuration
test_config = create_test_hf_config()
```

## Best Practices

1. **Use Templates**: Start with provided templates and customize as needed
2. **Environment Variables**: Use environment variables for sensitive or environment-specific values
3. **Class Weights**: Use class weights for imbalanced datasets
4. **Test Mode**: Use test mode for CI/CD and quick validation
5. **Validation**: Always validate configuration before training
6. **Version Tracking**: Use `run_version` for experiment versioning

## Examples

See `examples/hf_config_example.py` for comprehensive usage examples covering all features.

## Requirements

- Python 3.9+
- PyTorch 2.3+
- Transformers 4.41+
- PyYAML
- WandB (optional, for experiment tracking)

## Integration with Existing Codebase

The HFTrainingConfig is designed to integrate seamlessly with the existing RobBERT-2023 training pipeline:

- Uses same model (`DTAI-KULeuven/robbert-2023-dutch-base`)
- Compatible with existing label schemes
- Integrates with existing WandB configuration
- Follows existing project structure conventions