#!/usr/bin/env python
"""
lastnames2bucket.py

• reads   last_names_enriched.csv     (surname,count)  – no header
• normalises  count column:
      "< 5"  ->  3
      "-"    ->  0
      ""     ->  0
• sorts by descending frequency
• assigns bucket:
      top   5 %   -> common
      next 25 %   -> mid
      bottom 70 % -> tail
• writes  last_names_bucketed.csv  (surname,freq,bucket)  in the same folder
"""

import csv
import os
import sys
from pathlib import Path

# ---------- helper functions -----------------------------------------------
def norm_count(raw: str) -> int:
    """
    Convert the raw count field to an int.
        "< 5"  -> 3
        "-"    -> 0
        ""     -> 0
        "84"   -> 84
    """
    s = str(raw).strip()
    if not s or s == "-":
        return 0
    if s.startswith("<"):
        return 3
    return int(s)

def bucket(percentile: float) -> str:
    if percentile < 5:
        return "common"
    if percentile < 30:
        return "mid"
    return "tail"

# ---------- paths -----------------------------------------------------------
BASE_DIR  = Path(os.path.dirname(__file__)).resolve()
SRC_CSV   = BASE_DIR / "last_names_enriched.csv"
DST_CSV   = BASE_DIR / "last_names_bucketed.csv"

if not SRC_CSV.exists():
    sys.exit(f"[ERROR] {SRC_CSV} not found.")

# ---------- load & process --------------------------------------------------
records = []                   # (surname, freq)

with SRC_CSV.open(encoding="utf-8") as f:
    reader = csv.reader(f)
    for row in reader:
        if not row:
            continue
        surname = row[0].strip()
        freq_raw = row[1] if len(row) > 1 else "0"
        freq = norm_count(freq_raw)
        records.append((surname, freq))

# sort by descending freq
records.sort(key=lambda x: x[1], reverse=True)
n = len(records)

# ---------- write bucketed file --------------------------------------------
with DST_CSV.open("w", encoding="utf-8", newline="") as f:
    w = csv.writer(f)
    w.writerow(["surname", "freq", "bucket"])
    for idx, (surname, freq) in enumerate(records):
        pct = (idx / n) * 100.0
        w.writerow([surname, freq, bucket(pct)])

print(f"[OK] processed {n:,} rows  →  {DST_CSV.name}")
