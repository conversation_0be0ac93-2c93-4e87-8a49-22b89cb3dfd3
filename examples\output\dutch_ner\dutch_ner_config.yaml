hf_training:
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_epsilon: 1.0e-08
  batch_size: 8
  bf16: false
  class_weight_entity_types:
  - PER
  - LOC
  - ORG
  class_weight_method: balanced
  class_weights:
    B-LOC: 5.52
    B-ORG: 13.8
    B-PER: 9.2
    I-LOC: 1.0
    I-ORG: 1.0
    I-PER: 5.52
    O: 0.22439024390243903
  cosine_schedule_num_cycles: 0.5
  data_seed: 42
  dataloader_num_workers: 2
  dataloader_pin_memory: true
  ddp_bucket_cap_mb: null
  ddp_find_unused_parameters: false
  early_stopping_min_delta: 0.0001
  early_stopping_monitor_metrics:
  - eval_f1
  - eval_loss
  early_stopping_patience: 3
  early_stopping_restore_best_weights: true
  early_stopping_threshold: 0.001
  early_stopping_threshold_type: absolute
  end_learning_rate: 0.0
  epochs: 3
  eval_batch_size: 8
  eval_steps: 100
  evaluation_strategy: steps
  experiment_name: null
  fp16: true
  generate_model_card: true
  gradient_accumulation_steps: 1
  gradient_checkpointing: false
  greater_is_better: true
  hub_model_id: null
  hub_strategy: every_save
  ignore_data_skip: false
  label_smoothing_factor: 0.0
  learning_rate: 2.0e-05
  load_best_model_at_end: true
  logging_steps: 50
  logging_strategy: steps
  lr_scheduler_kwargs: null
  lr_scheduler_type: linear
  max_grad_norm: 1.0
  max_length: 512
  metric_for_best_model: eval_f1
  model_name: DTAI-KULeuven/robbert-2023-dutch-base
  optim: adamw_torch
  output_dir: checkpoints/dutch_ner_class_weights
  padding: max_length
  per_head_class_weights: true
  polynomial_decay_power: 1.0
  prediction_loss_only: false
  push_to_hub: false
  remove_unused_columns: true
  report_to: wandb
  resume_from_checkpoint: null
  run_name: imbalanced-dutch-ner
  run_version: v1.0
  save_class_weights: true
  save_predictions: true
  save_steps: 500
  save_strategy: steps
  save_total_limit: 3
  seed: 42
  test_disable_wandb: true
  test_epochs: 1
  test_mode: false
  test_sample_limit: 50
  truncation: true
  use_class_weights: true
  use_gpu: true
  version_description: null
  wandb_confusion_matrix_frequency: epoch
  wandb_confusion_matrix_steps: 500
  wandb_entity: slippydongle
  wandb_log_confusion_matrix: true
  wandb_log_evaluation_tables: true
  wandb_log_model_artifacts: true
  wandb_log_per_class_metrics: true
  wandb_notes: Hugging Face Trainer integration for RobBERT-2023 NER
  wandb_project: dutch-ner-class-weights
  wandb_tags:
  - dutch-nlp
  - ner
  - class-weights
  - robbert-2023
  warmup_ratio: 0.0
  warmup_schedule_type: linear
  warmup_steps: 500
  weight_decay: 0.01
