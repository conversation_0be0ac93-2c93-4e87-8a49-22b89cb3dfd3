"""
Tests for configuration management with RobBERT-2023 model identifiers.
"""

import os
import pytest
import tempfile
import yaml
from pathlib import Path
from pydantic import ValidationError

from src.config import (
    Config, ModelConfig, TrainingConfig, InferenceConfig, LoggingConfig,
    load_config, get_default_config, substitute_env_vars
)


class TestModelConfig:
    """Test ModelConfig validation and RobBERT-specific settings."""

    def test_valid_robbert_config(self):
        """Test valid RobBERT configuration."""
        config_data = {
            'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'tokenizer_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'max_length': 512,
            'tokenizer_type': 'byte_level_bpe',
            'num_labels': {'ner': 3, 'compliance': 2}
        }
        
        model_config = ModelConfig(**config_data)
        
        assert model_config.model_name == 'DTAI-KULeuven/robbert-2023-dutch-base'
        assert model_config.tokenizer_name == 'DTAI-KULeuven/robbert-2023-dutch-base'
        assert model_config.tokenizer_type == 'byte_level_bpe'
        assert model_config.num_labels['ner'] == 3
        assert model_config.encoder_weights is None

    def test_legacy_encoder_weights_support(self):
        """Test backward compatibility with encoder_weights field."""
        config_data = {
            'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'tokenizer_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'encoder_weights': 'src/models/weights/bertje_conll',
            'max_length': 512,
            'tokenizer_type': 'byte_level_bpe',
            'num_labels': {'ner': 3}
        }
        
        model_config = ModelConfig(**config_data)
        
        assert model_config.encoder_weights == 'src/models/weights/bertje_conll'
        assert model_config.model_name == 'DTAI-KULeuven/robbert-2023-dutch-base'

    def test_invalid_tokenizer_type(self):
        """Test validation of tokenizer_type field."""
        config_data = {
            'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'tokenizer_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'tokenizer_type': 'invalid_type',
            'num_labels': {'ner': 3}
        }
        
        with pytest.raises(ValidationError) as exc_info:
            ModelConfig(**config_data)
        
        assert 'Invalid tokenizer type' in str(exc_info.value)

    def test_empty_model_name(self):
        """Test validation of empty model_name."""
        config_data = {
            'model_name': '',
            'tokenizer_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'num_labels': {'ner': 3}
        }
        
        with pytest.raises(ValidationError) as exc_info:
            ModelConfig(**config_data)
        
        assert 'model_name must be a non-empty string' in str(exc_info.value)

    def test_empty_tokenizer_name(self):
        """Test validation of empty tokenizer_name."""
        config_data = {
            'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'tokenizer_name': '',
            'num_labels': {'ner': 3}
        }
        
        with pytest.raises(ValidationError) as exc_info:
            ModelConfig(**config_data)
        
        assert 'tokenizer_name must be a non-empty string' in str(exc_info.value)

    def test_wordpiece_tokenizer_type(self):
        """Test support for wordpiece tokenizer type."""
        config_data = {
            'model_name': 'wietsedv/bert-base-dutch-cased-finetuned-conll2002-ner',
            'tokenizer_name': 'wietsedv/bert-base-dutch-cased-finetuned-conll2002-ner',
            'tokenizer_type': 'wordpiece',
            'num_labels': {'ner': 9}
        }
        
        model_config = ModelConfig(**config_data)
        assert model_config.tokenizer_type == 'wordpiece'


class TestEnvironmentVariableSubstitution:
    """Test environment variable substitution for RobBERT configuration."""

    def test_robbert_model_env_vars(self):
        """Test environment variable substitution for RobBERT model identifiers."""
        # Set environment variables
        os.environ['ROBBERT_MODEL_NAME'] = 'custom/robbert-model'
        os.environ['ROBBERT_TOKENIZER_NAME'] = 'custom/robbert-tokenizer'
        
        try:
            data = {
                'model_name': '${ROBBERT_MODEL_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}',
                'tokenizer_name': '${ROBBERT_TOKENIZER_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}'
            }
            
            result = substitute_env_vars(data)
            
            assert result['model_name'] == 'custom/robbert-model'
            assert result['tokenizer_name'] == 'custom/robbert-tokenizer'
        finally:
            # Clean up environment variables
            os.environ.pop('ROBBERT_MODEL_NAME', None)
            os.environ.pop('ROBBERT_TOKENIZER_NAME', None)

    def test_default_robbert_values(self):
        """Test default values when environment variables are not set."""
        # Ensure environment variables are not set
        os.environ.pop('ROBBERT_MODEL_NAME', None)
        os.environ.pop('ROBBERT_TOKENIZER_NAME', None)
        
        data = {
            'model_name': '${ROBBERT_MODEL_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}',
            'tokenizer_name': '${ROBBERT_TOKENIZER_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}'
        }
        
        result = substitute_env_vars(data)
        
        assert result['model_name'] == 'DTAI-KULeuven/robbert-2023-dutch-base'
        assert result['tokenizer_name'] == 'DTAI-KULeuven/robbert-2023-dutch-base'


class TestConfigurationLoading:
    """Test configuration loading with RobBERT settings."""

    def test_load_robbert_config_from_yaml(self):
        """Test loading RobBERT configuration from YAML file."""
        config_data = {
            'model': {
                'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
                'tokenizer_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
                'max_length': 512,
                'tokenizer_type': 'byte_level_bpe',
                'num_labels': {'ner': 3, 'compliance': 2}
            },
            'training': {
                'epochs': 3,
                'batch_size': 8,
                'learning_rate': 2e-5,
                'warmup_steps': 500,
                'weight_decay': 0.01,
                'gradient_accumulation_steps': 1,
                'max_grad_norm': 1.0
            },
            'inference': {
                'batch_size': 16,
                'thresholds': {},
                'api': {'host': '0.0.0.0', 'port': 8000, 'workers': 1}
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            config = load_config(temp_path)
            
            assert config.model.model_name == 'DTAI-KULeuven/robbert-2023-dutch-base'
            assert config.model.tokenizer_name == 'DTAI-KULeuven/robbert-2023-dutch-base'
            assert config.model.tokenizer_type == 'byte_level_bpe'
            assert config.model.num_labels['ner'] == 3
            assert config.training.epochs == 3
            assert config.inference.batch_size == 16
            assert config.logging.level == 'INFO'
        finally:
            os.unlink(temp_path)

    def test_load_config_with_env_vars(self):
        """Test loading configuration with environment variable substitution."""
        os.environ['TEST_MODEL_NAME'] = 'test/robbert-model'
        os.environ['TEST_TOKENIZER_NAME'] = 'test/robbert-tokenizer'
        
        try:
            config_data = {
                'model': {
                    'model_name': '${TEST_MODEL_NAME:-default}',
                    'tokenizer_name': '${TEST_TOKENIZER_NAME:-default}',
                    'max_length': 512,
                    'tokenizer_type': 'byte_level_bpe',
                    'num_labels': {'ner': 3}
                },
                'training': {
                    'epochs': 3,
                    'batch_size': 8,
                    'learning_rate': 2e-5,
                    'warmup_steps': 500,
                    'weight_decay': 0.01,
                    'gradient_accumulation_steps': 1,
                    'max_grad_norm': 1.0
                },
                'inference': {
                    'batch_size': 16,
                    'thresholds': {},
                    'api': {'host': '0.0.0.0', 'port': 8000, 'workers': 1}
                },
                'logging': {
                    'level': 'INFO',
                    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                }
            }
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                yaml.dump(config_data, f)
                temp_path = f.name
            
            config = load_config(temp_path)
            
            assert config.model.model_name == 'test/robbert-model'
            assert config.model.tokenizer_name == 'test/robbert-tokenizer'
        finally:
            os.environ.pop('TEST_MODEL_NAME', None)
            os.environ.pop('TEST_TOKENIZER_NAME', None)
            os.unlink(temp_path)

    def test_load_default_config(self):
        """Test loading default configuration with RobBERT settings."""
        # This test assumes the default config file exists and is valid
        try:
            config = get_default_config()
            
            # Verify RobBERT-specific fields are present
            assert hasattr(config.model, 'model_name')
            assert hasattr(config.model, 'tokenizer_name')
            assert hasattr(config.model, 'tokenizer_type')
            
            # Verify default values
            assert config.model.tokenizer_type in ['byte_level_bpe', 'wordpiece']
            assert config.model.max_length == 512
            assert isinstance(config.model.num_labels, dict)
            assert 'ner' in config.model.num_labels
        except FileNotFoundError:
            pytest.skip("Default configuration file not found")

    def test_invalid_config_file(self):
        """Test error handling for invalid configuration file."""
        with pytest.raises(FileNotFoundError):
            load_config('nonexistent_config.yaml')

    def test_invalid_yaml_syntax(self):
        """Test error handling for invalid YAML syntax."""
        invalid_yaml = "model:\n  model_name: [invalid yaml syntax"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(invalid_yaml)
            temp_path = f.name
        
        try:
            with pytest.raises(yaml.YAMLError):
                load_config(temp_path)
        finally:
            os.unlink(temp_path)

    def test_missing_required_fields(self):
        """Test validation error for missing required fields."""
        incomplete_config = {
            'model': {
                'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
                # Missing tokenizer_name and num_labels
                'max_length': 512
            },
            'training': {
                'epochs': 3,
                'batch_size': 8,
                'learning_rate': 2e-5,
                'warmup_steps': 500,
                'weight_decay': 0.01,
                'gradient_accumulation_steps': 1,
                'max_grad_norm': 1.0
            },
            'inference': {
                'batch_size': 16,
                'thresholds': {},
                'api': {'host': '0.0.0.0', 'port': 8000, 'workers': 1}
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(incomplete_config, f)
            temp_path = f.name
        
        try:
            with pytest.raises(ValueError) as exc_info:
                load_config(temp_path)
            
            assert 'Configuration validation failed' in str(exc_info.value)
        finally:
            os.unlink(temp_path)


class TestRobBERTSpecificValidation:
    """Test RobBERT-specific configuration validation."""

    def test_robbert_ner_labels_count(self):
        """Test that RobBERT configuration uses correct NER label count."""
        config_data = {
            'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'tokenizer_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'tokenizer_type': 'byte_level_bpe',
            'num_labels': {'ner': 3}  # O, B-PER, I-PER for RobBERT
        }
        
        model_config = ModelConfig(**config_data)
        assert model_config.num_labels['ner'] == 3

    def test_bertje_ner_labels_count(self):
        """Test that BERTje configuration can still use original label count."""
        config_data = {
            'model_name': 'wietsedv/bert-base-dutch-cased-finetuned-conll2002-ner',
            'tokenizer_name': 'wietsedv/bert-base-dutch-cased-finetuned-conll2002-ner',
            'tokenizer_type': 'wordpiece',
            'num_labels': {'ner': 9}  # CoNLL-2002 NER tags for BERTje
        }
        
        model_config = ModelConfig(**config_data)
        assert model_config.num_labels['ner'] == 9

    def test_mixed_model_tokenizer_names(self):
        """Test configuration with different model and tokenizer names."""
        config_data = {
            'model_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'tokenizer_name': 'DTAI-KULeuven/robbert-2023-dutch-base',
            'tokenizer_type': 'byte_level_bpe',
            'num_labels': {'ner': 3}
        }
        
        model_config = ModelConfig(**config_data)
        assert model_config.model_name == model_config.tokenizer_name