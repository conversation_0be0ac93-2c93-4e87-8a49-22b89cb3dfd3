# Project Structure

## Directory Organization

### Root Level
```
bertjeNER/
├── README.md              # Comprehensive project documentation
├── requirements.txt       # Python dependencies with pinned versions
├── LICENSE               # MIT license
├── .gitignore           # Git ignore patterns
└── venv/                # Virtual environment (local only)
```

### Source Code (`src/`)
```
src/
├── __init__.py          # Package initialization
├── config/              # Configuration management
│   ├── __init__.py      # Config loading and validation
│   └── default.yaml     # Default configuration file
├── models/              # Model definitions and loading
│   ├── multitask_robbert.py  # Main multi-head model
│   └── weights/         # Model checkpoints (downloaded)
├── heads/               # Task-specific model heads
│   └── ner_head.py      # Named Entity Recognition head
├── training/            # Training pipeline
│   └── train_multitask.py  # Multi-task training script
├── inference/           # Inference interfaces
│   ├── api_fastapi.py   # FastAPI REST server
│   └── cli_batch.py     # Batch processing CLI
└── data/                # Data processing utilities
    └── dataset_builder.py  # Dataset construction
```

### Scripts (`scripts/`)
```
scripts/
├── fetch_checkpoints.py    # Download model checkpoints
├── smoke_test.py          # Basic functionality validation
├── enhanced_ner_test.py   # Advanced NER testing
└── validate_model.py      # Model validation utilities
```

### Tests (`tests/`)
```
tests/
├── __init__.py           # Test package initialization
├── conftest.py          # Pytest configuration and fixtures
├── test_models.py       # Model testing (unit + integration)
└── test_inference.py    # Inference pipeline testing
```

### Data (`data_sets/` & `process_data/`)
```
data_sets/
└── synthetic1.json      # Sample synthetic data

process_data/
├── input/               # Input data for processing
└── output/              # Processed output data
```

### Notebooks (`notebooks/`)
```
notebooks/               # Jupyter notebooks for exploration
```

### Kiro Configuration (`.kiro/`)
```
.kiro/
├── steering/            # AI assistant steering rules
│   ├── product.md       # Product overview and features
│   ├── tech.md         # Technology stack and commands
│   └── structure.md    # Project organization (this file)
└── specs/              # Feature specifications
    └── dutch-ner-gdpr-pipeline/  # GDPR pipeline specification
        ├── requirements.md
        ├── design.md
        └── tasks.md
```

## Code Organization Patterns

### Package Structure
- **Flat Module Hierarchy**: Avoid deep nesting, prefer flat structure under `src/`
- **Functional Grouping**: Group by functionality (models, heads, inference, training)
- **Clear Separation**: Separate concerns (config, data, models, inference)

### Import Conventions
```python
# Absolute imports from src root
from src.models.multitask_robbert import MultiTaskRobBERT
from src.config import load_config
from src.heads.ner_head import NERHead

# Relative imports within modules
from ..config import Config
from .ner_head import NERHead
```

### File Naming
- **Snake Case**: All Python files use snake_case naming
- **Descriptive Names**: Clear, descriptive file and module names
- **Consistent Patterns**: Similar functionality uses consistent naming

### Configuration Files
- **YAML Format**: Primary configuration in `src/config/default.yaml`
- **Environment Variables**: Support for environment variable substitution
- **Validation**: Pydantic models for configuration validation

## Module Responsibilities

### `src/models/`
- **Purpose**: Core model definitions and loading logic
- **Key Files**: `multitask_bertje.py` (main model class)
- **Responsibilities**: Model architecture, checkpoint loading, device management

### `src/heads/`
- **Purpose**: Task-specific neural network heads
- **Pattern**: One file per head type (e.g., `ner_head.py`)
- **Interface**: Consistent forward() method signature

### `src/config/`
- **Purpose**: Configuration management and validation
- **Key Files**: `__init__.py` (loading functions), `default.yaml` (settings)
- **Features**: Environment variable substitution, Pydantic validation

### `src/inference/`
- **Purpose**: Inference interfaces and serving
- **Key Files**: `api_fastapi.py` (REST API), `cli_batch.py` (batch processing)
- **Pattern**: Separate files for different interface types

### `src/training/`
- **Purpose**: Model training and fine-tuning
- **Key Files**: `train_multitask.py` (main training script)
- **Responsibilities**: Training loops, loss calculation, checkpointing

### `src/data/`
- **Purpose**: Data processing and dataset utilities
- **Key Files**: `dataset_builder.py` (dataset construction)
- **Responsibilities**: Data loading, preprocessing, format conversion

## Testing Organization

### Test Structure
- **Mirror Source**: Test structure mirrors `src/` organization
- **Fixtures**: Centralized fixtures in `conftest.py`
- **Categories**: Unit tests, integration tests, slow tests

### Test Naming
```python
# Test classes mirror source classes
class TestMultiTaskRobBERT:
    def test_model_init(self):
    def test_forward_pass(self):

# Test functions describe behavior
def test_config_loading_with_env_vars():
def test_ner_entity_extraction():
```

### Test Data
- **Fixtures**: Reusable test data in pytest fixtures
- **Mocking**: Mock external dependencies (model downloads, API calls)
- **Synthetic Data**: Use synthetic Dutch text for consistent testing

## Development Workflow

### Adding New Features
1. **Create Module**: Add new module under appropriate `src/` subdirectory
2. **Add Tests**: Create corresponding test file in `tests/`
3. **Update Config**: Add configuration options if needed
4. **Document**: Update README.md and add docstrings

### Adding New Model Heads
1. **Create Head**: New file in `src/heads/` following existing patterns
2. **Register Head**: Add to `MultiTaskRobBERT` model class
3. **Add Config**: Update configuration with head parameters
4. **Test**: Add unit and integration tests

### Configuration Changes
1. **Update YAML**: Modify `src/config/default.yaml`
2. **Update Models**: Adjust Pydantic models in `src/config/__init__.py`
3. **Test**: Ensure configuration validation works
4. **Document**: Update README.md with new options

## File Conventions

### Python Files
- **Encoding**: UTF-8 with explicit encoding declaration
- **Docstrings**: Google-style docstrings for all public functions
- **Type Hints**: Required for all function signatures
- **Imports**: Organized (standard library, third-party, local)

### Configuration Files
- **YAML**: Primary format for configuration
- **JSON**: Used for data files and API responses
- **Environment**: `.env` files for local development only

### Documentation
- **Markdown**: All documentation in Markdown format
- **README**: Comprehensive project documentation
- **Inline**: Docstrings and comments for complex logic