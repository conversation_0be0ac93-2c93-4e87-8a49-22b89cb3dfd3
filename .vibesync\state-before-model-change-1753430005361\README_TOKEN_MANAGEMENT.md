# Token Management for BERTje NER Model

This document provides a complete guide for managing tokens in the BERTje NER model, including checking current vocabulary, adding new tokens, and resizing model embeddings.

## Quick Reference

### Essential Commands

```bash
# Activate environment (always do this first!)
.\venv\Scripts\activate  # Windows
source venv/bin/activate # Linux/macOS

# Check vocabulary info
python scripts/token_management.py info

# Search for tokens
python scripts/token_management.py search "amsterdam"

# Check if tokens exist
python scripts/token_management.py check "Amsterdam" "[GEMEENTE]"

# Add tokens with model update
python scripts/token_management.py add "[GEMEENTE]" "[POSTCODE]" --resize --save models/updated_bertje

# Add GDPR tokens (specialized script)
python scripts/add_gdpr_tokens.py --save-path models/bertje_gdpr
```

## Available Scripts

### 1. `scripts/token_management.py` - Main Token Management Tool

**Purpose**: Complete toolkit for vocabulary inspection and modification

**Key Features**:
- Inspect current vocabulary (30,000 tokens)
- Search for existing tokens by pattern
- Add new regular or special tokens
- Resize model embeddings automatically
- Export vocabulary to JSON/text files

**Usage Examples**:
```bash
# Get vocabulary statistics
python scripts/token_management.py info

# Find Dutch city names
python scripts/token_management.py search "dam" --limit 10

# Check specific tokens
python scripts/token_management.py check "Amsterdam" "Rotterdam" "[GEMEENTE]"

# Add new tokens and update model
python scripts/token_management.py add "[GEMEENTE]" "[POSTCODE]" "[STRAAT]" \
    --resize --save models/municipality_bertje

# Export vocabulary
python scripts/token_management.py export vocab.json --format json
```

### 2. `scripts/add_gdpr_tokens.py` - GDPR Compliance Tokens

**Purpose**: Add Dutch GDPR/AVG compliance tokens for privacy-aware NLP

**Features**:
- Adds 25+ GDPR-specific tokens
- Includes redaction markers: `[REDACTED_PERSON]`, `[REDACTED_ADDRESS]`
- Includes entity markers: `[PERSON]`, `[EMAIL]`, `[BSN]`
- Includes Dutch legal terms: `verwerkingsverantwoordelijke`, `betrokkene`

**Usage**:
```bash
# Preview what will be added
python scripts/add_gdpr_tokens.py --dry-run

# Add GDPR tokens to model
python scripts/add_gdpr_tokens.py --save-path models/bertje_gdpr
```

### 3. `scripts/test_token_addition.py` - Testing & Demonstration

**Purpose**: Safe testing of token addition functionality

**Features**:
- Tests token addition on temporary model copy
- Demonstrates search functionality
- Validates embedding resize process
- No risk to original model

**Usage**:
```bash
python scripts/test_token_addition.py
```

## Understanding the Process

### 1. Current Model Vocabulary

The BERTje model starts with:
- **Vocabulary Size**: 30,000 tokens
- **Special Tokens**: `[PAD]`, `[UNK]`, `[CLS]`, `[SEP]`, `[MASK]`
- **Language**: Dutch-focused with subword tokenization
- **Coverage**: General Dutch text, news, web content

### 2. When to Add Tokens

**Add tokens when**:
- You have domain-specific terms appearing frequently (>100 times)
- You need special markers for processing (GDPR redaction, entity types)
- Existing tokenization splits important terms poorly
- You're building domain-specific applications

**Don't add tokens when**:
- Terms appear rarely in your data
- Existing subword tokenization works well
- You're just experimenting (use copies!)

### 3. Token Addition Process

1. **Check Existing**: Verify tokens don't already exist
2. **Add to Tokenizer**: Expand vocabulary with new tokens
3. **Resize Embeddings**: Expand model's embedding layer
4. **Initialize Weights**: New embeddings get random initialization
5. **Save Model**: Persist updated model and tokenizer

### 4. Embedding Resize Details

When you add tokens, the model's embedding layers must be resized:

```python
# Before: embeddings.shape = [30000, 768]
# After:  embeddings.shape = [30025, 768]  # +25 new tokens
```

**Important Notes**:
- New token embeddings start with random weights
- Model performance may initially decrease
- Fine-tuning recommended after adding many tokens
- Both encoder and task head embeddings are resized

## Common Use Cases

### 1. Dutch Municipality Names

```bash
# Check which cities exist
python scripts/token_management.py check \
    "Amsterdam" "Rotterdam" "Utrecht" "Eindhoven" "Groningen"

# Add missing municipalities
python scripts/token_management.py add \
    "Almere" "Zoetermeer" "Maastricht" "Dordrecht" \
    --resize --save models/municipality_bertje
```

### 2. GDPR Compliance Tokens

```bash
# Add comprehensive GDPR token set
python scripts/add_gdpr_tokens.py --save-path models/bertje_gdpr

# Verify GDPR tokens were added
python scripts/token_management.py check \
    "[REDACTED_PERSON]" "[REDACTED_ADDRESS]" "[BSN]"
```

### 3. Domain-Specific Terms

```bash
# Legal/compliance domain
python scripts/token_management.py add \
    "AVG" "GDPR" "privacyverklaring" "cookiebeleid" \
    --resize --save models/legal_bertje

# Medical domain
python scripts/token_management.py add \
    "diagnose" "behandeling" "medicatie" "ziekenhuis" \
    --resize --save models/medical_bertje
```

### 4. Special Processing Markers

```bash
# Add processing markers
python scripts/token_management.py add \
    "[START_ENTITY]" "[END_ENTITY]" "[REDACTED]" "[SENSITIVE]" \
    --special --resize --save models/marked_bertje
```

## Best Practices

### 1. Always Backup First

```bash
# Backup original model
cp -r src/models/weights/bertje_conll src/models/weights/bertje_conll_backup
```

### 2. Test on Copies

```bash
# Use test script for safe experimentation
python scripts/test_token_addition.py
```

### 3. Validate After Changes

```bash
# Test model still works
python scripts/smoke_test.py

# Run enhanced tests
python scripts/enhanced_ner_test.py
```

### 4. Document Changes

Keep track of what tokens you've added and why:

```bash
# Export vocabulary after changes
python scripts/token_management.py export vocab_after_gdpr.json

# Compare with original
diff vocab_original.json vocab_after_gdpr.json
```

## Integration with Training

After adding tokens, consider retraining:

### 1. Light Fine-tuning (Recommended)

```bash
# Fine-tune for 1-2 epochs on domain data
python -m src.training.train_multitask \
    --heads ner \
    --epochs 2 \
    --batch-size 8 \
    --learning-rate 1e-5 \
    --model-path models/bertje_gdpr
```

### 2. Embedding-Only Training

```python
# Freeze encoder, train only new embeddings
for param in model.encoder.parameters():
    param.requires_grad = False

# Train only embedding layer
for param in model.encoder.embeddings.parameters():
    param.requires_grad = True
```

## Troubleshooting

### Common Issues

**"Token already exists"**
```bash
# Check first
python scripts/token_management.py check "your_token"
```

**"Model size mismatch"**
```bash
# Resize embeddings
python scripts/token_management.py add --resize
```

**"Out of memory"**
- Use CPU-only mode for embedding operations
- Reduce batch size during training
- Consider gradient checkpointing

### Validation Steps

```bash
# 1. Check model loads correctly
python -c "from src.models.multitask_bertje import MultiTaskBERTje; m = MultiTaskBERTje.from_pretrained('models/updated_bertje'); print('✓ Model loads')"

# 2. Check tokenizer works
python -c "from transformers import AutoTokenizer; t = AutoTokenizer.from_pretrained('models/updated_bertje'); print('✓ Tokenizer works')"

# 3. Test inference
python scripts/smoke_test.py
```

## Files Created

This token management system includes:

- `scripts/token_management.py` - Main token management CLI tool
- `scripts/add_gdpr_tokens.py` - GDPR-specific token addition
- `scripts/test_token_addition.py` - Safe testing script
- `docs/token_management.md` - Comprehensive documentation
- `README_TOKEN_MANAGEMENT.md` - This quick reference guide

## Next Steps

1. **Explore Current Vocabulary**: Run `python scripts/token_management.py info`
2. **Test Safely**: Run `python scripts/test_token_addition.py`
3. **Add Domain Tokens**: Use appropriate scripts for your use case
4. **Validate Changes**: Run smoke tests after modifications
5. **Fine-tune if Needed**: Retrain on domain data with new tokens

The token management system is designed to be safe, comprehensive, and easy to use. Always test changes on copies before modifying your production models!