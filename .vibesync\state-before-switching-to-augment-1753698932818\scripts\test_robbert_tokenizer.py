#!/usr/bin/env python3
"""
Test script for RobBERT tokenizer with label alignment.
Demonstrates tokenization quality and alignment functionality.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment


def test_basic_functionality():
    """Test basic tokenizer functionality."""
    print("=== Testing Basic RobBERT Tokenizer Functionality ===")
    
    tokenizer = RobBERTTokenizerWithAlignment()
    print(f"✓ Tokenizer initialized: {tokenizer.model_name}")
    print(f"✓ Label mappings: {tokenizer.label2id}")
    print()


def test_compound_names():
    """Test compound Dutch name handling."""
    print("=== Testing Compound Dutch Names ===")
    
    tokenizer = RobBERTTokenizerWithAlignment()
    
    test_cases = [
        {
            "words": ["Jan-Willem", "van", "der", "Berg"],
            "labels": ["B-PER", "I-PER", "I-<PERSON><PERSON>", "I-<PERSON>ER"],
            "description": "Compound first name with prefixes"
        },
        {
            "words": ["<PERSON><PERSON><PERSON>", "de", "Jong-Bakker"],
            "labels": ["B-PER", "I-PER", "I-PER"],
            "description": "Compound names"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"{i}. {case['description']}")
        print(f"   Input: {' '.join(case['words'])}")
        print(f"   Labels: {' '.join(case['labels'])}")
        
        alignment = tokenizer.tokenize_with_alignment(case["words"], case["labels"])
        
        print(f"   Tokens: {alignment.tokens}")
        print(f"   Token labels: {alignment.token_labels}")
        print(f"   Word IDs: {alignment.word_ids}")
        
        # Show alignment
        print("   Alignment:")
        for j, (token, word_id, label) in enumerate(zip(alignment.tokens, alignment.word_ids, alignment.token_labels)):
            word = case["words"][word_id] if word_id is not None and word_id < len(case["words"]) else "[SPECIAL]"
            print(f"     {j:2d}: '{token:12}' -> word_{word_id} ({word:15}) -> {label}")
        
        print()


def test_diacritics():
    """Test diacritics handling."""
    print("=== Testing Diacritics Handling ===")
    
    tokenizer = RobBERTTokenizerWithAlignment()
    
    test_cases = [
        {
            "words": ["José", "María", "González"],
            "labels": ["B-PER", "I-PER", "I-PER"],
            "description": "Spanish names with diacritics"
        },
        {
            "words": ["François", "Müller", "café"],
            "labels": ["B-PER", "I-PER", "O"],
            "description": "Mixed diacritics"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"{i}. {case['description']}")
        print(f"   Input: {' '.join(case['words'])}")
        
        alignment = tokenizer.tokenize_with_alignment(case["words"], case["labels"])
        
        print(f"   Tokens: {alignment.tokens}")
        print(f"   Token labels: {alignment.token_labels}")
        print()


def test_oov_analysis():
    """Test out-of-vocabulary analysis."""
    print("=== Testing OOV Rate Analysis ===")
    
    tokenizer = RobBERTTokenizerWithAlignment()
    
    # Test with various Dutch text samples
    test_texts = [
        ["Dit", "is", "een", "Nederlandse", "zin"],
        ["Geachte", "heer", "Jansen", "hartelijk", "dank"],
        ["Amsterdam", "Rotterdam", "Den", "Haag", "Utrecht"],
        ["maandag", "dinsdag", "woensdag", "donderdag", "vrijdag"],
        ["één", "twee", "drie", "vier", "vijf"],
        ["<EMAIL>", "werkt", "bij", "Google"],
        ["www.example.com", "is", "een", "website"]
    ]
    
    total_words = 0
    total_tokens = 0
    total_subword_splits = 0
    
    for i, words in enumerate(test_texts, 1):
        alignment = tokenizer.tokenize_with_alignment(words)
        
        # Count non-special tokens
        non_special_tokens = sum(1 for wid in alignment.word_ids if wid is not None)
        
        # Count subword splits
        word_token_count = {}
        for word_id in alignment.word_ids:
            if word_id is not None:
                word_token_count[word_id] = word_token_count.get(word_id, 0) + 1
        
        splits = sum(1 for count in word_token_count.values() if count > 1)
        
        print(f"{i}. {' '.join(words)}")
        print(f"   Words: {len(words)}, Tokens: {non_special_tokens}, Splits: {splits}")
        print(f"   Ratio: {non_special_tokens/len(words):.2f}")
        
        total_words += len(words)
        total_tokens += non_special_tokens
        total_subword_splits += splits
    
    print(f"\nOverall Statistics:")
    print(f"  Total words: {total_words}")
    print(f"  Total tokens: {total_tokens}")
    print(f"  Total subword splits: {total_subword_splits}")
    print(f"  Average token-to-word ratio: {total_tokens/total_words:.2f}")
    print(f"  Subword split rate: {total_subword_splits/total_words:.2f}")
    print()


def test_alignment_validation():
    """Test alignment validation."""
    print("=== Testing Alignment Validation ===")
    
    tokenizer = RobBERTTokenizerWithAlignment()
    
    words = ["Jan-Willem", "van", "der", "Berg", "woont", "in", "Amsterdam"]
    labels = ["B-PER", "I-PER", "I-PER", "I-PER", "O", "O", "O"]
    
    print(f"Input: {' '.join(words)}")
    print(f"Labels: {' '.join(labels)}")
    
    validation = tokenizer.validate_alignment(words, labels, verbose=True)
    
    print(f"\nValidation result: {'✓ PASSED' if validation['valid'] else '✗ FAILED'}")
    print()


def test_model_encoding():
    """Test model encoding functionality."""
    print("=== Testing Model Encoding ===")
    
    tokenizer = RobBERTTokenizerWithAlignment()
    
    words = ["Jan", "Jansen", "werkt", "bij", "Google"]
    labels = ["B-PER", "I-PER", "O", "O", "O"]
    
    print(f"Input: {' '.join(words)}")
    print(f"Labels: {' '.join(labels)}")
    
    encoding = tokenizer.encode_for_model(words, labels, max_length=32)
    
    print(f"Encoded shapes:")
    print(f"  input_ids: {encoding['input_ids'].shape}")
    print(f"  attention_mask: {encoding['attention_mask'].shape}")
    print(f"  labels: {encoding['labels'].shape}")
    
    # Show first few tokens
    input_ids = encoding["input_ids"].squeeze().tolist()
    attention_mask = encoding["attention_mask"].squeeze().tolist()
    label_ids = encoding["labels"].squeeze().tolist()
    
    print(f"\nFirst 10 positions:")
    for i in range(min(10, len(input_ids))):
        token = tokenizer.tokenizer.convert_ids_to_tokens([input_ids[i]])[0]
        print(f"  {i:2d}: token='{token:12}' id={input_ids[i]:5d} mask={attention_mask[i]} label={label_ids[i]} ({tokenizer.id2label.get(label_ids[i], 'UNK')})")
    
    print()


def main():
    """Run all tests."""
    print("RobBERT Tokenizer Test Suite")
    print("=" * 50)
    print()
    
    try:
        test_basic_functionality()
        test_compound_names()
        test_diacritics()
        test_oov_analysis()
        test_alignment_validation()
        test_model_encoding()
        
        print("=" * 50)
        print("✓ All tests completed successfully!")
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())