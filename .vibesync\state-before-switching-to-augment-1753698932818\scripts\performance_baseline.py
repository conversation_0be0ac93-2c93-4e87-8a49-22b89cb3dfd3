#!/usr/bin/env python3
"""
Performance baseline measurement script for RobBERT-2023 model.

This script measures performance metrics for RobBERT-2023:
- Inference speed (tokens/sec)
- Memory usage (RAM and GPU)
- CPU utilization
- Model loading time
- Tokenization speed

Usage:
    python scripts/performance_baseline.py [--output results.json] [--samples 100] [--warmup 10]
"""

import argparse
import json
import time
import statistics
import gc
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import threading
import sys
import os

import torch
import psutil

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.models.multitask_robbert import MultiTaskRobBERT
from src.utils.logging_utils import get_logger
from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    model_name: str
    model_loading_time: float
    tokenization_speed: float  # tokens/sec
    inference_speed: float  # tokens/sec
    memory_usage_mb: float
    gpu_memory_usage_mb: float
    cpu_utilization_percent: float
    model_size_mb: float
    num_parameters: int
    avg_inference_time_ms: float
    std_inference_time_ms: float
    throughput_samples_per_sec: float


class CPUMonitor:
    """Monitor CPU utilization during inference."""
    
    def __init__(self, interval: float = 0.1):
        self.interval = interval
        self.cpu_percentages = []
        self.monitoring = False
        self.thread = None
    
    def _monitor(self):
        """Monitor CPU usage in background thread."""
        while self.monitoring:
            self.cpu_percentages.append(psutil.cpu_percent(interval=None))
            time.sleep(self.interval)
    
    def start(self):
        """Start monitoring."""
        self.cpu_percentages = []
        self.monitoring = True
        # Initialize cpu_percent to avoid first call returning 0
        psutil.cpu_percent(interval=None)
        self.thread = threading.Thread(target=self._monitor)
        self.thread.start()
    
    def stop(self):
        """Stop monitoring and return average CPU usage."""
        self.monitoring = False
        if self.thread:
            self.thread.join()
        
        if self.cpu_percentages:
            return statistics.mean(self.cpu_percentages)
        return 0.0


class PerformanceBenchmark:
    """Performance benchmark suite for RobBERT-2023."""
    
    def __init__(self, output_file: Optional[str] = None, verbose: bool = True):
        self.output_file = output_file
        self.verbose = verbose
        self.logger = get_logger(__name__)
        
        # Test texts for benchmarking
        self.test_texts = self._generate_test_texts()
    
    def _generate_test_texts(self) -> List[str]:
        """Generate test texts for benchmarking."""
        return [
            "Jan Jansen woont in Amsterdam.",
            "Marie van der Berg werkt bij Google Nederland.",
            "De minister-president heeft een vergadering met Jan-Willem de Vries.",
            "Dr. Anna Müller-Schmidt is hoogleraar aan de Universiteit van Amsterdam.",
            "Het bedrijf Van der Meer & Zonen B.V. heeft kantoren in Rotterdam en Den Haag.",
            "Mevrouw Janneke van den Berg-Smit is directeur van de Nederlandse Bank.",
            "Professor emeritus Pieter Jan van der Hoeven publiceerde een nieuw boek.",
            "De heer A.J. (Ton) Vermeulen is benoemd tot burgemeester van Utrecht.",
            "Drs. Elisabeth de Jong-van der Meer promoveerde aan de TU Delft.",
            "Jan, Marie, Pieter en Anna werkten samen aan het onderzoeksproject."
        ]
    
    def get_model_info(self, model) -> Dict[str, Any]:
        """Get model information including size and parameter count."""
        # Calculate model size
        param_size = 0
        buffer_size = 0
        
        for param in model.parameters():
            param_size += param.nelement() * param.element_size()
        
        for buffer in model.buffers():
            buffer_size += buffer.nelement() * buffer.element_size()
        
        model_size_mb = (param_size + buffer_size) / (1024 * 1024)
        
        # Count parameters
        num_parameters = sum(p.numel() for p in model.parameters())
        
        return {
            'model_size_mb': model_size_mb,
            'num_parameters': num_parameters
        }
    
    def measure_model_loading(self, model_creator_func) -> Tuple[Any, float]:
        """Measure model loading time."""
        start_time = time.time()
        model = model_creator_func()
        loading_time = time.time() - start_time
        return model, loading_time
    
    def measure_tokenization_speed(self, tokenizer, num_samples: int = 1000) -> float:
        """Measure tokenization speed in tokens per second."""
        texts = (self.test_texts * (num_samples // len(self.test_texts) + 1))[:num_samples]
        
        start_time = time.time()
        total_tokens = 0
        
        for text in texts:
            if hasattr(tokenizer, 'tokenizer'):
                # RobBERTTokenizerWithAlignment
                tokens = tokenizer.tokenizer(text, add_special_tokens=False)['input_ids']
            else:
                # Standard tokenizer
                tokens = tokenizer(text, add_special_tokens=False)['input_ids']
            total_tokens += len(tokens)
        
        elapsed_time = time.time() - start_time
        return total_tokens / elapsed_time if elapsed_time > 0 else 0
    
    def _run_single_inference(self, model, tokenizer, text: str):
        """Run single inference and return timing."""
        start_time = time.time()
        
        # Tokenize
        if hasattr(tokenizer, 'encode_for_model'):
            # RobBERTTokenizerWithAlignment
            inputs = tokenizer.encode_for_model([text.split()], [['O'] * len(text.split())])
        else:
            # Standard tokenizer
            inputs = tokenizer(text, return_tensors='pt', truncation=True, max_length=512)
        
        # Move to device
        device = next(model.parameters()).device
        inputs = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in inputs.items()}
        
        # Inference
        with torch.no_grad():
            if hasattr(model, 'heads') and hasattr(model, 'forward'):
                # MultiTaskRobBERT - check if it has the custom forward method
                try:
                    outputs = model(
                        input_ids=inputs['input_ids'],
                        attention_mask=inputs['attention_mask'],
                        heads=['ner']
                    )
                except:
                    outputs = model.model(**inputs)
            else:
                # Direct model
                outputs = model(**inputs)
        
        inference_time = time.time() - start_time
        return inference_time
    
    def measure_inference_performance(self, model, tokenizer, num_samples: int = 100, 
                                    warmup_samples: int = 10) -> Dict[str, float]:
        """Measure inference performance metrics."""
        # Warmup
        for _ in range(warmup_samples):
            text = self.test_texts[0]
            self._run_single_inference(model, tokenizer, text)
        
        # Clear cache after warmup
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # Measure memory before inference
        process = psutil.Process()
        memory_before = process.memory_info().rss / (1024 * 1024)  # MB
        gpu_memory_before = 0
        if torch.cuda.is_available():
            gpu_memory_before = torch.cuda.memory_allocated() / (1024 * 1024)  # MB
        
        # Start CPU monitoring
        cpu_monitor = CPUMonitor()
        cpu_monitor.start()
        
        # Run inference samples
        inference_times = []
        texts = (self.test_texts * (num_samples // len(self.test_texts) + 1))[:num_samples]
        
        for text in texts:
            inference_time = self._run_single_inference(model, tokenizer, text)
            inference_times.append(inference_time)
        
        # Stop CPU monitoring
        avg_cpu_percent = cpu_monitor.stop()
        
        # Measure memory after inference
        memory_after = process.memory_info().rss / (1024 * 1024)  # MB
        gpu_memory_after = 0
        if torch.cuda.is_available():
            gpu_memory_after = torch.cuda.memory_allocated() / (1024 * 1024)  # MB
        
        # Calculate metrics
        avg_inference_time = statistics.mean(inference_times)
        std_inference_time = statistics.stdev(inference_times) if len(inference_times) > 1 else 0
        
        # Calculate tokens per second (approximate)
        avg_tokens_per_text = 20  # Rough estimate
        total_tokens = num_samples * avg_tokens_per_text
        total_time = sum(inference_times)
        inference_speed = total_tokens / total_time if total_time > 0 else 0
        
        # Throughput in samples per second
        throughput = num_samples / total_time if total_time > 0 else 0
        
        return {
            'inference_speed': inference_speed,
            'memory_usage_mb': memory_after - memory_before,
            'gpu_memory_usage_mb': gpu_memory_after - gpu_memory_before,
            'cpu_utilization_percent': avg_cpu_percent,
            'avg_inference_time_ms': avg_inference_time * 1000,
            'std_inference_time_ms': std_inference_time * 1000,
            'throughput_samples_per_sec': throughput
        }
    
    def benchmark_robbert(self, num_samples: int = 100, warmup_samples: int = 10) -> PerformanceMetrics:
        """Benchmark RobBERT-2023 model."""
        self.logger.logger.info("=== Benchmarking RobBERT-2023 ===")
        
        # Load model
        model, loading_time = self.measure_model_loading(
            lambda: MultiTaskRobBERT.from_pretrained()
        )
        model.eval()
        
        # Get model info
        model_info = self.get_model_info(model)
        
        # Get tokenizer
        tokenizer = model.tokenizer
        
        # Measure tokenization speed
        tokenization_speed = self.measure_tokenization_speed(tokenizer, num_samples)
        
        # Measure inference performance
        inference_metrics = self.measure_inference_performance(
            model, tokenizer, num_samples, warmup_samples
        )
        
        return PerformanceMetrics(
            model_name="RobBERT-2023",
            model_loading_time=loading_time,
            tokenization_speed=tokenization_speed,
            inference_speed=inference_metrics['inference_speed'],
            memory_usage_mb=inference_metrics['memory_usage_mb'],
            gpu_memory_usage_mb=inference_metrics['gpu_memory_usage_mb'],
            cpu_utilization_percent=inference_metrics['cpu_utilization_percent'],
            model_size_mb=model_info['model_size_mb'],
            num_parameters=model_info['num_parameters'],
            avg_inference_time_ms=inference_metrics['avg_inference_time_ms'],
            std_inference_time_ms=inference_metrics['std_inference_time_ms'],
            throughput_samples_per_sec=inference_metrics['throughput_samples_per_sec']
        )
    
    def save_results(self, robbert_metrics: PerformanceMetrics):
        """Save benchmark results to file."""
        if not self.output_file:
            return
            
        results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'system_info': {
                'python_version': sys.version,
                'torch_version': torch.__version__,
                'cuda_available': torch.cuda.is_available(),
                'cuda_version': torch.version.cuda if torch.cuda.is_available() else None,
                'gpu_name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
                'cpu_count': psutil.cpu_count(),
                'memory_gb': psutil.virtual_memory().total / (1024**3)
            },
            'robbert_metrics': asdict(robbert_metrics)
        }
        
        with open(self.output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        self.logger.logger.info(f"Results saved to {self.output_file}")
    
    def run_full_benchmark(self, num_samples: int = 100, warmup_samples: int = 10):
        """Run complete benchmark suite."""
        self.logger.logger.info("Starting RobBERT-2023 performance measurement...")
        
        # Benchmark RobBERT
        robbert_metrics = self.benchmark_robbert(num_samples, warmup_samples)
        
        # Save results
        self.save_results(robbert_metrics)
        
        self.logger.logger.info("Performance measurement completed!")
        
        return robbert_metrics


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Performance baseline measurement for RobBERT-2023")
    parser.add_argument('--output', '-o', type=str, default='performance_baseline_results.json',
                       help='Output file for results (default: performance_baseline_results.json)')
    parser.add_argument('--samples', '-s', type=int, default=100,
                       help='Number of samples for benchmarking (default: 100)')
    parser.add_argument('--warmup', '-w', type=int, default=10,
                       help='Number of warmup samples (default: 10)')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Reduce output verbosity')
    
    args = parser.parse_args()
    
    # Create benchmark instance
    benchmark = PerformanceBenchmark(
        output_file=args.output,
        verbose=not args.quiet
    )
    
    try:
        # Run benchmark
        results = benchmark.run_full_benchmark(args.samples, args.warmup)
        
        print("\n" + "="*60)
        print("ROBBERT-2023 PERFORMANCE SUMMARY")
        print("="*60)
        print(f"Model Loading Time: {results.model_loading_time:.2f}s")
        print(f"Tokenization Speed: {results.tokenization_speed:.1f} tokens/sec")
        print(f"Inference Speed: {results.inference_speed:.1f} tokens/sec")
        print(f"Memory Usage: {results.memory_usage_mb:.1f} MB")
        print(f"GPU Memory Usage: {results.gpu_memory_usage_mb:.1f} MB")
        print(f"CPU Utilization: {results.cpu_utilization_percent:.1f}%")
        print(f"Model Size: {results.model_size_mb:.1f} MB")
        print(f"Parameters: {results.num_parameters:,}")
        print(f"Avg Inference Time: {results.avg_inference_time_ms:.1f}ms")
        print(f"Throughput: {results.throughput_samples_per_sec:.1f} samples/sec")
        print()
        print(f"Results saved to: {args.output}")
        
    except KeyboardInterrupt:
        print("\nBenchmark interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Benchmark failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()