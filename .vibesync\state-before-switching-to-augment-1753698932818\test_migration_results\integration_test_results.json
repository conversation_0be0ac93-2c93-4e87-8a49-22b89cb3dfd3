{"data_migration_tests": [{"test_name": "tokenized_jsonl_to_sentence_entities", "description": "Convert tokenized JSONL to sentence+entities format", "status": "PASSED", "error": null, "stats": {"total_lines": 2, "converted_examples": 2, "skipped_lines": 0, "errors": 0, "entity_count": 2, "format_detected": "tokenized"}, "warnings": []}, {"test_name": "conll_format_to_sentence_entities", "description": "Convert CoNLL format to sentence+entities format", "status": "PASSED", "error": null, "stats": {"total_lines": 1, "converted_examples": 1, "skipped_lines": 0, "errors": 0, "entity_count": 1, "format_detected": "conll"}, "warnings": []}, {"test_name": "sentence_entities_validation", "description": "Validate existing sentence+entities format", "status": "PASSED", "error": null, "stats": {"total_lines": 1, "converted_examples": 1, "skipped_lines": 0, "errors": 0, "entity_count": 1, "format_detected": "sentence_entities"}, "warnings": []}], "checkpoint_compatibility_tests": [{"test_name": "hf_trainer_checkpoint", "description": "HF Trainer compatible checkpoint", "status": "FAILED", "error": "Can't pickle <class 'unittest.mock.MagicMock'>: it's not the same object as unittest.mock.MagicMock", "compatibility_report": null, "migration_report": null, "warnings": []}, {"test_name": "legacy_checkpoint", "description": "Legacy format checkpoint", "status": "PASSED", "error": null, "compatibility_report": {"checkpoint_path": "test_migration_results\\checkpoint_tests\\legacy_checkpoint", "target_model": "DTAI-KULeuven/robbert-2023-dutch-base", "is_compatible": true, "issues": [], "warnings": ["Label configuration may not be NER-compatible (num_labels=3)"], "recommendations": ["Checkpoint appears compatible", "Consider retraining with current Hugging Face Trainer for optimal compatibility"], "files_found": {"config.json": true, "pytorch_model.bin": true, "tokenizer.json": false, "tokenizer_config.json": false, "vocab.json": false, "merges.txt": false}, "model_info": {"config": {"model_type": "roberta", "num_labels": 3, "hidden_size": 768}, "num_labels": 3, "state_dict_keys": ["roberta.embeddings.word_embeddings.weight", "classifier.weight", "classifier.bias"], "total_parameters": 770307}}, "migration_report": {"source_path": "test_migration_results\\checkpoint_tests\\legacy_checkpoint", "target_path": "test_migration_results\\checkpoint_tests\\legacy_checkpoint_migrated", "target_format": "hf_trainer", "files_migrated": ["config.json", "pytorch_model.bin"], "files_created": ["training_args.bin", "README.md"], "warnings": [], "success": true}, "warnings": []}, {"test_name": "incompatible_checkpoint", "description": "Incompatible checkpoint", "status": "FAILED", "error": "Compatibility mismatch: expected False, got True", "compatibility_report": {"checkpoint_path": "test_migration_results\\checkpoint_tests\\incompatible_checkpoint", "target_model": "DTAI-KULeuven/robbert-2023-dutch-base", "is_compatible": true, "issues": [], "warnings": ["Label configuration may not be NER-compatible (num_labels=5)", "Non-standard hidden size: 512 (expected 768 for RobBERT)"], "recommendations": ["Checkpoint appears compatible", "Consider retraining with current Hugging Face Trainer for optimal compatibility"], "files_found": {"config.json": true, "pytorch_model.bin": true, "tokenizer.json": false, "tokenizer_config.json": false, "vocab.json": false, "merges.txt": false}, "model_info": {"config": {"model_type": "bert", "num_labels": 5, "hidden_size": 512}, "num_labels": 5, "state_dict_keys": ["roberta.embeddings.word_embeddings.weight", "classifier.weight", "classifier.bias"], "total_parameters": 770307}}, "migration_report": null, "warnings": []}], "config_migration_tests": [{"test_name": "legacy_config_migration", "description": "Migrate legacy YAML config to HF Trainer format", "status": "PASSED", "error": null, "migration_report": {"source_path": "test_migration_results\\config_migration_tests\\legacy_config.yaml", "target_path": "test_migration_results\\config_migration_tests\\hf_trainer_config.yaml", "target_format": "hf_trainer", "success": true, "warnings": ["Loss weights configuration needs manual review - HF Trainer uses different loss weighting approach", "Multi-head configuration detected - HF Trainer integration focuses on single-head NER training"], "converted_parameters": {"training.epochs": {"source_value": 5, "target_path": "hf_training.epochs"}, "training.batch_size": {"source_value": 16, "target_path": "hf_training.batch_size"}, "training.learning_rate": {"source_value": 2e-05, "target_path": "hf_training.learning_rate"}, "training.weight_decay": {"source_value": 0.01, "target_path": "hf_training.weight_decay"}, "training.warmup_steps": {"source_value": 1000, "target_path": "hf_training.warmup_steps"}, "model.model_name": {"source_value": "DTAI-KULeuven/robbert-2023-dutch-base", "target_path": "hf_training.model_name"}, "wandb.project": {"source_value": "test-project", "target_path": "hf_training.wandb_project"}, "wandb.entity": {"source_value": "test-entity", "target_path": "hf_training.wandb_entity"}, "evaluation.eval_steps": {"source_value": 200, "target_path": "hf_training.eval_steps"}, "evaluation.logging_steps": {"source_value": 100, "target_path": "hf_training.logging_steps"}, "evaluation.save_steps": {"source_value": 1000, "target_path": "hf_training.save_steps"}}, "unmapped_parameters": [{"parameter": "model.heads", "value": ["ner"], "reason": "No mapping defined"}, {"parameter": "model.max_length", "value": 512, "reason": "No mapping defined"}, {"parameter": "training.loss_weights", "value": {"ner": 1.0}, "reason": "No mapping defined"}, {"parameter": "training.max_grad_norm", "value": 1.0, "reason": "No mapping defined"}]}, "warnings": ["Loss weights configuration needs manual review - HF Trainer uses different loss weighting approach", "Multi-head configuration detected - HF Trainer integration focuses on single-head NER training"]}], "api_compatibility_tests": [{"test_name": "response_adaptation", "description": "Test API response adaptation for backward compatibility", "status": "PASSED", "error": null, "warnings": []}, {"test_name": "request_validation", "description": "Test API request validation and legacy task mapping", "status": "PASSED", "error": null, "warnings": []}, {"test_name": "checkpoint_compatibility_check", "description": "Test checkpoint compatibility checking function", "status": "PASSED", "error": null, "warnings": []}], "integration_tests": [{"test_name": "end_to_end_workflow", "description": "Complete migration workflow from legacy to HF Trainer", "status": "PASSED", "error": null, "workflow_steps": ["✓ Created legacy data file", "✓ Migrated data format", "✓ Migrated configuration", "✓ Validated checkpoint compatibility", "✓ Validated API compatibility"], "warnings": ["Mock checkpoint not compatible"]}], "summary": {"total_tests": 11, "passed_tests": 9, "failed_tests": 2, "warnings": 3}}