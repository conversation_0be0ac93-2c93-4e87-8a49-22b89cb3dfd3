{"migration_timestamp": "1753696186.5090127", "summary": {"data_files": {"processed": 5, "converted": 5, "failed": 0}, "config_files": {"processed": 45, "converted": 35, "failed": 10}, "checkpoints": {"processed": 0, "migrated": 0, "failed": 0}, "api_compatibility": {"tested": 0, "compatible": 0}}, "detailed_results": {"data_migration": {"files_processed": 5, "files_converted": 5, "files_failed": 0, "conversion_stats": [{"source_file": "data_sets\\legacy_test.jsonl", "target_file": "migrated_output\\data\\data_sets\\legacy_test.json", "stats": {"total_lines": 3, "converted_examples": 3, "skipped_lines": 0, "errors": 0, "entity_count": 3, "format_detected": "tokenized"}}, {"source_file": "test_migration_results\\data_migration_tests\\conll_format_to_sentence_entities\\input.jsonl", "target_file": "migrated_output\\data\\test_migration_results\\data_migration_tests\\conll_format_to_sentence_entities\\input.json", "stats": {"total_lines": 1, "converted_examples": 1, "skipped_lines": 0, "errors": 0, "entity_count": 1, "format_detected": "conll"}}, {"source_file": "test_migration_results\\data_migration_tests\\sentence_entities_validation\\input.jsonl", "target_file": "migrated_output\\data\\test_migration_results\\data_migration_tests\\sentence_entities_validation\\input.json", "stats": {"total_lines": 1, "converted_examples": 1, "skipped_lines": 0, "errors": 0, "entity_count": 1, "format_detected": "sentence_entities"}}, {"source_file": "test_migration_results\\data_migration_tests\\tokenized_jsonl_to_sentence_entities\\input.jsonl", "target_file": "migrated_output\\data\\test_migration_results\\data_migration_tests\\tokenized_jsonl_to_sentence_entities\\input.json", "stats": {"total_lines": 2, "converted_examples": 2, "skipped_lines": 0, "errors": 0, "entity_count": 2, "format_detected": "tokenized"}}, {"source_file": "test_migration_results\\integration_test\\legacy_data.jsonl", "target_file": "migrated_output\\data\\test_migration_results\\integration_test\\legacy_data.json", "stats": {"total_lines": 2, "converted_examples": 2, "skipped_lines": 0, "errors": 0, "entity_count": 2, "format_detected": "tokenized"}}], "errors": []}, "config_migration": {"files_processed": 45, "files_converted": 35, "files_failed": 10, "migration_reports": [{"source_path": ".vibesync\\state-before-model-change-1753430005361\\src\\config\\default.yaml", "target_path": "migrated_output\\configs\\hf_.vibesync\\state-before-model-change-1753430005361\\src\\config\\default.yaml", "target_format": "hf_trainer", "success": true, "warnings": ["Loss weights configuration needs manual review - HF Trainer uses different loss weighting approach"], "converted_parameters": {"training.epochs": {"source_value": 3, "target_path": "hf_training.epochs"}, "training.batch_size": {"source_value": 8, "target_path": "hf_training.batch_size"}, "training.learning_rate": {"source_value": 2e-05, "target_path": "hf_training.learning_rate"}, "training.weight_decay": {"source_value": 0.01, "target_path": "hf_training.weight_decay"}, "training.warmup_steps": {"source_value": 500, "target_path": "hf_training.warmup_steps"}}, "unmapped_parameters": [{"parameter": "model.encoder_weights", "value": "${BERTJE_WEIGHTS_DIR:-src/models/weights/bertje_conll}", "reason": "No mapping defined"}, {"parameter": "model.max_length", "value": 512, "reason": "No mapping defined"}, {"parameter": "model.num_labels", "value": {"ner": 9, "compliance": 2, "label": 15, "reason": 10, "topic": 8}, "reason": "No mapping defined"}, {"parameter": "training.gradient_accumulation_steps", "value": 1, "reason": "No mapping defined"}, {"parameter": "training.max_grad_norm", "value": 1.0, "reason": "No mapping defined"}, {"parameter": "training.loss_weights", "value": {"compliance": 2.0, "label": 1.0, "reason": 1.0, "topic": 1.0, "ner": 0.5}, "reason": "No mapping defined"}, {"parameter": "inference.batch_size", "value": 16, "reason": "No mapping defined"}, {"parameter": "inference.thresholds", "value": {"compliance": 0.5, "topic": 0.5, "label": 0.4, "reason": 0.45}, "reason": "No mapping defined"}, {"parameter": "inference.api", "value": {"host": "0.0.0.0", "port": 8000, "workers": 1}, "reason": "No mapping defined"}, {"parameter": "logging.level", "value": "INFO", "reason": "No mapping defined"}, {"parameter": "logging.format", "value": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "reason": "No mapping defined"}]}, {"source_path": ".vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\datasets\\utils\\resources\\readme_structure.yaml", "target_path": "migrated_output\\configs\\hf_.vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\datasets\\utils\\resources\\readme_structure.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": ".vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_file_listing.yaml", "target_path": "migrated_output\\configs\\hf_.vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_file_listing.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": ".vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_mkdir.yaml", "target_path": "migrated_output\\configs\\hf_.vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_mkdir.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": ".vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_read_pyarrow_non_partitioned.yaml", "target_path": "migrated_output\\configs\\hf_.vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_read_pyarrow_non_partitioned.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": ".vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_read_range.yaml", "target_path": "migrated_output\\configs\\hf_.vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_read_range.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": ".vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_read_range_chunked.yaml", "target_path": "migrated_output\\configs\\hf_.vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_read_range_chunked.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": ".vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_write_and_read.yaml", "target_path": "migrated_output\\configs\\hf_.vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_write_and_read.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": ".vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_write_pyarrow_non_partitioned.yaml", "target_path": "migrated_output\\configs\\hf_.vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_write_pyarrow_non_partitioned.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": ".vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\torch\\_export\\serde\\schema.yaml", "target_path": "migrated_output\\configs\\hf_.vibesync\\state-before-model-change-1753430005361\\venv\\Lib\\site-packages\\torch\\_export\\serde\\schema.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "Argument.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "Argument.fields", "value": {"as_none": {"type": "Tuple[()]"}, "as_tensor": {"type": "TensorArgument"}, "as_tensors": {"type": "List[TensorArgument]"}, "as_int": {"type": "int"}, "as_ints": {"type": "List[int]"}, "as_float": {"type": "float"}, "as_floats": {"type": "List[float]"}, "as_string": {"type": "str"}, "as_strings": {"type": "List[str]"}, "as_sym_int": {"type": "SymIntArgument"}, "as_sym_ints": {"type": "List[SymIntArgument]"}, "as_scalar_type": {"type": "ScalarType"}, "as_memory_format": {"type": "MemoryFormat"}, "as_layout": {"type": "Layout"}, "as_device": {"type": "<PERSON><PERSON>"}, "as_bool": {"type": "bool"}, "as_bools": {"type": "List[bool]"}, "as_sym_bool": {"type": "SymBoolArgument"}, "as_sym_bools": {"type": "List[SymBoolArgument]"}, "as_graph": {"type": "GraphArgument"}, "as_optional_tensors": {"type": "List[OptionalTensorArgument]"}, "as_custom_obj": {"type": "CustomObjArgument"}, "as_operator": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "BufferMutationSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "BufferMutationSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "buffer_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "ConstantInputSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "ConstantInputSpec.fields", "value": {"name": {"type": "str"}, "value": {"type": "ConstantV<PERSON>ue"}}, "reason": "No mapping defined"}, {"parameter": "ConstantValue.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "ConstantValue.fields", "value": {"as_none": {"type": "Tuple[()]"}, "as_int": {"type": "int"}, "as_float": {"type": "float"}, "as_string": {"type": "str"}, "as_bool": {"type": "bool"}}, "reason": "No mapping defined"}, {"parameter": "CustomObjArgument.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "CustomObjArgument.fields", "value": {"name": {"type": "str"}, "class_fqn": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "Device.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "Device.fields", "value": {"type": {"type": "str"}, "index": {"type": "Optional[int]", "default": "None"}}, "reason": "No mapping defined"}, {"parameter": "ExportedProgram.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "ExportedProgram.fields", "value": {"graph_module": {"type": "GraphModule"}, "opset_version": {"type": "Dict[str, int]"}, "range_constraints": {"type": "Dict[str, RangeConstraint]"}, "schema_version": {"type": "SchemaVersion"}, "verifiers": {"type": "List[str]", "default": "[]"}, "torch_version": {"type": "str", "default": "<=2.4"}}, "reason": "No mapping defined"}, {"parameter": "GradientToParameterSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "GradientToParameterSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "parameter_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "GradientToUserInputSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "GradientToUserInputSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "user_input_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "Graph.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "Graph.fields", "value": {"inputs": {"type": "List[Argument]"}, "outputs": {"type": "List[Argument]"}, "nodes": {"type": "List[Node]"}, "tensor_values": {"type": "Dict[str, TensorMeta]"}, "sym_int_values": {"type": "Dict[str, SymInt]"}, "sym_bool_values": {"type": "Dict[str, SymBool]"}, "is_single_tensor_return": {"type": "bool", "default": "False"}, "custom_obj_values": {"type": "Dict[str, CustomObjArgument]", "default": "{}"}}, "reason": "No mapping defined"}, {"parameter": "GraphArgument.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "GraphArgument.fields", "value": {"name": {"type": "str"}, "graph": {"type": "Graph"}}, "reason": "No mapping defined"}, {"parameter": "GraphModule.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "GraphModule.fields", "value": {"graph": {"type": "Graph"}, "signature": {"type": "GraphSignature"}, "module_call_graph": {"type": "List[ModuleCallEntry]"}, "metadata": {"type": "Dict[str, str]", "default": "{}"}}, "reason": "No mapping defined"}, {"parameter": "GraphSignature.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "GraphSignature.fields", "value": {"input_specs": {"type": "List[InputSpec]"}, "output_specs": {"type": "List[OutputSpec]"}}, "reason": "No mapping defined"}, {"parameter": "InputSpec.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "InputSpec.fields", "value": {"user_input": {"type": "UserInputSpec"}, "parameter": {"type": "InputToParameterSpec"}, "buffer": {"type": "InputToBufferSpec"}, "tensor_constant": {"type": "InputToTensorConstantSpec"}, "custom_obj": {"type": "InputToCustomObjSpec"}, "token": {"type": "InputTokenSpec"}, "constant_input": {"type": "ConstantInputSpec"}}, "reason": "No mapping defined"}, {"parameter": "InputToBufferSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "InputToBufferSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "buffer_name": {"type": "str"}, "persistent": {"type": "bool"}}, "reason": "No mapping defined"}, {"parameter": "InputToCustomObjSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "InputToCustomObjSpec.fields", "value": {"arg": {"type": "CustomObjArgument"}, "custom_obj_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "InputToParameterSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "InputToParameterSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "parameter_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "InputToTensorConstantSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "InputToTensorConstantSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "tensor_constant_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "InputTokenSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "InputTokenSpec.fields", "value": {"arg": {"type": "TokenArgument"}}, "reason": "No mapping defined"}, {"parameter": "Layout.kind", "value": "enum", "reason": "No mapping defined"}, {"parameter": "Layout.fields", "value": {"Unknown": 0, "SparseCoo": 1, "SparseCsr": 2, "SparseCsc": 3, "SparseBsr": 4, "SparseBsc": 5, "_mkldnn": 6, "Strided": 7}, "reason": "No mapping defined"}, {"parameter": "LossOutputSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "LossOutputSpec.fields", "value": {"arg": {"type": "TensorArgument"}}, "reason": "No mapping defined"}, {"parameter": "MemoryFormat.kind", "value": "enum", "reason": "No mapping defined"}, {"parameter": "MemoryFormat.fields", "value": {"Unknown": 0, "ContiguousFormat": 1, "ChannelsLast": 2, "ChannelsLast3d": 3, "PreserveFormat": 4}, "reason": "No mapping defined"}, {"parameter": "ModuleCallEntry.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "ModuleCallEntry.fields", "value": {"fqn": {"type": "str"}, "signature": {"type": "Optional[ModuleCallSignature]", "default": "None"}}, "reason": "No mapping defined"}, {"parameter": "ModuleCallSignature.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "ModuleCallSignature.fields", "value": {"inputs": {"type": "List[Argument]"}, "outputs": {"type": "List[Argument]"}, "in_spec": {"type": "str"}, "out_spec": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "NamedArgument.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "NamedArgument.fields", "value": {"name": {"type": "str"}, "arg": {"type": "Argument"}}, "reason": "No mapping defined"}, {"parameter": "Node.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "Node.fields", "value": {"target": {"type": "str"}, "inputs": {"type": "List[NamedArgument]"}, "outputs": {"type": "List[Argument]"}, "metadata": {"type": "Dict[str, str]"}}, "reason": "No mapping defined"}, {"parameter": "OptionalTensorArgument.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "OptionalTensorArgument.fields", "value": {"as_tensor": {"type": "TensorArgument"}, "as_none": {"type": "Tuple[()]"}}, "reason": "No mapping defined"}, {"parameter": "OutputSpec.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "OutputSpec.fields", "value": {"user_output": {"type": "UserOutputSpec"}, "loss_output": {"type": "LossOutputSpec"}, "buffer_mutation": {"type": "BufferMutationSpec"}, "gradient_to_parameter": {"type": "GradientToParameterSpec"}, "gradient_to_user_input": {"type": "GradientToUserInputSpec"}, "user_input_mutation": {"type": "UserInputMutationSpec"}, "token": {"type": "OutputTokenSpec"}}, "reason": "No mapping defined"}, {"parameter": "OutputTokenSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "OutputTokenSpec.fields", "value": {"arg": {"type": "TokenArgument"}}, "reason": "No mapping defined"}, {"parameter": "RangeConstraint.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "RangeConstraint.fields", "value": {"min_val": {"type": "int"}, "max_val": {"type": "int"}}, "reason": "No mapping defined"}, {"parameter": "ScalarType.kind", "value": "enum", "reason": "No mapping defined"}, {"parameter": "ScalarType.fields", "value": {"UNKNOWN": 0, "BYTE": 1, "CHAR": 2, "SHORT": 3, "INT": 4, "LONG": 5, "HALF": 6, "FLOAT": 7, "DOUBLE": 8, "COMPLEXHALF": 9, "COMPLEXFLOAT": 10, "COMPLEXDOUBLE": 11, "BOOL": 12, "BFLOAT16": 13}, "reason": "No mapping defined"}, {"parameter": "SchemaVersion.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "SchemaVersion.fields", "value": {"major": {"type": "int"}, "minor": {"type": "int"}}, "reason": "No mapping defined"}, {"parameter": "SymBool.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "SymBool.fields", "value": {"as_expr": {"type": "SymExpr"}, "as_bool": {"type": "bool"}}, "reason": "No mapping defined"}, {"parameter": "SymBoolArgument.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "SymBoolArgument.fields", "value": {"as_name": {"type": "str"}, "as_bool": {"type": "bool"}}, "reason": "No mapping defined"}, {"parameter": "SymExpr.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "SymExpr.fields", "value": {"expr_str": {"type": "str"}, "hint": {"type": "Optional[SymExprHint]", "default": "None"}}, "reason": "No mapping defined"}, {"parameter": "SymExprHint.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "SymExprHint.fields", "value": {"as_int": {"type": "int"}, "as_float": {"type": "float"}, "as_bool": {"type": "bool"}}, "reason": "No mapping defined"}, {"parameter": "SymInt.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "SymInt.fields", "value": {"as_expr": {"type": "SymExpr"}, "as_int": {"type": "int"}}, "reason": "No mapping defined"}, {"parameter": "SymIntArgument.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "SymIntArgument.fields", "value": {"as_name": {"type": "str"}, "as_int": {"type": "int"}}, "reason": "No mapping defined"}, {"parameter": "TensorArgument.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "TensorArgument.fields", "value": {"name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "TensorMeta.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "TensorMeta.fields", "value": {"dtype": {"type": "ScalarType"}, "sizes": {"type": "List[SymInt]"}, "requires_grad": {"type": "bool"}, "device": {"type": "<PERSON><PERSON>"}, "strides": {"type": "List[SymInt]"}, "storage_offset": {"type": "SymInt"}, "layout": {"type": "Layout"}}, "reason": "No mapping defined"}, {"parameter": "TokenArgument.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "TokenArgument.fields", "value": {"name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "UserInputMutationSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "UserInputMutationSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "user_input_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "UserInputSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "UserInputSpec.fields", "value": {"arg": {"type": "Argument"}}, "reason": "No mapping defined"}, {"parameter": "UserOutputSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "UserOutputSpec.fields", "value": {"arg": {"type": "Argument"}}, "reason": "No mapping defined"}]}, {"source_path": "examples\\hf_trainer_example.yaml", "target_path": "migrated_output\\configs\\hf_examples\\hf_trainer_example.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "hf_training.model_name", "value": "DTAI-KULeuven/robbert-2023-dutch-base", "reason": "No mapping defined"}, {"parameter": "hf_training.epochs", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.learning_rate", "value": "5e-5", "reason": "No mapping defined"}, {"parameter": "hf_training.weight_decay", "value": 0.01, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_steps", "value": 500, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_steps", "value": 100, "reason": "No mapping defined"}, {"parameter": "hf_training.logging_steps", "value": 50, "reason": "No mapping defined"}, {"parameter": "hf_training.save_steps", "value": 500, "reason": "No mapping defined"}, {"parameter": "hf_training.evaluation_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_patience", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold", "value": 0.001, "reason": "No mapping defined"}, {"parameter": "hf_training.load_best_model_at_end", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.metric_for_best_model", "value": "eval_f1", "reason": "No mapping defined"}, {"parameter": "hf_training.greater_is_better", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_project", "value": "robbert2023-ner", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_entity", "value": "your-wandb-entity", "reason": "No mapping defined"}, {"parameter": "hf_training.run_name", "value": "hf-trainer-example", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_tags", "value": ["robbert-2023", "dutch-nlp", "ner", "hf-trainer"], "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_notes", "value": "Example training run with Hugging Face Trainer", "reason": "No mapping defined"}, {"parameter": "hf_training.report_to", "value": "wandb", "reason": "No mapping defined"}, {"parameter": "hf_training.use_gpu", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.fp16", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_num_workers", "value": 2, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_pin_memory", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.save_total_limit", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.output_dir", "value": "checkpoints", "reason": "No mapping defined"}, {"parameter": "hf_training.max_length", "value": 512, "reason": "No mapping defined"}, {"parameter": "hf_training.truncation", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.padding", "value": "max_length", "reason": "No mapping defined"}, {"parameter": "hf_training.seed", "value": 42, "reason": "No mapping defined"}, {"parameter": "hf_training.run_version", "value": "v1.0", "reason": "No mapping defined"}, {"parameter": "hf_training.experiment_name", "value": "hf-trainer-example", "reason": "No mapping defined"}, {"parameter": "hf_training.save_predictions", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.generate_model_card", "value": true, "reason": "No mapping defined"}]}, {"source_path": "examples\\configs\\default_training.yaml", "target_path": "migrated_output\\configs\\hf_examples\\configs\\default_training.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "hf_training.model_name", "value": "DTAI-KULeuven/robbert-2023-dutch-base", "reason": "No mapping defined"}, {"parameter": "hf_training.epochs", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.learning_rate", "value": "5e-5", "reason": "No mapping defined"}, {"parameter": "hf_training.weight_decay", "value": 0.01, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_steps", "value": 500, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_steps", "value": 100, "reason": "No mapping defined"}, {"parameter": "hf_training.logging_steps", "value": 50, "reason": "No mapping defined"}, {"parameter": "hf_training.save_steps", "value": 500, "reason": "No mapping defined"}, {"parameter": "hf_training.evaluation_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_patience", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold", "value": 0.001, "reason": "No mapping defined"}, {"parameter": "hf_training.load_best_model_at_end", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.metric_for_best_model", "value": "eval_f1", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_project", "value": "robbert2023-ner", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_entity", "value": "slippydongle", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_tags", "value": ["robbert-2023", "dutch-nlp", "ner"], "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_notes", "value": "Default Hugging Face Trainer configuration for RobBERT-2023 NER", "reason": "No mapping defined"}, {"parameter": "hf_training.use_gpu", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.fp16", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_num_workers", "value": 2, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_pin_memory", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.save_total_limit", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.output_dir", "value": "checkpoints", "reason": "No mapping defined"}, {"parameter": "hf_training.max_length", "value": 512, "reason": "No mapping defined"}, {"parameter": "hf_training.seed", "value": 42, "reason": "No mapping defined"}, {"parameter": "hf_training.run_version", "value": "v1.0", "reason": "No mapping defined"}]}, {"source_path": "examples\\configs\\production_training.yaml", "target_path": "migrated_output\\configs\\hf_examples\\configs\\production_training.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "hf_training.model_name", "value": "DTAI-KULeuven/robbert-2023-dutch-base", "reason": "No mapping defined"}, {"parameter": "hf_training.epochs", "value": 5, "reason": "No mapping defined"}, {"parameter": "hf_training.batch_size", "value": 16, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_batch_size", "value": 16, "reason": "No mapping defined"}, {"parameter": "hf_training.learning_rate", "value": "2e-5", "reason": "No mapping defined"}, {"parameter": "hf_training.weight_decay", "value": 0.01, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_steps", "value": 1000, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_ratio", "value": 0.1, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_steps", "value": 200, "reason": "No mapping defined"}, {"parameter": "hf_training.logging_steps", "value": 100, "reason": "No mapping defined"}, {"parameter": "hf_training.save_steps", "value": 1000, "reason": "No mapping defined"}, {"parameter": "hf_training.evaluation_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_patience", "value": 5, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold", "value": 0.0001, "reason": "No mapping defined"}, {"parameter": "hf_training.load_best_model_at_end", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.metric_for_best_model", "value": "eval_f1", "reason": "No mapping defined"}, {"parameter": "hf_training.use_class_weights", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.class_weight_method", "value": "balanced", "reason": "No mapping defined"}, {"parameter": "hf_training.per_head_class_weights", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.save_class_weights", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_project", "value": "robbert2023-ner-production", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_entity", "value": "slippydongle", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_tags", "value": ["robbert-2023", "dutch-nlp", "ner", "production"], "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_notes", "value": "Production Hugging Face Trainer configuration for RobBERT-2023 NER", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_log_confusion_matrix", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_log_per_class_metrics", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_log_model_artifacts", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_log_evaluation_tables", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.use_gpu", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.fp16", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.gradient_accumulation_steps", "value": 2, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_num_workers", "value": 4, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_pin_memory", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.gradient_checkpointing", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.lr_scheduler_type", "value": "cosine", "reason": "No mapping defined"}, {"parameter": "hf_training.cosine_schedule_num_cycles", "value": 0.5, "reason": "No mapping defined"}, {"parameter": "hf_training.max_grad_norm", "value": 1.0, "reason": "No mapping defined"}, {"parameter": "hf_training.label_smoothing_factor", "value": 0.1, "reason": "No mapping defined"}, {"parameter": "hf_training.save_total_limit", "value": 5, "reason": "No mapping defined"}, {"parameter": "hf_training.output_dir", "value": "production_checkpoints", "reason": "No mapping defined"}, {"parameter": "hf_training.save_predictions", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.generate_model_card", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.max_length", "value": 512, "reason": "No mapping defined"}, {"parameter": "hf_training.seed", "value": 42, "reason": "No mapping defined"}, {"parameter": "hf_training.run_version", "value": "v2.0", "reason": "No mapping defined"}, {"parameter": "hf_training.experiment_name", "value": "production_training", "reason": "No mapping defined"}]}, {"source_path": "examples\\configs\\test_training.yaml", "target_path": "migrated_output\\configs\\hf_examples\\configs\\test_training.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "hf_training.model_name", "value": "DTAI-KULeuven/robbert-2023-dutch-base", "reason": "No mapping defined"}, {"parameter": "hf_training.test_mode", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.test_epochs", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.test_sample_limit", "value": 50, "reason": "No mapping defined"}, {"parameter": "hf_training.test_disable_wandb", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.epochs", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.batch_size", "value": 4, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_batch_size", "value": 4, "reason": "No mapping defined"}, {"parameter": "hf_training.learning_rate", "value": "5e-5", "reason": "No mapping defined"}, {"parameter": "hf_training.weight_decay", "value": 0.01, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_steps", "value": 10, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_steps", "value": 10, "reason": "No mapping defined"}, {"parameter": "hf_training.logging_steps", "value": 5, "reason": "No mapping defined"}, {"parameter": "hf_training.save_steps", "value": 20, "reason": "No mapping defined"}, {"parameter": "hf_training.evaluation_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_patience", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold", "value": 0.001, "reason": "No mapping defined"}, {"parameter": "hf_training.load_best_model_at_end", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.metric_for_best_model", "value": "eval_f1", "reason": "No mapping defined"}, {"parameter": "hf_training.use_gpu", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.fp16", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_num_workers", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_pin_memory", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.save_total_limit", "value": 2, "reason": "No mapping defined"}, {"parameter": "hf_training.output_dir", "value": "test_checkpoints", "reason": "No mapping defined"}, {"parameter": "hf_training.max_length", "value": 256, "reason": "No mapping defined"}, {"parameter": "hf_training.seed", "value": 42, "reason": "No mapping defined"}, {"parameter": "hf_training.run_version", "value": "test", "reason": "No mapping defined"}]}, {"source_path": "examples\\output\\dutch_ner\\dutch_ner_config.yaml", "target_path": "migrated_output\\configs\\hf_examples\\output\\dutch_ner\\dutch_ner_config.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "hf_training.adam_beta1", "value": 0.9, "reason": "No mapping defined"}, {"parameter": "hf_training.adam_beta2", "value": 0.999, "reason": "No mapping defined"}, {"parameter": "hf_training.adam_epsilon", "value": 1e-08, "reason": "No mapping defined"}, {"parameter": "hf_training.batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.bf16", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.class_weight_entity_types", "value": ["PER", "LOC", "ORG"], "reason": "No mapping defined"}, {"parameter": "hf_training.class_weight_method", "value": "balanced", "reason": "No mapping defined"}, {"parameter": "hf_training.class_weights", "value": {"B-LOC": 5.52, "B-ORG": 13.8, "B-PER": 9.2, "I-LOC": 1.0, "I-ORG": 1.0, "I-PER": 5.52, "O": 0.22439024390243903}, "reason": "No mapping defined"}, {"parameter": "hf_training.cosine_schedule_num_cycles", "value": 0.5, "reason": "No mapping defined"}, {"parameter": "hf_training.data_seed", "value": 42, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_num_workers", "value": 2, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_pin_memory", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.ddp_bucket_cap_mb", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.ddp_find_unused_parameters", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_min_delta", "value": 0.0001, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_monitor_metrics", "value": ["eval_f1", "eval_loss"], "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_patience", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_restore_best_weights", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold", "value": 0.001, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold_type", "value": "absolute", "reason": "No mapping defined"}, {"parameter": "hf_training.end_learning_rate", "value": 0.0, "reason": "No mapping defined"}, {"parameter": "hf_training.epochs", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_steps", "value": 100, "reason": "No mapping defined"}, {"parameter": "hf_training.evaluation_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.experiment_name", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.fp16", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.generate_model_card", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.gradient_accumulation_steps", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.gradient_checkpointing", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.greater_is_better", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.hub_model_id", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.hub_strategy", "value": "every_save", "reason": "No mapping defined"}, {"parameter": "hf_training.ignore_data_skip", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.label_smoothing_factor", "value": 0.0, "reason": "No mapping defined"}, {"parameter": "hf_training.learning_rate", "value": 2e-05, "reason": "No mapping defined"}, {"parameter": "hf_training.load_best_model_at_end", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.logging_steps", "value": 50, "reason": "No mapping defined"}, {"parameter": "hf_training.logging_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.lr_scheduler_kwargs", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.lr_scheduler_type", "value": "linear", "reason": "No mapping defined"}, {"parameter": "hf_training.max_grad_norm", "value": 1.0, "reason": "No mapping defined"}, {"parameter": "hf_training.max_length", "value": 512, "reason": "No mapping defined"}, {"parameter": "hf_training.metric_for_best_model", "value": "eval_f1", "reason": "No mapping defined"}, {"parameter": "hf_training.model_name", "value": "DTAI-KULeuven/robbert-2023-dutch-base", "reason": "No mapping defined"}, {"parameter": "hf_training.optim", "value": "adamw_torch", "reason": "No mapping defined"}, {"parameter": "hf_training.output_dir", "value": "checkpoints/dutch_ner_class_weights", "reason": "No mapping defined"}, {"parameter": "hf_training.padding", "value": "max_length", "reason": "No mapping defined"}, {"parameter": "hf_training.per_head_class_weights", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.polynomial_decay_power", "value": 1.0, "reason": "No mapping defined"}, {"parameter": "hf_training.prediction_loss_only", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.push_to_hub", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.remove_unused_columns", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.report_to", "value": "wandb", "reason": "No mapping defined"}, {"parameter": "hf_training.resume_from_checkpoint", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.run_name", "value": "imbalanced-dutch-ner", "reason": "No mapping defined"}, {"parameter": "hf_training.run_version", "value": "v1.0", "reason": "No mapping defined"}, {"parameter": "hf_training.save_class_weights", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.save_predictions", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.save_steps", "value": 500, "reason": "No mapping defined"}, {"parameter": "hf_training.save_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.save_total_limit", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.seed", "value": 42, "reason": "No mapping defined"}, {"parameter": "hf_training.test_disable_wandb", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.test_epochs", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.test_mode", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.test_sample_limit", "value": 50, "reason": "No mapping defined"}, {"parameter": "hf_training.truncation", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.use_class_weights", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.use_gpu", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.version_description", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_confusion_matrix_frequency", "value": "epoch", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_confusion_matrix_steps", "value": 500, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_entity", "value": "slippydongle", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_log_confusion_matrix", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_log_evaluation_tables", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_log_model_artifacts", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_log_per_class_metrics", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_notes", "value": "Hugging Face Trainer integration for RobBERT-2023 NER", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_project", "value": "dutch-ner-class-weights", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_tags", "value": ["dutch-nlp", "ner", "class-weights", "robbert-2023"], "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_ratio", "value": 0.0, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_schedule_type", "value": "linear", "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_steps", "value": 500, "reason": "No mapping defined"}, {"parameter": "hf_training.weight_decay", "value": 0.01, "reason": "No mapping defined"}]}, {"source_path": "src\\config\\default.yaml", "target_path": "migrated_output\\configs\\hf_src\\config\\default.yaml", "target_format": "hf_trainer", "success": true, "warnings": ["Loss weights configuration needs manual review - HF Trainer uses different loss weighting approach"], "converted_parameters": {"training.epochs": {"source_value": 3, "target_path": "hf_training.epochs"}, "training.batch_size": {"source_value": 8, "target_path": "hf_training.batch_size"}, "training.learning_rate": {"source_value": 2e-05, "target_path": "hf_training.learning_rate"}, "training.weight_decay": {"source_value": 0.01, "target_path": "hf_training.weight_decay"}, "training.warmup_steps": {"source_value": 500, "target_path": "hf_training.warmup_steps"}, "model.model_name": {"source_value": "${ROBBERT_MODEL_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}", "target_path": "hf_training.model_name"}}, "unmapped_parameters": [{"parameter": "model.tokenizer_name", "value": "${ROBBERT_TOKENIZER_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}", "reason": "No mapping defined"}, {"parameter": "model.checkpoint_dir", "value": "${ROBBERT_CHECKPOINT_DIR:-models/robbert2023-per}", "reason": "No mapping defined"}, {"parameter": "model.max_length", "value": 512, "reason": "No mapping defined"}, {"parameter": "model.tokenizer_type", "value": "byte_level_bpe", "reason": "No mapping defined"}, {"parameter": "model.num_labels", "value": {"ner": 3}, "reason": "No mapping defined"}, {"parameter": "training.gradient_accumulation_steps", "value": 1, "reason": "No mapping defined"}, {"parameter": "training.max_grad_norm", "value": 1.0, "reason": "No mapping defined"}, {"parameter": "training.loss_weights", "value": {"ner": 1.0}, "reason": "No mapping defined"}, {"parameter": "inference.batch_size", "value": 16, "reason": "No mapping defined"}, {"parameter": "inference.thresholds", "value": {"ner": 0.5}, "reason": "No mapping defined"}, {"parameter": "inference.api", "value": {"host": "0.0.0.0", "port": 8000, "workers": 1}, "reason": "No mapping defined"}, {"parameter": "logging.level", "value": "INFO", "reason": "No mapping defined"}, {"parameter": "logging.format", "value": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "reason": "No mapping defined"}]}, {"source_path": "src\\config\\hf_training_class_weights.yaml", "target_path": "migrated_output\\configs\\hf_src\\config\\hf_training_class_weights.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "hf_training.model_name", "value": "${ROBBERT_MODEL_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}", "reason": "No mapping defined"}, {"parameter": "hf_training.epochs", "value": 5, "reason": "No mapping defined"}, {"parameter": "hf_training.batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.learning_rate", "value": 3e-05, "reason": "No mapping defined"}, {"parameter": "hf_training.weight_decay", "value": 0.01, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_steps", "value": 1000, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_ratio", "value": 0.1, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_steps", "value": 200, "reason": "No mapping defined"}, {"parameter": "hf_training.logging_steps", "value": 100, "reason": "No mapping defined"}, {"parameter": "hf_training.save_steps", "value": 1000, "reason": "No mapping defined"}, {"parameter": "hf_training.evaluation_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.logging_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.save_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_patience", "value": 5, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold", "value": 0.0005, "reason": "No mapping defined"}, {"parameter": "hf_training.load_best_model_at_end", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.metric_for_best_model", "value": "eval_f1", "reason": "No mapping defined"}, {"parameter": "hf_training.greater_is_better", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.use_class_weights", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.class_weights", "value": {"O": 0.3, "B-PER": 3.0, "I-PER": 2.5}, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_project", "value": "${WANDB_PROJECT:-robbert2023-ner-balanced}", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_entity", "value": "${WANDB_ENTITY:-slippydongle}", "reason": "No mapping defined"}, {"parameter": "hf_training.run_name", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_tags", "value": ["robbert-2023", "dutch-nlp", "ner", "class-weights", "imbalanced"], "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_notes", "value": "Class-weighted training for imbalanced NER dataset", "reason": "No mapping defined"}, {"parameter": "hf_training.report_to", "value": "wandb", "reason": "No mapping defined"}, {"parameter": "hf_training.use_gpu", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.fp16", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.bf16", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_num_workers", "value": 2, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_pin_memory", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.gradient_accumulation_steps", "value": 2, "reason": "No mapping defined"}, {"parameter": "hf_training.max_grad_norm", "value": 1.0, "reason": "No mapping defined"}, {"parameter": "hf_training.save_total_limit", "value": 5, "reason": "No mapping defined"}, {"parameter": "hf_training.push_to_hub", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.hub_model_id", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.hub_strategy", "value": "every_save", "reason": "No mapping defined"}, {"parameter": "hf_training.lr_scheduler_type", "value": "cosine", "reason": "No mapping defined"}, {"parameter": "hf_training.cosine_schedule_num_cycles", "value": 0.5, "reason": "No mapping defined"}, {"parameter": "hf_training.polynomial_decay_power", "value": 1.0, "reason": "No mapping defined"}, {"parameter": "hf_training.gradient_checkpointing", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.remove_unused_columns", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.label_smoothing_factor", "value": 0.1, "reason": "No mapping defined"}, {"parameter": "hf_training.optim", "value": "adamw_torch", "reason": "No mapping defined"}, {"parameter": "hf_training.adam_beta1", "value": 0.9, "reason": "No mapping defined"}, {"parameter": "hf_training.adam_beta2", "value": 0.999, "reason": "No mapping defined"}, {"parameter": "hf_training.adam_epsilon", "value": 1e-08, "reason": "No mapping defined"}, {"parameter": "hf_training.run_version", "value": "v1.0", "reason": "No mapping defined"}, {"parameter": "hf_training.version_description", "value": "Class-weighted training for imbalanced data", "reason": "No mapping defined"}, {"parameter": "hf_training.experiment_name", "value": "ner_class_weighted", "reason": "No mapping defined"}, {"parameter": "hf_training.test_mode", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.test_sample_limit", "value": 50, "reason": "No mapping defined"}, {"parameter": "hf_training.test_epochs", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.test_disable_wandb", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.output_dir", "value": "checkpoints", "reason": "No mapping defined"}, {"parameter": "hf_training.save_predictions", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.generate_model_card", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.prediction_loss_only", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.max_length", "value": 512, "reason": "No mapping defined"}, {"parameter": "hf_training.truncation", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.padding", "value": "max_length", "reason": "No mapping defined"}, {"parameter": "hf_training.seed", "value": 42, "reason": "No mapping defined"}, {"parameter": "hf_training.data_seed", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.resume_from_checkpoint", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.ignore_data_skip", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.ddp_find_unused_parameters", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.ddp_bucket_cap_mb", "value": null, "reason": "No mapping defined"}]}, {"source_path": "src\\config\\hf_training_default.yaml", "target_path": "migrated_output\\configs\\hf_src\\config\\hf_training_default.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "hf_training.model_name", "value": "${ROBBERT_MODEL_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}", "reason": "No mapping defined"}, {"parameter": "hf_training.epochs", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.learning_rate", "value": 5e-05, "reason": "No mapping defined"}, {"parameter": "hf_training.weight_decay", "value": 0.01, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_steps", "value": 500, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_ratio", "value": 0.0, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_steps", "value": 100, "reason": "No mapping defined"}, {"parameter": "hf_training.logging_steps", "value": 50, "reason": "No mapping defined"}, {"parameter": "hf_training.save_steps", "value": 500, "reason": "No mapping defined"}, {"parameter": "hf_training.evaluation_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.logging_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.save_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_patience", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold", "value": 0.001, "reason": "No mapping defined"}, {"parameter": "hf_training.load_best_model_at_end", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.metric_for_best_model", "value": "eval_f1", "reason": "No mapping defined"}, {"parameter": "hf_training.greater_is_better", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.use_class_weights", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.class_weights", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_project", "value": "${WANDB_PROJECT:-robbert2023-ner}", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_entity", "value": "${WANDB_ENTITY:-slippydongle}", "reason": "No mapping defined"}, {"parameter": "hf_training.run_name", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_tags", "value": ["robbert-2023", "dutch-nlp", "ner", "hf-trainer"], "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_notes", "value": "Hugging Face Trainer integration for RobBERT-2023 NER", "reason": "No mapping defined"}, {"parameter": "hf_training.report_to", "value": "wandb", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_log_confusion_matrix", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_log_per_class_metrics", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_log_model_artifacts", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_log_evaluation_tables", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_confusion_matrix_frequency", "value": "epoch", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_confusion_matrix_steps", "value": 500, "reason": "No mapping defined"}, {"parameter": "hf_training.use_gpu", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.fp16", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.bf16", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_num_workers", "value": 2, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_pin_memory", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.gradient_accumulation_steps", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.max_grad_norm", "value": 1.0, "reason": "No mapping defined"}, {"parameter": "hf_training.save_total_limit", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.push_to_hub", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.hub_model_id", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.hub_strategy", "value": "every_save", "reason": "No mapping defined"}, {"parameter": "hf_training.lr_scheduler_type", "value": "linear", "reason": "No mapping defined"}, {"parameter": "hf_training.cosine_schedule_num_cycles", "value": 0.5, "reason": "No mapping defined"}, {"parameter": "hf_training.polynomial_decay_power", "value": 1.0, "reason": "No mapping defined"}, {"parameter": "hf_training.lr_scheduler_kwargs", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_schedule_type", "value": "linear", "reason": "No mapping defined"}, {"parameter": "hf_training.end_learning_rate", "value": 0.0, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_monitor_metrics", "value": ["eval_f1", "eval_loss"], "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold_type", "value": "absolute", "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_min_delta", "value": 0.0001, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_restore_best_weights", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.gradient_checkpointing", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.remove_unused_columns", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.label_smoothing_factor", "value": 0.0, "reason": "No mapping defined"}, {"parameter": "hf_training.optim", "value": "adamw_torch", "reason": "No mapping defined"}, {"parameter": "hf_training.adam_beta1", "value": 0.9, "reason": "No mapping defined"}, {"parameter": "hf_training.adam_beta2", "value": 0.999, "reason": "No mapping defined"}, {"parameter": "hf_training.adam_epsilon", "value": 1e-08, "reason": "No mapping defined"}, {"parameter": "hf_training.run_version", "value": "v1.0", "reason": "No mapping defined"}, {"parameter": "hf_training.version_description", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.experiment_name", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.test_mode", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.test_sample_limit", "value": 50, "reason": "No mapping defined"}, {"parameter": "hf_training.test_epochs", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.test_disable_wandb", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.output_dir", "value": "checkpoints", "reason": "No mapping defined"}, {"parameter": "hf_training.save_predictions", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.generate_model_card", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.prediction_loss_only", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.max_length", "value": 512, "reason": "No mapping defined"}, {"parameter": "hf_training.truncation", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.padding", "value": "max_length", "reason": "No mapping defined"}, {"parameter": "hf_training.seed", "value": 42, "reason": "No mapping defined"}, {"parameter": "hf_training.data_seed", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.resume_from_checkpoint", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.ignore_data_skip", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.ddp_find_unused_parameters", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.ddp_bucket_cap_mb", "value": null, "reason": "No mapping defined"}]}, {"source_path": "src\\config\\hf_training_test.yaml", "target_path": "migrated_output\\configs\\hf_src\\config\\hf_training_test.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "hf_training.model_name", "value": "${ROBBERT_MODEL_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}", "reason": "No mapping defined"}, {"parameter": "hf_training.epochs", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.batch_size", "value": 4, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_batch_size", "value": 4, "reason": "No mapping defined"}, {"parameter": "hf_training.learning_rate", "value": 5e-05, "reason": "No mapping defined"}, {"parameter": "hf_training.weight_decay", "value": 0.01, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_steps", "value": 10, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_ratio", "value": 0.0, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_steps", "value": 10, "reason": "No mapping defined"}, {"parameter": "hf_training.logging_steps", "value": 5, "reason": "No mapping defined"}, {"parameter": "hf_training.save_steps", "value": 20, "reason": "No mapping defined"}, {"parameter": "hf_training.evaluation_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.logging_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.save_strategy", "value": "steps", "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_patience", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold", "value": 0.01, "reason": "No mapping defined"}, {"parameter": "hf_training.load_best_model_at_end", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.metric_for_best_model", "value": "eval_f1", "reason": "No mapping defined"}, {"parameter": "hf_training.greater_is_better", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.use_class_weights", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.class_weights", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_project", "value": "robbert2023-test", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_entity", "value": "${WANDB_ENTITY:-slippydongle}", "reason": "No mapping defined"}, {"parameter": "hf_training.run_name", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_tags", "value": ["test", "robbert-2023", "hf-trainer"], "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_notes", "value": "Test run for HF Trainer integration", "reason": "No mapping defined"}, {"parameter": "hf_training.report_to", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.use_gpu", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.fp16", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.bf16", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_num_workers", "value": 0, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_pin_memory", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.gradient_accumulation_steps", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.max_grad_norm", "value": 1.0, "reason": "No mapping defined"}, {"parameter": "hf_training.save_total_limit", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.push_to_hub", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.hub_model_id", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.hub_strategy", "value": "every_save", "reason": "No mapping defined"}, {"parameter": "hf_training.lr_scheduler_type", "value": "constant", "reason": "No mapping defined"}, {"parameter": "hf_training.cosine_schedule_num_cycles", "value": 0.5, "reason": "No mapping defined"}, {"parameter": "hf_training.polynomial_decay_power", "value": 1.0, "reason": "No mapping defined"}, {"parameter": "hf_training.lr_scheduler_kwargs", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_schedule_type", "value": "linear", "reason": "No mapping defined"}, {"parameter": "hf_training.end_learning_rate", "value": 0.0, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_monitor_metrics", "value": ["eval_f1"], "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold_type", "value": "absolute", "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_min_delta", "value": 0.01, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_restore_best_weights", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.gradient_checkpointing", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.remove_unused_columns", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.label_smoothing_factor", "value": 0.0, "reason": "No mapping defined"}, {"parameter": "hf_training.optim", "value": "adamw_torch", "reason": "No mapping defined"}, {"parameter": "hf_training.adam_beta1", "value": 0.9, "reason": "No mapping defined"}, {"parameter": "hf_training.adam_beta2", "value": 0.999, "reason": "No mapping defined"}, {"parameter": "hf_training.adam_epsilon", "value": 1e-08, "reason": "No mapping defined"}, {"parameter": "hf_training.run_version", "value": "test", "reason": "No mapping defined"}, {"parameter": "hf_training.version_description", "value": "Test configuration", "reason": "No mapping defined"}, {"parameter": "hf_training.experiment_name", "value": "test", "reason": "No mapping defined"}, {"parameter": "hf_training.test_mode", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.test_sample_limit", "value": 50, "reason": "No mapping defined"}, {"parameter": "hf_training.test_epochs", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.test_disable_wandb", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.output_dir", "value": "test_checkpoints", "reason": "No mapping defined"}, {"parameter": "hf_training.save_predictions", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.generate_model_card", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.prediction_loss_only", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.max_length", "value": 256, "reason": "No mapping defined"}, {"parameter": "hf_training.truncation", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.padding", "value": "max_length", "reason": "No mapping defined"}, {"parameter": "hf_training.seed", "value": 42, "reason": "No mapping defined"}, {"parameter": "hf_training.data_seed", "value": 42, "reason": "No mapping defined"}, {"parameter": "hf_training.resume_from_checkpoint", "value": null, "reason": "No mapping defined"}, {"parameter": "hf_training.ignore_data_skip", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.ddp_find_unused_parameters", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.ddp_bucket_cap_mb", "value": null, "reason": "No mapping defined"}]}, {"source_path": "src\\config\\legacy_config.yaml", "target_path": "migrated_output\\configs\\hf_src\\config\\legacy_config.yaml", "target_format": "hf_trainer", "success": true, "warnings": ["Loss weights configuration needs manual review - HF Trainer uses different loss weighting approach", "Multi-head configuration detected - HF Trainer integration focuses on single-head NER training"], "converted_parameters": {"training.epochs": {"source_value": 5, "target_path": "hf_training.epochs"}, "training.batch_size": {"source_value": 16, "target_path": "hf_training.batch_size"}, "training.learning_rate": {"source_value": "2e-5", "target_path": "hf_training.learning_rate"}, "training.weight_decay": {"source_value": 0.01, "target_path": "hf_training.weight_decay"}, "training.warmup_steps": {"source_value": 1000, "target_path": "hf_training.warmup_steps"}, "model.model_name": {"source_value": "DTAI-KULeuven/robbert-2023-dutch-base", "target_path": "hf_training.model_name"}, "wandb.project": {"source_value": "legacy-robbert-ner", "target_path": "hf_training.wandb_project"}, "wandb.entity": {"source_value": "test-entity", "target_path": "hf_training.wandb_entity"}, "evaluation.eval_steps": {"source_value": 200, "target_path": "hf_training.eval_steps"}, "evaluation.logging_steps": {"source_value": 100, "target_path": "hf_training.logging_steps"}, "evaluation.save_steps": {"source_value": 1000, "target_path": "hf_training.save_steps"}}, "unmapped_parameters": [{"parameter": "model.max_length", "value": 512, "reason": "No mapping defined"}, {"parameter": "model.heads", "value": ["ner"], "reason": "No mapping defined"}, {"parameter": "training.max_grad_norm", "value": 1.0, "reason": "No mapping defined"}, {"parameter": "training.loss_weights", "value": {"ner": 1.0}, "reason": "No mapping defined"}]}, {"source_path": "src\\config\\wandb.yaml", "target_path": "migrated_output\\configs\\hf_src\\config\\wandb.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {"wandb.project": {"source_value": "robbert2023", "target_path": "hf_training.wandb_project"}, "wandb.entity": {"source_value": "slippydongle", "target_path": "hf_training.wandb_entity"}}, "unmapped_parameters": [{"parameter": "wandb.api_key", "value": "${WANDB_API_KEY:-****************************************}", "reason": "No mapping defined"}, {"parameter": "wandb.enabled", "value": true, "reason": "No mapping defined"}, {"parameter": "wandb.log_model", "value": true, "reason": "No mapping defined"}, {"parameter": "wandb.log_gradients", "value": false, "reason": "No mapping defined"}, {"parameter": "wandb.log_parameters", "value": true, "reason": "No mapping defined"}, {"parameter": "wandb.log_metrics", "value": true, "reason": "No mapping defined"}, {"parameter": "wandb.log_freq", "value": 100, "reason": "No mapping defined"}, {"parameter": "wandb.save_freq", "value": 1000, "reason": "No mapping defined"}, {"parameter": "wandb.run_name", "value": null, "reason": "No mapping defined"}, {"parameter": "wandb.tags", "value": ["robbert-2023", "dutch-nlp", "multi-task"], "reason": "No mapping defined"}, {"parameter": "wandb.notes", "value": "Multi-head RobBERT training for Dutch NLP tasks", "reason": "No mapping defined"}, {"parameter": "wandb.heads", "value": {"ner": {"project_suffix": "-ner", "tags": ["named-entity-recognition", "token-classification", "person-entities"], "notes": "Person Named Entity Recognition with RobBERT-2023", "metrics": ["ner_loss", "ner_f1", "ner_precision", "ner_recall", "ner_accuracy", "ner_entity_f1"]}}, "reason": "No mapping defined"}, {"parameter": "wandb.multitask", "value": {"project_suffix": "-multitask", "tags": ["multi-task", "joint-training"], "notes": "Multi-head joint training", "log_head_losses": true, "log_combined_metrics": true}, "reason": "No mapping defined"}, {"parameter": "wandb.artifacts", "value": {"save_best_model": true, "save_final_model": true, "model_name_template": "robbert2023-{heads}-epoch-{epoch}", "metadata_keys": ["model_name", "heads", "epochs", "batch_size", "learning_rate", "loss_weights", "dataset_path", "config_path"]}, "reason": "No mapping defined"}]}, {"source_path": "src\\config\\heads\\ner.yaml", "target_path": "migrated_output\\configs\\hf_src\\config\\heads\\ner.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "head_config.name", "value": "ner", "reason": "No mapping defined"}, {"parameter": "head_config.type", "value": "token_classification", "reason": "No mapping defined"}, {"parameter": "head_config.model", "value": {"hidden_size": 768, "num_labels": 3, "dropout": 0.1, "classifier_dropout": 0.1}, "reason": "No mapping defined"}, {"parameter": "head_config.training", "value": {"epochs": 5, "batch_size": 16, "learning_rate": "3e-5", "weight_decay": 0.01, "warmup_ratio": 0.1, "max_grad_norm": 1.0, "loss_weight": 1.0, "class_weights": null, "ignore_index": -100, "optimizer": "adamw", "scheduler": "linear_with_warmup", "gradient_accumulation_steps": 1}, "reason": "No mapping defined"}, {"parameter": "head_config.data", "value": {"max_length": 512, "stride": 128, "label_all_tokens": false, "augmentation": {"enabled": false, "techniques": []}}, "reason": "No mapping defined"}, {"parameter": "head_config.evaluation", "value": {"metrics": ["precision", "recall", "f1", "accuracy"], "average": "weighted", "entity_level": true, "scheme": "IOB2"}, "reason": "No mapping defined"}, {"parameter": "head_config.wandb", "value": {"project_suffix": "-ner", "tags": ["named-entity-recognition", "token-classification", "dutch", "person-entities"], "notes": "Person Named Entity Recognition for Dutch text using RobBERT-2023", "track_metrics": ["ner_loss", "ner_f1", "ner_precision", "ner_recall", "ner_accuracy", "ner_entity_f1"], "save_best_model": true, "metric_for_best": "ner_f1"}, "reason": "No mapping defined"}, {"parameter": "head_config.inference", "value": {"batch_size": 32, "threshold": 0.5, "post_processing": {"merge_subwords": true, "filter_confidence": true, "min_entity_length": 1}}, "reason": "No mapping defined"}]}, {"source_path": "test_migration_results\\config_migration_tests\\hf_trainer_config.yaml", "target_path": "migrated_output\\configs\\hf_test_migration_results\\config_migration_tests\\hf_trainer_config.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "hf_training.batch_size", "value": 16, "reason": "No mapping defined"}, {"parameter": "hf_training.cache_dir", "value": "data/cache", "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_num_workers", "value": 2, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_pin_memory", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_patience", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold", "value": 0.001, "reason": "No mapping defined"}, {"parameter": "hf_training.epochs", "value": 5, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_steps", "value": 200, "reason": "No mapping defined"}, {"parameter": "hf_training.fp16", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.generate_model_card", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.learning_rate", "value": 2e-05, "reason": "No mapping defined"}, {"parameter": "hf_training.load_best_model_at_end", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.logging_steps", "value": 100, "reason": "No mapping defined"}, {"parameter": "hf_training.metric_for_best_model", "value": "eval_f1", "reason": "No mapping defined"}, {"parameter": "hf_training.model_name", "value": "DTAI-KULeuven/robbert-2023-dutch-base", "reason": "No mapping defined"}, {"parameter": "hf_training.push_to_hub", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.run_version", "value": "v1.0", "reason": "No mapping defined"}, {"parameter": "hf_training.save_predictions", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.save_steps", "value": 1000, "reason": "No mapping defined"}, {"parameter": "hf_training.save_total_limit", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.test_epochs", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.test_mode", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.test_sample_limit", "value": 50, "reason": "No mapping defined"}, {"parameter": "hf_training.use_cache", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.use_class_weights", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.use_gpu", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_entity", "value": "test-entity", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_project", "value": "test-project", "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_steps", "value": 1000, "reason": "No mapping defined"}, {"parameter": "hf_training.weight_decay", "value": 0.01, "reason": "No mapping defined"}]}, {"source_path": "test_migration_results\\config_migration_tests\\legacy_config.yaml", "target_path": "migrated_output\\configs\\hf_test_migration_results\\config_migration_tests\\legacy_config.yaml", "target_format": "hf_trainer", "success": true, "warnings": ["Loss weights configuration needs manual review - HF Trainer uses different loss weighting approach", "Multi-head configuration detected - HF Trainer integration focuses on single-head NER training"], "converted_parameters": {"training.epochs": {"source_value": 5, "target_path": "hf_training.epochs"}, "training.batch_size": {"source_value": 16, "target_path": "hf_training.batch_size"}, "training.learning_rate": {"source_value": 2e-05, "target_path": "hf_training.learning_rate"}, "training.weight_decay": {"source_value": 0.01, "target_path": "hf_training.weight_decay"}, "training.warmup_steps": {"source_value": 1000, "target_path": "hf_training.warmup_steps"}, "model.model_name": {"source_value": "DTAI-KULeuven/robbert-2023-dutch-base", "target_path": "hf_training.model_name"}, "wandb.project": {"source_value": "test-project", "target_path": "hf_training.wandb_project"}, "wandb.entity": {"source_value": "test-entity", "target_path": "hf_training.wandb_entity"}, "evaluation.eval_steps": {"source_value": 200, "target_path": "hf_training.eval_steps"}, "evaluation.logging_steps": {"source_value": 100, "target_path": "hf_training.logging_steps"}, "evaluation.save_steps": {"source_value": 1000, "target_path": "hf_training.save_steps"}}, "unmapped_parameters": [{"parameter": "model.heads", "value": ["ner"], "reason": "No mapping defined"}, {"parameter": "model.max_length", "value": 512, "reason": "No mapping defined"}, {"parameter": "training.loss_weights", "value": {"ner": 1.0}, "reason": "No mapping defined"}, {"parameter": "training.max_grad_norm", "value": 1.0, "reason": "No mapping defined"}]}, {"source_path": "test_migration_results\\integration_test\\hf_config.yaml", "target_path": "migrated_output\\configs\\hf_test_migration_results\\integration_test\\hf_config.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "hf_training.batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.cache_dir", "value": "data/cache", "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_num_workers", "value": 2, "reason": "No mapping defined"}, {"parameter": "hf_training.dataloader_pin_memory", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_patience", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.early_stopping_threshold", "value": 0.001, "reason": "No mapping defined"}, {"parameter": "hf_training.epochs", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_batch_size", "value": 8, "reason": "No mapping defined"}, {"parameter": "hf_training.eval_steps", "value": 100, "reason": "No mapping defined"}, {"parameter": "hf_training.fp16", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.generate_model_card", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.learning_rate", "value": 5e-05, "reason": "No mapping defined"}, {"parameter": "hf_training.load_best_model_at_end", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.logging_steps", "value": 50, "reason": "No mapping defined"}, {"parameter": "hf_training.metric_for_best_model", "value": "eval_f1", "reason": "No mapping defined"}, {"parameter": "hf_training.model_name", "value": "DTAI-KULeuven/robbert-2023-dutch-base", "reason": "No mapping defined"}, {"parameter": "hf_training.push_to_hub", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.run_version", "value": "v1.0", "reason": "No mapping defined"}, {"parameter": "hf_training.save_predictions", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.save_steps", "value": 500, "reason": "No mapping defined"}, {"parameter": "hf_training.save_total_limit", "value": 3, "reason": "No mapping defined"}, {"parameter": "hf_training.test_epochs", "value": 1, "reason": "No mapping defined"}, {"parameter": "hf_training.test_mode", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.test_sample_limit", "value": 50, "reason": "No mapping defined"}, {"parameter": "hf_training.use_cache", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.use_class_weights", "value": false, "reason": "No mapping defined"}, {"parameter": "hf_training.use_gpu", "value": true, "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_entity", "value": "slippydongle", "reason": "No mapping defined"}, {"parameter": "hf_training.wandb_project", "value": "integration-test", "reason": "No mapping defined"}, {"parameter": "hf_training.warmup_steps", "value": 500, "reason": "No mapping defined"}, {"parameter": "hf_training.weight_decay", "value": 0.01, "reason": "No mapping defined"}]}, {"source_path": "test_migration_results\\integration_test\\legacy_config.yaml", "target_path": "migrated_output\\configs\\hf_test_migration_results\\integration_test\\legacy_config.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {"training.epochs": {"source_value": 3, "target_path": "hf_training.epochs"}, "training.batch_size": {"source_value": 8, "target_path": "hf_training.batch_size"}, "training.learning_rate": {"source_value": 5e-05, "target_path": "hf_training.learning_rate"}, "wandb.project": {"source_value": "integration-test", "target_path": "hf_training.wandb_project"}}, "unmapped_parameters": []}, {"source_path": "venv\\Lib\\site-packages\\datasets\\utils\\resources\\readme_structure.yaml", "target_path": "migrated_output\\configs\\hf_venv\\Lib\\site-packages\\datasets\\utils\\resources\\readme_structure.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": "venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_file_listing.yaml", "target_path": "migrated_output\\configs\\hf_venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_file_listing.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": "venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_mkdir.yaml", "target_path": "migrated_output\\configs\\hf_venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_mkdir.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": "venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_read_pyarrow_non_partitioned.yaml", "target_path": "migrated_output\\configs\\hf_venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_read_pyarrow_non_partitioned.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": "venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_read_range.yaml", "target_path": "migrated_output\\configs\\hf_venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_read_range.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": "venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_read_range_chunked.yaml", "target_path": "migrated_output\\configs\\hf_venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_read_range_chunked.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": "venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_write_and_read.yaml", "target_path": "migrated_output\\configs\\hf_venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_write_and_read.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": "venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_write_pyarrow_non_partitioned.yaml", "target_path": "migrated_output\\configs\\hf_venv\\Lib\\site-packages\\fsspec\\implementations\\tests\\cassettes\\test_dbfs\\test_dbfs_write_pyarrow_non_partitioned.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": []}, {"source_path": "venv\\Lib\\site-packages\\torch\\_export\\serde\\schema.yaml", "target_path": "migrated_output\\configs\\hf_venv\\Lib\\site-packages\\torch\\_export\\serde\\schema.yaml", "target_format": "hf_trainer", "success": true, "warnings": [], "converted_parameters": {}, "unmapped_parameters": [{"parameter": "Argument.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "Argument.fields", "value": {"as_none": {"type": "Tuple[()]"}, "as_tensor": {"type": "TensorArgument"}, "as_tensors": {"type": "List[TensorArgument]"}, "as_int": {"type": "int"}, "as_ints": {"type": "List[int]"}, "as_float": {"type": "float"}, "as_floats": {"type": "List[float]"}, "as_string": {"type": "str"}, "as_strings": {"type": "List[str]"}, "as_sym_int": {"type": "SymIntArgument"}, "as_sym_ints": {"type": "List[SymIntArgument]"}, "as_scalar_type": {"type": "ScalarType"}, "as_memory_format": {"type": "MemoryFormat"}, "as_layout": {"type": "Layout"}, "as_device": {"type": "<PERSON><PERSON>"}, "as_bool": {"type": "bool"}, "as_bools": {"type": "List[bool]"}, "as_sym_bool": {"type": "SymBoolArgument"}, "as_sym_bools": {"type": "List[SymBoolArgument]"}, "as_graph": {"type": "GraphArgument"}, "as_optional_tensors": {"type": "List[OptionalTensorArgument]"}, "as_custom_obj": {"type": "CustomObjArgument"}, "as_operator": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "BufferMutationSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "BufferMutationSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "buffer_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "ConstantInputSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "ConstantInputSpec.fields", "value": {"name": {"type": "str"}, "value": {"type": "ConstantV<PERSON>ue"}}, "reason": "No mapping defined"}, {"parameter": "ConstantValue.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "ConstantValue.fields", "value": {"as_none": {"type": "Tuple[()]"}, "as_int": {"type": "int"}, "as_float": {"type": "float"}, "as_string": {"type": "str"}, "as_bool": {"type": "bool"}}, "reason": "No mapping defined"}, {"parameter": "CustomObjArgument.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "CustomObjArgument.fields", "value": {"name": {"type": "str"}, "class_fqn": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "Device.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "Device.fields", "value": {"type": {"type": "str"}, "index": {"type": "Optional[int]", "default": "None"}}, "reason": "No mapping defined"}, {"parameter": "ExportedProgram.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "ExportedProgram.fields", "value": {"graph_module": {"type": "GraphModule"}, "opset_version": {"type": "Dict[str, int]"}, "range_constraints": {"type": "Dict[str, RangeConstraint]"}, "schema_version": {"type": "SchemaVersion"}, "verifiers": {"type": "List[str]", "default": "[]"}, "torch_version": {"type": "str", "default": "<=2.4"}}, "reason": "No mapping defined"}, {"parameter": "GradientToParameterSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "GradientToParameterSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "parameter_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "GradientToUserInputSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "GradientToUserInputSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "user_input_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "Graph.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "Graph.fields", "value": {"inputs": {"type": "List[Argument]"}, "outputs": {"type": "List[Argument]"}, "nodes": {"type": "List[Node]"}, "tensor_values": {"type": "Dict[str, TensorMeta]"}, "sym_int_values": {"type": "Dict[str, SymInt]"}, "sym_bool_values": {"type": "Dict[str, SymBool]"}, "is_single_tensor_return": {"type": "bool", "default": "False"}, "custom_obj_values": {"type": "Dict[str, CustomObjArgument]", "default": "{}"}}, "reason": "No mapping defined"}, {"parameter": "GraphArgument.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "GraphArgument.fields", "value": {"name": {"type": "str"}, "graph": {"type": "Graph"}}, "reason": "No mapping defined"}, {"parameter": "GraphModule.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "GraphModule.fields", "value": {"graph": {"type": "Graph"}, "signature": {"type": "GraphSignature"}, "module_call_graph": {"type": "List[ModuleCallEntry]"}, "metadata": {"type": "Dict[str, str]", "default": "{}"}}, "reason": "No mapping defined"}, {"parameter": "GraphSignature.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "GraphSignature.fields", "value": {"input_specs": {"type": "List[InputSpec]"}, "output_specs": {"type": "List[OutputSpec]"}}, "reason": "No mapping defined"}, {"parameter": "InputSpec.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "InputSpec.fields", "value": {"user_input": {"type": "UserInputSpec"}, "parameter": {"type": "InputToParameterSpec"}, "buffer": {"type": "InputToBufferSpec"}, "tensor_constant": {"type": "InputToTensorConstantSpec"}, "custom_obj": {"type": "InputToCustomObjSpec"}, "token": {"type": "InputTokenSpec"}, "constant_input": {"type": "ConstantInputSpec"}}, "reason": "No mapping defined"}, {"parameter": "InputToBufferSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "InputToBufferSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "buffer_name": {"type": "str"}, "persistent": {"type": "bool"}}, "reason": "No mapping defined"}, {"parameter": "InputToCustomObjSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "InputToCustomObjSpec.fields", "value": {"arg": {"type": "CustomObjArgument"}, "custom_obj_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "InputToParameterSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "InputToParameterSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "parameter_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "InputToTensorConstantSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "InputToTensorConstantSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "tensor_constant_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "InputTokenSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "InputTokenSpec.fields", "value": {"arg": {"type": "TokenArgument"}}, "reason": "No mapping defined"}, {"parameter": "Layout.kind", "value": "enum", "reason": "No mapping defined"}, {"parameter": "Layout.fields", "value": {"Unknown": 0, "SparseCoo": 1, "SparseCsr": 2, "SparseCsc": 3, "SparseBsr": 4, "SparseBsc": 5, "_mkldnn": 6, "Strided": 7}, "reason": "No mapping defined"}, {"parameter": "LossOutputSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "LossOutputSpec.fields", "value": {"arg": {"type": "TensorArgument"}}, "reason": "No mapping defined"}, {"parameter": "MemoryFormat.kind", "value": "enum", "reason": "No mapping defined"}, {"parameter": "MemoryFormat.fields", "value": {"Unknown": 0, "ContiguousFormat": 1, "ChannelsLast": 2, "ChannelsLast3d": 3, "PreserveFormat": 4}, "reason": "No mapping defined"}, {"parameter": "ModuleCallEntry.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "ModuleCallEntry.fields", "value": {"fqn": {"type": "str"}, "signature": {"type": "Optional[ModuleCallSignature]", "default": "None"}}, "reason": "No mapping defined"}, {"parameter": "ModuleCallSignature.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "ModuleCallSignature.fields", "value": {"inputs": {"type": "List[Argument]"}, "outputs": {"type": "List[Argument]"}, "in_spec": {"type": "str"}, "out_spec": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "NamedArgument.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "NamedArgument.fields", "value": {"name": {"type": "str"}, "arg": {"type": "Argument"}}, "reason": "No mapping defined"}, {"parameter": "Node.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "Node.fields", "value": {"target": {"type": "str"}, "inputs": {"type": "List[NamedArgument]"}, "outputs": {"type": "List[Argument]"}, "metadata": {"type": "Dict[str, str]"}}, "reason": "No mapping defined"}, {"parameter": "OptionalTensorArgument.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "OptionalTensorArgument.fields", "value": {"as_tensor": {"type": "TensorArgument"}, "as_none": {"type": "Tuple[()]"}}, "reason": "No mapping defined"}, {"parameter": "OutputSpec.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "OutputSpec.fields", "value": {"user_output": {"type": "UserOutputSpec"}, "loss_output": {"type": "LossOutputSpec"}, "buffer_mutation": {"type": "BufferMutationSpec"}, "gradient_to_parameter": {"type": "GradientToParameterSpec"}, "gradient_to_user_input": {"type": "GradientToUserInputSpec"}, "user_input_mutation": {"type": "UserInputMutationSpec"}, "token": {"type": "OutputTokenSpec"}}, "reason": "No mapping defined"}, {"parameter": "OutputTokenSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "OutputTokenSpec.fields", "value": {"arg": {"type": "TokenArgument"}}, "reason": "No mapping defined"}, {"parameter": "RangeConstraint.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "RangeConstraint.fields", "value": {"min_val": {"type": "int"}, "max_val": {"type": "int"}}, "reason": "No mapping defined"}, {"parameter": "ScalarType.kind", "value": "enum", "reason": "No mapping defined"}, {"parameter": "ScalarType.fields", "value": {"UNKNOWN": 0, "BYTE": 1, "CHAR": 2, "SHORT": 3, "INT": 4, "LONG": 5, "HALF": 6, "FLOAT": 7, "DOUBLE": 8, "COMPLEXHALF": 9, "COMPLEXFLOAT": 10, "COMPLEXDOUBLE": 11, "BOOL": 12, "BFLOAT16": 13}, "reason": "No mapping defined"}, {"parameter": "SchemaVersion.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "SchemaVersion.fields", "value": {"major": {"type": "int"}, "minor": {"type": "int"}}, "reason": "No mapping defined"}, {"parameter": "SymBool.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "SymBool.fields", "value": {"as_expr": {"type": "SymExpr"}, "as_bool": {"type": "bool"}}, "reason": "No mapping defined"}, {"parameter": "SymBoolArgument.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "SymBoolArgument.fields", "value": {"as_name": {"type": "str"}, "as_bool": {"type": "bool"}}, "reason": "No mapping defined"}, {"parameter": "SymExpr.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "SymExpr.fields", "value": {"expr_str": {"type": "str"}, "hint": {"type": "Optional[SymExprHint]", "default": "None"}}, "reason": "No mapping defined"}, {"parameter": "SymExprHint.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "SymExprHint.fields", "value": {"as_int": {"type": "int"}, "as_float": {"type": "float"}, "as_bool": {"type": "bool"}}, "reason": "No mapping defined"}, {"parameter": "SymInt.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "SymInt.fields", "value": {"as_expr": {"type": "SymExpr"}, "as_int": {"type": "int"}}, "reason": "No mapping defined"}, {"parameter": "SymIntArgument.kind", "value": "union", "reason": "No mapping defined"}, {"parameter": "SymIntArgument.fields", "value": {"as_name": {"type": "str"}, "as_int": {"type": "int"}}, "reason": "No mapping defined"}, {"parameter": "TensorArgument.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "TensorArgument.fields", "value": {"name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "TensorMeta.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "TensorMeta.fields", "value": {"dtype": {"type": "ScalarType"}, "sizes": {"type": "List[SymInt]"}, "requires_grad": {"type": "bool"}, "device": {"type": "<PERSON><PERSON>"}, "strides": {"type": "List[SymInt]"}, "storage_offset": {"type": "SymInt"}, "layout": {"type": "Layout"}}, "reason": "No mapping defined"}, {"parameter": "TokenArgument.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "TokenArgument.fields", "value": {"name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "UserInputMutationSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "UserInputMutationSpec.fields", "value": {"arg": {"type": "TensorArgument"}, "user_input_name": {"type": "str"}}, "reason": "No mapping defined"}, {"parameter": "UserInputSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "UserInputSpec.fields", "value": {"arg": {"type": "Argument"}}, "reason": "No mapping defined"}, {"parameter": "UserOutputSpec.kind", "value": "struct", "reason": "No mapping defined"}, {"parameter": "UserOutputSpec.fields", "value": {"arg": {"type": "Argument"}}, "reason": "No mapping defined"}]}], "errors": ["Failed to migrate port.yaml: Failed to migrate configuration: 'list' object has no attribute 'items' | Details: {'data_path': None, 'processing_step': None, 'data_size': None}", "Failed to migrate native_functions.yaml: Failed to migrate configuration: 'list' object has no attribute 'items' | Details: {'data_path': None, 'processing_step': None, 'data_size': None}", "Failed to migrate tags.yaml: Failed to migrate configuration: 'list' object has no attribute 'items' | Details: {'data_path': None, 'processing_step': None, 'data_size': None}", "Failed to migrate deprecated.yaml: Failed to migrate configuration: 'list' object has no attribute 'items' | Details: {'data_path': None, 'processing_step': None, 'data_size': None}", "Failed to migrate derivatives.yaml: Failed to migrate configuration: 'list' object has no attribute 'items' | Details: {'data_path': None, 'processing_step': None, 'data_size': None}", "Failed to migrate port.yaml: Failed to migrate configuration: 'list' object has no attribute 'items' | Details: {'data_path': None, 'processing_step': None, 'data_size': None}", "Failed to migrate native_functions.yaml: Failed to migrate configuration: 'list' object has no attribute 'items' | Details: {'data_path': None, 'processing_step': None, 'data_size': None}", "Failed to migrate tags.yaml: Failed to migrate configuration: 'list' object has no attribute 'items' | Details: {'data_path': None, 'processing_step': None, 'data_size': None}", "Failed to migrate deprecated.yaml: Failed to migrate configuration: 'list' object has no attribute 'items' | Details: {'data_path': None, 'processing_step': None, 'data_size': None}", "Failed to migrate derivatives.yaml: Failed to migrate configuration: 'list' object has no attribute 'items' | Details: {'data_path': None, 'processing_step': None, 'data_size': None}"]}, "checkpoint_migration": {"checkpoints_processed": 0, "checkpoints_migrated": 0, "checkpoints_failed": 0, "errors": []}, "api_compatibility_tests": {"checkpoints_tested": 0, "checkpoints_compatible": 0, "compatibility_issues": []}}}