"""
FastAPI inference server for multi-task BERTje model.
"""

from fastapi import FastAPI, HTTPException, Query
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import torch
import logging
from contextlib import asynccontextmanager

from ..models.multitask_bertje import MultiTaskBERTje
from ..config import load_config


# Global model instance
model = None
config = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Load model on startup and cleanup on shutdown."""
    global model, config
    
    # Load configuration
    config = load_config('src/config/default.yaml')
    
    # Load model
    logging.info("Loading multi-task BERTje model...")
    model = MultiTaskBERTje.from_pretrained()
    model.eval()
    
    if torch.cuda.is_available():
        model = model.cuda()
        logging.info("Model loaded on GPU")
    else:
        logging.info("Model loaded on CPU")
    
    yield
    
    # Cleanup
    model = None
    config = None


app = FastAPI(
    title="Multi-task BERTje API",
    description="Dutch NLP inference API with multiple task heads",
    version="1.0.0",
    lifespan=lifespan
)


class PredictionRequest(BaseModel):
    """Request model for predictions."""
    text: str = Field(..., description="Input text for processing")
    tasks: Optional[List[str]] = Field(
        default=None, 
        description="List of tasks to run (default: all available)"
    )


class NEREntity(BaseModel):
    """Named entity with position and confidence."""
    text: str
    label: str
    start: int
    end: int
    confidence: float


class PredictionResponse(BaseModel):
    """Response model for predictions."""
    text: str
    results: Dict[str, Any]


@app.get("/ping")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "model_loaded": model is not None,
        "available_heads": list(model.heads.keys()) if model else []
    }


@app.post("/predict", response_model=PredictionResponse)
async def predict(
    request: PredictionRequest,
    tasks: Optional[str] = Query(None, description="Comma-separated list of tasks")
):
    """Run inference on input text."""
    if model is None:
        raise HTTPException(status_code=503, detail="Model not loaded")

    try:
        # Parse tasks
        active_tasks = None
        if tasks:
            active_tasks = [t.strip() for t in tasks.split(',')]
        elif request.tasks:
            active_tasks = request.tasks

        # Tokenize input
        inputs = model.tokenizer(
            request.text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        )

        # Move to device
        if torch.cuda.is_available():
            inputs = {k: v.cuda() for k, v in inputs.items()}

        # Run inference
        with torch.no_grad():
            outputs = model(
                input_ids=inputs['input_ids'],
                attention_mask=inputs['attention_mask'],
                heads=active_tasks
            )

        # Process results
        results = {}
        for task_name, task_output in outputs.items():
            if task_name == 'loss':
                continue

            logits = task_output['logits']

            if task_name == 'ner':
                # Process NER results
                predictions = torch.argmax(logits, dim=-1)
                entities = extract_ner_entities(
                    request.text,
                    predictions[0],
                    model.tokenizer,
                    logits[0]
                )
                results[task_name] = entities
            else:
                # Process classification results
                probabilities = torch.softmax(logits, dim=-1)
                predicted_class = torch.argmax(probabilities, dim=-1)
                confidence = probabilities.max().item()

                results[task_name] = {
                    'predicted_class': predicted_class.item(),
                    'confidence': confidence,
                    'probabilities': probabilities[0].tolist()
                }

        return PredictionResponse(text=request.text, results=results)

    except Exception as e:
        logging.error(f"Prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")


def extract_ner_entities(text: str, predictions: torch.Tensor,
                        tokenizer, logits: torch.Tensor) -> List[NEREntity]:
    """Extract named entities from NER predictions."""

    # CoNLL-2002 label mapping (from model config)
    id2label = {
        0: 'B-LOC', 1: 'B-MISC', 2: 'B-ORG', 3: 'B-PER', 4: 'I-LOC',
        5: 'I-MISC', 6: 'I-ORG', 7: 'I-PER', 8: 'O'
    }

    # Get tokens and predictions
    tokens = tokenizer.convert_ids_to_tokens(tokenizer.encode(text))
    predictions = predictions[:len(tokens)]

    entities = []
    current_entity = None

    for i, (token, pred_id) in enumerate(zip(tokens, predictions)):
        if token in ['[CLS]', '[SEP]', '[PAD]']:
            continue

        label = id2label.get(pred_id.item(), 'O')
        confidence = torch.softmax(logits[i], dim=-1).max().item()

        if label.startswith('B-'):
            # Start of new entity
            if current_entity:
                entities.append(current_entity)

            entity_type = label[2:]
            current_entity = {
                'text': token.replace('##', ''),
                'label': entity_type,
                'start': i,
                'end': i + 1,
                'confidence': confidence
            }
        elif label.startswith('I-') and current_entity:
            # Continuation of entity
            entity_type = label[2:]
            if current_entity['label'] == entity_type:
                current_entity['text'] += token.replace('##', '')
                current_entity['end'] = i + 1
                current_entity['confidence'] = min(current_entity['confidence'], confidence)
        else:
            # End of entity
            if current_entity:
                entities.append(current_entity)
                current_entity = None

    # Add final entity if exists
    if current_entity:
        entities.append(current_entity)

    # Convert to NEREntity objects
    return [
        NEREntity(
            text=entity['text'],
            label=entity['label'],
            start=entity['start'],
            end=entity['end'],
            confidence=entity['confidence']
        )
        for entity in entities
    ]


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)