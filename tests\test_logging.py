"""
Tests for logging functionality and utilities.
"""

import pytest
import logging
import tempfile
import torch
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import io
import sys

from src.utils.logging_utils import (
    RobBERTLogger, get_logger, log_execution_time, log_memory_usage,
    safe_execute, validate_tensor_shape, log_tensor_stats
)
from src.exceptions import RobBERTError, DimensionMismatchError


class TestRobBERTLogger:
    """Test RobBERTLogger class functionality."""
    
    def test_logger_creation(self):
        """Test basic logger creation."""
        logger = RobBERTLogger("test_logger")
        
        assert logger.logger.name == "test_logger"
        assert logger.logger.level == logging.INFO
        assert len(logger.logger.handlers) >= 1
    
    def test_logger_with_custom_level(self):
        """Test logger creation with custom level."""
        logger = RobBERTLogger("test_logger", level="DEBUG")
        
        assert logger.logger.level == logging.DEBUG
    
    def test_logger_with_file_output(self, temp_dir):
        """Test logger with file output."""
        log_file = temp_dir / "test.log"
        logger = RobBERTLogger("test_logger", log_file=str(log_file))
        
        # Test logging
        logger.logger.info("Test message")
        logger.logger.error("Test error")
        
        # Check file was created and contains messages
        assert log_file.exists()
        content = log_file.read_text()
        assert "Test message" in content
        assert "Test error" in content
    
    def test_log_system_info(self):
        """Test system info logging."""
        # Capture log output
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        
        logger = RobBERTLogger("test_logger")
        logger.logger.handlers = [handler]
        
        logger.log_system_info()
        
        log_output = log_stream.getvalue()
        assert "Python version" in log_output
        assert "PyTorch version" in log_output
        assert "CUDA available" in log_output
    
    def test_log_model_info(self):
        """Test model info logging."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        
        logger = RobBERTLogger("test_logger")
        logger.logger.handlers = [handler]
        
        logger.log_model_info(
            "robbert-2023",
            num_parameters=125000000,
            model_size_mb=500.0
        )
        
        log_output = log_stream.getvalue()
        assert "robbert-2023" in log_output
        assert "125,000,000" in log_output
        assert "500.0MB" in log_output
    
    def test_log_tokenization_stats(self):
        """Test tokenization stats logging."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setLevel(logging.DEBUG)
        
        logger = RobBERTLogger("test_logger", level="DEBUG")
        logger.logger.handlers = [handler]
        
        text = "This is a test sentence for tokenization."
        tokens = ["This", "is", "a", "test", "sentence", "for", "token", "ization", "."]
        stats = {"words": 7, "labels": 0, "truncated": False}
        
        logger.log_tokenization_stats(text, tokens, stats)
        
        log_output = log_stream.getvalue()
        assert "Text length: 41" in log_output
        assert "Token count: 9" in log_output
        assert "Tokens per character" in log_output
    
    def test_log_inference_stats(self):
        """Test inference stats logging."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setLevel(logging.DEBUG)
        
        logger = RobBERTLogger("test_logger", level="DEBUG")
        logger.logger.handlers = [handler]
        
        logger.log_inference_stats(
            input_shape=(2, 512),
            output_shape=(2, 512, 3),
            inference_time=0.5,
            heads=["ner", "compliance"]
        )
        
        log_output = log_stream.getvalue()
        assert "Inference completed in 0.500s" in log_output
        assert "Throughput" in log_output
        assert "tokens/second" in log_output
    
    def test_log_memory_usage(self):
        """Test memory usage logging."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setLevel(logging.DEBUG)
        
        logger = RobBERTLogger("test_logger", level="DEBUG")
        logger.logger.handlers = [handler]
        
        logger.log_memory_usage("test operation")
        
        log_output = log_stream.getvalue()
        assert "Memory usage" in log_output
        assert "RAM:" in log_output
    
    def test_log_memory_usage_with_gpu(self):
        """Test memory usage logging with GPU info."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setLevel(logging.DEBUG)
        
        logger = RobBERTLogger("test_logger", level="DEBUG")
        logger.logger.handlers = [handler]
        
        with patch('torch.cuda.is_available', return_value=True), \
             patch('torch.cuda.memory_allocated', return_value=1e9), \
             patch('torch.cuda.get_device_properties') as mock_props:
            
            mock_props.return_value = Mock(total_memory=4e9)
            logger.log_memory_usage("test operation")
        
        log_output = log_stream.getvalue()
        assert "GPU:" in log_output
    
    def test_log_error_with_robbert_error(self):
        """Test error logging with RobBERTError."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        
        logger = RobBERTLogger("test_logger")
        logger.logger.handlers = [handler]
        
        error = RobBERTError("Test error", {"key": "value"})
        context = {"function": "test_function", "line": 42}
        
        logger.log_error(error, context)
        
        log_output = log_stream.getvalue()
        assert "RobBERTError: Test error" in log_output
        assert "Details: {'key': 'value'}" in log_output
        assert "Context: {'function': 'test_function', 'line': 42}" in log_output
    
    def test_log_error_with_standard_exception(self):
        """Test error logging with standard exception."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        
        logger = RobBERTLogger("test_logger")
        logger.logger.handlers = [handler]
        
        error = ValueError("Standard error")
        
        logger.log_error(error)
        
        log_output = log_stream.getvalue()
        assert "ValueError: Standard error" in log_output


class TestLoggingUtilities:
    """Test logging utility functions."""
    
    def test_get_logger_factory(self):
        """Test get_logger factory function."""
        logger = get_logger("test_module")
        
        assert isinstance(logger, RobBERTLogger)
        assert logger.logger.name == "test_module"
    
    def test_get_logger_with_file(self, temp_dir):
        """Test get_logger with file output."""
        log_file = temp_dir / "factory_test.log"
        logger = get_logger("test_module", log_file=str(log_file))
        
        logger.logger.info("Factory test message")
        
        assert log_file.exists()
        assert "Factory test message" in log_file.read_text()


class TestLoggingDecorators:
    """Test logging decorators."""
    
    def test_log_execution_time_decorator(self):
        """Test execution time logging decorator."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        
        logger = RobBERTLogger("test_logger")
        logger.logger.handlers = [handler]
        
        @log_execution_time(logger=logger, operation_name="test_operation")
        def test_function(x, y):
            return x + y
        
        result = test_function(2, 3)
        
        assert result == 5
        log_output = log_stream.getvalue()
        assert "Starting test_operation: test_function" in log_output
        assert "Completed test_operation: test_function" in log_output
        assert "in" in log_output and "s" in log_output  # timing info
    
    def test_log_execution_time_with_exception(self):
        """Test execution time decorator with exception."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        
        logger = RobBERTLogger("test_logger")
        logger.logger.handlers = [handler]
        
        @log_execution_time(logger=logger, operation_name="test_operation")
        def failing_function():
            raise ValueError("Test error")
        
        with pytest.raises(ValueError):
            failing_function()
        
        log_output = log_stream.getvalue()
        assert "Starting test_operation" in log_output
        assert "ValueError: Test error" in log_output
    
    def test_log_memory_usage_decorator(self):
        """Test memory usage logging decorator."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setLevel(logging.DEBUG)
        
        logger = RobBERTLogger("test_logger", level="DEBUG")
        logger.logger.handlers = [handler]
        
        @log_memory_usage(logger=logger, operation_name="test_operation")
        def test_function():
            return "success"
        
        result = test_function()
        
        assert result == "success"
        log_output = log_stream.getvalue()
        assert "Before test_operation" in log_output
        assert "After test_operation" in log_output
        assert "Memory usage" in log_output
    
    def test_safe_execute_decorator_success(self):
        """Test safe execute decorator with successful execution."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        
        logger = RobBERTLogger("test_logger")
        logger.logger.handlers = [handler]
        
        @safe_execute(logger=logger, reraise=False)
        def test_function():
            return "success"
        
        result = test_function()
        assert result == "success"
    
    def test_safe_execute_decorator_with_exception_no_reraise(self):
        """Test safe execute decorator with exception and no reraise."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        
        logger = RobBERTLogger("test_logger")
        logger.logger.handlers = [handler]
        
        @safe_execute(logger=logger, default_return="default", reraise=False)
        def failing_function():
            raise ValueError("Test error")
        
        result = failing_function()
        
        assert result == "default"
        log_output = log_stream.getvalue()
        assert "ValueError: Test error" in log_output
        assert "Returning default value" in log_output
    
    def test_safe_execute_decorator_with_exception_reraise(self):
        """Test safe execute decorator with exception and reraise."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        
        logger = RobBERTLogger("test_logger")
        logger.logger.handlers = [handler]
        
        @safe_execute(logger=logger, reraise=True)
        def failing_function():
            raise ValueError("Test error")
        
        with pytest.raises(ValueError):
            failing_function()
        
        log_output = log_stream.getvalue()
        assert "ValueError: Test error" in log_output
    
    def test_decorator_without_explicit_logger(self):
        """Test decorator without explicitly provided logger."""
        @log_execution_time(operation_name="test_operation")
        def test_function():
            return "success"
        
        # Should not raise exception and should create logger automatically
        result = test_function()
        assert result == "success"


class TestTensorLogging:
    """Test tensor-related logging utilities."""
    
    def test_validate_tensor_shape_success(self):
        """Test successful tensor shape validation."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setLevel(logging.DEBUG)
        
        logger = RobBERTLogger("test_logger", level="DEBUG")
        logger.logger.handlers = [handler]
        
        tensor = torch.randn(2, 512, 768)
        
        # Should not raise exception
        validate_tensor_shape(tensor, (2, 512, 768), "test_tensor", logger)
        
        log_output = log_stream.getvalue()
        assert "shape validation passed" in log_output
    
    def test_validate_tensor_shape_with_flexible_dims(self):
        """Test tensor shape validation with flexible dimensions."""
        logger = RobBERTLogger("test_logger")
        tensor = torch.randn(3, 256, 768)
        
        # Should not raise exception (-1 means any size allowed)
        validate_tensor_shape(tensor, (-1, -1, 768), "test_tensor", logger)
    
    def test_validate_tensor_shape_failure(self):
        """Test tensor shape validation failure."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        
        logger = RobBERTLogger("test_logger")
        logger.logger.handlers = [handler]
        
        tensor = torch.randn(2, 256, 768)
        
        with pytest.raises(DimensionMismatchError):
            validate_tensor_shape(tensor, (2, 512, 768), "test_tensor", logger)
        
        log_output = log_stream.getvalue()
        assert "DimensionMismatchError" in log_output
    
    def test_log_tensor_stats(self):
        """Test tensor statistics logging."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setLevel(logging.DEBUG)
        
        logger = RobBERTLogger("test_logger", level="DEBUG")
        logger.logger.handlers = [handler]
        
        tensor = torch.randn(2, 10, 5)
        
        log_tensor_stats(tensor, "test_tensor", logger)
        
        log_output = log_stream.getvalue()
        assert "Tensor test_tensor stats" in log_output
        assert "shape" in log_output
        assert "dtype" in log_output
        assert "device" in log_output
        assert "mean" in log_output
        assert "std" in log_output
    
    def test_log_tensor_stats_empty_tensor(self):
        """Test tensor statistics logging with empty tensor."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setLevel(logging.DEBUG)
        
        logger = RobBERTLogger("test_logger", level="DEBUG")
        logger.logger.handlers = [handler]
        
        tensor = torch.empty(0)
        
        log_tensor_stats(tensor, "empty_tensor", logger)
        
        log_output = log_stream.getvalue()
        assert "Tensor empty_tensor stats" in log_output
        assert "num_elements: 0" in log_output
    
    def test_log_tensor_stats_gpu_tensor(self):
        """Test tensor statistics logging with GPU tensor."""
        if not torch.cuda.is_available():
            pytest.skip("CUDA not available")
        
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setLevel(logging.DEBUG)
        
        logger = RobBERTLogger("test_logger", level="DEBUG")
        logger.logger.handlers = [handler]
        
        tensor = torch.randn(2, 5).cuda()
        
        log_tensor_stats(tensor, "gpu_tensor", logger)
        
        log_output = log_stream.getvalue()
        assert "cuda" in log_output.lower()
    
    def test_log_tensor_stats_with_exception(self):
        """Test tensor statistics logging with exception handling."""
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        
        logger = RobBERTLogger("test_logger")
        logger.logger.handlers = [handler]
        
        # Create a mock tensor that raises exception on operations
        mock_tensor = Mock()
        mock_tensor.shape = (2, 5)
        mock_tensor.dtype = torch.float32
        mock_tensor.device = torch.device('cpu')
        mock_tensor.requires_grad = False
        mock_tensor.numel.return_value = 10
        mock_tensor.float.side_effect = Exception("Mock error")
        
        log_tensor_stats(mock_tensor, "problematic_tensor", logger)
        
        log_output = log_stream.getvalue()
        assert "Could not compute stats" in log_output


class TestLoggingIntegration:
    """Test logging integration scenarios."""
    
    def test_multiple_loggers_isolation(self):
        """Test that multiple loggers don't interfere with each other."""
        logger1 = RobBERTLogger("logger1", level="INFO")
        logger2 = RobBERTLogger("logger2", level="DEBUG")
        
        assert logger1.logger.level == logging.INFO
        assert logger2.logger.level == logging.DEBUG
        assert logger1.logger.name != logger2.logger.name
    
    def test_logger_with_different_handlers(self, temp_dir):
        """Test logger with multiple handlers."""
        log_file = temp_dir / "multi_handler.log"
        
        # Create logger with both console and file handlers
        logger = RobBERTLogger("multi_logger", log_file=str(log_file))
        
        logger.logger.info("Test message for multiple handlers")
        
        # Check file handler worked
        assert log_file.exists()
        assert "Test message for multiple handlers" in log_file.read_text()
    
    def test_logging_performance(self):
        """Test logging performance doesn't significantly impact execution."""
        import time
        
        logger = RobBERTLogger("perf_logger", level="DEBUG")
        
        @log_execution_time(logger=logger)
        @log_memory_usage(logger=logger)
        def fast_function():
            return sum(range(1000))
        
        start_time = time.time()
        result = fast_function()
        end_time = time.time()
        
        assert result == sum(range(1000))
        # Logging overhead should be minimal (less than 100ms for this simple operation)
        assert (end_time - start_time) < 0.1
    
    def test_concurrent_logging(self):
        """Test logging in concurrent scenarios."""
        import threading
        import queue
        
        log_queue = queue.Queue()
        
        def log_worker(worker_id):
            logger = RobBERTLogger(f"worker_{worker_id}")
            for i in range(10):
                logger.logger.info(f"Worker {worker_id} message {i}")
                log_queue.put(f"worker_{worker_id}_msg_{i}")
        
        # Start multiple threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=log_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        # Check that all messages were logged
        messages = []
        while not log_queue.empty():
            messages.append(log_queue.get())
        
        assert len(messages) == 30  # 3 workers * 10 messages each


if __name__ == "__main__":
    pytest.main([__file__, "-v"])