#!/usr/bin/env python3
"""
Test script for performance baseline measurement.

This script runs a quick performance test to validate the measurement functionality.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from scripts.performance_baseline import PerformanceBenchmark


def main():
    """Run quick performance test."""
    print("Running quick performance baseline test...")
    
    # Create benchmark with minimal samples for testing
    benchmark = PerformanceBenchmark(
        output_file="test_performance_results.json",
        verbose=True
    )
    
    try:
        # Run with minimal samples for quick testing
        results = benchmark.run_full_benchmark(num_samples=5, warmup_samples=2)
        
        print("\n" + "="*50)
        print("QUICK TEST RESULTS")
        print("="*50)
        print(f"RobBERT-2023 loaded in: {results.model_loading_time:.2f}s")
        print(f"RobBERT-2023 inference speed: {results.inference_speed:.1f} tokens/sec")
        print(f"RobBERT-2023 memory usage: {results.memory_usage_mb:.1f} MB")
        print(f"RobBERT-2023 parameters: {results.num_parameters:,}")
        print(f"RobBERT-2023 throughput: {results.throughput_samples_per_sec:.1f} samples/sec")
        
        print("\nTest completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()