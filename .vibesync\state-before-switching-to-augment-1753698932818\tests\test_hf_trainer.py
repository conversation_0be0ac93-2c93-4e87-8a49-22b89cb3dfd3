"""
Tests for Hugging Face Trainer integration.
"""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from src.training.hf_trainer import HF<PERSON><PERSON>rainer, train_ner_model
from src.training.hf_config import HFTrainingConfig, create_test_hf_config
from src.training.hf_evaluation_metrics import compute_metrics


class TestHFNERTrainer:
    """Test HFNERTrainer class."""
    
    def test_trainer_initialization(self):
        """Test trainer initialization with config."""
        config = create_test_hf_config()
        trainer = HFNERTrainer(config)
        
        assert trainer.config == config
        assert trainer.model is None
        assert trainer.tokenizer is None
        assert trainer.trainer is None
    
    @patch('src.training.hf_trainer.validate_model_compatibility')
    @patch('src.training.hf_trainer.create_ner_model')
    def test_setup_model_and_tokenizer(self, mock_create_model, mock_validate):
        """Test model and tokenizer setup."""
        # Mock model creation
        mock_model = Mock()
        mock_tokenizer = Mock()
        mock_label2id = {"O": 0, "B-PER": 1, "I-PER": 2}
        mock_id2label = {0: "O", 1: "B-PER", 2: "I-PER"}
        
        mock_create_model.return_value = (mock_model, mock_tokenizer, mock_label2id, mock_id2label)
        mock_validate.return_value = True
        
        config = create_test_hf_config()
        trainer = HFNERTrainer(config)
        
        # Setup model
        model, tokenizer, label2id, id2label = trainer.setup_model_and_tokenizer()
        
        assert trainer.model == mock_model
        assert trainer.tokenizer == mock_tokenizer
        assert trainer.label2id == mock_label2id
        assert trainer.id2label == mock_id2label
        assert trainer.label_list == ["O", "B-PER", "I-PER"]
    
    def test_create_data_collator(self):
        """Test data collator creation."""
        config = create_test_hf_config()
        trainer = HFNERTrainer(config)
        
        # Mock tokenizer
        mock_tokenizer = Mock()
        trainer.tokenizer = mock_tokenizer
        
        # Create data collator
        data_collator = trainer.create_data_collator()
        
        assert data_collator is not None
        assert data_collator.tokenizer == mock_tokenizer
    
    def test_create_data_collator_without_tokenizer(self):
        """Test data collator creation fails without tokenizer."""
        config = create_test_hf_config()
        trainer = HFNERTrainer(config)
        
        with pytest.raises(Exception):  # Should raise TrainingError
            trainer.create_data_collator()
    
    @patch('src.training.hf_trainer.wandb')
    def test_setup_wandb(self, mock_wandb):
        """Test WandB setup."""
        config = create_test_hf_config()
        config.test_disable_wandb = False  # Enable WandB for this test
        config.wandb_project = "test-project"
        
        trainer = HFNERTrainer(config)
        trainer.setup_wandb()
        
        mock_wandb.init.assert_called_once()
    
    @patch('src.training.hf_trainer.wandb')
    def test_setup_wandb_disabled_in_test_mode(self, mock_wandb):
        """Test WandB is disabled in test mode."""
        config = create_test_hf_config()  # test_disable_wandb=True by default
        
        trainer = HFNERTrainer(config)
        trainer.setup_wandb()
        
        mock_wandb.init.assert_not_called()
    
    def test_create_training_arguments(self):
        """Test training arguments creation."""
        config = create_test_hf_config()
        trainer = HFNERTrainer(config)
        
        output_dir = "/tmp/test_output"
        training_args = trainer.create_training_arguments(output_dir)
        
        assert training_args.output_dir == output_dir
        assert training_args.num_train_epochs == config.epochs
        assert training_args.per_device_train_batch_size == config.batch_size
        assert training_args.learning_rate == config.learning_rate
    
    def test_create_callbacks(self):
        """Test callback creation."""
        config = create_test_hf_config()
        config.early_stopping_patience = 3
        
        trainer = HFNERTrainer(config)
        trainer.label_list = ["O", "B-PER", "I-PER"]
        
        callbacks = trainer.create_callbacks()
        
        assert len(callbacks) == 2  # Early stopping + NER metrics
    
    def test_create_callbacks_no_early_stopping(self):
        """Test callback creation without early stopping."""
        config = create_test_hf_config()
        config.early_stopping_patience = 0  # Disable early stopping
        
        trainer = HFNERTrainer(config)
        trainer.label_list = ["O", "B-PER", "I-PER"]
        
        callbacks = trainer.create_callbacks()
        
        assert len(callbacks) == 1  # Only NER metrics callback


class TestTrainNERModel:
    """Test train_ner_model convenience function."""
    
    def create_sample_data(self, temp_dir: Path) -> Path:
        """Create sample training data."""
        sample_data = [
            {
                "id": 1,
                "sentence": "Jan Jansen woont in Amsterdam.",
                "entities": [
                    {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10}
                ]
            },
            {
                "id": 2,
                "sentence": "Marie de Wit werkt bij Google.",
                "entities": [
                    {"text": "Marie de Wit", "label": "PER", "start": 0, "end": 12}
                ]
            }
        ]
        
        data_file = temp_dir / "sample_data.json"
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, indent=2)
        
        return data_file
    
    @patch('src.training.hf_trainer.HFNERTrainer')
    def test_train_ner_model_with_config(self, mock_trainer_class):
        """Test train_ner_model with configuration."""
        # Mock trainer
        mock_trainer = Mock()
        mock_trainer.train.return_value = {"status": "success"}
        mock_trainer_class.return_value = mock_trainer
        
        config = create_test_hf_config()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            data_file = self.create_sample_data(temp_path)
            
            result = train_ner_model(
                data_path=str(data_file),
                config=config
            )
            
            assert result == {"status": "success"}
            mock_trainer_class.assert_called_once_with(config)
            mock_trainer.train.assert_called_once()
    
    @patch('src.training.hf_trainer.HFNERTrainer')
    @patch('src.training.hf_trainer.HFTrainingConfig')
    def test_train_ner_model_with_config_path(self, mock_config_class, mock_trainer_class):
        """Test train_ner_model with config file path."""
        # Mock config loading
        mock_config = Mock()
        mock_config_class.from_yaml.return_value = mock_config
        
        # Mock trainer
        mock_trainer = Mock()
        mock_trainer.train.return_value = {"status": "success"}
        mock_trainer_class.return_value = mock_trainer
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            data_file = self.create_sample_data(temp_path)
            config_file = temp_path / "config.yaml"
            config_file.write_text("hf_training:\n  epochs: 2\n")
            
            result = train_ner_model(
                data_path=str(data_file),
                config_path=str(config_file)
            )
            
            assert result == {"status": "success"}
            mock_config_class.from_yaml.assert_called_once_with(str(config_file))
            mock_trainer_class.assert_called_once_with(mock_config)


class TestComputeMetrics:
    """Test compute_metrics function."""
    
    def test_compute_metrics_basic(self):
        """Test basic metrics computation."""
        # Mock evaluation prediction
        import numpy as np
        
        # Create mock predictions (batch_size=2, seq_len=5, num_labels=3)
        predictions = np.array([
            [[0.8, 0.1, 0.1], [0.1, 0.8, 0.1], [0.1, 0.1, 0.8], [0.8, 0.1, 0.1], [0.8, 0.1, 0.1]],
            [[0.8, 0.1, 0.1], [0.8, 0.1, 0.1], [0.1, 0.8, 0.1], [0.1, 0.1, 0.8], [0.8, 0.1, 0.1]]
        ])
        
        # Create mock labels (batch_size=2, seq_len=5)
        labels = np.array([
            [0, 1, 2, 0, -100],  # -100 for ignored tokens
            [0, 0, 1, 2, -100]
        ])
        
        # Mock eval_pred object
        eval_pred = Mock()
        eval_pred.predictions = predictions
        eval_pred.label_ids = labels
        
        label_list = ["O", "B-PER", "I-PER"]
        
        # Compute metrics
        metrics = compute_metrics(eval_pred, label_list)
        
        # Check that metrics are returned
        assert "precision" in metrics
        assert "recall" in metrics
        assert "f1" in metrics
        assert "accuracy" in metrics
        
        # Check that values are reasonable
        assert 0.0 <= metrics["precision"] <= 1.0
        assert 0.0 <= metrics["recall"] <= 1.0
        assert 0.0 <= metrics["f1"] <= 1.0
        assert 0.0 <= metrics["accuracy"] <= 1.0
    
    def test_compute_metrics_empty_predictions(self):
        """Test metrics computation with empty predictions."""
        import numpy as np
        
        # Empty predictions
        predictions = np.array([]).reshape(0, 0, 3)
        labels = np.array([]).reshape(0, 0)
        
        eval_pred = Mock()
        eval_pred.predictions = predictions
        eval_pred.label_ids = labels
        
        label_list = ["O", "B-PER", "I-PER"]
        
        # Should not crash
        metrics = compute_metrics(eval_pred, label_list)
        
        assert isinstance(metrics, dict)
        assert "precision" in metrics
        assert "recall" in metrics
        assert "f1" in metrics
        assert "accuracy" in metrics


if __name__ == "__main__":
    pytest.main([__file__])