"""
Tests for Hugging Face dataset preparation pipeline.
"""

import json
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from datasets import Dataset, DatasetDict
from transformers import AutoTokenizer

from src.data.hf_dataset_preparation import (
    HFDatasetPreparator,
    prepare_ner_dataset,
    create_dataset_cache_key,
    DatasetStatistics
)
from src.data.label_discovery import EntitySpan, NERExample


@pytest.fixture
def sample_ner_data():
    """Sample NER data in sentence + entities format."""
    return [
        {
            "id": 1,
            "sentence": "<PERSON> woont in Amsterdam.",
            "entities": [
                {"text": "<PERSON>", "label": "PER", "start": 0, "end": 10},
                {"text": "Amsterdam", "label": "LOC", "start": 20, "end": 29}
            ]
        },
        {
            "id": 2,
            "sentence": "<PERSON> werkt bij Google.",
            "entities": [
                {"text": "<PERSON>", "label": "PER", "start": 0, "end": 18},
                {"text": "Google", "label": "ORG", "start": 29, "end": 35}
            ]
        },
        {
            "id": 3,
            "sentence": "Dit is een zin zonder entiteiten.",
            "entities": []
        },
        {
            "id": 4,
            "sentence": "Piet de Vries bezoekt Rotterdam en Den Haag.",
            "entities": [
                {"text": "Piet de Vries", "label": "PER", "start": 0, "end": 13},
                {"text": "Rotterdam", "label": "LOC", "start": 22, "end": 31},
                {"text": "Den Haag", "label": "LOC", "start": 35, "end": 43}
            ]
        }
    ]


@pytest.fixture
def sample_json_file(sample_ner_data):
    """Create temporary JSON file with sample data."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(sample_ner_data, f, indent=2)
        return f.name


@pytest.fixture
def sample_jsonl_file(sample_ner_data):
    """Create temporary JSONL file with sample data."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False, encoding='utf-8') as f:
        for item in sample_ner_data:
            json.dump(item, f)
            f.write('\n')
        return f.name


@pytest.fixture
def mock_tokenizer():
    """Mock tokenizer for testing."""
    tokenizer = Mock()
    tokenizer.model_max_length = 512
    
    # Mock tokenization results
    def mock_tokenize(text, **kwargs):
        # Simple mock: split on spaces and add special tokens
        tokens = ['<s>'] + text.split() + ['</s>']
        input_ids = list(range(len(tokens)))
        attention_mask = [1] * len(tokens)
        
        # Mock offset mapping (character positions)
        offset_mapping = [(None, None)]  # <s>
        char_pos = 0
        for token in tokens[1:-1]:  # Skip special tokens
            start = char_pos
            end = char_pos + len(token)
            offset_mapping.append((start, end))
            char_pos = end + 1  # +1 for space
        offset_mapping.append((None, None))  # </s>
        
        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'offset_mapping': offset_mapping
        }
    
    tokenizer.side_effect = mock_tokenize
    tokenizer.convert_ids_to_tokens = lambda ids: [f'token_{i}' for i in ids]
    
    return tokenizer


class TestHFDatasetPreparator:
    """Test HFDatasetPreparator class."""
    
    def test_init_success(self):
        """Test successful initialization."""
        with patch('src.data.hf_dataset_preparation.AutoTokenizer') as mock_tokenizer_class:
            mock_tokenizer_class.from_pretrained.return_value = Mock()
            
            preparator = HFDatasetPreparator("test-model")
            
            assert preparator.model_name == "test-model"
            assert preparator.tokenizer is not None
            assert preparator.label_discovery is not None
            mock_tokenizer_class.from_pretrained.assert_called_once_with("test-model")
    
    def test_init_tokenizer_failure(self):
        """Test initialization with tokenizer loading failure."""
        with patch('src.data.hf_dataset_preparation.AutoTokenizer') as mock_tokenizer_class:
            mock_tokenizer_class.from_pretrained.side_effect = Exception("Tokenizer not found")
            
            with pytest.raises(Exception):
                HFDatasetPreparator("invalid-model")
    
    @patch('src.data.hf_dataset_preparation.load_dataset')
    def test_load_dataset_json(self, mock_load_dataset, sample_json_file):
        """Test loading JSON dataset."""
        mock_dataset = Mock()
        mock_dataset.column_names = ['id', 'sentence', 'entities']
        mock_dataset.__len__ = Mock(return_value=4)  # Add length mock
        mock_load_dataset.return_value = mock_dataset
        
        with patch('src.data.hf_dataset_preparation.AutoTokenizer'):
            preparator = HFDatasetPreparator()
            result = preparator._load_dataset(sample_json_file)
            
            assert result == mock_dataset
            mock_load_dataset.assert_called_once()
    
    @patch('src.data.hf_dataset_preparation.load_dataset')
    def test_load_dataset_missing_file(self, mock_load_dataset):
        """Test loading non-existent dataset file."""
        with patch('src.data.hf_dataset_preparation.AutoTokenizer'):
            preparator = HFDatasetPreparator()
            
            with pytest.raises(FileNotFoundError):
                preparator._load_dataset("non_existent_file.json")
    
    def test_tokenize_and_align_labels_basic(self, mock_tokenizer):
        """Test basic tokenization and label alignment."""
        with patch('src.data.hf_dataset_preparation.AutoTokenizer') as mock_tokenizer_class:
            mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
            
            preparator = HFDatasetPreparator()
            
            examples = {
                'sentence': ['Jan Jansen woont in Amsterdam.'],
                'entities': [[
                    {'text': 'Jan Jansen', 'label': 'PER', 'start': 0, 'end': 10}
                ]]
            }
            
            label2id = {'O': 0, 'B-PER': 1, 'I-PER': 2}
            
            with patch.object(preparator, 'tokenizer', mock_tokenizer):
                result = preparator.tokenize_and_align_labels(examples, label2id)
            
            assert 'input_ids' in result
            assert 'attention_mask' in result
            assert 'labels' in result
            assert len(result['input_ids']) == 1
            assert len(result['labels']) == 1
    
    def test_tokenize_and_align_labels_empty_entities(self, mock_tokenizer):
        """Test tokenization with no entities."""
        with patch('src.data.hf_dataset_preparation.AutoTokenizer') as mock_tokenizer_class:
            mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
            
            preparator = HFDatasetPreparator()
            
            examples = {
                'sentence': ['Dit is een zin zonder entiteiten.'],
                'entities': [[]]
            }
            
            label2id = {'O': 0, 'B-PER': 1, 'I-PER': 2}
            
            with patch.object(preparator, 'tokenizer', mock_tokenizer):
                result = preparator.tokenize_and_align_labels(examples, label2id)
            
            assert 'input_ids' in result
            assert 'labels' in result
            # All labels should be 'O' (id=0)
            assert all(label == 0 for label in result['labels'][0])
    
    def test_setup_labels(self):
        """Test label discovery and BIO scheme creation."""
        mock_dataset = [
            {'sentence': 'Jan woont in Amsterdam.', 'entities': [{'text': 'Jan', 'label': 'PER'}]},
            {'sentence': 'Google is in California.', 'entities': [{'text': 'Google', 'label': 'ORG'}]}
        ]
        
        with patch('src.data.hf_dataset_preparation.AutoTokenizer'):
            preparator = HFDatasetPreparator()
            
            label_list, label2id, id2label = preparator._setup_labels(mock_dataset)
            
            # Should contain O, B-ORG, I-ORG, B-PER, I-PER
            assert 'O' in label_list
            assert 'B-PER' in label_list
            assert 'I-PER' in label_list
            assert 'B-ORG' in label_list
            assert 'I-ORG' in label_list
            
            # Check mappings consistency
            assert len(label2id) == len(id2label) == len(label_list)
            for i, label in enumerate(label_list):
                assert label2id[label] == i
                assert id2label[i] == label
    
    @patch('src.data.hf_dataset_preparation.train_test_split')
    def test_split_dataset_stratified(self, mock_train_test_split):
        """Test stratified dataset splitting."""
        # Mock dataset with labels
        mock_dataset = Mock()
        mock_dataset.__len__ = Mock(return_value=4)
        mock_dataset.__iter__ = Mock(return_value=iter([
            {'labels': [0, 1, 2, 0]},  # Has entities
            {'labels': [0, 0, 0, 0]},  # No entities
            {'labels': [0, 1, 0, 0]},  # Has entities
            {'labels': [0, 0, 0, 0]}   # No entities
        ]))
        mock_dataset.select = Mock(side_effect=lambda indices: f"subset_{len(indices)}")
        
        mock_train_test_split.return_value = ([0, 2], [1, 3])
        
        with patch('src.data.hf_dataset_preparation.AutoTokenizer'):
            preparator = HFDatasetPreparator()
            
            train_dataset, val_dataset = preparator._split_dataset(
                mock_dataset, train_split=0.5, stratify_by_entities=True, random_state=42
            )
            
            assert train_dataset == "subset_2"
            assert val_dataset == "subset_2"
            mock_train_test_split.assert_called_once()
    
    def test_calculate_statistics(self):
        """Test dataset statistics calculation."""
        mock_dataset_dict = {
            'train': [
                {'input_ids': [1, 2, 3, 0, 0], 'labels': [0, 1, 2, 0, 0]},
                {'input_ids': [1, 2, 0, 0, 0], 'labels': [0, 0, 0, 0, 0]}
            ],
            'validation': [
                {'input_ids': [1, 2, 3, 4, 0], 'labels': [0, 1, 0, 0, 0]}
            ]
        }
        
        label_list = ['O', 'B-PER', 'I-PER']
        
        with patch('src.data.hf_dataset_preparation.AutoTokenizer'):
            preparator = HFDatasetPreparator()
            
            stats = preparator._calculate_statistics(mock_dataset_dict, label_list)
            
            assert isinstance(stats, DatasetStatistics)
            assert stats.total_examples == 3
            assert stats.train_size == 2
            assert stats.val_size == 1
            assert stats.entity_coverage > 0  # Should have some entities


class TestConvenienceFunctions:
    """Test convenience functions."""
    
    @patch('src.data.hf_dataset_preparation.HFDatasetPreparator')
    def test_prepare_ner_dataset(self, mock_preparator_class):
        """Test prepare_ner_dataset convenience function."""
        mock_preparator = Mock()
        mock_preparator.prepare_ner_dataset.return_value = (
            "dataset_dict", "label_list", "label2id", "id2label", "statistics"
        )
        mock_preparator_class.return_value = mock_preparator
        
        result = prepare_ner_dataset(
            data_path="test.json",
            model_name="test-model",
            train_split=0.8,
            max_length=256
        )
        
        assert result == ("dataset_dict", "label_list", "label2id", "id2label", "statistics")
        mock_preparator_class.assert_called_once_with("test-model")
        mock_preparator.prepare_ner_dataset.assert_called_once()
    
    def test_create_dataset_cache_key(self):
        """Test dataset cache key creation."""
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            f.write(b'{"test": "data"}')
            temp_path = f.name
        
        try:
            key1 = create_dataset_cache_key(
                data_path=temp_path,
                model_name="test-model",
                train_split=0.8,
                max_length=512,
                stratify_by_entities=True,
                random_state=42
            )
            
            key2 = create_dataset_cache_key(
                data_path=temp_path,
                model_name="test-model",
                train_split=0.8,
                max_length=512,
                stratify_by_entities=True,
                random_state=42
            )
            
            # Same parameters should produce same key
            assert key1 == key2
            
            # Different parameters should produce different key
            key3 = create_dataset_cache_key(
                data_path=temp_path,
                model_name="different-model",
                train_split=0.8,
                max_length=512,
                stratify_by_entities=True,
                random_state=42
            )
            
            assert key1 != key3
            
        finally:
            Path(temp_path).unlink()


class TestDatasetStatistics:
    """Test DatasetStatistics dataclass."""
    
    def test_to_dict(self):
        """Test conversion to dictionary."""
        stats = DatasetStatistics(
            total_examples=100,
            total_entities=50,
            entity_coverage=75.0,
            label_distribution={'B-PER': 20, 'I-PER': 15},
            avg_sentence_length=25.5,
            avg_entities_per_sentence=0.5,
            truncated_examples=2,
            alignment_warnings=1,
            train_size=80,
            val_size=20
        )
        
        result = stats.to_dict()
        
        assert isinstance(result, dict)
        assert result['total_examples'] == 100
        assert result['entity_coverage'] == 75.0
        assert result['label_distribution'] == {'B-PER': 20, 'I-PER': 15}


@pytest.mark.integration
class TestIntegration:
    """Integration tests with real data."""
    
    def test_end_to_end_preparation(self, sample_json_file):
        """Test end-to-end dataset preparation."""
        try:
            # This test requires actual model loading, so we'll mock it
            with patch('src.data.hf_dataset_preparation.AutoTokenizer') as mock_tokenizer_class:
                # Create a more realistic mock tokenizer
                mock_tokenizer = Mock()
                mock_tokenizer.model_max_length = 512
                
                def mock_call(text, **kwargs):
                    tokens = text.split()
                    return {
                        'input_ids': list(range(len(tokens))),
                        'attention_mask': [1] * len(tokens),
                        'offset_mapping': [(i*5, (i+1)*5) for i in range(len(tokens))]
                    }
                
                mock_tokenizer.side_effect = mock_call
                mock_tokenizer.convert_ids_to_tokens = lambda ids: [f'token_{i}' for i in ids]
                mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
                
                with patch('src.data.hf_dataset_preparation.load_dataset') as mock_load_dataset:
                    # Mock the dataset loading
                    mock_dataset = Mock()
                    mock_dataset.column_names = ['id', 'sentence', 'entities']
                    mock_dataset.__len__ = Mock(return_value=4)
                    
                    # Sample data for mocking
                    sample_data = [
                        {'id': 1, 'sentence': 'Jan Jansen woont in Amsterdam.', 'entities': [{'text': 'Jan Jansen', 'label': 'PER'}]},
                        {'id': 2, 'sentence': 'Dit is een test.', 'entities': []},
                        {'id': 3, 'sentence': 'Marie werkt bij Google.', 'entities': [{'text': 'Marie', 'label': 'PER'}, {'text': 'Google', 'label': 'ORG'}]},
                        {'id': 4, 'sentence': 'Amsterdam is mooi.', 'entities': [{'text': 'Amsterdam', 'label': 'LOC'}]}
                    ]
                    
                    mock_dataset.__iter__ = Mock(return_value=iter(sample_data))
                    mock_dataset.map = Mock(return_value=mock_dataset)
                    mock_dataset.filter = Mock(return_value=mock_dataset)
                    mock_dataset.select = Mock(return_value=mock_dataset)
                    mock_dataset.train_test_split = Mock(return_value={'train': mock_dataset, 'test': mock_dataset})
                    
                    mock_load_dataset.return_value = mock_dataset
                    
                    # Run the preparation
                    dataset_dict, label_list, label2id, id2label, statistics = prepare_ner_dataset(
                        data_path=sample_json_file,
                        train_split=0.8,
                        max_length=512
                    )
                    
                    # Verify results
                    assert isinstance(statistics, DatasetStatistics)
                    assert len(label_list) > 1  # Should have more than just 'O'
                    assert 'O' in label2id
                    assert len(label2id) == len(id2label)
                    
        except Exception as e:
            pytest.skip(f"Integration test skipped due to: {e}")