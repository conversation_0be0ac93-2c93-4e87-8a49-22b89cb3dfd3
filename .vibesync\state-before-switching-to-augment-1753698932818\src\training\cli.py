#!/usr/bin/env python3
"""
Comprehensive CLI interface for RobBERT-2023 NER training with Hugging Face Trainer.

This module provides a typer-based command-line interface for all training operations
including single-head training, multi-head training, and data preprocessing.
"""

import sys
import json
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime

import typer
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import print as rprint

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.training.hf_config import HFTrainingConfig
from src.training.hf_single_head_trainer import train_single_head
from src.training.batch_train_heads import BatchHeadTrainer
from src.training.error_handling import TrainingErrorHandler
from src.data.preprocessing import preprocess_raw_data
from src.utils.logging_utils import get_logger
from src.exceptions import TrainingError, DataProcessingError, ConfigurationError

# Initialize typer app and console
app = typer.Typer(
    name="robbert-ner-trainer",
    help="RobBERT-2023 NER Training CLI with Hugging Face Trainer integration",
    add_completion=False,
    rich_markup_mode="rich"
)
console = Console()

# Common configuration options as individual parameters
CONFIG_OPTION = typer.Option(None, "--config", "-c", help="Path to YAML configuration file")
EPOCHS_OPTION = typer.Option(None, "--epochs", "-e", help="Number of training epochs")
BATCH_SIZE_OPTION = typer.Option(None, "--batch-size", "-b", help="Training batch size")
LEARNING_RATE_OPTION = typer.Option(None, "--learning-rate", "-lr", help="Learning rate")
MAX_LENGTH_OPTION = typer.Option(None, "--max-length", help="Maximum sequence length")
OUTPUT_DIR_OPTION = typer.Option(None, "--output-dir", "-o", help="Output directory for checkpoints")
WANDB_PROJECT_OPTION = typer.Option(None, "--wandb-project", help="WandB project name")
WANDB_ENTITY_OPTION = typer.Option(None, "--wandb-entity", help="WandB entity name")
RUN_NAME_OPTION = typer.Option(None, "--run-name", help="WandB run name")
RUN_VERSION_OPTION = typer.Option(None, "--run-version", help="Training run version (e.g., v1.0, v2.1)")
TEST_MODE_OPTION = typer.Option(False, "--test-mode", help="Enable test mode (reduced epochs, sample limit, no WandB)")
USE_CLASS_WEIGHTS_OPTION = typer.Option(False, "--use-class-weights", help="Enable class weight calculation for imbalanced datasets")
EARLY_STOPPING_PATIENCE_OPTION = typer.Option(None, "--early-stopping-patience", help="Early stopping patience (epochs)")
SEED_OPTION = typer.Option(None, "--seed", help="Random seed for reproducibility")
NO_GPU_OPTION = typer.Option(False, "--no-gpu", help="Disable GPU usage (force CPU training)")
FP16_OPTION = typer.Option(False, "--fp16", help="Enable mixed precision training (FP16)")


def create_config_from_options(**options) -> HFTrainingConfig:
    """Create training configuration from CLI options."""
    # Load base configuration
    if options["config"]:
        config_path = Path(options["config"])
        if not config_path.exists():
            raise typer.BadParameter(f"Configuration file not found: {options['config']}")
        config = HFTrainingConfig.from_yaml(str(config_path))
    else:
        config = HFTrainingConfig()
    
    # Override with CLI options
    for key, value in options.items():
        if value is not None and key != "config":
            if key == "no_gpu":
                if value:
                    config.use_gpu = False
                    config.fp16 = False
            else:
                if hasattr(config, key):
                    setattr(config, key, value)
    
    return config


def validate_data_file(data_path: str) -> Path:
    """Validate that data file exists and return Path object."""
    path = Path(data_path)
    if not path.exists():
        raise typer.BadParameter(f"Data file not found: {data_path}")
    return path


def validate_entity_types(entity_types: List[str]) -> List[str]:
    """Validate entity types."""
    valid_types = {"PER", "LOC", "ORG", "MISC"}
    invalid_types = set(entity_types) - valid_types
    if invalid_types:
        raise typer.BadParameter(f"Invalid entity types: {invalid_types}. Valid types: {valid_types}")
    return entity_types


@app.command("single-head")
def train_single_head_cmd(
    entity_type: str = typer.Argument(
        ...,
        help="Entity type to train (PER, LOC, ORG, MISC)"
    ),
    data: str = typer.Argument(
        ...,
        help="Path to training data (JSON/JSONL file with sentence + entities format)"
    ),
    filtering_strategy: str = typer.Option(
        "strict",
        "--filtering-strategy",
        help="Dataset filtering strategy"
    ),
    train_split: float = typer.Option(
        0.8,
        "--train-split",
        help="Training split ratio"
    ),
    resume_from_checkpoint: Optional[str] = typer.Option(
        None,
        "--resume-from-checkpoint",
        help="Path to checkpoint to resume training from"
    ),
    # Configuration options
    config: Optional[str] = CONFIG_OPTION,
    epochs: Optional[int] = EPOCHS_OPTION,
    batch_size: Optional[int] = BATCH_SIZE_OPTION,
    learning_rate: Optional[float] = LEARNING_RATE_OPTION,
    max_length: Optional[int] = MAX_LENGTH_OPTION,
    output_dir: Optional[str] = OUTPUT_DIR_OPTION,
    wandb_project: Optional[str] = WANDB_PROJECT_OPTION,
    wandb_entity: Optional[str] = WANDB_ENTITY_OPTION,
    run_name: Optional[str] = RUN_NAME_OPTION,
    run_version: Optional[str] = RUN_VERSION_OPTION,
    test_mode: bool = TEST_MODE_OPTION,
    use_class_weights: bool = USE_CLASS_WEIGHTS_OPTION,
    early_stopping_patience: Optional[int] = EARLY_STOPPING_PATIENCE_OPTION,
    seed: Optional[int] = SEED_OPTION,
    no_gpu: bool = NO_GPU_OPTION,
    fp16: bool = FP16_OPTION
):
    """
    Train a single entity type head with isolation.
    
    This command trains a model for a specific entity type (PER, LOC, ORG, MISC)
    using dataset filtering to focus on that entity type only.
    
    Examples:
    
        # Train PER head with strict filtering
        robbert-ner-trainer single-head PER data_sets/dutch_ner.json
        
        # Train LOC head with mixed filtering and custom config
        robbert-ner-trainer single-head LOC data_sets/dutch_ner.json --filtering-strategy mixed --config config/training.yaml
        
        # Resume training from checkpoint
        robbert-ner-trainer single-head PER data_sets/dutch_ner.json --resume-from-checkpoint checkpoints/PER_20250126_143022_v1.0/checkpoint-500
    """
    logger = get_logger("cli.single_head")
    error_handler = TrainingErrorHandler(logger)
    
    try:
        # Validate inputs with enhanced error messages
        if entity_type not in ["PER", "LOC", "ORG", "MISC"]:
            raise typer.BadParameter(f"Invalid entity type: {entity_type}. Must be one of: PER, LOC, ORG, MISC")
        
        if filtering_strategy not in ["strict", "mixed"]:
            raise typer.BadParameter(f"Invalid filtering strategy: {filtering_strategy}. Must be 'strict' or 'mixed'")
        
        if not 0.0 < train_split < 1.0:
            raise typer.BadParameter(f"Train split must be between 0.0 and 1.0, got: {train_split}")
        
        # Validate data file with comprehensive error handling
        try:
            data_path = validate_data_file(data)
            # Test loading the data to catch issues early
            data_records = error_handler.data_handler.safe_json_load(data)
            console.print(f"[green]✓[/green] Loaded {len(data_records)} records from {data}")
        except (DataProcessingError, FileNotFoundError) as e:
            console.print(f"[red]✗[/red] Data file error: {e}")
            raise typer.Exit(1)
        
        if resume_from_checkpoint:
            checkpoint_path = Path(resume_from_checkpoint)
            if not checkpoint_path.exists():
                console.print(f"[red]✗[/red] Checkpoint not found: {resume_from_checkpoint}")
                raise typer.Exit(1)
            console.print(f"[green]✓[/green] Checkpoint found: {resume_from_checkpoint}")
        
        # Create configuration with error handling
        try:
            config_dict = {
                "config": config,
                "epochs": epochs,
                "batch_size": batch_size,
                "learning_rate": learning_rate,
                "max_length": max_length,
                "output_dir": output_dir,
                "wandb_project": wandb_project,
                "wandb_entity": wandb_entity,
                "run_name": run_name,
                "run_version": run_version,
                "test_mode": test_mode,
                "use_class_weights": use_class_weights,
                "early_stopping_patience": early_stopping_patience,
                "seed": seed,
                "no_gpu": no_gpu,
                "fp16": fp16
            }
            config = create_config_from_options(**config_dict)
            console.print("[green]✓[/green] Configuration created successfully")
        except ConfigurationError as e:
            console.print(f"[red]✗[/red] Configuration error: {e}")
            raise typer.Exit(1)
        config = create_config_from_options(**config_dict)
        
        # Display training setup
        console.print("\n[bold blue]Single-Head Training Configuration[/bold blue]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Parameter", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Entity Type", entity_type)
        table.add_row("Data Path", str(data_path))
        table.add_row("Filtering Strategy", filtering_strategy)
        table.add_row("Train Split", f"{train_split:.1%}")
        table.add_row("Epochs", str(config.epochs))
        table.add_row("Batch Size", str(config.batch_size))
        table.add_row("Learning Rate", f"{config.learning_rate:.2e}")
        table.add_row("Max Length", str(config.max_length))
        table.add_row("Test Mode", str(config.test_mode))
        table.add_row("Use GPU", str(config.use_gpu))
        table.add_row("Mixed Precision", str(config.fp16))
        
        if output_dir:
            table.add_row("Output Directory", output_dir)
        
        if resume_from_checkpoint:
            table.add_row("Resume From", resume_from_checkpoint)
        
        console.print(table)
        console.print()
        
        # Check system resources before training
        logger.log_system_info()
        
        # Check WandB availability
        wandb_available = error_handler.wandb_handler.check_wandb_availability()
        if not wandb_available and not test_mode:
            console.print("[yellow]⚠[/yellow] WandB not available, using fallback logging")
        
        # Start training with progress indicator and error handling
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task(f"Training {entity_type} head...", total=None)
            
            try:
                results = train_single_head(
                    entity_type=entity_type,
                    data_path=str(data_path),
                    filtering_strategy=filtering_strategy,
                    config=config,
                    output_dir=output_dir,
                    resume_from_checkpoint=resume_from_checkpoint
                )
                
                progress.update(task, description=f"✅ {entity_type} head training completed")
                
            except TrainingError as e:
                progress.update(task, description=f"❌ {entity_type} head training failed")
                console.print(f"\n[red]Training Error: {e}[/red]")
                
                # Show error summary if available
                if hasattr(e, 'details') and e.details:
                    console.print(f"[red]Details: {e.details}[/red]")
                
                # Show recovery suggestions
                console.print("\n[yellow]Suggestions for resolution:[/yellow]")
                console.print("1. Check data format and entity annotations")
                console.print("2. Verify sufficient memory (reduce batch size if needed)")
                console.print("3. Check model and tokenizer compatibility")
                console.print("4. Review configuration parameters")
                
                raise typer.Exit(1)
                
            except Exception as e:
                progress.update(task, description=f"❌ {entity_type} head training failed")
                console.print(f"\n[red]Unexpected error: {e}[/red]")
                logger.log_error(e, {"entity_type": entity_type, "data_path": str(data_path)})
                raise typer.Exit(1)
        
        # Display results
        console.print(f"\n[bold green]Training completed successfully![/bold green]")
        console.print(f"Model saved to: [cyan]{results['output_dir']}[/cyan]")
        
        if results.get('wandb_run_name'):
            console.print(f"WandB run: [cyan]{results['wandb_run_name']}[/cyan]")
        
        # Display error summary if any errors occurred
        if "error_summary" in results:
            error_summary = results["error_summary"]
            if error_summary.get("total_errors", 0) > 0:
                console.print(f"\n[yellow]⚠ Training completed with {error_summary['total_errors']} recoverable errors[/yellow]")
                console.print(f"Error report saved to: [cyan]{results['output_dir']}/error_report.json[/cyan]")
        
        # Display final metrics
        if "eval_result" in results:
            console.print("\n[bold blue]Final Evaluation Metrics[/bold blue]")
            metrics_table = Table(show_header=True, header_style="bold magenta")
            metrics_table.add_column("Metric", style="cyan")
            metrics_table.add_column("Value", style="green")
            
            for key, value in results["eval_result"].items():
                if isinstance(value, (int, float)):
                    metrics_table.add_row(key, f"{value:.4f}")
            
            console.print(metrics_table)
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Training interrupted by user[/yellow]")
        
        # Save error report if possible
        if hasattr(error_handler, 'output_dir') and error_handler.output_dir:
            error_report_path = Path(error_handler.output_dir) / "error_report_interrupted.json"
            error_handler.save_error_report(str(error_report_path))
            console.print(f"Error report saved to: [cyan]{error_report_path}[/cyan]")
        
        raise typer.Exit(1)
        
    except typer.Exit:
        # Re-raise typer exits (already handled above)
        raise
        
    except Exception as e:
        console.print(f"\n[red]Unexpected CLI error: {e}[/red]")
        logger.log_error(e, {
            "command": "single-head",
            "entity_type": entity_type,
            "data_path": data
        })
        raise typer.Exit(1)


@app.command("multi-head")
def train_multi_head_cmd(
    entity_types: List[str] = typer.Argument(
        ...,
        help="Entity types to train (space-separated: PER LOC ORG)"
    ),
    data: str = typer.Argument(
        ...,
        help="Path to training data (JSON/JSONL file)"
    ),
    filtering_strategy: str = typer.Option(
        "strict",
        "--filtering-strategy",
        help="Dataset filtering strategy"
    ),
    parallel: bool = typer.Option(
        False,
        "--parallel",
        help="Train heads in parallel (default: sequential)"
    ),
    max_workers: Optional[int] = typer.Option(
        None,
        "--max-workers",
        help="Maximum parallel workers (auto-detect if not specified)"
    ),
    # Configuration options
    config: Optional[str] = CONFIG_OPTION,
    epochs: Optional[int] = EPOCHS_OPTION,
    batch_size: Optional[int] = BATCH_SIZE_OPTION,
    learning_rate: Optional[float] = LEARNING_RATE_OPTION,
    max_length: Optional[int] = MAX_LENGTH_OPTION,
    output_dir: Optional[str] = OUTPUT_DIR_OPTION,
    wandb_project: Optional[str] = WANDB_PROJECT_OPTION,
    wandb_entity: Optional[str] = WANDB_ENTITY_OPTION,
    run_name: Optional[str] = RUN_NAME_OPTION,
    run_version: Optional[str] = RUN_VERSION_OPTION,
    test_mode: bool = TEST_MODE_OPTION,
    use_class_weights: bool = USE_CLASS_WEIGHTS_OPTION,
    early_stopping_patience: Optional[int] = EARLY_STOPPING_PATIENCE_OPTION,
    seed: Optional[int] = SEED_OPTION,
    no_gpu: bool = NO_GPU_OPTION,
    fp16: bool = FP16_OPTION
):
    """
    Train multiple entity type heads.
    
    This command trains models for multiple entity types either sequentially
    or in parallel, with consolidated reporting.
    
    Examples:
    
        # Train all heads sequentially
        robbert-ner-trainer multi-head PER LOC ORG data_sets/dutch_ner.json
        
        # Train heads in parallel
        robbert-ner-trainer multi-head PER LOC data_sets/dutch_ner.json --parallel --max-workers 2
        
        # Train with custom config and mixed filtering
        robbert-ner-trainer multi-head PER LOC ORG data_sets/dutch_ner.json --config config/training.yaml --filtering-strategy mixed
    """
    logger = get_logger("cli.multi_head")
    
    try:
        # Validate inputs
        entity_types = validate_entity_types(entity_types)
        
        if filtering_strategy not in ["strict", "mixed"]:
            raise typer.BadParameter(f"Invalid filtering strategy: {filtering_strategy}. Must be 'strict' or 'mixed'")
        
        data_path = validate_data_file(data)
        
        if max_workers is not None and max_workers <= 0:
            raise typer.BadParameter(f"Max workers must be positive, got: {max_workers}")
        
        # Create configuration
        config_dict = {
            "config": config,
            "epochs": epochs,
            "batch_size": batch_size,
            "learning_rate": learning_rate,
            "max_length": max_length,
            "output_dir": output_dir,
            "wandb_project": wandb_project,
            "wandb_entity": wandb_entity,
            "run_name": run_name,
            "run_version": run_version,
            "test_mode": test_mode,
            "use_class_weights": use_class_weights,
            "early_stopping_patience": early_stopping_patience,
            "seed": seed,
            "no_gpu": no_gpu,
            "fp16": fp16
        }
        config = create_config_from_options(**config_dict)
        
        # Display training setup
        console.print("\n[bold blue]Multi-Head Training Configuration[/bold blue]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Parameter", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Entity Types", ", ".join(entity_types))
        table.add_row("Data Path", str(data_path))
        table.add_row("Filtering Strategy", filtering_strategy)
        table.add_row("Training Mode", "Parallel" if parallel else "Sequential")
        table.add_row("Epochs", str(config.epochs))
        table.add_row("Batch Size", str(config.batch_size))
        table.add_row("Learning Rate", f"{config.learning_rate:.2e}")
        table.add_row("Test Mode", str(config.test_mode))
        
        if parallel:
            table.add_row("Max Workers", str(max_workers or "auto-detect"))
        
        console.print(table)
        console.print()
        
        # Create batch trainer
        batch_trainer = BatchHeadTrainer(config, output_dir)
        
        # Start training with progress indicator
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task(f"Training {len(entity_types)} heads...", total=None)
            
            if parallel:
                results = batch_trainer.train_heads_parallel(
                    entity_types=entity_types,
                    data_path=str(data_path),
                    filtering_strategy=filtering_strategy,
                    max_workers=max_workers
                )
            else:
                results = batch_trainer.train_heads_sequential(
                    entity_types=entity_types,
                    data_path=str(data_path),
                    filtering_strategy=filtering_strategy
                )
            
            progress.update(task, description="✅ Multi-head training completed")
        
        # Display results summary
        successful = sum(1 for r in results.values() if r["status"] == "completed")
        failed = len(results) - successful
        
        console.print(f"\n[bold green]Multi-head training completed![/bold green]")
        console.print(f"Successful heads: [green]{successful}/{len(results)}[/green]")
        console.print(f"Failed heads: [red]{failed}[/red]")
        
        # Display individual results
        console.print("\n[bold blue]Individual Results[/bold blue]")
        results_table = Table(show_header=True, header_style="bold magenta")
        results_table.add_column("Entity Type", style="cyan")
        results_table.add_column("Status", style="green")
        results_table.add_column("Output Directory", style="yellow")
        
        for entity_type, result in results.items():
            status_icon = "✅" if result["status"] == "completed" else "❌"
            status = f"{status_icon} {result['status'].title()}"
            output_dir = result.get("output_dir", "N/A")
            results_table.add_row(entity_type, status, output_dir)
        
        console.print(results_table)
        
        if failed > 0:
            raise typer.Exit(1)
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Training interrupted by user[/yellow]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"\n[red]Multi-head training failed: {e}[/red]")
        logger.logger.error(f"Multi-head training failed: {e}")
        raise typer.Exit(1)


@app.command("preprocess")
def preprocess_data_cmd(
    input_path: str = typer.Argument(
        ...,
        help="Path to input data file (JSON/JSONL with sentence + entities format)"
    ),
    output_path: str = typer.Argument(
        ...,
        help="Path to output preprocessed data file (JSON)"
    ),
    model_name: str = typer.Option(
        "DTAI-KULeuven/robbert-2023-dutch-base",
        "--model-name",
        help="Hugging Face model name for tokenizer"
    ),
    max_length: int = typer.Option(
        512,
        "--max-length",
        help="Maximum sequence length"
    ),
    validate_output: bool = typer.Option(
        True,
        "--validate-output/--no-validate-output",
        help="Validate output format after preprocessing"
    )
):
    """
    Preprocess raw data from sentence + entities format to tokenized format.
    
    This command converts raw training data with sentences and entity annotations
    to the tokenized format required for Hugging Face Trainer.
    
    Input format:
    {
        "id": 1,
        "sentence": "Jan Jansen woont in Amsterdam.",
        "entities": [
            {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10}
        ]
    }
    
    Output format:
    {
        "id": 1,
        "sentence": "Jan Jansen woont in Amsterdam.",
        "tokens": ["<s>", "Jan", "Jansen", "woont", "In", "Amsterdam", ".", "</s>"],
        "labels": ["O", "B-PER", "I-PER", "O", "O", "O", "O", "O"],
        "input_ids": [0, 2335, 8554, 23, 4, 812, 5, 2],
        "attention_mask": [1, 1, 1, 1, 1, 1, 1, 1]
    }
    
    Examples:
    
        # Basic preprocessing
        robbert-ner-trainer preprocess data_sets/raw_dutch_ner.json data_sets/processed_dutch_ner.json
        
        # Custom model and max length
        robbert-ner-trainer preprocess data_sets/raw_dutch_ner.json data_sets/processed_dutch_ner.json --model-name DTAI-KULeuven/robbert-2023-dutch-base --max-length 256
    """
    logger = get_logger("cli.preprocess")
    
    try:
        # Validate inputs
        input_file = validate_data_file(input_path)
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        if max_length <= 0:
            raise typer.BadParameter(f"Max length must be positive, got: {max_length}")
        
        # Display preprocessing setup
        console.print("\n[bold blue]Data Preprocessing Configuration[/bold blue]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Parameter", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Input Path", str(input_file))
        table.add_row("Output Path", str(output_file))
        table.add_row("Model Name", model_name)
        table.add_row("Max Length", str(max_length))
        table.add_row("Validate Output", str(validate_output))
        
        console.print(table)
        console.print()
        
        # Start preprocessing with progress indicator
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Preprocessing data...", total=None)
            
            result_path = preprocess_raw_data(
                input_path=str(input_file),
                output_path=str(output_file),
                model_name=model_name,
                max_length=max_length
            )
            
            progress.update(task, description="✅ Preprocessing completed")
        
        # Validate output if requested
        if validate_output:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task("Validating output...", total=None)
                
                # Load and validate output
                with open(result_path, 'r', encoding='utf-8') as f:
                    processed_data = json.load(f)
                
                if not isinstance(processed_data, list):
                    raise ValueError("Output should be a list of examples")
                
                # Validate first few examples
                required_fields = ["tokens", "labels", "input_ids", "attention_mask"]
                for i, example in enumerate(processed_data[:5]):  # Check first 5
                    for field in required_fields:
                        if field not in example:
                            raise ValueError(f"Missing field '{field}' in example {i}")
                
                progress.update(task, description="✅ Output validation completed")
        
        # Display results
        console.print(f"\n[bold green]Preprocessing completed successfully![/bold green]")
        console.print(f"Processed data saved to: [cyan]{result_path}[/cyan]")
        
        # Show basic statistics
        with open(result_path, 'r', encoding='utf-8') as f:
            processed_data = json.load(f)
        
        console.print(f"Total examples processed: [green]{len(processed_data)}[/green]")
        
        if processed_data:
            avg_tokens = sum(len(ex.get("tokens", [])) for ex in processed_data) / len(processed_data)
            console.print(f"Average tokens per example: [green]{avg_tokens:.1f}[/green]")
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Preprocessing interrupted by user[/yellow]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"\n[red]Preprocessing failed: {e}[/red]")
        logger.logger.error(f"Data preprocessing failed: {e}")
        raise typer.Exit(1)


@app.command("config")
def create_config_cmd(
    output_path: str = typer.Argument(
        "config/hf_training.yaml",
        help="Path to output configuration file"
    ),
    template: str = typer.Option(
        "default",
        "--template",
        help="Configuration template (default, test, production)"
    )
):
    """
    Create a configuration file template.
    
    This command generates a YAML configuration file with default or customized
    settings for training.
    
    Examples:
    
        # Create default config
        robbert-ner-trainer config config/training.yaml
        
        # Create test mode config
        robbert-ner-trainer config config/test.yaml --template test
        
        # Create production config
        robbert-ner-trainer config config/production.yaml --template production
    """
    try:
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Create configuration based on template
        if template == "default":
            config = HFTrainingConfig()
        elif template == "test":
            config = HFTrainingConfig(
                test_mode=True,
                test_epochs=1,
                test_sample_limit=50,
                eval_steps=10,
                logging_steps=5,
                save_steps=20,
                early_stopping_patience=1,
            )
        elif template == "production":
            config = HFTrainingConfig(
                epochs=5,
                batch_size=16,
                learning_rate=2e-5,
                eval_steps=200,
                logging_steps=100,
                save_steps=1000,
                early_stopping_patience=5,
                use_class_weights=True,
                wandb_log_confusion_matrix=True,
                wandb_log_per_class_metrics=True,
            )
        else:
            raise typer.BadParameter(f"Invalid template: {template}. Must be one of: default, test, production")
        
        # Save configuration
        config.save_yaml(str(output_file))
        
        console.print(f"\n[bold green]Configuration file created![/bold green]")
        console.print(f"Template: [cyan]{template}[/cyan]")
        console.print(f"Saved to: [cyan]{output_file}[/cyan]")
        
        # Display key settings
        console.print("\n[bold blue]Key Configuration Settings[/bold blue]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Parameter", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Epochs", str(config.epochs))
        table.add_row("Batch Size", str(config.batch_size))
        table.add_row("Learning Rate", f"{config.learning_rate:.2e}")
        table.add_row("Max Length", str(config.max_length))
        table.add_row("Early Stopping Patience", str(config.early_stopping_patience))
        table.add_row("Use Class Weights", str(config.use_class_weights))
        table.add_row("Test Mode", str(config.test_mode))
        
        console.print(table)
        
    except Exception as e:
        console.print(f"\n[red]Failed to create configuration: {e}[/red]")
        raise typer.Exit(1)


@app.command("validate")
def validate_config_cmd(
    config_path: str = typer.Argument(
        ...,
        help="Path to configuration file to validate"
    )
):
    """
    Validate a configuration file.
    
    This command loads and validates a YAML configuration file to ensure
    all parameters are correct and compatible.
    
    Examples:
    
        # Validate configuration
        robbert-ner-trainer validate config/training.yaml
    """
    try:
        config_file = Path(config_path)
        if not config_file.exists():
            raise typer.BadParameter(f"Configuration file not found: {config_path}")
        
        # Load and validate configuration
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Validating configuration...", total=None)
            
            config = HFTrainingConfig.from_yaml(str(config_file))
            
            progress.update(task, description="✅ Configuration validation completed")
        
        console.print(f"\n[bold green]Configuration is valid![/bold green]")
        console.print(f"File: [cyan]{config_file}[/cyan]")
        
        # Display configuration summary
        console.print("\n[bold blue]Configuration Summary[/bold blue]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Parameter", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Model Name", config.model_name)
        table.add_row("Epochs", str(config.epochs))
        table.add_row("Batch Size", str(config.batch_size))
        table.add_row("Learning Rate", f"{config.learning_rate:.2e}")
        table.add_row("Max Length", str(config.max_length))
        table.add_row("Output Directory", config.output_dir)
        table.add_row("WandB Project", config.wandb_project)
        table.add_row("Run Version", config.run_version)
        table.add_row("Test Mode", str(config.test_mode))
        table.add_row("Use GPU", str(config.use_gpu))
        table.add_row("Mixed Precision", str(config.fp16))
        
        console.print(table)
        
    except Exception as e:
        console.print(f"\n[red]Configuration validation failed: {e}[/red]")
        raise typer.Exit(1)


def main():
    """Main CLI entry point."""
    app()


if __name__ == "__main__":
    main()