"""
Command-line interface for Hugging Face NER training.

This script provides a CLI for training NER models using the Hugging Face Trainer
with comprehensive configuration support and WandB integration.
"""

import argparse
import sys
from pathlib import Path
from typing import Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.training.hf_trainer import train_ner_model
from src.training.hf_config import HFTrainingConfig, create_test_hf_config
from src.utils.logging_utils import get_logger


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Train NER model using Hugging Face Trainer",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Required arguments
    parser.add_argument(
        "data_path",
        type=str,
        help="Path to training data (JSON/JSONL file)"
    )
    
    # Configuration arguments
    parser.add_argument(
        "--config",
        type=str,
        help="Path to YAML configuration file"
    )
    
    parser.add_argument(
        "--output-dir",
        type=str,
        help="Output directory for model checkpoints (auto-generated if not provided)"
    )
    
    parser.add_argument(
        "--resume-from-checkpoint",
        type=str,
        help="Path to checkpoint to resume training from"
    )
    
    # Training parameters (override config)
    parser.add_argument(
        "--epochs",
        type=int,
        help="Number of training epochs"
    )
    
    parser.add_argument(
        "--batch-size",
        type=int,
        help="Training batch size"
    )
    
    parser.add_argument(
        "--learning-rate",
        type=float,
        help="Learning rate"
    )
    
    parser.add_argument(
        "--model-name",
        type=str,
        default="DTAI-KULeuven/robbert-2023-dutch-base",
        help="Hugging Face model name"
    )
    
    # Special modes
    parser.add_argument(
        "--test-mode",
        action="store_true",
        help="Run in test mode with reduced dataset and epochs"
    )
    
    parser.add_argument(
        "--no-wandb",
        action="store_true",
        help="Disable WandB logging"
    )
    
    # WandB configuration
    parser.add_argument(
        "--wandb-project",
        type=str,
        help="WandB project name"
    )
    
    parser.add_argument(
        "--wandb-entity",
        type=str,
        help="WandB entity name"
    )
    
    parser.add_argument(
        "--run-name",
        type=str,
        help="WandB run name"
    )
    
    parser.add_argument(
        "--run-version",
        type=str,
        help="Training run version"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logger = get_logger(__name__)
    
    try:
        # Load configuration
        if args.config:
            logger.logger.info(f"Loading configuration from: {args.config}")
            config = HFTrainingConfig.from_yaml(args.config)
        elif args.test_mode:
            logger.logger.info("Using test mode configuration")
            config = create_test_hf_config()
        else:
            logger.logger.info("Using default configuration")
            config = HFTrainingConfig()
        
        # Override configuration with CLI arguments
        config = _override_config_with_args(config, args)
        
        # Validate data path
        data_path = Path(args.data_path)
        if not data_path.exists():
            raise FileNotFoundError(f"Training data not found: {data_path}")
        
        logger.logger.info(f"Starting training with data: {data_path}")
        logger.logger.info(f"Configuration: {config.to_dict()}")
        
        # Run training
        results = train_ner_model(
            data_path=str(data_path),
            config=config,
            output_dir=args.output_dir,
            resume_from_checkpoint=args.resume_from_checkpoint
        )
        
        # Log results
        logger.logger.info("Training completed successfully!")
        logger.logger.info(f"Model saved to: {results['output_dir']}")
        
        # Print key metrics
        eval_result = results.get("eval_result", {})
        if eval_result:
            print("\nFinal Evaluation Results:")
            for metric, value in eval_result.items():
                if isinstance(value, (int, float)):
                    print(f"  {metric}: {value:.4f}")
        
        return 0
        
    except Exception as e:
        logger.logger.error(f"Training failed: {e}")
        return 1


def _override_config_with_args(config: HFTrainingConfig, args: argparse.Namespace) -> HFTrainingConfig:
    """Override configuration with command-line arguments."""
    
    # Training parameters
    if args.epochs is not None:
        config.epochs = args.epochs
    
    if args.batch_size is not None:
        config.batch_size = args.batch_size
    
    if args.learning_rate is not None:
        config.learning_rate = args.learning_rate
    
    if args.model_name:
        config.model_name = args.model_name
    
    # Special modes
    if args.test_mode:
        config.test_mode = True
    
    if args.no_wandb:
        config.report_to = None
    
    # WandB configuration
    if args.wandb_project:
        config.wandb_project = args.wandb_project
    
    if args.wandb_entity:
        config.wandb_entity = args.wandb_entity
    
    if args.run_name:
        config.run_name = args.run_name
    
    if args.run_version:
        config.run_version = args.run_version
    
    return config


if __name__ == "__main__":
    sys.exit(main())