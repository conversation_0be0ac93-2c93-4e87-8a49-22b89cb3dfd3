"""
Checkpoint management utilities for RobBERT-2023 fine-tuned models.

This module provides comprehensive checkpoint management including:
- Saving and loading RobBERT-2023 fine-tuned models
- Directory structure management for organized model artifacts
- Checkpoint validation and compatibility checking
- Model metadata and versioning support
"""

import os
import json
import shutil
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import torch
from transformers import AutoConfig, AutoTokenizer
import logging

from ..models.multitask_robbert import MultiTaskRobBERT
from ..config import Config
from ..exceptions import ModelLoadingError
from ..utils.logging_utils import get_logger


class CheckpointManager:
    """Manages RobBERT-2023 model checkpoints with validation and organization."""
    
    def __init__(self, base_dir: str = "models"):
        """
        Initialize checkpoint manager.
        
        Args:
            base_dir: Base directory for model storage
        """
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.base_dir = Path(base_dir)
        self.robbert_dir = self.base_dir / "robbert2023-per"
        
        # Create directory structure
        self._create_directory_structure()
        
        # Supported model versions and compatibility
        self.supported_models = {
            "DTAI-KULeuven/robbert-2023-dutch-base": {
                "tokenizer_type": "byte_level_bpe",
                "vocab_size": 50265,
                "hidden_size": 768,
                "num_attention_heads": 12,
                "num_hidden_layers": 12
            }
        }
        
        self.logger.logger.info(f"Initialized checkpoint manager with base directory: {self.base_dir}")
    
    def _create_directory_structure(self):
        """Create organized directory structure for RobBERT checkpoints."""
        directories = [
            self.robbert_dir,
            self.robbert_dir / "fine-tuned",
            self.robbert_dir / "base-models", 
            self.robbert_dir / "experiments",
            self.robbert_dir / "backups"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            self.logger.logger.debug(f"Created directory: {directory}")
    
    def save_checkpoint(self, 
                       model: MultiTaskRobBERT, 
                       checkpoint_name: str,
                       metadata: Optional[Dict[str, Any]] = None,
                       experiment_name: Optional[str] = None) -> Path:
        """
        Save RobBERT-2023 model checkpoint with metadata and validation.
        
        Args:
            model: MultiTaskRobBERT model to save
            checkpoint_name: Name for the checkpoint
            metadata: Additional metadata to save with checkpoint
            experiment_name: Optional experiment name for organization
            
        Returns:
            Path to saved checkpoint directory
            
        Raises:
            ModelLoadingError: If saving fails
        """
        try:
            # Determine save directory
            if experiment_name:
                save_dir = self.robbert_dir / "experiments" / experiment_name / checkpoint_name
            else:
                save_dir = self.robbert_dir / "fine-tuned" / checkpoint_name
            
            save_dir.mkdir(parents=True, exist_ok=True)
            self.logger.logger.info(f"Saving checkpoint to: {save_dir}")
            
            # Save model using existing save_pretrained method
            model.save_pretrained(str(save_dir))
            
            # Create comprehensive metadata
            checkpoint_metadata = self._create_checkpoint_metadata(model, metadata)
            
            # Save metadata
            metadata_path = save_dir / "checkpoint_metadata.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_metadata, f, indent=2, ensure_ascii=False)
            
            # Create checkpoint manifest
            manifest = self._create_checkpoint_manifest(save_dir, checkpoint_metadata)
            manifest_path = save_dir / "checkpoint_manifest.json"
            with open(manifest_path, 'w', encoding='utf-8') as f:
                json.dump(manifest, f, indent=2)
            
            # Validate saved checkpoint
            if not self.validate_checkpoint(save_dir):
                raise ModelLoadingError(f"Checkpoint validation failed after saving", 
                                      checkpoint_path=str(save_dir))
            
            self.logger.logger.info(f"Successfully saved checkpoint: {save_dir}")
            return save_dir
            
        except Exception as e:
            error_msg = f"Failed to save checkpoint {checkpoint_name}"
            self.logger.logger.error(f"{error_msg}: {e}")
            raise ModelLoadingError(error_msg, checkpoint_path=checkpoint_name) from e
    
    def load_checkpoint(self, checkpoint_path: str, **kwargs) -> MultiTaskRobBERT:
        """
        Load RobBERT-2023 model checkpoint with validation.
        
        Args:
            checkpoint_path: Path to checkpoint directory or name
            **kwargs: Additional arguments for model loading
            
        Returns:
            Loaded MultiTaskRobBERT model
            
        Raises:
            ModelLoadingError: If loading or validation fails
        """
        try:
            # Resolve checkpoint path
            resolved_path = self._resolve_checkpoint_path(checkpoint_path)
            
            if not resolved_path.exists():
                raise ModelLoadingError(f"Checkpoint not found: {checkpoint_path}",
                                      checkpoint_path=str(resolved_path))
            
            self.logger.logger.info(f"Loading checkpoint from: {resolved_path}")
            
            # Validate checkpoint before loading
            if not self.validate_checkpoint(resolved_path):
                raise ModelLoadingError(f"Checkpoint validation failed: {checkpoint_path}",
                                      checkpoint_path=str(resolved_path))
            
            # Load model
            model = MultiTaskRobBERT.load_pretrained(str(resolved_path), **kwargs)
            
            # Load and log metadata
            metadata_path = resolved_path / "checkpoint_metadata.json"
            if metadata_path.exists():
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                self.logger.logger.info(f"Loaded checkpoint metadata: {metadata.get('checkpoint_name', 'unknown')}")
                self.logger.logger.debug(f"Model heads: {metadata.get('model_heads', [])}")
            
            self.logger.logger.info(f"Successfully loaded checkpoint: {resolved_path}")
            return model
            
        except ModelLoadingError:
            raise
        except Exception as e:
            error_msg = f"Failed to load checkpoint {checkpoint_path}"
            self.logger.logger.error(f"{error_msg}: {e}")
            raise ModelLoadingError(error_msg, checkpoint_path=checkpoint_path) from e
    
    def validate_checkpoint(self, checkpoint_path: Path) -> bool:
        """
        Validate RobBERT-2023 checkpoint integrity and compatibility.
        
        Args:
            checkpoint_path: Path to checkpoint directory
            
        Returns:
            True if checkpoint is valid, False otherwise
        """
        try:
            self.logger.logger.debug(f"Validating checkpoint: {checkpoint_path}")
            
            # Check required files exist
            required_files = [
                "config.json",
                "pytorch_model.bin",
                "tokenizer_config.json",
                "vocab.json",
                "merges.txt"
            ]
            
            for file_name in required_files:
                file_path = checkpoint_path / file_name
                if not file_path.exists():
                    self.logger.logger.error(f"Missing required file: {file_name}")
                    return False
            
            # Validate configuration
            config_path = checkpoint_path / "config.json"
            try:
                config = AutoConfig.from_pretrained(str(checkpoint_path))
                
                # Check model architecture compatibility
                if not self._validate_model_architecture(config):
                    self.logger.logger.error("Model architecture validation failed")
                    return False
                    
            except Exception as e:
                self.logger.logger.error(f"Config validation failed: {e}")
                return False
            
            # Validate tokenizer
            try:
                tokenizer = AutoTokenizer.from_pretrained(str(checkpoint_path))
                
                if not self._validate_tokenizer(tokenizer):
                    self.logger.logger.error("Tokenizer validation failed")
                    return False
                    
            except Exception as e:
                self.logger.logger.error(f"Tokenizer validation failed: {e}")
                return False
            
            # Validate model weights
            model_path = checkpoint_path / "pytorch_model.bin"
            try:
                state_dict = torch.load(model_path, map_location='cpu')
                
                if not self._validate_model_weights(state_dict, config):
                    self.logger.logger.error("Model weights validation failed")
                    return False
                    
            except Exception as e:
                self.logger.logger.error(f"Model weights validation failed: {e}")
                return False
            
            # Validate manifest if exists
            manifest_path = checkpoint_path / "checkpoint_manifest.json"
            if manifest_path.exists():
                if not self._validate_checkpoint_manifest(manifest_path):
                    self.logger.logger.warning("Checkpoint manifest validation failed")
                    # Don't fail validation for manifest issues
            
            self.logger.logger.debug(f"Checkpoint validation successful: {checkpoint_path}")
            return True
            
        except Exception as e:
            self.logger.logger.error(f"Checkpoint validation error: {e}")
            return False
    
    def list_checkpoints(self, experiment_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List available checkpoints with metadata.
        
        Args:
            experiment_name: Optional experiment name to filter by
            
        Returns:
            List of checkpoint information dictionaries
        """
        checkpoints = []
        
        try:
            # Search directories
            search_dirs = []
            if experiment_name:
                exp_dir = self.robbert_dir / "experiments" / experiment_name
                if exp_dir.exists():
                    search_dirs.append(exp_dir)
            else:
                search_dirs.extend([
                    self.robbert_dir / "fine-tuned",
                    self.robbert_dir / "experiments"
                ])
            
            for search_dir in search_dirs:
                if not search_dir.exists():
                    continue
                    
                for checkpoint_dir in search_dir.iterdir():
                    if checkpoint_dir.is_dir():
                        checkpoint_info = self._get_checkpoint_info(checkpoint_dir)
                        if checkpoint_info:
                            checkpoints.append(checkpoint_info)
            
            # Sort by creation time (newest first)
            checkpoints.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            
            self.logger.logger.info(f"Found {len(checkpoints)} checkpoints")
            return checkpoints
            
        except Exception as e:
            self.logger.logger.error(f"Failed to list checkpoints: {e}")
            return []
    
    def backup_checkpoint(self, checkpoint_path: str, backup_name: Optional[str] = None) -> Path:
        """
        Create backup of checkpoint.
        
        Args:
            checkpoint_path: Path to checkpoint to backup
            backup_name: Optional backup name
            
        Returns:
            Path to backup directory
            
        Raises:
            ModelLoadingError: If backup fails
        """
        try:
            resolved_path = self._resolve_checkpoint_path(checkpoint_path)
            
            if not resolved_path.exists():
                raise ModelLoadingError(f"Checkpoint not found for backup: {checkpoint_path}",
                                      checkpoint_path=str(resolved_path))
            
            # Generate backup name
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"{resolved_path.name}_backup_{timestamp}"
            
            backup_dir = self.robbert_dir / "backups" / backup_name
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy checkpoint
            shutil.copytree(resolved_path, backup_dir, dirs_exist_ok=True)
            
            # Add backup metadata
            backup_metadata = {
                "original_path": str(resolved_path),
                "backup_created_at": datetime.now().isoformat(),
                "backup_name": backup_name
            }
            
            backup_metadata_path = backup_dir / "backup_metadata.json"
            with open(backup_metadata_path, 'w', encoding='utf-8') as f:
                json.dump(backup_metadata, f, indent=2)
            
            self.logger.logger.info(f"Created backup: {backup_dir}")
            return backup_dir
            
        except Exception as e:
            error_msg = f"Failed to backup checkpoint {checkpoint_path}"
            self.logger.logger.error(f"{error_msg}: {e}")
            raise ModelLoadingError(error_msg, checkpoint_path=checkpoint_path) from e
    
    def cleanup_old_checkpoints(self, keep_count: int = 5, experiment_name: Optional[str] = None):
        """
        Clean up old checkpoints, keeping only the most recent ones.
        
        Args:
            keep_count: Number of checkpoints to keep
            experiment_name: Optional experiment name to clean up
        """
        try:
            checkpoints = self.list_checkpoints(experiment_name)
            
            if len(checkpoints) <= keep_count:
                self.logger.logger.info(f"No cleanup needed, {len(checkpoints)} checkpoints <= {keep_count}")
                return
            
            # Sort by creation time and remove oldest
            checkpoints_to_remove = checkpoints[keep_count:]
            
            for checkpoint in checkpoints_to_remove:
                checkpoint_path = Path(checkpoint['path'])
                
                # Create backup before removal
                try:
                    self.backup_checkpoint(str(checkpoint_path))
                    self.logger.logger.info(f"Backed up checkpoint before cleanup: {checkpoint_path}")
                except Exception as e:
                    self.logger.logger.warning(f"Failed to backup before cleanup: {e}")
                
                # Remove checkpoint
                try:
                    shutil.rmtree(checkpoint_path)
                    self.logger.logger.info(f"Removed old checkpoint: {checkpoint_path}")
                except Exception as e:
                    self.logger.logger.error(f"Failed to remove checkpoint {checkpoint_path}: {e}")
            
            self.logger.logger.info(f"Cleanup completed, kept {keep_count} most recent checkpoints")
            
        except Exception as e:
            self.logger.logger.error(f"Checkpoint cleanup failed: {e}")
    
    def _resolve_checkpoint_path(self, checkpoint_path: str) -> Path:
        """Resolve checkpoint path from name or full path."""
        path = Path(checkpoint_path)
        
        # If absolute path, return as-is
        if path.is_absolute():
            return path
        
        # If relative path with separators, resolve relative to base
        if os.sep in checkpoint_path or '/' in checkpoint_path:
            return self.base_dir / checkpoint_path
        
        # Otherwise, search in standard locations
        search_locations = [
            self.robbert_dir / "fine-tuned" / checkpoint_path,
            self.robbert_dir / "experiments" / checkpoint_path,
        ]
        
        # Also search in experiment subdirectories
        experiments_dir = self.robbert_dir / "experiments"
        if experiments_dir.exists():
            for exp_dir in experiments_dir.iterdir():
                if exp_dir.is_dir():
                    search_locations.append(exp_dir / checkpoint_path)
        
        for location in search_locations:
            if location.exists():
                return location
        
        # Default to fine-tuned directory
        return self.robbert_dir / "fine-tuned" / checkpoint_path
    
    def _create_checkpoint_metadata(self, model: MultiTaskRobBERT, metadata: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Create comprehensive checkpoint metadata."""
        checkpoint_metadata = {
            "checkpoint_name": metadata.get('checkpoint_name', 'unknown') if metadata else 'unknown',
            "created_at": datetime.now().isoformat(),
            "model_type": "RobBERT-2023-dutch-base",
            "model_name": "DTAI-KULeuven/robbert-2023-dutch-base",
            "tokenizer_type": "byte_level_bpe",
            "model_heads": list(model.heads.keys()) if hasattr(model, 'heads') else [],
            "num_parameters": sum(p.numel() for p in model.parameters()),
            "trainable_parameters": sum(p.numel() for p in model.parameters() if p.requires_grad),
            "pytorch_version": torch.__version__,
            "transformers_version": "4.41.x",  # From requirements
        }
        
        # Add custom metadata if provided
        if metadata:
            checkpoint_metadata.update(metadata)
        
        return checkpoint_metadata
    
    def _create_checkpoint_manifest(self, checkpoint_dir: Path, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Create checkpoint manifest with file hashes and sizes."""
        manifest = {
            "checkpoint_path": str(checkpoint_dir),
            "created_at": datetime.now().isoformat(),
            "metadata": metadata,
            "files": {}
        }
        
        # Calculate file hashes and sizes
        for file_path in checkpoint_dir.iterdir():
            if file_path.is_file():
                try:
                    file_size = file_path.stat().st_size
                    file_hash = self._calculate_file_hash(file_path)
                    
                    manifest["files"][file_path.name] = {
                        "size": file_size,
                        "hash": file_hash
                    }
                except Exception as e:
                    self.logger.logger.warning(f"Failed to process file {file_path}: {e}")
        
        return manifest
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA256 hash of file."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def _validate_model_architecture(self, config: AutoConfig) -> bool:
        """Validate model architecture compatibility."""
        try:
            # Check if it's a RoBERTa-based model (RobBERT uses RoBERTa architecture)
            if not hasattr(config, 'model_type') or config.model_type != 'roberta':
                self.logger.logger.error(f"Unsupported model type: {getattr(config, 'model_type', 'unknown')}")
                return False
            
            # Check key architecture parameters
            expected_params = self.supported_models.get("DTAI-KULeuven/robbert-2023-dutch-base", {})
            
            for param, expected_value in expected_params.items():
                if hasattr(config, param):
                    actual_value = getattr(config, param)
                    if actual_value != expected_value:
                        self.logger.logger.warning(f"Architecture mismatch - {param}: expected {expected_value}, got {actual_value}")
                        # Don't fail validation for minor differences
            
            return True
            
        except Exception as e:
            self.logger.logger.error(f"Architecture validation error: {e}")
            return False
    
    def _validate_tokenizer(self, tokenizer) -> bool:
        """Validate tokenizer compatibility."""
        try:
            # Check tokenizer type
            if not hasattr(tokenizer, 'is_fast') or not tokenizer.is_fast:
                self.logger.logger.warning("Tokenizer is not fast tokenizer")
            
            # Check vocab size
            vocab_size = len(tokenizer.get_vocab())
            expected_vocab_size = self.supported_models["DTAI-KULeuven/robbert-2023-dutch-base"]["vocab_size"]
            
            if vocab_size != expected_vocab_size:
                self.logger.logger.warning(f"Vocab size mismatch: expected {expected_vocab_size}, got {vocab_size}")
            
            # Test basic tokenization
            test_text = "Dit is een test voor de Nederlandse tokenizer."
            tokens = tokenizer.tokenize(test_text)
            
            if not tokens:
                self.logger.logger.error("Tokenizer produced no tokens for test text")
                return False
            
            return True
            
        except Exception as e:
            self.logger.logger.error(f"Tokenizer validation error: {e}")
            return False
    
    def _validate_model_weights(self, state_dict: Dict[str, torch.Tensor], config: AutoConfig) -> bool:
        """Validate model weights structure."""
        try:
            # Check for required weight keys
            required_keys = [
                'roberta.embeddings.word_embeddings.weight',
                'roberta.embeddings.position_embeddings.weight',
                'roberta.encoder.layer.0.attention.self.query.weight'
            ]
            
            for key in required_keys:
                if key not in state_dict:
                    self.logger.logger.error(f"Missing required weight key: {key}")
                    return False
            
            # Check embedding dimensions
            word_embeddings = state_dict.get('roberta.embeddings.word_embeddings.weight')
            if word_embeddings is not None:
                vocab_size, hidden_size = word_embeddings.shape
                
                expected_hidden_size = getattr(config, 'hidden_size', 768)
                if hidden_size != expected_hidden_size:
                    self.logger.logger.error(f"Hidden size mismatch: expected {expected_hidden_size}, got {hidden_size}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.logger.error(f"Model weights validation error: {e}")
            return False
    
    def _validate_checkpoint_manifest(self, manifest_path: Path) -> bool:
        """Validate checkpoint manifest integrity."""
        try:
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest = json.load(f)
            
            # Check manifest structure
            required_keys = ['checkpoint_path', 'created_at', 'files']
            for key in required_keys:
                if key not in manifest:
                    self.logger.logger.error(f"Missing manifest key: {key}")
                    return False
            
            # Validate file hashes if files still exist
            checkpoint_dir = manifest_path.parent
            files_info = manifest.get('files', {})
            
            for filename, file_info in files_info.items():
                file_path = checkpoint_dir / filename
                if file_path.exists():
                    expected_hash = file_info.get('hash')
                    if expected_hash:
                        actual_hash = self._calculate_file_hash(file_path)
                        if actual_hash != expected_hash:
                            self.logger.logger.warning(f"File hash mismatch for {filename}")
                            # Don't fail validation for hash mismatches
            
            return True
            
        except Exception as e:
            self.logger.logger.error(f"Manifest validation error: {e}")
            return False
    
    def _get_checkpoint_info(self, checkpoint_dir: Path) -> Optional[Dict[str, Any]]:
        """Get checkpoint information from directory."""
        try:
            info = {
                "name": checkpoint_dir.name,
                "path": str(checkpoint_dir),
                "created_at": datetime.fromtimestamp(checkpoint_dir.stat().st_ctime).isoformat(),
                "size_mb": sum(f.stat().st_size for f in checkpoint_dir.rglob('*') if f.is_file()) / (1024 * 1024),
                "valid": self.validate_checkpoint(checkpoint_dir)
            }
            
            # Add metadata if available
            metadata_path = checkpoint_dir / "checkpoint_metadata.json"
            if metadata_path.exists():
                try:
                    with open(metadata_path, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    info.update(metadata)
                except Exception as e:
                    self.logger.logger.warning(f"Failed to load metadata for {checkpoint_dir}: {e}")
            
            return info
            
        except Exception as e:
            self.logger.logger.error(f"Failed to get checkpoint info for {checkpoint_dir}: {e}")
            return None


# Convenience functions for common operations
def save_robbert_checkpoint(model: MultiTaskRobBERT, 
                           checkpoint_name: str,
                           metadata: Optional[Dict[str, Any]] = None,
                           experiment_name: Optional[str] = None,
                           base_dir: str = "models") -> Path:
    """
    Convenience function to save RobBERT checkpoint.
    
    Args:
        model: MultiTaskRobBERT model to save
        checkpoint_name: Name for the checkpoint
        metadata: Additional metadata
        experiment_name: Optional experiment name
        base_dir: Base directory for models
        
    Returns:
        Path to saved checkpoint
    """
    manager = CheckpointManager(base_dir)
    return manager.save_checkpoint(model, checkpoint_name, metadata, experiment_name)


def load_robbert_checkpoint(checkpoint_path: str, 
                           base_dir: str = "models",
                           **kwargs) -> MultiTaskRobBERT:
    """
    Convenience function to load RobBERT checkpoint.
    
    Args:
        checkpoint_path: Path to checkpoint
        base_dir: Base directory for models
        **kwargs: Additional model loading arguments
        
    Returns:
        Loaded MultiTaskRobBERT model
    """
    manager = CheckpointManager(base_dir)
    return manager.load_checkpoint(checkpoint_path, **kwargs)


def list_robbert_checkpoints(experiment_name: Optional[str] = None,
                            base_dir: str = "models") -> List[Dict[str, Any]]:
    """
    Convenience function to list RobBERT checkpoints.
    
    Args:
        experiment_name: Optional experiment name filter
        base_dir: Base directory for models
        
    Returns:
        List of checkpoint information
    """
    manager = CheckpointManager(base_dir)
    return manager.list_checkpoints(experiment_name)
