"""
Tests for OOV analysis and tokenization verification functionality.
"""

import pytest
import json
import tempfile
from pathlib import Path
import sys

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from scripts.oov_analysis_and_tokenization_verification import (
    OOVAnalyzer, 
    OOVAnalysisResult, 
    TokenizationExample,
    create_test_cases
)


@pytest.fixture
def sample_dataset():
    """Create a sample dataset for testing."""
    return [
        {
            "tokens": ["Jan", "<PERSON><PERSON>", "woont", "in", "Amsterdam"],
            "ner_tags": ["B-PER", "I-PER", "O", "O", "B-LOC"]
        },
        {
            "tokens": ["<PERSON>-<PERSON>", "van", "der", "Berg-Jansen", "werkt", "bij", "TNO"],
            "ner_tags": ["B-PER", "I-PER", "I-PER", "I-PER", "O", "O", "B-ORG"]
        },
        {
            "tokens": ["Het", "e-mailadres", "<EMAIL>", "is", "geldig"],
            "ner_tags": ["O", "O", "O", "O", "O"]
        }
    ]


@pytest.fixture
def temp_dataset_file(sample_dataset):
    """Create a temporary dataset file."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(sample_dataset, f)
        return f.name


class TestOOVAnalyzer:
    """Test cases for OOVAnalyzer class."""
    
    def test_analyzer_initialization(self):
        """Test that analyzer initializes correctly."""
        analyzer = OOVAnalyzer()
        assert analyzer.tokenizer is not None
        assert analyzer.vocabulary is not None
        assert len(analyzer.vocabulary) > 0
    
    def test_analyze_dataset_oov(self, temp_dataset_file):
        """Test OOV analysis on dataset."""
        analyzer = OOVAnalyzer()
        result = analyzer.analyze_dataset_oov(temp_dataset_file)
        
        assert isinstance(result, OOVAnalysisResult)
        assert result.total_tokens > 0
        assert result.oov_rate >= 0.0
        assert result.oov_rate <= 1.0
        assert result.vocabulary_coverage >= 0.0
        assert result.vocabulary_coverage <= 1.0
        assert len(result.oov_examples) >= 0
    
    def test_check_tokenization_quality(self):
        """Test tokenization quality checking."""
        analyzer = OOVAnalyzer()
        test_cases = [
            "<EMAIL>",
            "Café Müller",
            "Jan-Willem van der Berg",
            "Normal text"
        ]
        
        results = analyzer.check_tokenization_quality(test_cases)
        
        assert 'email_handling' in results
        assert 'diacritic_handling' in results
        assert 'compound_handling' in results
        assert 'general_quality' in results
        assert 'statistics' in results
        
        stats = results['statistics']
        assert 'total_cases' in stats
        assert 'avg_tokens_per_word' in stats
        assert 'subword_split_rate' in stats
        assert stats['total_cases'] == len(test_cases)
    
    def test_generate_verification_examples(self, temp_dataset_file):
        """Test verification example generation."""
        analyzer = OOVAnalyzer()
        examples = analyzer.generate_verification_examples(temp_dataset_file, num_examples=2)
        
        assert len(examples) <= 2
        for example in examples:
            assert isinstance(example, TokenizationExample)
            assert example.original_text
            assert example.words
            assert example.tokens
            assert example.word_ids
            assert len(example.tokens) == len(example.word_ids)
    
    def test_validate_token_splits_and_alignment(self, temp_dataset_file):
        """Test token split and alignment validation."""
        analyzer = OOVAnalyzer()
        examples = analyzer.generate_verification_examples(temp_dataset_file, num_examples=2)
        results = analyzer.validate_token_splits_and_alignment(examples)
        
        assert 'total_examples' in results
        assert 'valid_alignments' in results
        assert 'alignment_errors' in results
        assert 'split_statistics' in results
        assert 'label_alignment_stats' in results
        
        assert results['total_examples'] == len(examples)
        assert results['valid_alignments'] >= 0
        assert results['valid_alignments'] <= results['total_examples']


class TestTokenizationQuality:
    """Test cases for tokenization quality analysis."""
    
    def test_create_test_cases(self):
        """Test that test cases are created correctly."""
        test_cases = create_test_cases()
        
        assert len(test_cases) > 0
        assert any('@' in case for case in test_cases)  # Email cases
        assert any('ë' in case or 'ü' in case for case in test_cases)  # Diacritic cases
        assert any('-' in case for case in test_cases)  # Compound cases
    
    def test_email_pattern_detection(self):
        """Test email pattern detection."""
        analyzer = OOVAnalyzer()
        
        # Test email detection
        assert analyzer.email_pattern.search("<NAME_EMAIL>")
        assert analyzer.email_pattern.search("<EMAIL> is valid")
        assert not analyzer.email_pattern.search("No email here")
    
    def test_diacritic_pattern_detection(self):
        """Test diacritic pattern detection."""
        analyzer = OOVAnalyzer()
        
        # Test diacritic detection
        assert analyzer.diacritic_pattern.search("Café Müller")
        assert analyzer.diacritic_pattern.search("Naïve approach")
        assert analyzer.diacritic_pattern.search("Coördinatie")
        assert not analyzer.diacritic_pattern.search("Regular text")
    
    def test_compound_pattern_detection(self):
        """Test compound name pattern detection."""
        analyzer = OOVAnalyzer()
        
        # Test compound detection
        assert analyzer.compound_pattern.search("Jan-Willem")
        assert analyzer.compound_pattern.search("Marie-Claire")
        assert analyzer.compound_pattern.search("state-of-the-art")
        assert not analyzer.compound_pattern.search("Regular text")


class TestOOVAnalysisResult:
    """Test cases for OOVAnalysisResult dataclass."""
    
    def test_oov_result_creation(self):
        """Test OOV result creation."""
        result = OOVAnalysisResult(
            total_tokens=1000,
            oov_tokens=50,
            oov_rate=0.05,
            oov_examples=["token1", "token2"],
            vocabulary_coverage=0.95,
            unique_tokens=800,
            unique_oov_tokens=40
        )
        
        assert result.total_tokens == 1000
        assert result.oov_tokens == 50
        assert result.oov_rate == 0.05
        assert len(result.oov_examples) == 2
        assert result.vocabulary_coverage == 0.95


class TestTokenizationExample:
    """Test cases for TokenizationExample dataclass."""
    
    def test_tokenization_example_creation(self):
        """Test tokenization example creation."""
        example = TokenizationExample(
            original_text="Jan Jansen woont in Amsterdam",
            words=["Jan", "Jansen", "woont", "in", "Amsterdam"],
            tokens=["Jan", "Jansen", "woont", "in", "Amsterdam"],
            word_ids=[0, 1, 2, 3, 4],
            labels=["B-PER", "I-PER", "O", "O", "B-LOC"],
            token_labels=["B-PER", "I-PER", "O", "O", "B-LOC"]
        )
        
        assert example.original_text == "Jan Jansen woont in Amsterdam"
        assert len(example.words) == 5
        assert len(example.tokens) == 5
        assert len(example.word_ids) == 5
        assert len(example.labels) == 5
        assert len(example.token_labels) == 5


@pytest.mark.integration
class TestIntegration:
    """Integration tests for the complete OOV analysis workflow."""
    
    def test_full_analysis_workflow(self, temp_dataset_file):
        """Test the complete analysis workflow."""
        analyzer = OOVAnalyzer()
        
        # Step 1: OOV analysis
        oov_result = analyzer.analyze_dataset_oov(temp_dataset_file)
        assert isinstance(oov_result, OOVAnalysisResult)
        
        # Step 2: Quality analysis
        test_cases = create_test_cases()
        quality_results = analyzer.check_tokenization_quality(test_cases)
        assert 'statistics' in quality_results
        
        # Step 3: Generate examples
        examples = analyzer.generate_verification_examples(temp_dataset_file, num_examples=2)
        assert len(examples) > 0
        
        # Step 4: Validate alignment
        validation_results = analyzer.validate_token_splits_and_alignment(examples)
        assert validation_results['total_examples'] == len(examples)
        
        # Verify results are consistent
        assert oov_result.total_tokens > 0
        assert quality_results['statistics']['total_cases'] == len(test_cases)
        assert validation_results['valid_alignments'] <= validation_results['total_examples']


if __name__ == "__main__":
    pytest.main([__file__, "-v"])