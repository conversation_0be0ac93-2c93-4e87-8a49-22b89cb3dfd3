model:
  encoder_weights: "${BERTJE_WEIGHTS_DIR:-src/models/weights/bertje_conll}"
  max_length: 512
  num_labels:
    ner: 9  # CoNLL-2002 NER tags
    compliance: 2
    label: 15
    reason: 10
    topic: 8

training:
  epochs: 3
  batch_size: 8
  learning_rate: 2.0e-5
  warmup_steps: 500
  weight_decay: 0.01
  gradient_accumulation_steps: 1
  max_grad_norm: 1.0

  loss_weights:
    compliance: 2.0
    label: 1.0
    reason: 1.0
    topic: 1.0
    ner: 0.5

inference:
  batch_size: 16
  thresholds:
    compliance: 0.50
    topic: 0.50
    label: 0.40
    reason: 0.45

  api:
    host: "0.0.0.0"
    port: 8000
    workers: 1

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"