"""
Comprehensive testing suite for Hugging Face Trainer integration.

This module implements task 15 from the HF trainer integration spec:
- Unit tests for data preprocessing and tokenization alignment
- Integration tests for end-to-end training pipeline
- Performance tests comparing with existing custom training
- Tests for WandB integration and metric logging
- Validation tests for model compatibility and checkpoint loading
- Test mode flag implementation for CI with small data subset
"""

import pytest
import json
import tempfile
import shutil
import time
import torch
import numpy as np
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, List, Any

from datasets import Dataset, DatasetDict
from transformers import AutoTokenizer, RobertaForTokenClassification, TrainingArguments

from src.data.hf_dataset_preparation import prepare_ner_dataset, HFDatasetPreparator
from src.training.hf_trainer import HF<PERSON>RTrainer, train_ner_model
from src.training.hf_config import HFTrainingConfig, create_test_hf_config
from src.training.hf_model_setup import create_ner_model, validate_model_compatibility
from src.training.hf_evaluation_metrics import compute_metrics
from src.training.hf_wandb_integration import CustomWandb<PERSON>allback, setup_wandb_for_hf_trainer


class TestDataPreprocessingAndTokenization:
    """Unit tests for data preprocessing and tokenization alignment."""
    
    @pytest.fixture
    def sample_ner_data(self):
        """Sample NER data for testing."""
        return [
            {
                "id": 1,
                "sentence": "Jan Jansen woont in Amsterdam.",
                "entities": [
                    {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10},
                    {"text": "Amsterdam", "label": "LOC", "start": 20, "end": 29}
                ]
            },
            {
                "id": 2,
                "sentence": "Marie van der Berg werkt bij Google Nederland.",
                "entities": [
                    {"text": "Marie van der Berg", "label": "PER", "start": 0, "end": 18},
                    {"text": "Google Nederland", "label": "ORG", "start": 29, "end": 45}
                ]
            },
            {
                "id": 3,
                "sentence": "Dit is een zin zonder entiteiten.",
                "entities": []
            }
        ]
    
    @pytest.fixture
    def mock_tokenizer(self):
        """Mock RobBERT tokenizer for testing."""
        tokenizer = Mock()
        tokenizer.model_max_length = 512
        
        def mock_tokenize(text, **kwargs):
            # Simple mock: split on spaces and add special tokens
            words = text.split()
            tokens = ['<s>'] + words + ['</s>']
            input_ids = list(range(len(tokens)))
            attention_mask = [1] * len(tokens)
            
            # Mock offset mapping (character positions)
            offset_mapping = [(None, None)]  # <s>
            char_pos = 0
            for word in words:
                start = char_pos
                end = char_pos + len(word)
                offset_mapping.append((start, end))
                char_pos = end + 1  # +1 for space
            offset_mapping.append((None, None))  # </s>
            
            return {
                'input_ids': input_ids,
                'attention_mask': attention_mask,
                'offset_mapping': offset_mapping
            }
        
        tokenizer.side_effect = mock_tokenize
        tokenizer.convert_ids_to_tokens = lambda ids: [f'token_{i}' for i in ids]
        return tokenizer
    
    def test_entity_span_detection(self, sample_ner_data, mock_tokenizer):
        """Test entity span detection and alignment."""
        with patch('src.data.hf_dataset_preparation.AutoTokenizer') as mock_tokenizer_class:
            mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
            
            preparator = HFDatasetPreparator()
            
            # Test entity span detection
            sentence = sample_ner_data[0]["sentence"]
            entities = sample_ner_data[0]["entities"]
            
            spans = preparator._get_entity_spans(sentence, entities)
            
            assert len(spans) == 2
            assert spans[0]["text"] == "Jan Jansen"
            assert spans[0]["label"] == "PER"
            assert spans[0]["start"] == 0
            assert spans[0]["end"] == 10
    
    def test_tokenization_alignment_with_entities(self, sample_ner_data, mock_tokenizer):
        """Test tokenization and label alignment with entities."""
        with patch('src.data.hf_dataset_preparation.AutoTokenizer') as mock_tokenizer_class:
            mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
            
            preparator = HFDatasetPreparator()
            
            examples = {
                'sentence': [sample_ner_data[0]["sentence"]],
                'entities': [sample_ner_data[0]["entities"]]
            }
            
            label2id = {'O': 0, 'B-PER': 1, 'I-PER': 2, 'B-LOC': 3, 'I-LOC': 4, 'B-ORG': 5, 'I-ORG': 6}
            
            result = preparator.tokenize_and_align_labels(examples, label2id)
            
            assert 'input_ids' in result
            assert 'attention_mask' in result
            assert 'labels' in result
            assert len(result['input_ids']) == 1
            assert len(result['labels']) == 1
            assert len(result['labels'][0]) == len(result['input_ids'][0])
    
    def test_tokenization_alignment_without_entities(self, sample_ner_data, mock_tokenizer):
        """Test tokenization with sentences that have no entities."""
        with patch('src.data.hf_dataset_preparation.AutoTokenizer') as mock_tokenizer_class:
            mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
            
            preparator = HFDatasetPreparator()
            
            examples = {
                'sentence': [sample_ner_data[2]["sentence"]],  # No entities
                'entities': [sample_ner_data[2]["entities"]]
            }
            
            label2id = {'O': 0, 'B-PER': 1, 'I-PER': 2}
            
            result = preparator.tokenize_and_align_labels(examples, label2id)
            
            # All labels should be 'O' (id=0)
            assert all(label == 0 for label in result['labels'][0])
    
    def test_label_discovery_and_bio_tagging(self, sample_ner_data):
        """Test dynamic label discovery and BIO tagging system."""
        with patch('src.data.hf_dataset_preparation.AutoTokenizer'):
            preparator = HFDatasetPreparator()
            
            label_list, label2id, id2label = preparator._setup_labels(sample_ner_data)
            
            # Should contain O, B-PER, I-PER, B-LOC, I-LOC, B-ORG, I-ORG
            expected_labels = ['O', 'B-LOC', 'I-LOC', 'B-ORG', 'I-ORG', 'B-PER', 'I-PER']
            assert set(label_list) == set(expected_labels)
            
            # Check mappings consistency
            assert len(label2id) == len(id2label) == len(label_list)
            for i, label in enumerate(label_list):
                assert label2id[label] == i
                assert id2label[i] == label
    
    def test_truncation_handling(self, mock_tokenizer):
        """Test handling of truncated sequences."""
        with patch('src.data.hf_dataset_preparation.AutoTokenizer') as mock_tokenizer_class:
            # Mock tokenizer that truncates
            truncating_tokenizer = Mock()
            truncating_tokenizer.model_max_length = 10  # Very short for testing
            
            def mock_truncate(text, **kwargs):
                words = text.split()[:5]  # Truncate to 5 words
                tokens = ['<s>'] + words + ['</s>']
                return {
                    'input_ids': list(range(len(tokens))),
                    'attention_mask': [1] * len(tokens),
                    'offset_mapping': [(None, None)] + [(i*5, (i+1)*5) for i in range(len(words))] + [(None, None)]
                }
            
            truncating_tokenizer.side_effect = mock_truncate
            mock_tokenizer_class.from_pretrained.return_value = truncating_tokenizer
            
            preparator = HFDatasetPreparator()
            
            # Long sentence that will be truncated
            examples = {
                'sentence': ["Dit is een zeer lange zin die zeker zal worden afgekapt door de tokenizer."],
                'entities': [{"text": "afgekapt", "label": "PER", "start": 50, "end": 58}]  # Entity at end
            }
            
            label2id = {'O': 0, 'B-PER': 1, 'I-PER': 2}
            
            # Should not crash even with truncation
            result = preparator.tokenize_and_align_labels(examples, label2id)
            assert 'input_ids' in result
            assert 'labels' in result
    
    @pytest.mark.unit
    def test_preprocessing_error_handling(self):
        """Test error handling in preprocessing pipeline."""
        with patch('src.data.hf_dataset_preparation.AutoTokenizer') as mock_tokenizer_class:
            mock_tokenizer_class.from_pretrained.side_effect = Exception("Tokenizer loading failed")
            
            with pytest.raises(Exception):
                HFDatasetPreparator("invalid-model")
    
    @pytest.mark.unit
    def test_dataset_statistics_calculation(self, sample_ner_data):
        """Test dataset statistics calculation."""
        with patch('src.data.hf_dataset_preparation.AutoTokenizer'):
            preparator = HFDatasetPreparator()
            
            # Mock dataset dict
            mock_dataset_dict = {
                'train': [
                    {'input_ids': [1, 2, 3, 0], 'labels': [0, 1, 2, 0]},
                    {'input_ids': [1, 2, 0, 0], 'labels': [0, 0, 0, 0]}
                ],
                'validation': [
                    {'input_ids': [1, 2, 3, 4], 'labels': [0, 1, 0, 0]}
                ]
            }
            
            label_list = ['O', 'B-PER', 'I-PER']
            
            stats = preparator._calculate_statistics(mock_dataset_dict, label_list)
            
            assert stats.total_examples == 3
            assert stats.train_size == 2
            assert stats.val_size == 1
            assert stats.entity_coverage > 0


class TestEndToEndTrainingPipeline:
    """Integration tests for end-to-end training pipeline."""
    
    @pytest.fixture
    def sample_training_data(self):
        """Create sample training data file."""
        data = [
            {
                "id": 1,
                "sentence": "Jan Jansen woont in Amsterdam.",
                "entities": [{"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10}]
            },
            {
                "id": 2,
                "sentence": "Marie de Wit werkt bij Google.",
                "entities": [{"text": "Marie de Wit", "label": "PER", "start": 0, "end": 12}]
            },
            {
                "id": 3,
                "sentence": "Dit is een test zonder entiteiten.",
                "entities": []
            },
            {
                "id": 4,
                "sentence": "Piet Janssen bezoekt Rotterdam.",
                "entities": [
                    {"text": "Piet Janssen", "label": "PER", "start": 0, "end": 12},
                    {"text": "Rotterdam", "label": "LOC", "start": 21, "end": 30}
                ]
            }
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump(data, f, indent=2)
            return f.name
    
    @pytest.mark.integration
    def test_complete_training_pipeline_with_mocks(self, sample_training_data):
        """Test complete training pipeline with mocked components."""
        try:
            with patch('src.training.hf_trainer.HFNERTrainer') as mock_trainer_class:
                # Mock trainer
                mock_trainer = Mock()
                mock_trainer.train.return_value = {
                    "train_runtime": 10.0,
                    "train_samples_per_second": 5.0,
                    "eval_f1": 0.85,
                    "eval_precision": 0.87,
                    "eval_recall": 0.83
                }
                mock_trainer_class.return_value = mock_trainer
                
                # Create test config
                config = create_test_hf_config()
                
                # Run training
                result = train_ner_model(
                    data_path=sample_training_data,
                    config=config
                )
                
                # Verify training was called
                mock_trainer_class.assert_called_once()
                mock_trainer.train.assert_called_once()
                
                # Verify results
                assert "train_runtime" in result
                assert "eval_f1" in result
                assert result["eval_f1"] == 0.85
                
        finally:
            Path(sample_training_data).unlink()
    
    @pytest.mark.integration
    def test_model_setup_and_validation(self):
        """Test model setup and compatibility validation."""
        with patch('src.training.hf_model_setup.RobertaForTokenClassification') as mock_model_class, \
             patch('src.training.hf_model_setup.AutoTokenizer') as mock_tokenizer_class:
            
            # Mock model
            mock_model = Mock()
            mock_model.config.num_labels = 3
            mock_model_class.from_pretrained.return_value = mock_model
            
            # Mock tokenizer
            mock_tokenizer = Mock()
            mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
            
            # Test model creation
            model, tokenizer, label2id, id2label = create_ner_model(
                model_name="test-model",
                label_list=["O", "B-PER", "I-PER"]
            )
            
            assert model == mock_model
            assert tokenizer == mock_tokenizer
            assert len(label2id) == 3
            assert len(id2label) == 3
            
            # Test validation
            is_valid = validate_model_compatibility(model, tokenizer, label2id)
            assert is_valid == True
    
    @pytest.mark.integration
    def test_dataset_preparation_integration(self, sample_training_data):
        """Test dataset preparation integration."""
        try:
            with patch('src.data.hf_dataset_preparation.AutoTokenizer') as mock_tokenizer_class, \
                 patch('src.data.hf_dataset_preparation.load_dataset') as mock_load_dataset:
                
                # Mock tokenizer
                mock_tokenizer = Mock()
                mock_tokenizer.model_max_length = 512
                
                def mock_tokenize(text, **kwargs):
                    words = text.split()
                    return {
                        'input_ids': list(range(len(words) + 2)),  # +2 for special tokens
                        'attention_mask': [1] * (len(words) + 2),
                        'offset_mapping': [(None, None)] + [(i*5, (i+1)*5) for i in range(len(words))] + [(None, None)]
                    }
                
                mock_tokenizer.side_effect = mock_tokenize
                mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
                
                # Mock dataset
                with open(sample_training_data, 'r') as f:
                    sample_data = json.load(f)
                
                mock_dataset = Mock()
                mock_dataset.__iter__ = Mock(return_value=iter(sample_data))
                mock_dataset.__len__ = Mock(return_value=len(sample_data))
                mock_dataset.map = Mock(return_value=mock_dataset)
                mock_dataset.filter = Mock(return_value=mock_dataset)
                mock_dataset.select = Mock(return_value=mock_dataset)
                mock_dataset.train_test_split = Mock(return_value={'train': mock_dataset, 'test': mock_dataset})
                mock_load_dataset.return_value = mock_dataset
                
                # Test dataset preparation
                dataset_dict, label_list, label2id, id2label, statistics = prepare_ner_dataset(
                    data_path=sample_training_data,
                    train_split=0.8
                )
                
                # Verify results
                assert len(label_list) > 1
                assert 'O' in label2id
                assert len(label2id) == len(id2label)
                assert statistics.total_examples > 0
                
        finally:
            Path(sample_training_data).unlink()
    
    @pytest.mark.integration
    def test_training_arguments_creation(self):
        """Test training arguments creation from config."""
        config = create_test_hf_config()
        
        trainer = HFNERTrainer(config)
        training_args = trainer.create_training_arguments("/tmp/test_output")
        
        assert isinstance(training_args, TrainingArguments)
        assert training_args.output_dir == "/tmp/test_output"
        assert training_args.num_train_epochs == config.epochs
        assert training_args.per_device_train_batch_size == config.batch_size
        assert training_args.learning_rate == config.learning_rate
        
        # Test mode should disable WandB
        assert training_args.report_to == [] or training_args.report_to is None
    
    @pytest.mark.integration
    def test_callback_creation_and_setup(self):
        """Test callback creation and setup."""
        config = create_test_hf_config()
        config.early_stopping_patience = 2
        
        trainer = HFNERTrainer(config)
        trainer.label_list = ["O", "B-PER", "I-PER"]
        
        callbacks = trainer.create_callbacks()
        
        # Should have early stopping and NER metrics callbacks
        assert len(callbacks) >= 1
        
        # Test callback types
        from transformers import EarlyStoppingCallback
        from src.training.hf_callbacks import NERMetricsCallback
        
        has_early_stopping = any(isinstance(cb, EarlyStoppingCallback) for cb in callbacks)
        has_ner_metrics = any(isinstance(cb, NERMetricsCallback) for cb in callbacks)
        
        assert has_early_stopping == True
        assert has_ner_metrics == True


class TestPerformanceComparison:
    """Performance tests comparing HF Trainer with existing custom training."""
    
    @pytest.fixture
    def performance_data(self):
        """Create larger dataset for performance testing."""
        data = []
        for i in range(100):  # 100 examples for performance testing
            data.append({
                "id": i,
                "sentence": f"Persoon {i} woont in Amsterdam en werkt bij Bedrijf {i}.",
                "entities": [
                    {"text": f"Persoon {i}", "label": "PER", "start": 0, "end": len(f"Persoon {i}")},
                    {"text": "Amsterdam", "label": "LOC", "start": len(f"Persoon {i}") + 10, "end": len(f"Persoon {i}") + 19},
                    {"text": f"Bedrijf {i}", "label": "ORG", "start": len(f"Persoon {i}") + 35, "end": len(f"Persoon {i}") + 35 + len(f"Bedrijf {i}")}
                ]
            })
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump(data, f, indent=2)
            return f.name
    
    @pytest.mark.slow
    def test_training_speed_benchmark(self, performance_data):
        """Benchmark training speed with HF Trainer."""
        try:
            with patch('src.training.hf_trainer.HFNERTrainer') as mock_trainer_class:
                # Mock trainer with timing
                mock_trainer = Mock()
                
                def mock_train():
                    time.sleep(0.1)  # Simulate training time
                    return {
                        "train_runtime": 0.1,
                        "train_samples_per_second": 1000.0,
                        "eval_f1": 0.85
                    }
                
                mock_trainer.train = mock_train
                mock_trainer_class.return_value = mock_trainer
                
                config = create_test_hf_config()
                
                # Measure training time
                start_time = time.time()
                result = train_ner_model(data_path=performance_data, config=config)
                end_time = time.time()
                
                training_time = end_time - start_time
                
                # Verify performance metrics
                assert "train_runtime" in result
                assert "train_samples_per_second" in result
                assert training_time < 1.0  # Should be fast with mocks
                
        finally:
            Path(performance_data).unlink()
    
    @pytest.mark.slow
    def test_memory_usage_monitoring(self, performance_data):
        """Test memory usage during training."""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            with patch('src.training.hf_trainer.HFNERTrainer') as mock_trainer_class:
                mock_trainer = Mock()
                mock_trainer.train.return_value = {"eval_f1": 0.85}
                mock_trainer_class.return_value = mock_trainer
                
                config = create_test_hf_config()
                train_ner_model(data_path=performance_data, config=config)
                
                final_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = final_memory - initial_memory
                
                # Memory increase should be reasonable (less than 100MB for mocked training)
                assert memory_increase < 100
                
        except ImportError:
            pytest.skip("psutil not available for memory monitoring")
        finally:
            Path(performance_data).unlink()
    
    @pytest.mark.slow
    def test_batch_size_scaling(self, performance_data):
        """Test training with different batch sizes."""
        try:
            batch_sizes = [2, 4, 8]
            results = {}
            
            for batch_size in batch_sizes:
                with patch('src.training.hf_trainer.HFNERTrainer') as mock_trainer_class:
                    mock_trainer = Mock()
                    mock_trainer.train.return_value = {
                        "train_samples_per_second": 100.0 / batch_size,  # Simulate scaling
                        "eval_f1": 0.85
                    }
                    mock_trainer_class.return_value = mock_trainer
                    
                    config = create_test_hf_config()
                    config.batch_size = batch_size
                    
                    start_time = time.time()
                    result = train_ner_model(data_path=performance_data, config=config)
                    end_time = time.time()
                    
                    results[batch_size] = {
                        "time": end_time - start_time,
                        "samples_per_second": result["train_samples_per_second"]
                    }
            
            # Verify that larger batch sizes have different performance characteristics
            assert len(results) == len(batch_sizes)
            for batch_size in batch_sizes:
                assert "time" in results[batch_size]
                assert "samples_per_second" in results[batch_size]
                
        finally:
            Path(performance_data).unlink()


class TestWandBIntegrationAndMetrics:
    """Tests for WandB integration and metric logging."""
    
    @pytest.fixture
    def mock_wandb(self):
        """Mock WandB for testing."""
        with patch('src.training.hf_wandb_integration.wandb') as mock_wandb:
            mock_run = Mock()
            mock_wandb.run = mock_run
            mock_wandb.init.return_value = mock_run
            mock_wandb.log = Mock()
            mock_wandb.config = Mock()
            mock_wandb.plot.confusion_matrix.return_value = Mock()
            mock_wandb.Table = Mock()
            mock_wandb.Artifact = Mock()
            yield mock_wandb
    
    def test_wandb_setup_and_initialization(self, mock_wandb):
        """Test WandB setup and initialization."""
        setup_wandb_for_hf_trainer(
            project="test-project",
            entity="test-entity",
            name="test-run",
            tags=["test"],
            config={"test": True}
        )
        
        mock_wandb.init.assert_called_once_with(
            project="test-project",
            entity="test-entity",
            name="test-run",
            tags=["test"],
            config={"test": True},
            reinit=True
        )
    
    def test_custom_wandb_callback_creation(self, mock_wandb):
        """Test CustomWandbCallback creation and setup."""
        label_list = ["O", "B-PER", "I-PER", "B-LOC", "I-LOC"]
        config = {
            'log_confusion_matrix': True,
            'log_per_class_metrics': True,
            'log_model_artifacts': True
        }
        
        callback = CustomWandbCallback(label_list, config)
        
        assert callback.label_list == label_list
        assert callback.config == config
        assert callback.log_confusion_matrix == True
        assert callback.log_per_class_metrics == True
        assert callback.log_model_artifacts == True
    
    def test_metrics_logging_functionality(self, mock_wandb):
        """Test metrics logging functionality."""
        label_list = ["O", "B-PER", "I-PER"]
        callback = CustomWandbCallback(label_list)
        callback._wandb = mock_wandb
        
        # Mock state
        from transformers import TrainerState
        state = Mock(spec=TrainerState)
        state.epoch = 1
        state.global_step = 100
        
        # Test metrics logging
        eval_metrics = {
            "eval_f1": 0.85,
            "eval_precision": 0.87,
            "eval_recall": 0.83,
            "eval_loss": 0.25
        }
        
        callback._log_enhanced_metrics(eval_metrics, state)
        
        # Verify metrics were logged
        assert len(callback.metrics_history) == 1
        assert callback.metrics_history[0]["eval_f1"] == 0.85
        mock_wandb.log.assert_called()
    
    def test_confusion_matrix_logging(self, mock_wandb):
        """Test confusion matrix logging."""
        label_list = ["O", "B-PER", "I-PER"]
        callback = CustomWandbCallback(label_list)
        callback._wandb = mock_wandb
        
        # Mock state
        from transformers import TrainerState
        state = Mock(spec=TrainerState)
        state.epoch = 1
        state.global_step = 100
        
        # Sample predictions and labels
        predictions = [["O", "B-PER", "I-PER"], ["B-PER", "O"]]
        labels = [["O", "B-PER", "I-PER"], ["B-PER", "O"]]
        
        callback._log_confusion_matrix(predictions, labels, state)
        
        # Verify confusion matrix was logged
        assert len(callback.confusion_matrices) == 1
        mock_wandb.plot.confusion_matrix.assert_called()
        mock_wandb.log.assert_called()
    
    def test_per_class_metrics_logging(self, mock_wandb):
        """Test per-class metrics logging."""
        label_list = ["O", "B-PER", "I-PER", "B-LOC", "I-LOC"]
        callback = CustomWandbCallback(label_list)
        callback._wandb = mock_wandb
        
        # Mock state
        from transformers import TrainerState
        state = Mock(spec=TrainerState)
        state.epoch = 1
        state.global_step = 100
        
        # Sample evaluation metrics with per-class metrics
        eval_metrics = {
            "eval_f1": 0.85,
            "eval_PER_precision": 0.90,
            "eval_PER_recall": 0.88,
            "eval_PER_f1": 0.89,
            "eval_LOC_precision": 0.82,
            "eval_LOC_recall": 0.80,
            "eval_LOC_f1": 0.81
        }
        
        callback._log_per_class_metrics(eval_metrics, state)
        
        # Verify per-class metrics were logged
        mock_wandb.log.assert_called()
        mock_wandb.Table.assert_called()
    
    def test_model_artifact_logging(self, mock_wandb):
        """Test model artifact logging."""
        label_list = ["O", "B-PER", "I-PER"]
        callback = CustomWandbCallback(label_list)
        callback._wandb = mock_wandb
        
        # Mock model
        model = Mock()
        model.__class__.__name__ = "RobertaForTokenClassification"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            callback._log_model_artifacts(model, temp_dir, {"eval_f1": 0.85})
            
            # Verify artifact was created and logged
            mock_wandb.Artifact.assert_called()
            mock_wandb.log_artifact.assert_called()
    
    def test_wandb_disabled_in_test_mode(self):
        """Test that WandB is properly disabled in test mode."""
        config = create_test_hf_config()  # test_disable_wandb=True by default
        
        trainer = HFNERTrainer(config)
        
        with patch('src.training.hf_trainer.wandb') as mock_wandb:
            trainer.setup_wandb()
            
            # WandB should not be initialized in test mode
            mock_wandb.init.assert_not_called()


class TestModelCompatibilityAndCheckpoints:
    """Validation tests for model compatibility and checkpoint loading."""
    
    def test_model_architecture_compatibility(self):
        """Test model architecture compatibility validation."""
        with patch('src.training.hf_model_setup.RobertaForTokenClassification') as mock_model_class:
            # Mock model with correct architecture
            mock_model = Mock()
            mock_model.config.model_type = "roberta"
            mock_model.config.num_labels = 3
            mock_model_class.from_pretrained.return_value = mock_model
            
            # Mock tokenizer
            with patch('src.training.hf_model_setup.AutoTokenizer') as mock_tokenizer_class:
                mock_tokenizer = Mock()
                mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
                
                model, tokenizer, label2id, id2label = create_ner_model(
                    model_name="DTAI-KULeuven/robbert-2023-dutch-base",
                    label_list=["O", "B-PER", "I-PER"]
                )
                
                # Test compatibility validation
                is_compatible = validate_model_compatibility(model, tokenizer, label2id)
                assert is_compatible == True
    
    def test_checkpoint_loading_compatibility(self):
        """Test checkpoint loading and compatibility."""
        with tempfile.TemporaryDirectory() as temp_dir:
            checkpoint_dir = Path(temp_dir) / "test_checkpoint"
            checkpoint_dir.mkdir()
            
            # Create mock checkpoint files
            (checkpoint_dir / "config.json").write_text('{"model_type": "roberta", "num_labels": 3}')
            (checkpoint_dir / "pytorch_model.bin").write_text("mock model data")
            (checkpoint_dir / "tokenizer.json").write_text('{"model": {"vocab": {}}}')
            
            with patch('src.training.hf_model_setup.RobertaForTokenClassification') as mock_model_class, \
                 patch('src.training.hf_model_setup.AutoTokenizer') as mock_tokenizer_class:
                
                mock_model = Mock()
                mock_tokenizer = Mock()
                mock_model_class.from_pretrained.return_value = mock_model
                mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
                
                # Test loading from checkpoint
                model, tokenizer, label2id, id2label = create_ner_model(
                    model_name=str(checkpoint_dir),
                    label_list=["O", "B-PER", "I-PER"]
                )
                
                # Verify model was loaded from checkpoint
                mock_model_class.from_pretrained.assert_called_with(
                    str(checkpoint_dir),
                    num_labels=3,
                    id2label={0: "O", 1: "B-PER", 2: "I-PER"},
                    label2id={"O": 0, "B-PER": 1, "I-PER": 2}
                )
    
    def test_label_mapping_consistency(self):
        """Test label mapping consistency across model loading."""
        label_list = ["O", "B-PER", "I-PER", "B-LOC", "I-LOC", "B-ORG", "I-ORG"]
        
        with patch('src.training.hf_model_setup.RobertaForTokenClassification') as mock_model_class, \
             patch('src.training.hf_model_setup.AutoTokenizer') as mock_tokenizer_class:
            
            mock_model = Mock()
            mock_tokenizer = Mock()
            mock_model_class.from_pretrained.return_value = mock_model
            mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
            
            model, tokenizer, label2id, id2label = create_ner_model(
                model_name="test-model",
                label_list=label_list
            )
            
            # Test label mapping consistency
            assert len(label2id) == len(id2label) == len(label_list)
            
            for i, label in enumerate(label_list):
                assert label2id[label] == i
                assert id2label[i] == label
            
            # Test that model was configured with correct mappings
            mock_model_class.from_pretrained.assert_called_with(
                "test-model",
                num_labels=len(label_list),
                id2label=id2label,
                label2id=label2id
            )
    
    def test_inference_pipeline_compatibility(self):
        """Test compatibility with existing inference pipeline."""
        with patch('src.training.hf_model_setup.RobertaForTokenClassification') as mock_model_class, \
             patch('src.training.hf_model_setup.AutoTokenizer') as mock_tokenizer_class:
            
            # Mock model that mimics RobBERT structure
            mock_model = Mock()
            mock_model.config.model_type = "roberta"
            mock_model.config.hidden_size = 768
            mock_model.config.num_labels = 3
            
            # Mock forward pass
            mock_output = Mock()
            mock_output.logits = torch.randn(1, 10, 3)  # batch_size=1, seq_len=10, num_labels=3
            mock_model.return_value = mock_output
            mock_model_class.from_pretrained.return_value = mock_model
            
            # Mock tokenizer
            mock_tokenizer = Mock()
            mock_tokenizer.return_value = {
                'input_ids': torch.tensor([[1, 2, 3, 4, 5]]),
                'attention_mask': torch.tensor([[1, 1, 1, 1, 1]])
            }
            mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
            
            model, tokenizer, label2id, id2label = create_ner_model(
                model_name="DTAI-KULeuven/robbert-2023-dutch-base",
                label_list=["O", "B-PER", "I-PER"]
            )
            
            # Test inference compatibility
            test_text = "Jan Jansen woont in Amsterdam."
            inputs = tokenizer(test_text)
            
            # Should not crash
            with torch.no_grad():
                outputs = model(**inputs)
            
            assert hasattr(outputs, 'logits')
            assert outputs.logits.shape[-1] == 3  # num_labels


class TestModeImplementation:
    """Tests for --test-mode flag implementation for CI."""
    
    def test_test_mode_configuration(self):
        """Test test mode configuration settings."""
        config = create_test_hf_config()
        
        # Verify test mode settings
        assert config.test_mode == True
        assert config.epochs == config.test_epochs == 1
        assert config.test_sample_limit == 50
        assert config.test_disable_wandb == True
        assert config.report_to is None  # WandB disabled
        
        # Verify reduced resource usage
        assert config.batch_size <= 8  # Small batch size
        assert config.eval_steps <= 20  # Frequent evaluation
        assert config.logging_steps <= 10  # Frequent logging
        assert config.early_stopping_patience <= 3  # Quick stopping
    
    def test_test_mode_training_args_conversion(self):
        """Test training arguments conversion in test mode."""
        config = create_test_hf_config()
        
        training_args = config.to_training_args("/tmp/test")
        
        # Verify test mode settings in TrainingArguments
        assert training_args.num_train_epochs == 1
        assert training_args.report_to == [] or training_args.report_to is None
        assert training_args.per_device_train_batch_size <= 8
        assert training_args.eval_steps <= 20
        assert training_args.logging_steps <= 10
    
    def test_test_mode_dataset_limiting(self):
        """Test dataset limiting in test mode."""
        with patch('src.data.hf_dataset_preparation.load_dataset') as mock_load_dataset:
            # Create mock dataset with more than test_sample_limit examples
            large_dataset = [{"id": i, "sentence": f"Test {i}", "entities": []} for i in range(100)]
            
            mock_dataset = Mock()
            mock_dataset.__iter__ = Mock(return_value=iter(large_dataset))
            mock_dataset.__len__ = Mock(return_value=len(large_dataset))
            mock_dataset.select = Mock(return_value=mock_dataset)
            mock_dataset.map = Mock(return_value=mock_dataset)
            mock_dataset.filter = Mock(return_value=mock_dataset)
            mock_dataset.train_test_split = Mock(return_value={'train': mock_dataset, 'test': mock_dataset})
            mock_load_dataset.return_value = mock_dataset
            
            with patch('src.data.hf_dataset_preparation.AutoTokenizer'):
                config = create_test_hf_config()
                
                # In test mode, dataset should be limited
                if config.test_mode:
                    # Mock the dataset preparation to simulate limiting
                    preparator = HFDatasetPreparator()
                    
                    # Simulate dataset limiting
                    limited_data = large_dataset[:config.test_sample_limit]
                    assert len(limited_data) == config.test_sample_limit
    
    def test_test_mode_wandb_disabled(self):
        """Test that WandB is disabled in test mode."""
        config = create_test_hf_config()
        
        # WandB should be disabled
        assert config.test_disable_wandb == True
        assert config.report_to is None
        
        # Training arguments should not report to WandB
        training_args = config.to_training_args("/tmp/test")
        assert training_args.report_to == [] or training_args.report_to is None
    
    def test_test_mode_early_stopping(self):
        """Test aggressive early stopping in test mode."""
        config = create_test_hf_config()
        
        # Early stopping should be aggressive
        assert config.early_stopping_patience <= 3
        assert config.early_stopping_threshold >= 0.001  # Less strict threshold
        
        trainer = HFNERTrainer(config)
        trainer.label_list = ["O", "B-PER", "I-PER"]
        
        callbacks = trainer.create_callbacks()
        
        # Should have early stopping callback
        from transformers import EarlyStoppingCallback
        has_early_stopping = any(isinstance(cb, EarlyStoppingCallback) for cb in callbacks)
        assert has_early_stopping == True
    
    def test_ci_compatibility(self):
        """Test CI/CD compatibility features."""
        config = create_test_hf_config()
        
        # Should use CPU for consistent CI results
        assert config.use_gpu == False or not torch.cuda.is_available()
        assert config.fp16 == False  # No mixed precision on CPU
        assert config.dataloader_num_workers <= 2  # Avoid multiprocessing issues
        
        # Should have deterministic settings
        assert config.seed == 42
        assert config.data_seed == 42
        
        # Should have minimal resource usage
        assert config.batch_size <= 8
        assert config.max_length <= 512
        assert config.save_total_limit <= 3


class TestEvaluationMetrics:
    """Tests for evaluation metrics computation."""
    
    def test_compute_metrics_basic(self):
        """Test basic NER metrics computation."""
        # Mock evaluation prediction
        predictions = np.array([
            [[0.8, 0.1, 0.1], [0.1, 0.8, 0.1], [0.1, 0.1, 0.8], [0.8, 0.1, 0.1]],
            [[0.8, 0.1, 0.1], [0.8, 0.1, 0.1], [0.1, 0.8, 0.1], [0.1, 0.1, 0.8]]
        ])
        
        labels = np.array([
            [0, 1, 2, 0],
            [0, 0, 1, 2]
        ])
        
        eval_pred = Mock()
        eval_pred.predictions = predictions
        eval_pred.label_ids = labels
        
        label_list = ["O", "B-PER", "I-PER"]
        
        metrics = compute_metrics(eval_pred, label_list)
        
        # Check that all required metrics are present
        required_metrics = ["precision", "recall", "f1", "accuracy"]
        for metric in required_metrics:
            assert metric in metrics
            assert 0.0 <= metrics[metric] <= 1.0
    
    def test_compute_metrics_with_ignored_tokens(self):
        """Test metrics computation with ignored tokens (-100)."""
        predictions = np.array([
            [[0.8, 0.1, 0.1], [0.1, 0.8, 0.1], [0.1, 0.1, 0.8], [0.8, 0.1, 0.1], [0.8, 0.1, 0.1]]
        ])
        
        labels = np.array([
            [0, 1, 2, -100, -100]  # Last two tokens ignored
        ])
        
        eval_pred = Mock()
        eval_pred.predictions = predictions
        eval_pred.label_ids = labels
        
        label_list = ["O", "B-PER", "I-PER"]
        
        metrics = compute_metrics(eval_pred, label_list)
        
        # Should handle ignored tokens correctly
        assert "precision" in metrics
        assert "recall" in metrics
        assert "f1" in metrics
        assert "accuracy" in metrics
    
    def test_compute_metrics_empty_predictions(self):
        """Test metrics computation with empty predictions."""
        predictions = np.array([]).reshape(0, 0, 3)
        labels = np.array([]).reshape(0, 0)
        
        eval_pred = Mock()
        eval_pred.predictions = predictions
        eval_pred.label_ids = labels
        
        label_list = ["O", "B-PER", "I-PER"]
        
        # Should not crash with empty predictions
        metrics = compute_metrics(eval_pred, label_list)
        
        assert isinstance(metrics, dict)
        assert "precision" in metrics
        assert "recall" in metrics
        assert "f1" in metrics
        assert "accuracy" in metrics
    
    def test_compute_metrics_per_entity_type(self):
        """Test per-entity-type metrics computation."""
        # Create predictions with multiple entity types
        predictions = np.array([
            [[0.8, 0.1, 0.05, 0.05], [0.1, 0.8, 0.05, 0.05], [0.1, 0.1, 0.8, 0.0], [0.05, 0.05, 0.05, 0.85]]
        ])
        
        labels = np.array([
            [0, 1, 2, 3]  # O, B-PER, I-PER, B-LOC
        ])
        
        eval_pred = Mock()
        eval_pred.predictions = predictions
        eval_pred.label_ids = labels
        
        label_list = ["O", "B-PER", "I-PER", "B-LOC"]
        
        metrics = compute_metrics(eval_pred, label_list)
        
        # Should include overall metrics
        assert "precision" in metrics
        assert "recall" in metrics
        assert "f1" in metrics
        assert "accuracy" in metrics


# Test markers and configuration
@pytest.mark.unit
class TestUnitTests:
    """Marker for unit tests."""
    pass


@pytest.mark.integration
class TestIntegrationTests:
    """Marker for integration tests."""
    pass


@pytest.mark.slow
class TestSlowTests:
    """Marker for slow tests."""
    pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])