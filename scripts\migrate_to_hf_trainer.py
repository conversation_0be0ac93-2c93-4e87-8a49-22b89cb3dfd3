#!/usr/bin/env python3
"""
Migration script for converting existing training data and configurations
to Hugging Face Trainer format.

This script handles:
- Data format conversion (JSONL to sentence+entities)
- Configuration migration (legacy YAML to HF Trainer config)
- Checkpoint compatibility validation and migration
- Integration testing with existing API endpoints
"""

import sys
import os
import argparse
import json
from pathlib import Path
from typing import Dict, List, Optional, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.migration_utils import (
    DataFormatMigrator,
    CheckpointCompatibilityValidator,
    ConfigurationMigrator
)
from src.utils.compatibility_layer import (
    BackwardCompatibilityManager,
    ensure_checkpoint_compatibility
)
from src.utils.logging_utils import get_logger


def setup_logging():
    """Set up logging for migration script."""
    return get_logger("migration_script", level="INFO")


def migrate_data_files(
    input_dir: Path,
    output_dir: Path,
    logger
) -> Dict[str, Any]:
    """
    Migrate data files from JSONL to sentence+entities format.
    
    Args:
        input_dir: Input directory containing JSONL files
        output_dir: Output directory for converted files
        logger: Logger instance
        
    Returns:
        Migration results
    """
    logger.logger.info("Starting data file migration...")
    
    data_migrator = DataFormatMigrator()
    migration_results = {
        "files_processed": 0,
        "files_converted": 0,
        "files_failed": 0,
        "conversion_stats": [],
        "errors": []
    }
    
    # Create output directory
    data_output_dir = output_dir / "data"
    data_output_dir.mkdir(parents=True, exist_ok=True)
    
    # Find JSONL files
    jsonl_files = list(input_dir.glob("*.jsonl")) + list(input_dir.glob("**/*.jsonl"))
    
    if not jsonl_files:
        logger.logger.warning("No JSONL files found for migration")
        return migration_results
    
    logger.logger.info(f"Found {len(jsonl_files)} JSONL files to migrate")
    
    for jsonl_file in jsonl_files:
        migration_results["files_processed"] += 1
        
        try:
            # Create output path
            relative_path = jsonl_file.relative_to(input_dir)
            output_file = data_output_dir / relative_path.with_suffix('.json')
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            logger.logger.info(f"Converting {jsonl_file.name}...")
            
            # Convert file
            stats = data_migrator.convert_jsonl_to_sentence_entities(
                jsonl_file, output_file
            )
            
            migration_results["files_converted"] += 1
            migration_results["conversion_stats"].append({
                "source_file": str(jsonl_file),
                "target_file": str(output_file),
                "stats": stats
            })
            
            logger.logger.info(f"✓ Converted {jsonl_file.name} ({stats['converted_examples']} examples)")
            
        except Exception as e:
            migration_results["files_failed"] += 1
            error_msg = f"Failed to convert {jsonl_file.name}: {e}"
            migration_results["errors"].append(error_msg)
            logger.logger.error(f"✗ {error_msg}")
    
    logger.logger.info(f"Data migration completed: {migration_results['files_converted']}/{migration_results['files_processed']} files converted")
    
    return migration_results


def migrate_config_files(
    input_dir: Path,
    output_dir: Path,
    logger
) -> Dict[str, Any]:
    """
    Migrate configuration files to HF Trainer format.
    
    Args:
        input_dir: Input directory containing config files
        output_dir: Output directory for migrated configs
        logger: Logger instance
        
    Returns:
        Migration results
    """
    logger.logger.info("Starting configuration file migration...")
    
    config_migrator = ConfigurationMigrator()
    migration_results = {
        "files_processed": 0,
        "files_converted": 0,
        "files_failed": 0,
        "migration_reports": [],
        "errors": []
    }
    
    # Create output directory
    config_output_dir = output_dir / "configs"
    config_output_dir.mkdir(parents=True, exist_ok=True)
    
    # Find YAML config files
    config_files = list(input_dir.glob("*.yaml")) + list(input_dir.glob("*.yml"))
    config_files += list(input_dir.glob("**/*.yaml")) + list(input_dir.glob("**/*.yml"))
    
    if not config_files:
        logger.logger.warning("No YAML config files found for migration")
        return migration_results
    
    logger.logger.info(f"Found {len(config_files)} config files to migrate")
    
    for config_file in config_files:
        migration_results["files_processed"] += 1
        
        try:
            # Create output path
            relative_path = config_file.relative_to(input_dir)
            output_file = config_output_dir / f"hf_{relative_path}"
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            logger.logger.info(f"Migrating {config_file.name}...")
            
            # Migrate configuration
            report = config_migrator.migrate_training_config(
                config_file, output_file, "hf_trainer"
            )
            
            migration_results["files_converted"] += 1
            migration_results["migration_reports"].append(report)
            
            logger.logger.info(f"✓ Migrated {config_file.name}")
            
        except Exception as e:
            migration_results["files_failed"] += 1
            error_msg = f"Failed to migrate {config_file.name}: {e}"
            migration_results["errors"].append(error_msg)
            logger.logger.error(f"✗ {error_msg}")
    
    logger.logger.info(f"Config migration completed: {migration_results['files_converted']}/{migration_results['files_processed']} files converted")
    
    return migration_results


def migrate_checkpoints(
    input_dir: Path,
    output_dir: Path,
    logger
) -> Dict[str, Any]:
    """
    Migrate and validate model checkpoints.
    
    Args:
        input_dir: Input directory containing checkpoints
        output_dir: Output directory for migrated checkpoints
        logger: Logger instance
        
    Returns:
        Migration results
    """
    logger.logger.info("Starting checkpoint migration...")
    
    checkpoint_validator = CheckpointCompatibilityValidator()
    migration_results = {
        "checkpoints_processed": 0,
        "checkpoints_migrated": 0,
        "checkpoints_failed": 0,
        "compatibility_reports": [],
        "migration_reports": [],
        "errors": []
    }
    
    # Create output directory
    checkpoint_output_dir = output_dir / "checkpoints"
    checkpoint_output_dir.mkdir(parents=True, exist_ok=True)
    
    # Find checkpoint directories
    checkpoint_dirs = [d for d in input_dir.iterdir() if d.is_dir()]
    
    if not checkpoint_dirs:
        logger.logger.warning("No checkpoint directories found for migration")
        return migration_results
    
    logger.logger.info(f"Found {len(checkpoint_dirs)} checkpoint directories to process")
    
    for checkpoint_dir in checkpoint_dirs:
        migration_results["checkpoints_processed"] += 1
        
        try:
            logger.logger.info(f"Processing checkpoint {checkpoint_dir.name}...")
            
            # Validate compatibility first
            compatibility_report = checkpoint_validator.validate_checkpoint_compatibility(
                checkpoint_dir
            )
            migration_results["compatibility_reports"].append(compatibility_report)
            
            if compatibility_report["is_compatible"]:
                # Migrate checkpoint
                output_checkpoint = checkpoint_output_dir / checkpoint_dir.name
                migration_report = checkpoint_validator.migrate_checkpoint_format(
                    checkpoint_dir, output_checkpoint, "hf_trainer"
                )
                
                migration_results["checkpoints_migrated"] += 1
                migration_results["migration_reports"].append(migration_report)
                
                logger.logger.info(f"✓ Migrated checkpoint {checkpoint_dir.name}")
                
            else:
                logger.logger.warning(f"⚠ Checkpoint {checkpoint_dir.name} is not compatible:")
                for issue in compatibility_report["issues"]:
                    logger.logger.warning(f"    - {issue}")
                
                # Still count as processed but not migrated
                migration_results["errors"].append(f"Checkpoint {checkpoint_dir.name} not compatible")
                
        except Exception as e:
            migration_results["checkpoints_failed"] += 1
            error_msg = f"Failed to process checkpoint {checkpoint_dir.name}: {e}"
            migration_results["errors"].append(error_msg)
            logger.logger.error(f"✗ {error_msg}")
    
    logger.logger.info(f"Checkpoint migration completed: {migration_results['checkpoints_migrated']}/{migration_results['checkpoints_processed']} checkpoints migrated")
    
    return migration_results


def test_api_compatibility(
    migrated_checkpoints: List[str],
    logger
) -> Dict[str, Any]:
    """
    Test API compatibility with migrated checkpoints.
    
    Args:
        migrated_checkpoints: List of migrated checkpoint paths
        logger: Logger instance
        
    Returns:
        Test results
    """
    logger.logger.info("Testing API compatibility...")
    
    test_results = {
        "checkpoints_tested": 0,
        "checkpoints_compatible": 0,
        "compatibility_issues": [],
        "test_details": []
    }
    
    compatibility_manager = BackwardCompatibilityManager()
    
    for checkpoint_path in migrated_checkpoints:
        test_results["checkpoints_tested"] += 1
        
        try:
            logger.logger.info(f"Testing compatibility for {Path(checkpoint_path).name}...")
            
            # Test checkpoint compatibility
            is_compatible = ensure_checkpoint_compatibility(checkpoint_path)
            
            if is_compatible:
                test_results["checkpoints_compatible"] += 1
                logger.logger.info(f"✓ Checkpoint {Path(checkpoint_path).name} is API compatible")
            else:
                issue = f"Checkpoint {Path(checkpoint_path).name} has API compatibility issues"
                test_results["compatibility_issues"].append(issue)
                logger.logger.warning(f"⚠ {issue}")
            
            test_results["test_details"].append({
                "checkpoint": checkpoint_path,
                "compatible": is_compatible
            })
            
        except Exception as e:
            error_msg = f"Failed to test {Path(checkpoint_path).name}: {e}"
            test_results["compatibility_issues"].append(error_msg)
            logger.logger.error(f"✗ {error_msg}")
    
    logger.logger.info(f"API compatibility testing completed: {test_results['checkpoints_compatible']}/{test_results['checkpoints_tested']} checkpoints compatible")
    
    return test_results


def create_migration_summary(
    output_dir: Path,
    data_results: Dict[str, Any],
    config_results: Dict[str, Any],
    checkpoint_results: Dict[str, Any],
    api_test_results: Dict[str, Any]
) -> None:
    """
    Create comprehensive migration summary report.
    
    Args:
        output_dir: Output directory for report
        data_results: Data migration results
        config_results: Config migration results
        checkpoint_results: Checkpoint migration results
        api_test_results: API compatibility test results
    """
    summary = {
        "migration_timestamp": str(Path(__file__).stat().st_mtime),
        "summary": {
            "data_files": {
                "processed": data_results["files_processed"],
                "converted": data_results["files_converted"],
                "failed": data_results["files_failed"]
            },
            "config_files": {
                "processed": config_results["files_processed"],
                "converted": config_results["files_converted"],
                "failed": config_results["files_failed"]
            },
            "checkpoints": {
                "processed": checkpoint_results["checkpoints_processed"],
                "migrated": checkpoint_results["checkpoints_migrated"],
                "failed": checkpoint_results["checkpoints_failed"]
            },
            "api_compatibility": {
                "tested": api_test_results["checkpoints_tested"],
                "compatible": api_test_results["checkpoints_compatible"]
            }
        },
        "detailed_results": {
            "data_migration": data_results,
            "config_migration": config_results,
            "checkpoint_migration": checkpoint_results,
            "api_compatibility_tests": api_test_results
        }
    }
    
    # Save summary report
    summary_file = output_dir / "migration_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2)
    
    # Create human-readable report
    readme_content = f"""# Migration Summary Report

## Overview

This report summarizes the migration from legacy format to Hugging Face Trainer integration.

## Results Summary

### Data Files
- **Processed**: {data_results['files_processed']}
- **Converted**: {data_results['files_converted']}
- **Failed**: {data_results['files_failed']}

### Configuration Files
- **Processed**: {config_results['files_processed']}
- **Converted**: {config_results['files_converted']}
- **Failed**: {config_results['files_failed']}

### Model Checkpoints
- **Processed**: {checkpoint_results['checkpoints_processed']}
- **Migrated**: {checkpoint_results['checkpoints_migrated']}
- **Failed**: {checkpoint_results['checkpoints_failed']}

### API Compatibility
- **Tested**: {api_test_results['checkpoints_tested']}
- **Compatible**: {api_test_results['checkpoints_compatible']}

## Next Steps

1. **Review migrated data**: Check converted data files in `data/` directory
2. **Update configurations**: Review migrated configs in `configs/` directory
3. **Test checkpoints**: Validate migrated checkpoints work with your inference pipeline
4. **Update scripts**: Update training scripts to use new HF Trainer integration

## Files Generated

- `migration_summary.json`: Detailed migration results
- `data/`: Converted data files in sentence+entities format
- `configs/`: Migrated configuration files for HF Trainer
- `checkpoints/`: Migrated model checkpoints

## Troubleshooting

If you encounter issues:

1. Check the detailed error messages in `migration_summary.json`
2. Validate data format using the migration utilities
3. Test checkpoint compatibility using the compatibility layer
4. Refer to the migration documentation for common issues

## Support

For additional help with migration:
- Review the migration utilities documentation
- Check the compatibility layer for backward compatibility features
- Test with small datasets first before full migration
"""
    
    readme_file = output_dir / "README.md"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)


def main():
    """Main migration script."""
    parser = argparse.ArgumentParser(
        description="Migrate existing data and configurations to Hugging Face Trainer format"
    )
    parser.add_argument(
        "--input-dir",
        type=Path,
        default=Path("."),
        help="Input directory containing legacy data and configs (default: current directory)"
    )
    parser.add_argument(
        "--output-dir",
        type=Path,
        default=Path("migrated"),
        help="Output directory for migrated files (default: ./migrated)"
    )
    parser.add_argument(
        "--skip-data",
        action="store_true",
        help="Skip data file migration"
    )
    parser.add_argument(
        "--skip-configs",
        action="store_true",
        help="Skip configuration file migration"
    )
    parser.add_argument(
        "--skip-checkpoints",
        action="store_true",
        help="Skip checkpoint migration"
    )
    parser.add_argument(
        "--skip-api-tests",
        action="store_true",
        help="Skip API compatibility tests"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be migrated without actually doing it"
    )
    
    args = parser.parse_args()
    
    # Set up logging
    logger = setup_logging()
    
    logger.logger.info("Starting Hugging Face Trainer migration process")
    logger.logger.info(f"Input directory: {args.input_dir}")
    logger.logger.info(f"Output directory: {args.output_dir}")
    
    if args.dry_run:
        logger.logger.info("DRY RUN MODE - No files will be modified")
    
    # Create output directory
    if not args.dry_run:
        args.output_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize results
    data_results = {"files_processed": 0, "files_converted": 0, "files_failed": 0, "errors": []}
    config_results = {"files_processed": 0, "files_converted": 0, "files_failed": 0, "errors": []}
    checkpoint_results = {"checkpoints_processed": 0, "checkpoints_migrated": 0, "checkpoints_failed": 0, "errors": []}
    api_test_results = {"checkpoints_tested": 0, "checkpoints_compatible": 0, "compatibility_issues": []}
    
    try:
        # Migrate data files
        if not args.skip_data:
            if args.dry_run:
                logger.logger.info("Would migrate data files...")
            else:
                data_results = migrate_data_files(args.input_dir, args.output_dir, logger)
        
        # Migrate configuration files
        if not args.skip_configs:
            if args.dry_run:
                logger.logger.info("Would migrate configuration files...")
            else:
                config_results = migrate_config_files(args.input_dir, args.output_dir, logger)
        
        # Migrate checkpoints
        if not args.skip_checkpoints:
            if args.dry_run:
                logger.logger.info("Would migrate checkpoint files...")
            else:
                checkpoint_results = migrate_checkpoints(args.input_dir, args.output_dir, logger)
        
        # Test API compatibility
        if not args.skip_api_tests and not args.dry_run:
            migrated_checkpoints = []
            if checkpoint_results.get("migration_reports"):
                migrated_checkpoints = [
                    report["target_path"] for report in checkpoint_results["migration_reports"]
                    if report.get("success", False)
                ]
            
            if migrated_checkpoints:
                api_test_results = test_api_compatibility(migrated_checkpoints, logger)
        
        # Create migration summary
        if not args.dry_run:
            create_migration_summary(
                args.output_dir,
                data_results,
                config_results,
                checkpoint_results,
                api_test_results
            )
        
        # Print final summary
        logger.logger.info("\n" + "="*60)
        logger.logger.info("MIGRATION COMPLETED")
        logger.logger.info("="*60)
        logger.logger.info(f"Data files: {data_results['files_converted']}/{data_results['files_processed']} converted")
        logger.logger.info(f"Config files: {config_results['files_converted']}/{config_results['files_processed']} converted")
        logger.logger.info(f"Checkpoints: {checkpoint_results['checkpoints_migrated']}/{checkpoint_results['checkpoints_processed']} migrated")
        logger.logger.info(f"API compatibility: {api_test_results['checkpoints_compatible']}/{api_test_results['checkpoints_tested']} compatible")
        
        if not args.dry_run:
            logger.logger.info(f"\nMigrated files saved to: {args.output_dir}")
            logger.logger.info(f"Migration summary: {args.output_dir / 'migration_summary.json'}")
        
        # Check for errors
        total_errors = (
            len(data_results.get("errors", [])) +
            len(config_results.get("errors", [])) +
            len(checkpoint_results.get("errors", [])) +
            len(api_test_results.get("compatibility_issues", []))
        )
        
        if total_errors > 0:
            logger.logger.warning(f"\n⚠ Migration completed with {total_errors} errors/warnings")
            logger.logger.warning("Check the detailed migration summary for more information")
            return 1
        else:
            logger.logger.info("\n✓ Migration completed successfully with no errors")
            return 0
    
    except Exception as e:
        logger.logger.error(f"Migration failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())