#!/usr/bin/env python3
"""
Test script to verify API startup and basic functionality.
This script tests the actual API server to ensure backward compatibility.
"""

import sys
import os
import time
import requests
import subprocess
import threading
from typing import Dict, Any

# Add src to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def start_api_server():
    """Start the API server in a subprocess."""
    try:
        # Start the server
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "src.inference.api_fastapi:app",
            "--host", "127.0.0.1",
            "--port", "8001",
            "--log-level", "warning"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for server to start
        time.sleep(5)
        
        return process
    except Exception as e:
        print(f"Failed to start API server: {e}")
        return None

def test_api_endpoints():
    """Test API endpoints for backward compatibility."""
    base_url = "http://127.0.0.1:8001"
    
    print("Testing API backward compatibility...")
    
    # Test 1: Health check
    print("\n1. Testing health check endpoint...")
    try:
        response = requests.get(f"{base_url}/ping", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {data}")
            
            # Verify expected fields
            required_fields = ["status", "model_loaded", "available_heads"]
            for field in required_fields:
                if field not in data:
                    print(f"   ❌ Missing field: {field}")
                    return False
                else:
                    print(f"   ✅ Field present: {field}")
            
            if not data.get("model_loaded", False):
                print("   ⚠️  Model not loaded - skipping inference tests")
                return True
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Health check error: {e}")
        return False
    
    # Test 2: Prediction endpoint
    print("\n2. Testing prediction endpoint...")
    try:
        test_request = {
            "text": "Jan Jansen woont in Amsterdam.",
            "tasks": ["ner"]
        }
        
        response = requests.post(
            f"{base_url}/predict", 
            json=test_request,
            timeout=30
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response keys: {list(data.keys())}")
            
            # Verify response structure
            if "text" not in data:
                print("   ❌ Missing 'text' field")
                return False
            
            if "results" not in data:
                print("   ❌ Missing 'results' field")
                return False
            
            if data["text"] != test_request["text"]:
                print("   ❌ Text field doesn't match input")
                return False
            
            print("   ✅ Response structure valid")
            
            # Check NER results if present
            if "ner" in data["results"]:
                ner_results = data["results"]["ner"]
                print(f"   NER results: {len(ner_results)} entities")
                
                for i, entity in enumerate(ner_results):
                    required_entity_fields = ["text", "label", "start", "end", "confidence"]
                    for field in required_entity_fields:
                        if field not in entity:
                            print(f"   ❌ Entity {i} missing field: {field}")
                            return False
                    
                    # Verify field types
                    if not isinstance(entity["text"], str):
                        print(f"   ❌ Entity {i} 'text' not string")
                        return False
                    
                    if not isinstance(entity["label"], str):
                        print(f"   ❌ Entity {i} 'label' not string")
                        return False
                    
                    if not isinstance(entity["start"], int):
                        print(f"   ❌ Entity {i} 'start' not int")
                        return False
                    
                    if not isinstance(entity["end"], int):
                        print(f"   ❌ Entity {i} 'end' not int")
                        return False
                    
                    if not isinstance(entity["confidence"], (int, float)):
                        print(f"   ❌ Entity {i} 'confidence' not numeric")
                        return False
                    
                    # Verify confidence range
                    if not (0.0 <= entity["confidence"] <= 1.0):
                        print(f"   ❌ Entity {i} confidence out of range: {entity['confidence']}")
                        return False
                    
                    print(f"   ✅ Entity {i}: {entity['text']} ({entity['label']}) [{entity['start']}:{entity['end']}] conf={entity['confidence']:.3f}")
                
                print("   ✅ All entities valid")
            else:
                print("   ℹ️  No NER results (expected for base model)")
            
        elif response.status_code == 503:
            print("   ⚠️  Model not available (503)")
            return True
        else:
            print(f"   ❌ Prediction failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Error text: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Prediction error: {e}")
        return False
    
    # Test 3: Annotation endpoint
    print("\n3. Testing annotation endpoint...")
    try:
        test_request = {
            "text": "Jan Jansen en Marie de Wit wonen in Amsterdam."
        }
        
        response = requests.post(
            f"{base_url}/annotate", 
            json=test_request,
            timeout=30
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response keys: {list(data.keys())}")
            
            # Verify response structure
            required_fields = ["original_text", "annotated_text", "entities", "html_output"]
            for field in required_fields:
                if field not in data:
                    print(f"   ❌ Missing field: {field}")
                    return False
                else:
                    print(f"   ✅ Field present: {field}")
            
            if data["original_text"] != test_request["text"]:
                print("   ❌ Original text doesn't match input")
                return False
            
            print("   ✅ Annotation response valid")
            
        elif response.status_code == 503:
            print("   ⚠️  Model not available (503)")
            return True
        else:
            print(f"   ❌ Annotation failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Annotation error: {e}")
        return False
    
    # Test 4: Query parameter compatibility
    print("\n4. Testing query parameter compatibility...")
    try:
        test_request = {
            "text": "Jan Jansen woont in Amsterdam."
        }
        
        response = requests.post(
            f"{base_url}/predict?tasks=ner", 
            json=test_request,
            timeout=30
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code in [200, 503]:
            print("   ✅ Query parameter format accepted")
        else:
            print(f"   ❌ Query parameter test failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Query parameter error: {e}")
        return False
    
    print("\n✅ All backward compatibility tests passed!")
    return True

def main():
    """Main test function."""
    print("=== API Backward Compatibility Test ===")
    
    # Start API server
    print("Starting API server...")
    server_process = start_api_server()
    
    if not server_process:
        print("❌ Failed to start API server")
        return False
    
    try:
        # Test API endpoints
        success = test_api_endpoints()
        
        if success:
            print("\n🎉 Backward compatibility verification successful!")
            print("✅ RobBERT-2023 API maintains BERTje compatibility")
        else:
            print("\n❌ Backward compatibility issues detected")
        
        return success
        
    finally:
        # Clean up server
        print("\nShutting down API server...")
        server_process.terminate()
        server_process.wait(timeout=5)
        print("Server stopped.")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)