{"timestamp": "2025-07-25 19:46:43", "system_info": {"python_version": "3.11.4 (tags/v3.11.4:d2340ef, Jun  7 2023, 05:45:37) [MSC v.1934 64 bit (AMD64)]", "pytorch_version": "2.5.1+cu121", "cuda_available": true, "cuda_version": "12.1", "gpu_count": 1, "cpu_count": 16, "total_memory_gb": 34.20461056}, "robbert_metrics": {"model_name": "RobBERT-2023", "model_loading_time": 3.137540817260742, "tokenization_speed": 27940.585450014376, "inference_speed": 1140.4049525785351, "memory_usage_mb": -5.808128000000579, "gpu_memory_usage_mb": 0.0, "cpu_utilization_percent": 12.35, "model_size_mb": 495.522968, "num_parameters": 123880742, "avg_inference_time_ms": 34.782965977986656, "std_inference_time_ms": 2.4660734921462746, "throughput_samples_per_sec": 28.749704686853825}, "legacy_metrics": {"model_name": "Legacy-Reference", "model_loading_time": 0.8362243175506592, "tokenization_speed": 83443.57618688772, "inference_speed": 2894.4445143950215, "memory_usage_mb": 0.4587519999986398, "gpu_memory_usage_mb": 0.0, "cpu_utilization_percent": 0.0, "model_size_mb": 433.990692, "num_parameters": 108497673, "avg_inference_time_ms": 13.12859853108724, "std_inference_time_ms": 0.8603506898985802, "throughput_samples_per_sec": 76.16959248407952}, "comparison": {"loading_time_ratio": 3.7520325006222595, "tokenization_speed_ratio": 0.33484405543017637, "inference_speed_ratio": 0.39399786277018867, "memory_usage_ratio": -12.660714285753087, "model_size_ratio": 1.1417824785974902, "parameter_ratio": 1.1417824785974904, "throughput_ratio": 0.37744333072102104}}