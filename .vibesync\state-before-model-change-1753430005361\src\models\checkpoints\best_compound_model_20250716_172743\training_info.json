{"epoch": 2, "best_f1": 0.8293220924181367, "target_f1": 0.85, "entity_f1_scores": {"PER": 0.8858939802336029, "LOC": 0.8, "ORG": 0.7772277227722773, "MISC": 0.8541666666666666}, "training_args": {"data": "data_sets/combined_training.json", "max_epochs": 3, "batch_size": 8, "learning_rate": 2e-05, "target_f1": 0.85, "patience": 2, "output_dir": "src/models/checkpoints"}, "training_history": [{"epoch": 1, "train_loss": 1.5501857706478663, "val_loss": 0.7910366488827599, "val_f1": 0.8034359608534003, "entity_f1_scores": {"PER": 0.8870967741935484, "LOC": 0.7154471544715447, "ORG": 0.7570332480818415, "MISC": 0.8541666666666666}}, {"epoch": 2, "train_loss": 0.5094836897083691, "val_loss": 0.673121254477236, "val_f1": 0.8293220924181367, "entity_f1_scores": {"PER": 0.8858939802336029, "LOC": 0.8, "ORG": 0.7772277227722773, "MISC": 0.8541666666666666}}]}