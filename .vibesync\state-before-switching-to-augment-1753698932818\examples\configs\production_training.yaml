hf_training:
  # Model configuration
  model_name: "DTAI-KULeuven/robbert-2023-dutch-base"
  
  # Training parameters (optimized for production)
  epochs: 5
  batch_size: 16
  eval_batch_size: 16
  learning_rate: 2e-5
  weight_decay: 0.01
  warmup_steps: 1000
  warmup_ratio: 0.1
  
  # Evaluation and logging
  eval_steps: 200
  logging_steps: 100
  save_steps: 1000
  evaluation_strategy: "steps"
  
  # Early stopping (more patient for production)
  early_stopping_patience: 5
  early_stopping_threshold: 0.0001
  load_best_model_at_end: true
  metric_for_best_model: "eval_f1"
  
  # Class balancing for imbalanced datasets
  use_class_weights: true
  class_weight_method: "balanced"
  per_head_class_weights: true
  save_class_weights: true
  
  # WandB integration with enhanced logging
  wandb_project: "robbert2023-ner-production"
  wandb_entity: "slippydongle"
  wandb_tags: ["robbert-2023", "dutch-nlp", "ner", "production"]
  wandb_notes: "Production Hugging Face Trainer configuration for RobBERT-2023 NER"
  wandb_log_confusion_matrix: true
  wandb_log_per_class_metrics: true
  wandb_log_model_artifacts: true
  wandb_log_evaluation_tables: true
  
  # Hardware optimization
  use_gpu: true
  fp16: true
  gradient_accumulation_steps: 2
  dataloader_num_workers: 4
  dataloader_pin_memory: true
  gradient_checkpointing: true
  
  # Advanced training options
  lr_scheduler_type: "cosine"
  cosine_schedule_num_cycles: 0.5
  max_grad_norm: 1.0
  label_smoothing_factor: 0.1
  
  # Checkpointing
  save_total_limit: 5
  output_dir: "production_checkpoints"
  
  # Output and prediction settings
  save_predictions: true
  generate_model_card: true
  
  # Data processing
  max_length: 512
  
  # Reproducibility
  seed: 42
  
  # Versioning
  run_version: "v2.0"
  experiment_name: "production_training"