"""
Named Entity Recognition head for BERTje model.
"""

import torch
import torch.nn as nn
from typing import Optional, Dict, Any
from transformers import AutoConfig


class NERHead(nn.Module):
    """Token classification head for Named Entity Recognition."""
    
    def __init__(self, hidden_size: int, num_labels: int, dropout: float = 0.1):
        super().__init__()
        self.num_labels = num_labels
        self.dropout = nn.Dropout(dropout)
        self.classifier = nn.Linear(hidden_size, num_labels)
        
    def forward(self, hidden_states: torch.Tensor, labels: Optional[torch.Tensor] = None):
        """Forward pass through NER head."""
        hidden_states = self.dropout(hidden_states)
        logits = self.classifier(hidden_states)
        
        outputs = {"logits": logits}
        
        if labels is not None:
            loss_fct = nn.CrossEntropyLoss()
            loss = loss_fct(logits.view(-1, self.num_labels), labels.view(-1))
            outputs["loss"] = loss
            
        return outputs
    
    def get_num_labels(self) -> int:
        """Get number of labels for this head."""
        return self.num_labels
    
    @classmethod
    def load_from_pretrained(cls, model_path: str, **kwargs):
        """Load NER head from pretrained model."""
        config = AutoConfig.from_pretrained(model_path)
        return cls(
            hidden_size=config.hidden_size,
            num_labels=config.num_labels,
            **kwargs
        )