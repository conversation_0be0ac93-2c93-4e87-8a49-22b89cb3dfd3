{"timestamp": "2025-07-25 19:47:09", "system_info": {"python_version": "3.11.4 (tags/v3.11.4:d2340ef, Jun  7 2023, 05:45:37) [MSC v.1934 64 bit (AMD64)]", "pytorch_version": "2.5.1+cu121", "cuda_available": true, "cuda_version": "12.1", "gpu_count": 1, "cpu_count": 16, "total_memory_gb": 34.20461056}, "robbert_metrics": {"model_name": "RobBERT-2023", "model_loading_time": 2.9638121128082275, "tokenization_speed": 128050.87927993675, "inference_speed": 1507.6093454333877, "memory_usage_mb": 15.056895999998233, "gpu_memory_usage_mb": 0.0, "cpu_utilization_percent": 34.95, "model_size_mb": 495.522968, "num_parameters": 123880742, "avg_inference_time_ms": 34.94863510131836, "std_inference_time_ms": 4.73561772132599, "throughput_samples_per_sec": 28.613420727331274}, "legacy_metrics": {"model_name": "Legacy-Reference", "model_loading_time": 0.8389031887054443, "tokenization_speed": 268165.8247881911, "inference_speed": 3477.6348891356292, "memory_usage_mb": 0.3481600000013714, "gpu_memory_usage_mb": 0.0, "cpu_utilization_percent": 31.21666666666667, "model_size_mb": 433.990692, "num_parameters": 108497673, "avg_inference_time_ms": 13.18904028998481, "std_inference_time_ms": 2.440219801740443, "throughput_samples_per_sec": 75.8205281061547}, "comparison": {"loading_time_ratio": 3.5329608382843816, "tokenization_speed_ratio": 0.4775063316926265, "inference_speed_ratio": 0.43351570636217823, "memory_usage_ratio": 43.24705882335399, "model_size_ratio": 1.1417824785974902, "parameter_ratio": 1.1417824785974904, "throughput_ratio": 0.37738355880705865}}