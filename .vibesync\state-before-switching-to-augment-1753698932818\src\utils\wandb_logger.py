#!/usr/bin/env python3
"""
Weights & Biases integration for RobBERT-2023 training.
"""

import os
import wandb
import torch
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import json
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class WandBLogger:
    """
    Weights & Biases logger for RobBERT-2023 multi-task training.
    
    Supports both single-head and multi-task training with appropriate
    project organization and metric tracking.
    """
    
    def __init__(self, config: Dict[str, Any], heads: List[str], args: Any):
        """
        Initialize wandb logger.
        
        Args:
            config: wandb configuration from wandb.yaml
            heads: List of heads being trained
            args: Training arguments
        """
        self.config = config
        self.heads = heads
        self.args = args
        self.enabled = config.get('enabled', True)
        self.run = None
        self.step = 0
        
        if self.enabled:
            self._setup_wandb()
    
    def _setup_wandb(self):
        """Setup wandb run with appropriate project and configuration."""
        if not self.enabled:
            return
            
        # Set API key
        api_key = self.config.get('api_key')
        if api_key:
            os.environ['WANDB_API_KEY'] = api_key
        
        # Determine project name based on heads
        project_name = self._get_project_name()
        
        # Generate run name if not provided
        run_name = self._generate_run_name()
        
        # Collect tags
        tags = self._collect_tags()
        
        # Setup run configuration
        run_config = self._build_run_config()
        
        # Initialize wandb run
        try:
            self.run = wandb.init(
                entity=self.config.get('entity'),
                project=project_name,
                name=run_name,
                tags=tags,
                notes=self._get_notes(),
                config=run_config,
                reinit=True
            )
            logger.info(f"Initialized wandb run: {project_name}/{run_name}")
            
        except Exception as e:
            logger.error(f"Failed to initialize wandb: {e}")
            self.enabled = False
    
    def _get_project_name(self) -> str:
        """Determine appropriate project name based on heads being trained."""
        base_project = self.config.get('project', 'robbert2023')
        
        if len(self.heads) == 1:
            # Single head training - use head-specific project
            head = self.heads[0]
            head_config = self.config.get('heads', {}).get(head, {})
            suffix = head_config.get('project_suffix', f'-{head}')
            return f"{base_project}{suffix}"
        else:
            # Multi-task training - use multitask project
            multitask_config = self.config.get('multitask', {})
            suffix = multitask_config.get('project_suffix', '-multitask')
            return f"{base_project}{suffix}"
    
    def _generate_run_name(self) -> str:
        """Generate run name if not provided."""
        run_name = self.config.get('run_name')
        if run_name:
            return run_name
            
        # Auto-generate based on configuration
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        heads_str = "-".join(self.heads)
        epochs = getattr(self.args, 'epochs', 3)
        batch_size = getattr(self.args, 'batch_size', 8)
        lr = getattr(self.args, 'learning_rate', 2e-5)
        
        return f"{heads_str}_e{epochs}_bs{batch_size}_lr{lr:.0e}_{timestamp}"
    
    def _collect_tags(self) -> List[str]:
        """Collect tags for the run."""
        base_tags = self.config.get('tags', [])
        
        if len(self.heads) == 1:
            # Single head - use head-specific tags
            head = self.heads[0]
            head_config = self.config.get('heads', {}).get(head, {})
            head_tags = head_config.get('tags', [])
            return base_tags + head_tags + [f"head-{head}"]
        else:
            # Multi-task - use multitask tags
            multitask_config = self.config.get('multitask', {})
            multitask_tags = multitask_config.get('tags', [])
            head_tags = [f"head-{head}" for head in self.heads]
            return base_tags + multitask_tags + head_tags
    
    def _get_notes(self) -> str:
        """Get notes for the run."""
        if len(self.heads) == 1:
            head = self.heads[0]
            head_config = self.config.get('heads', {}).get(head, {})
            return head_config.get('notes', self.config.get('notes', ''))
        else:
            multitask_config = self.config.get('multitask', {})
            return multitask_config.get('notes', self.config.get('notes', ''))
    
    def _build_run_config(self) -> Dict[str, Any]:
        """Build configuration dictionary for wandb."""
        config = {
            'heads': self.heads,
            'epochs': getattr(self.args, 'epochs', 3),
            'batch_size': getattr(self.args, 'batch_size', 8),
            'learning_rate': getattr(self.args, 'learning_rate', 2e-5),
            'model_name': getattr(self.args, 'model_name', 'DTAI-KULeuven/robbert-2023-dutch-base'),
            'max_length': 512,
            'optimizer': 'AdamW',
            'scheduler': 'linear_with_warmup',
        }
        
        # Add training-specific config if available
        if hasattr(self.args, 'config_dict'):
            training_config = self.args.config_dict.get('training', {})
            config.update({
                'weight_decay': training_config.get('weight_decay', 0.01),
                'warmup_steps': training_config.get('warmup_steps', 500),
                'max_grad_norm': training_config.get('max_grad_norm', 1.0),
                'loss_weights': training_config.get('loss_weights', {}),
            })
        
        # Add dataset info if available
        if hasattr(self.args, 'data') and self.args.data:
            config['dataset_path'] = self.args.data
        
        return config
    
    def log_metrics(self, metrics: Dict[str, float], step: Optional[int] = None):
        """
        Log metrics to wandb.
        
        Args:
            metrics: Dictionary of metric names and values
            step: Training step (uses internal counter if None)
        """
        if not self.enabled or not self.run:
            return
            
        if step is None:
            step = self.step
            
        try:
            self.run.log(metrics, step=step)
        except Exception as e:
            logger.error(f"Failed to log metrics to wandb: {e}")
    
    def log_model_artifact(self, model_path: Union[str, Path], 
                          name: str, metadata: Optional[Dict] = None):
        """
        Log model as wandb artifact.
        
        Args:
            model_path: Path to model directory or file
            name: Artifact name
            metadata: Additional metadata
        """
        if not self.enabled or not self.run:
            return
            
        if not self.config.get('log_model', True):
            return
            
        try:
            artifact = wandb.Artifact(
                name=name,
                type="model",
                metadata=metadata or {}
            )
            
            if Path(model_path).is_dir():
                artifact.add_dir(str(model_path))
            else:
                artifact.add_file(str(model_path))
                
            self.run.log_artifact(artifact)
            logger.info(f"Logged model artifact: {name}")
            
        except Exception as e:
            logger.error(f"Failed to log model artifact: {e}")
    
    def log_gradients(self, model: torch.nn.Module):
        """
        Log gradient information.
        
        Args:
            model: PyTorch model
        """
        if not self.enabled or not self.run:
            return
            
        if not self.config.get('log_gradients', False):
            return
            
        try:
            self.run.watch(model, log_freq=self.config.get('log_freq', 100))
        except Exception as e:
            logger.error(f"Failed to log gradients: {e}")
    
    def log_learning_rate(self, lr: float, step: Optional[int] = None):
        """Log current learning rate."""
        self.log_metrics({'learning_rate': lr}, step)
    
    def log_epoch_summary(self, epoch: int, train_metrics: Dict[str, float], 
                         val_metrics: Optional[Dict[str, float]] = None):
        """
        Log epoch summary metrics.
        
        Args:
            epoch: Current epoch
            train_metrics: Training metrics
            val_metrics: Validation metrics (optional)
        """
        if not self.enabled:
            return
            
        summary_metrics = {
            'epoch': epoch,
            **{f'train_{k}': v for k, v in train_metrics.items()}
        }
        
        if val_metrics:
            summary_metrics.update({
                f'val_{k}': v for k, v in val_metrics.items()
            })
        
        self.log_metrics(summary_metrics)
    
    def increment_step(self):
        """Increment internal step counter."""
        self.step += 1
    
    def finish(self):
        """Finish wandb run."""
        if self.enabled and self.run:
            try:
                self.run.finish()
                logger.info("Finished wandb run")
            except Exception as e:
                logger.error(f"Error finishing wandb run: {e}")


def create_wandb_logger(wandb_config: Dict[str, Any], heads: List[str], 
                       args: Any) -> WandBLogger:
    """
    Factory function to create wandb logger.
    
    Args:
        wandb_config: wandb configuration dictionary
        heads: List of heads being trained
        args: Training arguments
        
    Returns:
        WandBLogger instance
    """
    return WandBLogger(wandb_config, heads, args)