#!/usr/bin/env python3
"""
Standalone test for error handling functionality.
"""

import os
import gc
import time
import json
import torch
import psutil
import traceback
import functools
from typing import Dict, List, Optional, Any, Union, Callable, Tuple
from pathlib import Path
from contextlib import contextmanager

# Simple logger for testing
class SimpleLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")
    def debug(self, msg): print(f"DEBUG: {msg}")

# Simple exception classes for testing
class TrainingError(Exception): pass
class DataProcessingError(Exception): pass

class CUDAMemoryManager:
    """CUDA memory management with automatic batch size reduction."""
    
    def __init__(self, logger=None):
        """Initialize CUDA memory manager."""
        self.logger = logger or SimpleLogger()
        
    def get_memory_info(self) -> Dict[str, Any]:
        """Get current memory information."""
        memory_info = {
            "cuda_available": torch.cuda.is_available(),
            "system_memory_gb": psutil.virtual_memory().total / 1e9,
            "system_memory_available_gb": psutil.virtual_memory().available / 1e9,
            "system_memory_percent": psutil.virtual_memory().percent
        }
        
        if torch.cuda.is_available():
            memory_info.update({
                "cuda_memory_allocated_gb": torch.cuda.memory_allocated() / 1e9,
                "cuda_memory_reserved_gb": torch.cuda.memory_reserved() / 1e9,
                "cuda_memory_total_gb": torch.cuda.get_device_properties(0).total_memory / 1e9,
                "cuda_device_count": torch.cuda.device_count(),
                "cuda_current_device": torch.cuda.current_device()
            })
        
        return memory_info
    
    def clear_cuda_cache(self):
        """Clear CUDA cache to free up memory."""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            gc.collect()
            self.logger.info("Cleared CUDA cache and ran garbage collection")

class DataProcessingErrorHandler:
    """Error handler for data loading and preprocessing operations."""
    
    def __init__(self, logger=None):
        """Initialize data processing error handler."""
        self.logger = logger or SimpleLogger()
        self.error_counts = {}
    
    def safe_json_load(self, file_path: str) -> List[Dict[str, Any]]:
        """Safely load JSON data with error handling."""
        try:
            if not os.path.exists(file_path):
                raise DataProcessingError(f"Data file not found: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list):
                    self.logger.info(f"Loaded {len(data)} records from {file_path}")
                    return data
                else:
                    return [data]
        
        except Exception as e:
            raise DataProcessingError(f"Failed to load data from {file_path}: {str(e)}") from e
    
    def get_error_summary(self) -> Dict[str, int]:
        """Get summary of encountered errors."""
        return self.error_counts.copy()

class TrainingErrorHandler:
    """Comprehensive error handler for training operations."""
    
    def __init__(self, logger=None):
        """Initialize comprehensive training error handler."""
        self.logger = logger or SimpleLogger()
        
        # Initialize sub-handlers
        self.cuda_manager = CUDAMemoryManager(logger)
        self.data_handler = DataProcessingErrorHandler(logger)
        
        # Training state tracking
        self.training_errors = []
    
    def handle_training_error(self, error: Exception) -> bool:
        """Handle training errors with automatic recovery strategies."""
        error_info = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": time.time()
        }
        
        self.training_errors.append(error_info)
        self.logger.error(f"Training error: {error}")
        
        # Handle CUDA out of memory errors
        if isinstance(error, RuntimeError) and "out of memory" in str(error).lower():
            return self._handle_cuda_oom(error)
        
        # Handle data loading errors
        elif isinstance(error, DataProcessingError):
            return self._handle_data_error(error)
        
        # Unknown error - log and re-raise
        else:
            self.logger.error(f"Unhandled training error: {error}")
            return False
    
    def _handle_cuda_oom(self, error: Exception) -> bool:
        """Handle CUDA out of memory errors."""
        try:
            self.cuda_manager.clear_cuda_cache()
            self.logger.info("Cleared CUDA cache due to OOM error")
            return False  # Cannot automatically recover from OOM
            
        except Exception as recovery_error:
            self.logger.error(f"Failed to recover from CUDA OOM: {recovery_error}")
        
        return False
    
    def _handle_data_error(self, error: Exception) -> bool:
        """Handle data processing errors."""
        self.logger.info("Suggestions for data error resolution:")
        self.logger.info("1. Check input data format and structure")
        self.logger.info("2. Validate entity annotations and spans")
        return False
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of all encountered errors."""
        error_types = {}
        for error in self.training_errors:
            error_type = error["error_type"]
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return {
            "total_errors": len(self.training_errors),
            "error_types": error_types,
            "data_errors": self.data_handler.get_error_summary()
        }
    
    def save_error_report(self, output_path: str):
        """Save comprehensive error report."""
        try:
            report = {
                "summary": self.get_error_summary(),
                "detailed_errors": self.training_errors,
                "cuda_memory_info": self.cuda_manager.get_memory_info()
            }
            
            with open(output_path, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"Saved error report to {output_path}")
            
        except Exception as e:
            self.logger.error(f"Error saving error report: {e}")

def main():
    """Test the error handling functionality."""
    print("🧪 Testing Standalone Error Handling Implementation\n")
    
    # Test 1: Basic initialization
    print("1. Testing basic initialization...")
    try:
        handler = TrainingErrorHandler()
        print("✅ TrainingErrorHandler initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize TrainingErrorHandler: {e}")
        return
    
    # Test 2: Memory info
    print("\n2. Testing memory information...")
    try:
        memory_info = handler.cuda_manager.get_memory_info()
        print(f"✅ Memory info retrieved: CUDA available = {memory_info['cuda_available']}")
        print(f"   System memory: {memory_info['system_memory_gb']:.1f}GB")
    except Exception as e:
        print(f"❌ Failed to get memory info: {e}")
    
    # Test 3: Error handling
    print("\n3. Testing error handling...")
    try:
        # Test data processing error
        test_error = DataProcessingError("Test data processing error")
        handled = handler.handle_training_error(test_error)
        print(f"✅ Data processing error handled: recovery = {handled}")
        
        # Test runtime error (simulating CUDA OOM)
        test_oom_error = RuntimeError("CUDA out of memory: test error")
        handled = handler.handle_training_error(test_oom_error)
        print(f"✅ CUDA OOM error handled: recovery = {handled}")
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
    
    # Test 4: Error summary
    print("\n4. Testing error summary...")
    try:
        summary = handler.get_error_summary()
        print(f"✅ Error summary: {summary}")
    except Exception as e:
        print(f"❌ Failed to get error summary: {e}")
    
    # Test 5: Save error report
    print("\n5. Testing error report saving...")
    try:
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            report_path = f.name
        
        handler.save_error_report(report_path)
        
        # Check if report was created
        if Path(report_path).exists():
            print("✅ Error report saved successfully")
            # Clean up
            Path(report_path).unlink()
        else:
            print("❌ Error report was not created")
    except Exception as e:
        print(f"❌ Failed to save error report: {e}")
    
    print("\n✅ Standalone error handling tests completed!")

if __name__ == "__main__":
    main()