#!/usr/bin/env python3
"""
Simple test for error handling import.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    print("Testing imports...")
    
    # Test individual imports
    from src.utils.logging_utils import get_logger
    print("✅ Logging utils imported")
    
    from src.exceptions import TrainingError, DataProcessingError
    print("✅ Exceptions imported")
    
    # Test error handling module
    import src.training.error_handling as eh
    print("✅ Error handling module imported")
    
    # Check what's in the module
    print(f"Module contents: {[name for name in dir(eh) if not name.startswith('_')]}")
    
    # Try to access the classes
    if hasattr(eh, 'TrainingErrorHandler'):
        print("✅ TrainingErrorHandler found")
        handler = eh.TrainingErrorHandler()
        print("✅ TrainingErrorHandler instantiated")
    else:
        print("❌ TrainingErrorHandler not found")
    
    if hasattr(eh, 'CUDAMemoryManager'):
        print("✅ CUDAMemoryManager found")
    else:
        print("❌ CUDAMemoryManager not found")
        
    if hasattr(eh, 'DataProcessingErrorHandler'):
        print("✅ DataProcessingErrorHandler found")
    else:
        print("❌ DataProcessingErrorHandler not found")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()