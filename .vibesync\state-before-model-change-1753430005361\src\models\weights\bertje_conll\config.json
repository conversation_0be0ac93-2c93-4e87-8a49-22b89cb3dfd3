{"_num_labels": 9, "architectures": ["BertForTokenClassification"], "attention_probs_dropout_prob": 0.2, "bad_words_ids": null, "bos_token_id": null, "decoder_start_token_id": null, "do_sample": false, "early_stopping": false, "eos_token_id": null, "finetuning_task": null, "hidden_act": "gelu", "hidden_dropout_prob": 0.3, "hidden_size": 768, "id2label": {"0": "B-loc", "1": "B-misc", "2": "B-org", "3": "B-per", "4": "I-loc", "5": "I-misc", "6": "I-org", "7": "I-per", "8": "O"}, "initializer_range": 0.02, "intermediate_size": 3072, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"B-loc": 0, "B-misc": 1, "B-org": 2, "B-per": 3, "I-loc": 4, "I-misc": 5, "I-org": 6, "I-per": 7, "O": 8}, "layer_norm_eps": 1e-12, "length_penalty": 1.0, "max_length": 20, "max_position_embeddings": 512, "min_length": 0, "model_type": "bert", "no_repeat_ngram_size": 0, "num_attention_heads": 12, "num_beams": 1, "num_hidden_layers": 12, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_past": true, "pad_token_id": 0, "prefix": null, "pruned_heads": {}, "repetition_penalty": 1.0, "task_specific_params": null, "temperature": 1.0, "top_k": 50, "top_p": 1.0, "torchscript": false, "type_vocab_size": 2, "use_bfloat16": false, "vocab_size": 30000}