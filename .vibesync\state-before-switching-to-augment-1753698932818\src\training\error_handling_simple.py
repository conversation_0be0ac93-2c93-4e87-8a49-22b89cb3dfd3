"""
Simplified error handling for testing.
"""

import os
import gc
import time
import torch
import psutil
import traceback
import functools
from typing import Dict, List, Optional, Any, Union, Callable, Tuple
from pathlib import Path
from contextlib import contextmanager

try:
    from ..utils.logging_utils import get_logger, RobBERTLogger
    from ..exceptions import (
        TrainingError, DataProcessingError, TokenizationError, 
        LabelAlignmentError, MemoryError as RobBERTMemoryError,
        ModelLoadingError, ConfigurationError
    )
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent.parent))
    
    from src.utils.logging_utils import get_logger, RobBERTLogger
    from src.exceptions import (
        TrainingError, DataProcessingError, TokenizationError, 
        LabelAlignmentError, MemoryError as RobBERTMemoryError,
        ModelLoadingError, ConfigurationError
    )


class SimpleErrorHandler:
    """Simple error handler for testing."""
    
    def __init__(self, logger: Optional[RobBERTLogger] = None):
        """Initialize simple error handler."""
        self.logger = logger or get_logger(f"{__name__}.{self.__class__.__name__}")
        self.errors = []
    
    def handle_error(self, error: Exception) -> bool:
        """Handle an error."""
        self.errors.append(str(error))
        self.logger.error(f"Handled error: {error}")
        return True
    
    def get_error_count(self) -> int:
        """Get number of errors handled."""
        return len(self.errors)


print("Simple error handling module loaded successfully")