# RobBERT-2023 Checkpoint Management

This document describes the comprehensive checkpoint management system for RobBERT-2023 fine-tuned models, including directory organization, validation, backup strategies, and CLI utilities.

## Overview

The checkpoint management system provides:

- **Organized Storage**: Structured directory layout for different types of checkpoints
- **Validation**: Comprehensive checkpoint integrity and compatibility checking
- **Metadata Management**: Rich metadata storage with training information
- **Backup & Recovery**: Automated backup creation and cleanup utilities
- **CLI Tools**: Command-line interface for checkpoint operations
- **Integration**: Seamless integration with training and inference pipelines

## Directory Structure

The checkpoint management system organizes RobBERT-2023 models in a structured hierarchy:

```
models/
└── robbert2023-per/
    ├── fine-tuned/          # Production fine-tuned models
    │   ├── checkpoint_1/
    │   ├── checkpoint_2/
    │   └── ...
    ├── base-models/         # Base RobBERT models (cached)
    ├── experiments/         # Experimental checkpoints organized by experiment
    │   ├── experiment_1/
    │   │   ├── checkpoint_a/
    │   │   └── checkpoint_b/
    │   └── experiment_2/
    │       └── checkpoint_c/
    └── backups/            # Backup copies of important checkpoints
        ├── checkpoint_1_backup_20250725_120000/
        └── ...
```

### Directory Purposes

- **`fine-tuned/`**: Production-ready fine-tuned models for deployment
- **`base-models/`**: Cached copies of base RobBERT models
- **`experiments/`**: Experimental checkpoints organized by experiment name
- **`backups/`**: Backup copies created before cleanup or major changes

## Checkpoint Structure

Each checkpoint directory contains:

### Required Files
- `config.json` - Model configuration (Hugging Face format)
- `pytorch_model.bin` - Model weights
- `tokenizer_config.json` - Tokenizer configuration
- `vocab.json` - Vocabulary mapping
- `merges.txt` - BPE merge rules

### Metadata Files
- `checkpoint_metadata.json` - Comprehensive checkpoint metadata
- `checkpoint_manifest.json` - File integrity information with hashes
- `training_info.json` - Training-specific information (legacy compatibility)
- `backup_metadata.json` - Backup information (for backup directories)

### Example Checkpoint Metadata

```json
{
  "checkpoint_name": "robbert2023_checkpoint_20250725_184935",
  "created_at": "2025-07-25T18:49:35.123456",
  "model_type": "RobBERT-2023-dutch-base",
  "model_name": "DTAI-KULeuven/robbert-2023-dutch-base",
  "tokenizer_type": "byte_level_bpe",
  "model_heads": ["ner", "compliance", "label"],
  "num_parameters": 125000000,
  "trainable_parameters": 125000000,
  "pytorch_version": "2.3.0",
  "transformers_version": "4.41.x",
  "heads": ["ner"],
  "epochs": 3,
  "batch_size": 8,
  "learning_rate": 2e-05,
  "training_completed_at": "2025-07-25T18:49:35.123456",
  "dataset_path": "data/training.jsonl"
}
```

## Python API

### CheckpointManager Class

The main class for checkpoint operations:

```python
from src.utils.checkpoint_manager import CheckpointManager

# Initialize manager
manager = CheckpointManager(base_dir="models")

# Save checkpoint
checkpoint_path = manager.save_checkpoint(
    model=model,
    checkpoint_name="my_checkpoint",
    metadata={"experiment": "test_run"},
    experiment_name="my_experiment"  # Optional
)

# Load checkpoint
model = manager.load_checkpoint("my_checkpoint")

# List checkpoints
checkpoints = manager.list_checkpoints()

# Validate checkpoint
is_valid = manager.validate_checkpoint(checkpoint_path)

# Backup checkpoint
backup_path = manager.backup_checkpoint("my_checkpoint")

# Cleanup old checkpoints
manager.cleanup_old_checkpoints(keep_count=5)
```

### Convenience Functions

For simple operations:

```python
from src.utils.checkpoint_manager import (
    save_robbert_checkpoint,
    load_robbert_checkpoint,
    list_robbert_checkpoints
)

# Save checkpoint
checkpoint_path = save_robbert_checkpoint(
    model=model,
    checkpoint_name="my_checkpoint",
    metadata={"notes": "Best performing model"}
)

# Load checkpoint
model = load_robbert_checkpoint("my_checkpoint")

# List checkpoints
checkpoints = list_robbert_checkpoints()
```

### Training Integration

The training script automatically uses the checkpoint management system:

```bash
# Train with checkpoint management
python -m src.training.train_multitask \
    --heads ner \
    --epochs 3 \
    --experiment my_experiment \
    --data data/training.jsonl
```

This will:
1. Save checkpoint using the checkpoint manager
2. Create comprehensive metadata
3. Validate the saved checkpoint
4. Maintain backward compatibility with legacy format

## CLI Utilities

The `scripts/checkpoint_management.py` script provides command-line access to checkpoint operations.

### List Checkpoints

```bash
# List all checkpoints
python scripts/checkpoint_management.py list

# List checkpoints for specific experiment
python scripts/checkpoint_management.py list --experiment my_experiment
```

### Validate Checkpoint

```bash
# Validate specific checkpoint
python scripts/checkpoint_management.py validate robbert2023_checkpoint_20250725_184935
```

### Create Backup

```bash
# Create backup with auto-generated name
python scripts/checkpoint_management.py backup robbert2023_checkpoint_20250725_184935

# Create backup with custom name
python scripts/checkpoint_management.py backup robbert2023_checkpoint_20250725_184935 --backup-name important_model_backup
```

### Cleanup Old Checkpoints

```bash
# Keep 3 most recent checkpoints, backup others
python scripts/checkpoint_management.py cleanup --keep 3

# Cleanup specific experiment
python scripts/checkpoint_management.py cleanup --keep 2 --experiment my_experiment

# Force cleanup without confirmation
python scripts/checkpoint_management.py cleanup --keep 5 --force
```

### Test Loading

```bash
# Test loading checkpoint
python scripts/checkpoint_management.py load robbert2023_checkpoint_20250725_184935

# Test loading with inference test
python scripts/checkpoint_management.py load robbert2023_checkpoint_20250725_184935 --test-inference
```

### System Information

```bash
# Show system information and checkpoint summary
python scripts/checkpoint_management.py info
```

## Validation System

The checkpoint validation system performs comprehensive checks:

### File Validation
- Verifies all required files exist
- Checks file integrity using SHA256 hashes
- Validates file sizes against manifest

### Model Architecture Validation
- Confirms RoBERTa-based architecture (RobBERT uses RoBERTa)
- Validates key parameters (hidden_size, vocab_size, etc.)
- Checks compatibility with supported model versions

### Tokenizer Validation
- Verifies tokenizer type (byte-level BPE)
- Validates vocabulary size
- Tests basic tokenization functionality

### Model Weights Validation
- Checks for required weight tensor keys
- Validates tensor dimensions
- Ensures compatibility with model architecture

### Example Validation Output

```bash
$ python scripts/checkpoint_management.py validate robbert2023_checkpoint_20250725_184935

Validating checkpoint: models/robbert2023-per/fine-tuned/robbert2023_checkpoint_20250725_184935
✅ Checkpoint validation passed

Checkpoint Details:
  Name: robbert2023_checkpoint_20250725_184935
  Size: 498.2 MB
  Created: 2025-07-25T18:49:35.123456
  Heads: ner
  Model: DTAI-KULeuven/robbert-2023-dutch-base
```

## Backup Strategy

The checkpoint management system implements a comprehensive backup strategy:

### Automatic Backups
- Created before checkpoint cleanup
- Generated with timestamped names
- Include original metadata and backup information

### Manual Backups
- On-demand backup creation via CLI or API
- Custom backup naming support
- Verification of backup integrity

### Backup Metadata
Each backup includes metadata about:
- Original checkpoint path
- Backup creation timestamp
- Backup reason (cleanup, manual, etc.)
- Original checkpoint metadata

## Error Handling

The system provides robust error handling:

### Common Errors
- **ModelLoadingError**: Checkpoint loading/saving failures
- **ValidationError**: Checkpoint validation failures
- **FileNotFoundError**: Missing checkpoint files
- **PermissionError**: File system permission issues

### Recovery Strategies
- Automatic fallback to legacy saving format
- Graceful degradation for validation failures
- Detailed error logging for troubleshooting
- Backup restoration capabilities

## Performance Considerations

### Storage Optimization
- Efficient directory organization
- File deduplication for common files
- Compressed backup storage (future enhancement)

### Memory Management
- Lazy loading of checkpoint metadata
- Streaming file operations for large checkpoints
- Memory-efficient validation processes

### Scalability
- Supports hundreds of checkpoints
- Efficient listing and search operations
- Parallel validation capabilities

## Migration from Legacy System

For existing checkpoints in the legacy format:

### Automatic Detection
The system automatically detects and works with legacy checkpoints in:
- `src/models/checkpoints/`
- Custom checkpoint directories

### Compatibility Layer
- Reads legacy `training_info.json` files
- Converts to new metadata format
- Maintains backward compatibility

### Migration Utility
```bash
# Future enhancement: migrate legacy checkpoints
python scripts/checkpoint_management.py migrate --from src/models/checkpoints --to models/robbert2023-per/fine-tuned
```

## Best Practices

### Naming Conventions
- Use descriptive checkpoint names
- Include timestamps for uniqueness
- Organize by experiment when appropriate

### Metadata Management
- Include comprehensive training information
- Document model purpose and performance
- Track dataset versions and preprocessing

### Backup Strategy
- Regular backups of important checkpoints
- Test backup restoration procedures
- Monitor backup storage usage

### Validation
- Validate checkpoints after saving
- Regular integrity checks for stored checkpoints
- Automated validation in CI/CD pipelines

## Troubleshooting

### Common Issues

#### Checkpoint Validation Fails
```bash
# Check specific validation errors
python scripts/checkpoint_management.py validate my_checkpoint

# Common fixes:
# 1. Verify all required files exist
# 2. Check file permissions
# 3. Validate model architecture compatibility
```

#### Loading Errors
```bash
# Test checkpoint loading
python scripts/checkpoint_management.py load my_checkpoint --test-inference

# Common fixes:
# 1. Check PyTorch version compatibility
# 2. Verify transformers library version
# 3. Ensure sufficient memory
```

#### Storage Issues
```bash
# Check storage usage
python scripts/checkpoint_management.py info

# Cleanup old checkpoints
python scripts/checkpoint_management.py cleanup --keep 3
```

### Debug Mode
Enable debug logging for detailed troubleshooting:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

from src.utils.checkpoint_manager import CheckpointManager
manager = CheckpointManager("models")
```

## Future Enhancements

### Planned Features
- **Compression**: Automatic checkpoint compression
- **Cloud Storage**: Integration with cloud storage backends
- **Versioning**: Semantic versioning for checkpoints
- **Metrics**: Performance metrics tracking
- **Distributed**: Multi-node checkpoint management

### Integration Opportunities
- **MLflow**: Integration with MLflow tracking
- **Weights & Biases**: W&B experiment tracking
- **DVC**: Data Version Control integration
- **Kubernetes**: Container orchestration support

## API Reference

### CheckpointManager

#### Methods

##### `__init__(base_dir: str = "models")`
Initialize checkpoint manager with base directory.

##### `save_checkpoint(model, checkpoint_name, metadata=None, experiment_name=None) -> Path`
Save model checkpoint with metadata and validation.

##### `load_checkpoint(checkpoint_path, **kwargs) -> MultiTaskRobBERT`
Load model checkpoint with validation.

##### `validate_checkpoint(checkpoint_path: Path) -> bool`
Validate checkpoint integrity and compatibility.

##### `list_checkpoints(experiment_name=None) -> List[Dict]`
List available checkpoints with metadata.

##### `backup_checkpoint(checkpoint_path, backup_name=None) -> Path`
Create backup of checkpoint.

##### `cleanup_old_checkpoints(keep_count=5, experiment_name=None)`
Clean up old checkpoints, keeping most recent.

### Convenience Functions

##### `save_robbert_checkpoint(model, checkpoint_name, metadata=None, experiment_name=None, base_dir="models") -> Path`
Convenience function to save RobBERT checkpoint.

##### `load_robbert_checkpoint(checkpoint_path, base_dir="models", **kwargs) -> MultiTaskRobBERT`
Convenience function to load RobBERT checkpoint.

##### `list_robbert_checkpoints(experiment_name=None, base_dir="models") -> List[Dict]`
Convenience function to list RobBERT checkpoints.

## Configuration

### Environment Variables
- `ROBBERT_CHECKPOINT_DIR`: Override default checkpoint directory
- `ROBBERT_BACKUP_RETENTION`: Default backup retention count
- `ROBBERT_VALIDATION_STRICT`: Enable strict validation mode

### Configuration File
Future enhancement: YAML configuration file for checkpoint management settings.

```yaml
# checkpoint_config.yaml
checkpoint_management:
  base_dir: "models"
  backup_retention: 10
  validation_strict: true
  compression_enabled: false
  cloud_storage:
    enabled: false
    provider: "s3"
    bucket: "my-checkpoints"
```