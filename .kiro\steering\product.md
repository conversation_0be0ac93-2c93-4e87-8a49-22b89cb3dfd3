# Product Overview

## RobBERT-2023 for Dutch Person Entity Recognition

A production-ready Python project hosting a RobBERT-2023 model for Dutch Named Entity Recognition, specifically focused on Person entity detection. The system is designed for on-premises deployment with CPU optimization and extensible multi-head architecture.

### Core Features

- **RobBERT-2023 Architecture**: Uses the latest RobBERT-2023-dutch-base model with byte-level BPE tokenization
- **Person Entity Focus**: Specialized for detecting person names in Dutch text with high accuracy
- **Extensible Multi-head Design**: Architecture supports adding additional classification heads as needed
- **Dutch Language Optimized**: Handles compound names, diacritics, and Dutch-specific linguistic patterns
- **Production Ready**: FastAPI server, batch processing CLI, comprehensive configuration management
- **Cross-platform**: Linux/macOS/Windows support with Python 3.9-3.11
- **CPU Optimized**: Designed for efficient CPU inference without GPU requirements
- **WandB Integration**: Comprehensive experiment tracking and model artifact management

### Entity Types Supported

- **PER**: Person names (<PERSON>, <PERSON>, <PERSON>)
  - Handles compound names with hyphens and particles
  - Supports Dutch naming conventions and prefixes
  - Optimized for both formal and informal name variations

### Target Use Cases

- Dutch document processing and person name extraction
- Privacy-aware text processing for Dutch organizations
- Research and development in Dutch NLP
- On-premises deployment without cloud dependencies
- Foundation for expanding to additional entity types

### Performance Characteristics

- **NER Accuracy**: 90.24% F1 score on CoNLL-2002 Dutch NER dataset
- **CPU Friendly**: Optimized for CPU inference with quantization support
- **Scalable**: Supports batch processing and horizontal scaling
- **Memory Efficient**: Designed for production deployment constraints