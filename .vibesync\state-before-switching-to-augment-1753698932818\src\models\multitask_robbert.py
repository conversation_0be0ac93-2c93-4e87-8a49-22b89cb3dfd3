"""
Multi-task RobBERT model with multiple classification heads.
"""

import os
import torch
import torch.nn as nn
from typing import Dict, Optional, List, Any
from transformers import AutoModel, AutoConfig, AutoTokenizer, AutoModelForTokenClassification
from pathlib import Path
import psutil

from ..heads.ner_head import NERHead
from ..data.tokenizer_utils import RobBERTTokenizerWithAlignment
from ..exceptions import ModelLoadingError, InferenceError, DimensionMismatchError, MemoryError
from ..utils.logging_utils import get_logger, log_execution_time, log_memory_usage, validate_tensor_shape, log_tensor_stats


class MultiTaskRobBERT(nn.Module):
    """Multi-task RobBERT-2023 model with configurable heads for Dutch NLP tasks."""
    
    @log_memory_usage(operation_name="model_initialization")
    def __init__(self, model_path: Optional[str] = None, head_configs: Optional[Dict[str, Dict]] = None):
        super().__init__()
        
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.logger.log_system_info()
        
        # Check available memory before loading
        memory = psutil.virtual_memory()
        if memory.available < 2e9:  # Less than 2GB available
            self.logger.logger.warning(f"Low memory available: {memory.available / 1e9:.1f}GB")

        # Load configuration and tokenizer
        # For RobBERT transition, use RobBERT-2023-dutch-base
        robbert_model_name = "DTAI-KULeuven/robbert-2023-dutch-base"
        
        try:
            self.logger.logger.info(f"Loading RobBERT configuration: {robbert_model_name}")
            self.config = AutoConfig.from_pretrained(robbert_model_name)
            self.logger.logger.info(f"Configuration loaded - hidden_size: {self.config.hidden_size}")
            
        except Exception as e:
            error_msg = f"Failed to load RobBERT configuration"
            self.logger.log_error(ModelLoadingError(error_msg, model_name=robbert_model_name))
            raise ModelLoadingError(error_msg, model_name=robbert_model_name) from e
        
        try:
            self.logger.logger.info(f"Initializing RobBERT tokenizer: {robbert_model_name}")
            self.tokenizer = RobBERTTokenizerWithAlignment(robbert_model_name)
            
        except Exception as e:
            error_msg = f"Failed to initialize RobBERT tokenizer"
            self.logger.log_error(ModelLoadingError(error_msg, model_name=robbert_model_name))
            raise ModelLoadingError(error_msg, model_name=robbert_model_name) from e

        # Configure label mappings for NER (3 labels: O, B-PER, I-PER)
        id2label = {0: "O", 1: "B-PER", 2: "I-PER"}
        label2id = {v: k for k, v in id2label.items()}
        self.logger.logger.debug(f"NER label mappings: {id2label}")

        # Load RobBERT-2023 model with token classification head
        try:
            self.logger.logger.info(f"Loading RobBERT model: {robbert_model_name}")
            self.logger.log_memory_usage("Before model loading")
            
            self.model = AutoModelForTokenClassification.from_pretrained(
                robbert_model_name,
                num_labels=3,
                id2label=id2label,
                label2id=label2id
            )
            
            self.logger.log_memory_usage("After model loading")
            
            # Count parameters
            num_params = sum(p.numel() for p in self.model.parameters())
            trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
            
            self.logger.log_model_info(
                robbert_model_name,
                num_parameters=num_params,
                model_size_mb=num_params * 4 / 1e6  # Rough estimate for float32
            )
            self.logger.logger.info(f"Trainable parameters: {trainable_params:,}")
            
        except Exception as e:
            error_msg = f"Failed to load RobBERT model"
            self.logger.log_error(ModelLoadingError(error_msg, model_name=robbert_model_name))
            raise ModelLoadingError(error_msg, model_name=robbert_model_name) from e
        
        # Extract encoder for compatibility with existing code
        try:
            self.encoder = self.model.roberta  # RobBERT uses RoBERTa architecture
            self.logger.logger.debug("Successfully extracted RoBERTa encoder")
        except AttributeError as e:
            error_msg = f"Failed to extract encoder from RobBERT model"
            self.logger.log_error(ModelLoadingError(error_msg, model_name=robbert_model_name))
            raise ModelLoadingError(error_msg, model_name=robbert_model_name) from e

        # Initialize heads
        self.heads = nn.ModuleDict()

        # Add NER head - use the RobBERT model's classifier
        try:
            self.heads['ner'] = self.model.classifier
            self.logger.logger.debug("Added NER head from RobBERT classifier")
        except AttributeError as e:
            error_msg = f"Failed to extract classifier from RobBERT model"
            self.logger.log_error(ModelLoadingError(error_msg, model_name=robbert_model_name))
            raise ModelLoadingError(error_msg, model_name=robbert_model_name) from e
        
        # Add placeholder heads for other tasks
        default_heads = {
            'compliance': {'num_labels': 2},
            'label': {'num_labels': 15}, 
            'reason': {'num_labels': 10},
            'topic': {'num_labels': 8}
        }
        
        if head_configs:
            default_heads.update(head_configs)
            self.logger.logger.debug(f"Updated head configs: {head_configs}")
            
        for head_name, config in default_heads.items():
            if head_name not in self.heads:
                try:
                    self.heads[head_name] = nn.Linear(
                        self.config.hidden_size, 
                        config['num_labels']
                    )
                    self.logger.logger.debug(f"Added {head_name} head with {config['num_labels']} labels")
                except Exception as e:
                    self.logger.logger.warning(f"Failed to add {head_name} head: {e}")
        
        self.logger.logger.info(f"Model initialization complete with heads: {list(self.heads.keys())}")
    
    @log_execution_time(operation_name="model_forward")
    def forward(self, input_ids: torch.Tensor, attention_mask: Optional[torch.Tensor] = None,
                heads: Optional[List[str]] = None, labels: Optional[Dict[str, torch.Tensor]] = None):
        """Forward pass through encoder and specified heads."""
        
        try:
            # Validate inputs
            if input_ids is None or input_ids.numel() == 0:
                raise InferenceError("Empty or None input_ids provided")
            
            # Log input shapes for debugging
            log_tensor_stats(input_ids, "input_ids", self.logger)
            if attention_mask is not None:
                log_tensor_stats(attention_mask, "attention_mask", self.logger)
                
                # Validate attention mask shape matches input_ids
                if attention_mask.shape != input_ids.shape:
                    raise DimensionMismatchError(
                        "Attention mask shape doesn't match input_ids shape",
                        expected_shape=input_ids.shape,
                        actual_shape=attention_mask.shape,
                        tensor_name="attention_mask"
                    )
            
            # Validate heads
            active_heads = heads if heads else list(self.heads.keys())
            invalid_heads = [h for h in active_heads if h not in self.heads]
            if invalid_heads:
                self.logger.logger.warning(f"Invalid heads requested: {invalid_heads}")
                active_heads = [h for h in active_heads if h in self.heads]
            
            if not active_heads:
                raise InferenceError("No valid heads specified for inference", heads=heads)
            
            self.logger.logger.debug(f"Running inference with heads: {active_heads}")
            
            # Encode input
            try:
                self.logger.log_memory_usage("Before encoder forward")
                encoder_outputs = self.encoder(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    return_dict=True
                )
                self.logger.log_memory_usage("After encoder forward")
                
            except Exception as e:
                raise InferenceError(f"Encoder forward pass failed: {e}", 
                                   input_shape=input_ids.shape) from e
            
            # Validate encoder outputs
            if not hasattr(encoder_outputs, 'last_hidden_state'):
                raise InferenceError("Encoder output missing last_hidden_state")
            
            hidden_states = encoder_outputs.last_hidden_state
            log_tensor_stats(hidden_states, "hidden_states", self.logger)
            
            # Validate hidden states shape
            expected_hidden_shape = (input_ids.shape[0], input_ids.shape[1], self.config.hidden_size)
            validate_tensor_shape(hidden_states, expected_hidden_shape, "hidden_states", self.logger)
            
            # Get pooled output for sequence-level tasks
            if hasattr(encoder_outputs, 'pooler_output') and encoder_outputs.pooler_output is not None:
                pooled_output = encoder_outputs.pooler_output
            else:
                pooled_output = hidden_states.mean(dim=1)
                self.logger.logger.debug("Using mean pooling for sequence-level tasks")
            
            log_tensor_stats(pooled_output, "pooled_output", self.logger)
            
            outputs = {}
            total_loss = 0.0
            
            # Run specified heads
            for head_name in active_heads:
                try:
                    head = self.heads[head_name]
                    self.logger.logger.debug(f"Processing head: {head_name}")

                    if head_name == 'ner':
                        # Token-level classification using RobBERT classifier
                        head_labels = labels.get(head_name) if labels else None
                        
                        try:
                            logits = head(hidden_states)
                            log_tensor_stats(logits, f"{head_name}_logits", self.logger)
                            
                            # Validate logits shape
                            expected_logits_shape = (input_ids.shape[0], input_ids.shape[1], 3)  # 3 NER labels
                            validate_tensor_shape(logits, expected_logits_shape, f"{head_name}_logits", self.logger)
                            
                        except Exception as e:
                            raise InferenceError(f"NER head forward pass failed: {e}", 
                                               heads=[head_name]) from e
                        
                        head_output = {"logits": logits}

                        # Add loss if labels provided
                        if head_labels is not None:
                            try:
                                log_tensor_stats(head_labels, f"{head_name}_labels", self.logger)
                                
                                loss_fct = nn.CrossEntropyLoss()
                                # Flatten for loss calculation
                                if attention_mask is not None:
                                    active_loss = attention_mask.view(-1) == 1
                                else:
                                    active_loss = torch.ones(logits.view(-1, logits.shape[-1]).shape[0], dtype=torch.bool)
                                
                                active_logits = logits.view(-1, logits.shape[-1])
                                active_labels = torch.where(
                                    active_loss,
                                    head_labels.view(-1),
                                    torch.tensor(loss_fct.ignore_index).type_as(head_labels)
                                )
                                loss = loss_fct(active_logits, active_labels)
                                head_output["loss"] = loss
                                
                                self.logger.logger.debug(f"{head_name} loss: {loss.item():.4f}")
                                
                            except Exception as e:
                                self.logger.logger.warning(f"Failed to compute {head_name} loss: {e}")
                    else:
                        # Sequence-level classification
                        head_labels = labels.get(head_name) if labels else None
                        
                        try:
                            logits = head(pooled_output)
                            log_tensor_stats(logits, f"{head_name}_logits", self.logger)
                            
                        except Exception as e:
                            raise InferenceError(f"{head_name} head forward pass failed: {e}", 
                                               heads=[head_name]) from e
                        
                        head_output = {"logits": logits}
                        
                        if head_labels is not None:
                            try:
                                log_tensor_stats(head_labels, f"{head_name}_labels", self.logger)
                                
                                loss_fct = nn.CrossEntropyLoss()
                                loss = loss_fct(logits, head_labels)
                                head_output["loss"] = loss
                                total_loss += loss
                                
                                self.logger.logger.debug(f"{head_name} loss: {loss.item():.4f}")
                                
                            except Exception as e:
                                self.logger.logger.warning(f"Failed to compute {head_name} loss: {e}")
                    
                    outputs[head_name] = head_output
                    if "loss" in head_output:
                        total_loss += head_output["loss"]
                        
                except Exception as e:
                    self.logger.logger.error(f"Error processing head {head_name}: {e}")
                    # Continue with other heads instead of failing completely
                    continue
            
            if total_loss > 0:
                outputs["loss"] = total_loss
                self.logger.logger.debug(f"Total loss: {total_loss.item():.4f}")
            
            # Log inference statistics
            batch_size, seq_len = input_ids.shape[:2]
            self.logger.log_inference_stats(
                input_shape=input_ids.shape,
                output_shape=(batch_size, seq_len, len(outputs)),
                inference_time=0.0,  # Will be logged by decorator
                heads=active_heads
            )
            
            return outputs
            
        except (InferenceError, DimensionMismatchError):
            raise
        except Exception as e:
            error_msg = f"Unexpected error during model forward pass"
            self.logger.log_error(InferenceError(error_msg, input_shape=input_ids.shape if input_ids is not None else None))
            raise InferenceError(error_msg, input_shape=input_ids.shape if input_ids is not None else None) from e
    
    def get_head(self, head_name: str):
        """Get specific head by name."""
        return self.heads[head_name] if head_name in self.heads else None
    
    def add_head(self, head_name: str, head: nn.Module):
        """Add new head to the model."""
        self.heads[head_name] = head
    
    @log_execution_time(operation_name="model_save")
    def save_pretrained(self, save_directory: str):
        """Save model and tokenizer."""
        try:
            save_path = Path(save_directory)
            save_path.mkdir(parents=True, exist_ok=True)
            
            self.logger.logger.info(f"Saving model to: {save_directory}")
            
            # Save model state
            model_path = save_path / "pytorch_model.bin"
            torch.save(self.state_dict(), model_path)
            self.logger.logger.info(f"Saved model weights: {model_path}")
            
            # Save config and tokenizer
            self.config.save_pretrained(save_directory)
            self.tokenizer.tokenizer.save_pretrained(save_directory)
            self.logger.logger.info("Saved configuration and tokenizer")
            
        except Exception as e:
            error_msg = f"Failed to save model to {save_directory}"
            self.logger.log_error(ModelLoadingError(error_msg, checkpoint_path=save_directory))
            raise ModelLoadingError(error_msg, checkpoint_path=save_directory) from e
    
    @classmethod
    @log_execution_time(operation_name="model_load_pretrained")
    def load_pretrained(cls, model_directory: str, **kwargs):
        """Load pretrained multi-task model."""
        logger = get_logger(f"{__name__}.{cls.__name__}")
        
        try:
            logger.logger.info(f"Loading pretrained model from: {model_directory}")
            model = cls(model_directory, **kwargs)
            
            # Load model weights if available
            weights_path = Path(model_directory) / "pytorch_model.bin"
            if weights_path.exists():
                try:
                    logger.logger.info(f"Loading model weights from: {weights_path}")
                    state_dict = torch.load(weights_path, map_location='cpu')
                    missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)
                    
                    if missing_keys:
                        logger.logger.warning(f"Missing keys in state dict: {missing_keys}")
                    if unexpected_keys:
                        logger.logger.warning(f"Unexpected keys in state dict: {unexpected_keys}")
                    
                    logger.logger.info("Successfully loaded model weights")
                    
                except Exception as e:
                    error_msg = f"Failed to load model weights from {weights_path}"
                    logger.log_error(ModelLoadingError(error_msg, checkpoint_path=str(weights_path)))
                    raise ModelLoadingError(error_msg, checkpoint_path=str(weights_path)) from e
            else:
                logger.logger.info("No model weights found, using pretrained RobBERT weights")
            
            return model
            
        except ModelLoadingError:
            raise
        except Exception as e:
            error_msg = f"Failed to load pretrained model from {model_directory}"
            logger.log_error(ModelLoadingError(error_msg, checkpoint_path=model_directory))
            raise ModelLoadingError(error_msg, checkpoint_path=model_directory) from e
    
    @classmethod
    def from_pretrained(cls, model_path: Optional[str] = None, **kwargs):
        """Create model from pretrained checkpoint."""
        logger = get_logger(f"{__name__}.{cls.__name__}")
        
        if model_path is None:
            model_path = os.getenv('ROBBERT_WEIGHTS_DIR', 'models/robbert2023-per')
            logger.logger.info(f"Using default model path: {model_path}")
        
        try:
            return cls(model_path, **kwargs)
        except Exception as e:
            error_msg = f"Failed to create model from pretrained checkpoint"
            logger.log_error(ModelLoadingError(error_msg, checkpoint_path=model_path))
            raise ModelLoadingError(error_msg, checkpoint_path=model_path) from e