#!/usr/bin/env python3
"""
Test script for the RobBERT-2023 NER training CLI.

This script tests all CLI commands to ensure they work correctly.
"""

import sys
import subprocess
import tempfile
from pathlib import Path

def run_command(cmd):
    """Run a command and return the result."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True, shell=True)
    print(f"Exit code: {result.returncode}")
    if result.stdout:
        print(f"STDOUT:\n{result.stdout}")
    if result.stderr:
        print(f"STDERR:\n{result.stderr}")
    return result

def test_cli():
    """Test all CLI commands."""
    print("Testing RobBERT-2023 NER Training CLI")
    print("=" * 50)
    
    # Test main help
    print("\n1. Testing main help...")
    result = run_command(["python", "-m", "src.training", "--help"])
    assert result.returncode == 0, "Main help failed"
    
    # Test single-head help
    print("\n2. Testing single-head help...")
    result = run_command(["python", "-m", "src.training", "single-head", "--help"])
    assert result.returncode == 0, "Single-head help failed"
    
    # Test multi-head help
    print("\n3. Testing multi-head help...")
    result = run_command(["python", "-m", "src.training", "multi-head", "--help"])
    assert result.returncode == 0, "Multi-head help failed"
    
    # Test preprocess help
    print("\n4. Testing preprocess help...")
    result = run_command(["python", "-m", "src.training", "preprocess", "--help"])
    assert result.returncode == 0, "Preprocess help failed"
    
    # Test config help
    print("\n5. Testing config help...")
    result = run_command(["python", "-m", "src.training", "config", "--help"])
    assert result.returncode == 0, "Config help failed"
    
    # Test validate help
    print("\n6. Testing validate help...")
    result = run_command(["python", "-m", "src.training", "validate", "--help"])
    assert result.returncode == 0, "Validate help failed"
    
    # Test config creation
    print("\n7. Testing config creation...")
    with tempfile.NamedTemporaryFile(suffix=".yaml", delete=False) as tmp:
        config_path = tmp.name
    
    result = run_command(["python", "-m", "src.training", "config", config_path, "--template", "test"])
    assert result.returncode == 0, "Config creation failed"
    
    # Test config validation
    print("\n8. Testing config validation...")
    result = run_command(["python", "-m", "src.training", "validate", config_path])
    assert result.returncode == 0, "Config validation failed"
    
    # Clean up
    Path(config_path).unlink()
    
    # Test preprocessing with example data
    print("\n9. Testing preprocessing...")
    with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as tmp:
        output_path = tmp.name
    
    result = run_command([
        "python", "-m", "src.training", "preprocess", 
        "data_sets/example.json", output_path,
        "--max-length", "256"
    ])
    assert result.returncode == 0, "Preprocessing failed"
    
    # Clean up
    Path(output_path).unlink()
    
    print("\n" + "=" * 50)
    print("✅ All CLI tests passed!")

if __name__ == "__main__":
    try:
        test_cli()
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)