# Enhanced WandB Integration for Hugging Face Trainer

This document describes the enhanced WandB integration features for RobBERT-2023 NER training using Hugging Face Trainer.

## Overview

The enhanced WandB integration provides advanced logging capabilities beyond the standard Hugging Face WandB integration, including:

- **Confusion Matrix Visualization**: Interactive confusion matrices logged at configurable intervals
- **Per-Class Metrics**: Detailed precision, recall, and F1 scores for each entity type
- **Model Artifact Logging**: Automatic model checkpoint logging with metadata
- **Evaluation Tables**: Comprehensive evaluation results in structured tables
- **Training Progress Tracking**: Enhanced metrics tracking and visualization

## Features

### 1. CustomWandbCallback

The `CustomWandbCallback` extends the standard `WandbCallback` to provide enhanced logging capabilities.

#### Key Features:
- Automatic confusion matrix generation and logging
- Per-entity-type metrics tracking
- Model artifact logging with comprehensive metadata
- Detailed evaluation results in WandB tables
- Best metrics tracking and summaries
- Training progress visualization

#### Configuration Options:
```python
callback_config = {
    'log_confusion_matrix': True,           # Enable confusion matrix logging
    'log_per_class_metrics': True,          # Enable per-class metrics
    'log_model_artifacts': True,            # Enable model artifact logging
    'log_evaluation_tables': True,          # Enable evaluation tables
    'confusion_matrix_frequency': 'epoch',  # 'epoch' or 'step'
    'confusion_matrix_steps': 500           # Steps between CM logging (if frequency='step')
}
```

### 2. Configuration Integration

Enhanced WandB settings are integrated into `HFTrainingConfig`:

```yaml
# Enhanced WandB logging options
wandb_log_confusion_matrix: true
wandb_log_per_class_metrics: true
wandb_log_model_artifacts: true
wandb_log_evaluation_tables: true
wandb_confusion_matrix_frequency: "epoch"  # "epoch" or "step"
wandb_confusion_matrix_steps: 500
```

### 3. Automatic Integration

The enhanced WandB callback is automatically created and added when using `HFNERTrainer`:

```python
from training.hf_trainer import HFNERTrainer
from training.hf_config import HFTrainingConfig

# Configuration with enhanced WandB
config = HFTrainingConfig(
    wandb_project="my-project",
    wandb_log_confusion_matrix=True,
    wandb_log_per_class_metrics=True,
    # ... other settings
)

# Trainer automatically uses enhanced WandB callback
trainer = HFNERTrainer(config)
results = trainer.train(data_path="path/to/data")
```

## Logged Information

### Model Information
- Model architecture details
- Total and trainable parameter counts
- Model size estimation
- Number of labels and entity types
- Label scheme detection (BIO/IO)

### Training Configuration
- All hyperparameters from TrainingArguments
- Hardware optimization settings (FP16, BF16, etc.)
- Learning rate scheduling configuration
- Early stopping and evaluation settings

### Enhanced Metrics
- Standard evaluation metrics with context
- Metric improvements between evaluations
- Best metrics tracking across training
- Historical performance data

### Confusion Matrix
- Interactive confusion matrix visualization
- Token-level and entity-level evaluation
- Configurable logging frequency
- Support for multi-class NER evaluation

### Per-Class Metrics
- Precision, recall, F1 for each entity type
- Organized in WandB tables for comparison
- Support for BIO tagging scheme
- Entity-specific performance tracking

### Model Artifacts
- Automatic checkpoint logging
- Comprehensive metadata including:
  - Training configuration
  - Final metrics
  - Label information
  - Model architecture details
- Version tracking for model iterations

### Evaluation Tables
- Detailed evaluation results
- Historical performance tracking
- Exportable data for analysis
- Timestamp and step information

## Usage Examples

### Basic Usage

```python
from training.hf_trainer import HFNERTrainer
from training.hf_config import HFTrainingConfig

# Create configuration
config = HFTrainingConfig(
    wandb_project="robbert2023-ner",
    wandb_entity="your-entity",
    wandb_log_confusion_matrix=True,
    wandb_log_per_class_metrics=True
)

# Train with enhanced logging
trainer = HFNERTrainer(config)
results = trainer.train("path/to/data")
```

### Custom Callback Configuration

```python
from training.hf_wandb_integration import create_enhanced_wandb_callback

# Custom callback configuration
wandb_config = {
    'log_confusion_matrix': True,
    'log_per_class_metrics': True,
    'confusion_matrix_frequency': 'step',
    'confusion_matrix_steps': 100
}

# Create custom callback
callback = create_enhanced_wandb_callback(
    label_list=["O", "B-PER", "I-PER"],
    config=wandb_config
)

# Use with Hugging Face Trainer
trainer = Trainer(
    # ... other arguments
    callbacks=[callback]
)
```

### Manual WandB Setup

```python
from training.hf_wandb_integration import setup_wandb_for_hf_trainer

# Manual WandB initialization
setup_wandb_for_hf_trainer(
    project="my-project",
    entity="my-entity",
    name="experiment-1",
    tags=["robbert", "ner", "dutch"],
    config={"learning_rate": 5e-5}
)
```

## Configuration Reference

### HFTrainingConfig WandB Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `wandb_project` | str | "robbert2023-ner" | WandB project name |
| `wandb_entity` | str | "slippydongle" | WandB entity/username |
| `run_name` | str | None | Run name (auto-generated if None) |
| `wandb_tags` | List[str] | ["robbert-2023", "dutch-nlp", "ner"] | Run tags |
| `wandb_notes` | str | "Hugging Face Trainer integration..." | Run notes |
| `report_to` | str | "wandb" | Reporting destination |
| `wandb_log_confusion_matrix` | bool | True | Enable confusion matrix logging |
| `wandb_log_per_class_metrics` | bool | True | Enable per-class metrics |
| `wandb_log_model_artifacts` | bool | True | Enable model artifact logging |
| `wandb_log_evaluation_tables` | bool | True | Enable evaluation tables |
| `wandb_confusion_matrix_frequency` | str | "epoch" | CM logging frequency ("epoch" or "step") |
| `wandb_confusion_matrix_steps` | int | 500 | Steps between CM logging |

### Environment Variables

The following environment variables can be used to configure WandB:

- `WANDB_PROJECT`: Override default project name
- `WANDB_ENTITY`: Override default entity
- `WANDB_API_KEY`: WandB API key for authentication
- `WANDB_DISABLED`: Set to "true" to disable WandB entirely
- `WANDB_LOG_MODEL`: Control model logging ("end", "checkpoint", "false")
- `WANDB_WATCH`: Control gradient logging ("gradients", "all", "parameters", "false")

## Best Practices

### 1. Project Organization
- Use descriptive project names
- Organize runs with consistent tagging
- Include experiment details in run names

### 2. Logging Frequency
- Use "epoch" frequency for confusion matrices in short training runs
- Use "step" frequency for long training runs with frequent evaluation
- Balance logging detail with performance impact

### 3. Model Artifacts
- Enable artifact logging for important experiments
- Use meaningful artifact names and descriptions
- Include comprehensive metadata for reproducibility

### 4. Evaluation Tables
- Enable for experiments requiring detailed analysis
- Export tables for further processing if needed
- Use timestamps for tracking experiment progression

### 5. Performance Considerations
- Confusion matrix generation can be computationally expensive
- Adjust logging frequency based on dataset size
- Monitor WandB quota usage for large experiments

## Troubleshooting

### Common Issues

1. **WandB Authentication**
   ```bash
   wandb login
   # or set WANDB_API_KEY environment variable
   ```

2. **Disabled WandB in Test Mode**
   ```python
   config = HFTrainingConfig(
       test_mode=True,
       test_disable_wandb=False  # Enable WandB in test mode
   )
   ```

3. **Missing Dependencies**
   ```bash
   pip install wandb scikit-learn
   ```

4. **Confusion Matrix Errors**
   - Ensure label_list is properly configured
   - Check for out-of-range predictions
   - Verify evaluation dataset format

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger("src.training.hf_wandb_integration").setLevel(logging.DEBUG)
```

## Integration with Existing Systems

The enhanced WandB integration is designed to be compatible with:

- Existing WandB configurations
- Standard Hugging Face Trainer workflows
- Custom training loops
- Multi-GPU training setups
- Distributed training environments

## Future Enhancements

Planned improvements include:

- Real-time training visualization
- Advanced error analysis tools
- Integration with WandB Sweeps
- Custom metric definitions
- Enhanced artifact management

## Support

For issues related to enhanced WandB integration:

1. Check the troubleshooting section above
2. Review WandB documentation
3. Examine log files for error details
4. Test with minimal configuration first

## Examples

See the following files for complete examples:

- `examples/hf_wandb_enhanced_example.py`: Comprehensive demonstration
- `tests/test_hf_wandb_integration.py`: Unit tests and usage patterns
- `examples/hf_trainer_example.py`: Basic training with enhanced logging