# Person Named Entity Recognition Head Configuration
head_config:
  name: "ner"
  type: "token_classification"
  
  # Model architecture
  model:
    hidden_size: 768
    num_labels: 3  # O, B-PER, I-PER (Person entities only)
    dropout: 0.1
    classifier_dropout: 0.1
    
  # Training parameters
  training:
    epochs: 5
    batch_size: 16
    learning_rate: 3e-5
    weight_decay: 0.01
    warmup_ratio: 0.1
    max_grad_norm: 1.0
    
    # Loss configuration
    loss_weight: 1.0
    class_weights: null  # Auto-calculate from data if null
    ignore_index: -100
    
    # Optimization
    optimizer: "adamw"
    scheduler: "linear_with_warmup"
    gradient_accumulation_steps: 1
    
  # Data configuration
  data:
    max_length: 512
    stride: 128  # For sliding window if needed
    label_all_tokens: false  # Only label first subword of each word
    
    # Data augmentation
    augmentation:
      enabled: false
      techniques: []
    
  # Evaluation metrics
  evaluation:
    metrics: ["precision", "recall", "f1", "accuracy"]
    average: "weighted"  # For multi-class metrics
    
    # Entity-level evaluation
    entity_level: true
    scheme: "IOB2"
    
  # WandB specific settings
  wandb:
    project_suffix: "-ner"
    tags: ["named-entity-recognition", "token-classification", "dutch", "person-entities"]
    notes: "Person Named Entity Recognition for Dutch text using RobBERT-2023"
    
    # Metrics to track
    track_metrics:
      - "ner_loss"
      - "ner_f1"
      - "ner_precision"
      - "ner_recall"
      - "ner_accuracy"
      - "ner_entity_f1"  # Entity-level F1
      
    # Model artifacts
    save_best_model: true
    metric_for_best: "ner_f1"
    
  # Inference settings
  inference:
    batch_size: 32
    threshold: 0.5  # Confidence threshold
    
    # Post-processing
    post_processing:
      merge_subwords: true
      filter_confidence: true
      min_entity_length: 1