#!/usr/bin/env python3
"""
Example usage of the Hugging Face model setup for RobBERT NER training.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.training.hf_model_setup import create_ner_model, validate_model_compatibility, test_model_forward_pass

def main():
    """Demonstrate the HF model setup functionality."""
    
    print("RobBERT-2023 NER Model Setup Example")
    print("=" * 40)
    
    # Create the model
    print("\n1. Creating RobBERT NER model...")
    model, tokenizer, label2id, id2label = create_ner_model()
    
    print(f"Model: {type(model).__name__}")
    print(f"Tokenizer: {type(tokenizer).__name__}")
    print(f"Labels: {list(label2id.keys())}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Validate compatibility
    print("\n2. Validating compatibility...")
    is_compatible = validate_model_compatibility(
        model, 
        tokenizer, 
        expected_labels=["O", "B-PER", "I-PER"]
    )
    print(f"Compatible with existing pipeline: {is_compatible}")
    
    # Test with Dutch text
    print("\n3. Testing with Dutch text...")
    test_texts = [
        "Jan Jansen werkt bij Google.",
        "Marie van der Berg woont in Amsterdam.",
        "Dit is een test zonder personen.",
        "Piet de Vries en Anna Smit zijn vrienden."
    ]
    
    for text in test_texts:
        print(f"\nText: '{text}'")
        result = test_model_forward_pass(model, tokenizer, text)
        
        # Show tokens and predictions
        tokens = tokenizer.tokenize(text)
        predictions = result['predicted_labels']
        
        print("Tokens and predictions:")
        for token, pred in zip(tokens[:len(predictions)], predictions):
            print(f"  {token:15} -> {pred}")
    
    print("\n✓ Example completed successfully!")

if __name__ == "__main__":
    main()