"""
Simple tests for migration utilities and compatibility layer.
"""

import pytest
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, List, Any

from src.utils.migration_utils import (
    DataFormatMigrator,
    CheckpointCompatibilityValidator,
    ConfigurationMigrator
)
from src.utils.compatibility_layer import (
    BackwardCompatibilityManager,
    adapt_response_for_compatibility,
    validate_request_for_compatibility,
    ensure_checkpoint_compatibility
)


class TestDataFormatMigrator:
    """Test data format migration utilities."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests."""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def sample_jsonl_data(self):
        """Sample JSONL data for testing."""
        return [
            {
                "tokens": ["Jan", "Jansen", "woont", "in", "Amsterdam"],
                "labels": ["B-PER", "I-PER", "O", "O", "O"],
                "id": 1
            },
            {
                "tokens": ["Marie", "de", "Wit", "werkt", "bij", "Google"],
                "labels": ["B-PER", "I-PER", "I-PER", "O", "O", "O"],
                "id": 2
            }
        ]
    
    def test_convert_tokenized_jsonl_to_sentence_entities(self, temp_dir, sample_jsonl_data):
        """Test conversion from tokenized JSONL to sentence+entities format."""
        migrator = DataFormatMigrator()
        
        # Create input JSONL file
        input_file = temp_dir / "input.jsonl"
        with open(input_file, 'w', encoding='utf-8') as f:
            for record in sample_jsonl_data:
                f.write(json.dumps(record) + '\n')
        
        # Convert
        output_file = temp_dir / "output.json"
        stats = migrator.convert_jsonl_to_sentence_entities(input_file, output_file)
        
        # Verify output file exists
        assert output_file.exists()
        
        # Load and verify converted data
        with open(output_file, 'r', encoding='utf-8') as f:
            converted_data = json.load(f)
        
        # Verify conversion statistics
        assert stats["converted_examples"] == 2
        assert stats["total_lines"] == 2
        assert stats["errors"] == 0
        
        # Verify converted data structure
        assert len(converted_data) == 2
        
        # Check first example
        example1 = converted_data[0]
        assert "sentence" in example1
        assert "entities" in example1
        assert example1["id"] == 1
        
        # Verify entities were extracted correctly
        entities1 = example1["entities"]
        assert len(entities1) == 1
        assert entities1[0]["label"] == "PER"
        assert "Jan" in entities1[0]["text"] and "Jansen" in entities1[0]["text"]
    
    def test_format_detection(self):
        """Test automatic format detection."""
        migrator = DataFormatMigrator()
        
        # Test tokenized format detection
        tokenized_data = {"tokens": ["Jan", "Jansen"], "labels": ["B-PER", "I-PER"]}
        assert migrator._detect_format(tokenized_data) == "tokenized"
        
        # Test sentence+entities format detection
        sentence_entities_data = {"sentence": "Jan Jansen", "entities": []}
        assert migrator._detect_format(sentence_entities_data) == "sentence_entities"
        
        # Test CoNLL format detection
        conll_data = {"words": ["Jan", "Jansen"], "ner_tags": [1, 2]}
        assert migrator._detect_format(conll_data) == "conll"
        
        # Test unknown format
        unknown_data = {"text": "some text", "other_field": "value"}
        assert migrator._detect_format(unknown_data) == "unknown"


class TestCompatibilityFunctions:
    """Test compatibility utility functions."""
    
    def test_adapt_response_for_compatibility(self):
        """Test API response adaptation."""
        # Test NER response adaptation
        original_response = {
            "text": "Jan Jansen woont in Amsterdam",
            "results": {
                "ner": [
                    {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10, "confidence": 0.95}
                ]
            }
        }
        
        adapted_response = adapt_response_for_compatibility(original_response)
        
        # Verify structure is maintained
        assert "text" in adapted_response
        assert "results" in adapted_response
        assert "ner" in adapted_response["results"]
        
        # Verify NER entities have required fields
        ner_entities = adapted_response["results"]["ner"]
        assert len(ner_entities) == 1
        entity = ner_entities[0]
        assert "text" in entity
        assert "label" in entity
        assert "start" in entity
        assert "end" in entity
        assert "confidence" in entity
    
    def test_validate_request_for_compatibility(self):
        """Test API request validation."""
        # Test legacy task name mapping
        original_request = {
            "text": "Test text",
            "tasks": ["person_extraction", "entity_recognition"]
        }
        
        validated_request = validate_request_for_compatibility(original_request)
        
        # Verify task name mapping
        assert validated_request["tasks"] == ["ner", "ner"]
        assert validated_request["text"] == "Test text"
        
        # Test single task string
        single_task_request = {
            "text": "Test text",
            "tasks": "named_entity"
        }
        
        validated_single = validate_request_for_compatibility(single_task_request)
        assert validated_single["tasks"] == "ner"


class TestCheckpointCompatibilityValidator:
    """Test checkpoint compatibility validation."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests."""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_checkpoint_dir(self, temp_dir):
        """Create mock checkpoint directory."""
        checkpoint_dir = temp_dir / "test_checkpoint"
        checkpoint_dir.mkdir()
        
        # Create config.json
        config = {
            "model_type": "roberta",
            "num_labels": 3,
            "id2label": {"0": "O", "1": "B-PER", "2": "I-PER"},
            "label2id": {"O": 0, "B-PER": 1, "I-PER": 2},
            "hidden_size": 768,
            "architectures": ["RobertaForTokenClassification"]
        }
        with open(checkpoint_dir / "config.json", 'w') as f:
            json.dump(config, f)
        
        # Create dummy model weights
        import torch
        dummy_weights = {"roberta.embeddings.word_embeddings.weight": torch.randn(1000, 768)}
        torch.save(dummy_weights, checkpoint_dir / "pytorch_model.bin")
        
        return checkpoint_dir
    
    def test_validate_checkpoint_compatibility(self, mock_checkpoint_dir):
        """Test checkpoint compatibility validation."""
        validator = CheckpointCompatibilityValidator()
        
        report = validator.validate_checkpoint_compatibility(mock_checkpoint_dir)
        
        # Verify report structure
        assert "is_compatible" in report
        assert "issues" in report
        assert "warnings" in report
        assert "files_found" in report
        assert "model_info" in report
        
        # Should be compatible with proper mock data
        assert report["is_compatible"] == True
        assert len(report["issues"]) == 0
        
        # Verify files were found
        assert report["files_found"]["config.json"] == True
        assert report["files_found"]["pytorch_model.bin"] == True
        
        # Verify model info was extracted
        assert report["model_info"]["num_labels"] == 3


class TestConfigurationMigrator:
    """Test configuration migration utilities."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests."""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def sample_legacy_config(self):
        """Sample legacy configuration."""
        return {
            "model": {
                "model_name": "DTAI-KULeuven/robbert-2023-dutch-base",
                "max_length": 512,
                "heads": ["ner"]
            },
            "training": {
                "epochs": 5,
                "batch_size": 16,
                "learning_rate": 2e-5,
                "weight_decay": 0.01,
                "warmup_steps": 1000,
                "max_grad_norm": 1.0,
                "loss_weights": {"ner": 1.0}
            },
            "wandb": {
                "project": "legacy-project",
                "entity": "legacy-entity"
            },
            "evaluation": {
                "eval_steps": 200,
                "logging_steps": 100,
                "save_steps": 1000
            }
        }
    
    def test_migrate_training_config(self, temp_dir, sample_legacy_config):
        """Test training configuration migration."""
        migrator = ConfigurationMigrator()
        
        # Create source config file
        source_file = temp_dir / "legacy_config.yaml"
        import yaml
        with open(source_file, 'w') as f:
            yaml.dump(sample_legacy_config, f)
        
        # Migrate configuration
        target_file = temp_dir / "hf_config.yaml"
        report = migrator.migrate_training_config(source_file, target_file, "hf_trainer")
        
        # Verify migration success
        assert report["success"] == True
        assert target_file.exists()
        
        # Load and verify migrated config
        with open(target_file, 'r') as f:
            migrated_config = yaml.safe_load(f)
        
        # Verify HF training section exists
        assert "hf_training" in migrated_config
        hf_config = migrated_config["hf_training"]
        
        # Verify parameter mapping
        assert hf_config["epochs"] == 5
        assert hf_config["batch_size"] == 16
        assert hf_config["learning_rate"] == 2e-5
        
        # Verify converted parameters are tracked
        assert "converted_parameters" in report
        assert "training.epochs" in report["converted_parameters"]
        
        # Verify warnings for complex parameters
        assert len(report["warnings"]) > 0
        warning_messages = [w for w in report["warnings"]]
        # Check for either loss_weights or multi-head warnings
        assert any("loss_weights" in w or "multi-head" in w.lower() for w in warning_messages)


def test_ensure_checkpoint_compatibility(tmp_path):
    """Test checkpoint compatibility checking function."""
    # Create mock checkpoint
    checkpoint_dir = tmp_path / "test_checkpoint"
    checkpoint_dir.mkdir()
    
    # Create minimal valid checkpoint
    config = {"model_type": "roberta", "num_labels": 3}
    with open(checkpoint_dir / "config.json", 'w') as f:
        json.dump(config, f)
    
    import torch
    torch.save({}, checkpoint_dir / "pytorch_model.bin")
    
    # Test compatibility check
    is_compatible = ensure_checkpoint_compatibility(checkpoint_dir)
    
    # Should return boolean
    assert isinstance(is_compatible, bool)