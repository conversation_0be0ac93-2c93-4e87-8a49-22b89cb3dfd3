#!/usr/bin/env python3
"""
End-to-end pipeline demonstration script for RobBERT-2023 transition.

This script demonstrates the complete pipeline from tokenization to prediction,
validating that the RobBERT-2023 model works correctly with proper alignment.
"""

import sys
import torch
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.models.multitask_robbert import MultiTaskRobBERT
from src.inference.api_fastapi import extract_ner_entities


def test_pipeline_sample(model, text: str, description: str = ""):
    """Test pipeline on a single text sample."""
    print(f"\n{'='*60}")
    print(f"Testing: {description}")
    print(f"Text: {text}")
    print(f"{'='*60}")
    
    try:
        # Step 1: Tokenization
        print("Step 1: Tokenization")
        inputs = model.tokenizer.tokenizer(
            text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        )
        
        print(f"  Input shape: {inputs['input_ids'].shape}")
        print(f"  Tokens: {model.tokenizer.tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])}")
        
        # Step 2: Model inference
        print("\nStep 2: Model Inference")
        with torch.no_grad():
            outputs = model(
                input_ids=inputs['input_ids'],
                attention_mask=inputs['attention_mask'],
                heads=['ner']
            )
        
        logits = outputs['ner']['logits']
        predictions = torch.argmax(logits, dim=-1)
        
        print(f"  Output shape: {logits.shape}")
        print(f"  Predictions: {predictions[0].tolist()}")
        
        # Step 3: Entity extraction
        print("\nStep 3: Entity Extraction")
        entities = extract_ner_entities(text, predictions[0], model.tokenizer, logits[0])
        
        if entities:
            print(f"  Found {len(entities)} entities:")
            for i, entity in enumerate(entities, 1):
                print(f"    {i}. '{entity.text}' ({entity.label}) [{entity.start}:{entity.end}] confidence={entity.confidence:.3f}")
        else:
            print("  No entities found")
        
        # Step 4: Validation
        print("\nStep 4: Validation")
        print(f"  ✓ Tokenization successful")
        print(f"  ✓ Model inference successful")
        print(f"  ✓ Entity extraction successful")
        print(f"  ✓ Output format valid")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Pipeline failed: {e}")
        return False


def main():
    """Run end-to-end pipeline tests."""
    print("RobBERT-2023 End-to-End Pipeline Test")
    print("=====================================")
    
    try:
        # Load model
        print("Loading RobBERT-2023 model...")
        model = MultiTaskRobBERT.from_pretrained()
        model.eval()
        print("✓ Model loaded successfully")
        
        # Test samples
        test_samples = [
            ("Linda Jansen woont in Amsterdam.", "Simple person name"),
            ("Jan de Wit werkt bij Google Nederland.", "Person with compound surname"),
            ("Maria van den Berg-Smit is directeur.", "Person with hyphenated surname"),
            ("De minister gaat naar Brussel.", "Text without person names"),
            ("Dr. P.J. van der Meer en Prof. A. de Vries zijn aanwezig.", "Multiple titles and names"),
            ("Amsterdam is de hoofdstad van Nederland.", "Geographic text only"),
            ("<EMAIL> en www.example.nl", "Technical text"),
            ("Café naïve résumé", "Text with accented characters"),
        ]
        
        successful_tests = 0
        total_tests = len(test_samples)
        
        for text, description in test_samples:
            success = test_pipeline_sample(model, text, description)
            if success:
                successful_tests += 1
        
        # Summary
        print(f"\n{'='*60}")
        print("PIPELINE TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Tests passed: {successful_tests}/{total_tests}")
        print(f"Success rate: {successful_tests/total_tests*100:.1f}%")
        
        if successful_tests == total_tests:
            print("✓ All pipeline tests passed!")
            print("✓ RobBERT-2023 transition is working correctly")
        else:
            print(f"⚠ {total_tests - successful_tests} tests failed")
            print("⚠ Some pipeline issues detected")
        
        # Additional validation
        print(f"\n{'='*60}")
        print("TECHNICAL VALIDATION")
        print(f"{'='*60}")
        
        # Check model properties
        print(f"Model type: {type(model).__name__}")
        print(f"Tokenizer type: {type(model.tokenizer).__name__}")
        print(f"Model name: {model.tokenizer.model_name}")
        print(f"Vocabulary size: {len(model.tokenizer.tokenizer)}")
        print(f"Available heads: {list(model.heads.keys())}")
        
        # Check tokenizer properties
        sample_text = "Linda Jansen woont in Amsterdam."
        encoding = model.tokenizer.tokenizer(sample_text, return_tensors="pt")
        print(f"Sample tokenization shape: {encoding['input_ids'].shape}")
        
        # Check model output
        with torch.no_grad():
            outputs = model(encoding['input_ids'], encoding['attention_mask'], heads=['ner'])
        print(f"Model output shape: {outputs['ner']['logits'].shape}")
        print(f"Output labels: 3 (O, B-PER, I-PER)")
        
        print("\n✓ Technical validation complete")
        
        return successful_tests == total_tests
        
    except Exception as e:
        print(f"✗ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)