"""
Tests for Hugging Face model setup functionality.
"""

import pytest
import torch
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
from transformers import RobertaForTokenClassification, AutoTokenizer

from src.training.hf_model_setup import (
    create_ner_model,
    validate_model_compatibility,
    test_model_forward_pass
)
from src.exceptions import ModelLoadingError


class TestCreateNERModel:
    """Test create_ner_model function."""
    
    @pytest.mark.unit
    def test_create_ner_model_default_labels(self):
        """Test model creation with default labels."""
        with patch('src.training.hf_model_setup.AutoTokenizer') as mock_tokenizer, \
             patch('src.training.hf_model_setup.AutoConfig') as mock_config, \
             patch('src.training.hf_model_setup.RobertaForTokenClassification') as mock_model:
            
            # Setup mocks
            mock_tokenizer_instance = Mock()
            mock_tokenizer_instance.vocab_size = 50000
            mock_tokenizer.from_pretrained.return_value = mock_tokenizer_instance
            
            mock_config_instance = Mock()
            mock_config_instance.hidden_size = 768
            mock_config_instance.num_labels = 3
            mock_config.from_pretrained.return_value = mock_config_instance
            
            mock_model_instance = Mock()
            mock_model_instance.roberta = Mock()
            mock_model_instance.classifier = Mock()
            mock_model_instance.classifier.out_features = 3
            mock_model_instance.parameters.return_value = [torch.randn(100, 100)]
            mock_model.from_pretrained.return_value = mock_model_instance
            
            # Test function
            model, tokenizer, label2id, id2label = create_ner_model()
            
            # Verify results
            assert model == mock_model_instance
            assert tokenizer == mock_tokenizer_instance
            assert label2id == {"O": 0, "B-PER": 1, "I-PER": 2}
            assert id2label == {0: "O", 1: "B-PER", 2: "I-PER"}
            
            # Verify calls
            mock_tokenizer.from_pretrained.assert_called_once()
            mock_config.from_pretrained.assert_called_once()
            mock_model.from_pretrained.assert_called_once()
    
    @pytest.mark.unit
    def test_create_ner_model_custom_labels(self):
        """Test model creation with custom labels."""
        custom_labels = ["O", "B-PER", "I-PER", "B-LOC", "I-LOC"]
        
        with patch('src.training.hf_model_setup.AutoTokenizer') as mock_tokenizer, \
             patch('src.training.hf_model_setup.AutoConfig') as mock_config, \
             patch('src.training.hf_model_setup.RobertaForTokenClassification') as mock_model:
            
            # Setup mocks
            mock_tokenizer_instance = Mock()
            mock_tokenizer_instance.vocab_size = 50000
            mock_tokenizer.from_pretrained.return_value = mock_tokenizer_instance
            
            mock_config_instance = Mock()
            mock_config_instance.hidden_size = 768
            mock_config_instance.num_labels = 5
            mock_config.from_pretrained.return_value = mock_config_instance
            
            mock_model_instance = Mock()
            mock_model_instance.roberta = Mock()
            mock_model_instance.classifier = Mock()
            mock_model_instance.classifier.out_features = 5
            mock_model_instance.parameters.return_value = [torch.randn(100, 100)]
            mock_model.from_pretrained.return_value = mock_model_instance
            
            # Test function
            model, tokenizer, label2id, id2label = create_ner_model(label_list=custom_labels)
            
            # Verify results
            assert len(label2id) == 5
            assert len(id2label) == 5
            assert label2id["B-LOC"] == 3
            assert id2label[4] == "I-LOC"
            
            # Verify config was updated
            assert mock_config_instance.num_labels == 5
            assert mock_config_instance.label2id == label2id
            assert mock_config_instance.id2label == id2label
    
    @pytest.mark.unit
    def test_create_ner_model_tokenizer_failure(self):
        """Test handling of tokenizer loading failure."""
        with patch('src.training.hf_model_setup.AutoTokenizer') as mock_tokenizer:
            mock_tokenizer.from_pretrained.side_effect = Exception("Tokenizer loading failed")
            
            with pytest.raises(ModelLoadingError) as exc_info:
                create_ner_model()
            
            assert "Failed to load tokenizer" in str(exc_info.value)
    
    @pytest.mark.unit
    def test_create_ner_model_config_failure(self):
        """Test handling of config loading failure."""
        with patch('src.training.hf_model_setup.AutoTokenizer') as mock_tokenizer, \
             patch('src.training.hf_model_setup.AutoConfig') as mock_config:
            
            mock_tokenizer.from_pretrained.return_value = Mock()
            mock_config.from_pretrained.side_effect = Exception("Config loading failed")
            
            with pytest.raises(ModelLoadingError) as exc_info:
                create_ner_model()
            
            assert "Failed to load configuration" in str(exc_info.value)
    
    @pytest.mark.unit
    def test_create_ner_model_model_failure(self):
        """Test handling of model loading failure."""
        with patch('src.training.hf_model_setup.AutoTokenizer') as mock_tokenizer, \
             patch('src.training.hf_model_setup.AutoConfig') as mock_config, \
             patch('src.training.hf_model_setup.RobertaForTokenClassification') as mock_model:
            
            mock_tokenizer.from_pretrained.return_value = Mock()
            mock_config.from_pretrained.return_value = Mock()
            mock_model.from_pretrained.side_effect = Exception("Model loading failed")
            
            with pytest.raises(ModelLoadingError) as exc_info:
                create_ner_model()
            
            assert "Failed to load model" in str(exc_info.value)
    
    @pytest.mark.unit
    def test_create_ner_model_validation_failure(self):
        """Test handling of model validation failure."""
        with patch('src.training.hf_model_setup.AutoTokenizer') as mock_tokenizer, \
             patch('src.training.hf_model_setup.AutoConfig') as mock_config, \
             patch('src.training.hf_model_setup.RobertaForTokenClassification') as mock_model:
            
            mock_tokenizer_instance = Mock()
            mock_tokenizer_instance.vocab_size = 50000
            mock_tokenizer.from_pretrained.return_value = mock_tokenizer_instance
            
            mock_config_instance = Mock()
            mock_config_instance.hidden_size = 768
            mock_config.from_pretrained.return_value = mock_config_instance
            
            # Model with wrong classifier output size to trigger validation failure
            mock_model_instance = Mock()
            mock_model_instance.parameters.return_value = [torch.randn(10, 10)]
            mock_model_instance.roberta = Mock()
            mock_model_instance.classifier = Mock()
            mock_model_instance.classifier.out_features = 5  # Wrong size (should be 3)
            mock_model.from_pretrained.return_value = mock_model_instance
            
            with pytest.raises(ModelLoadingError) as exc_info:
                create_ner_model()
            
            assert "Classifier output size mismatch" in str(exc_info.value)
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_create_ner_model_real(self):
        """Test real model creation (requires internet connection)."""
        try:
            model, tokenizer, label2id, id2label = create_ner_model()
            
            # Verify model structure
            assert isinstance(model, RobertaForTokenClassification)
            assert isinstance(tokenizer, AutoTokenizer)
            assert hasattr(model, 'roberta')
            assert hasattr(model, 'classifier')
            
            # Verify label mappings
            assert len(label2id) == 3
            assert len(id2label) == 3
            assert label2id["O"] == 0
            assert label2id["B-PER"] == 1
            assert label2id["I-PER"] == 2
            
            # Verify model configuration
            assert model.config.num_labels == 3
            assert model.config.id2label == id2label
            assert model.config.label2id == label2id
            
        except Exception as e:
            pytest.skip(f"Real model creation failed (likely network issue): {e}")


class TestValidateModelCompatibility:
    """Test validate_model_compatibility function."""
    
    @pytest.mark.unit
    def test_validate_compatibility_success(self):
        """Test successful model compatibility validation."""
        # Create mock model
        mock_model = Mock(spec=RobertaForTokenClassification)
        mock_model.config = Mock()
        mock_model.config.num_labels = 3
        mock_model.config.id2label = {0: "O", 1: "B-PER", 2: "I-PER"}
        mock_model.config.label2id = {"O": 0, "B-PER": 1, "I-PER": 2}
        
        # Mock forward pass
        mock_outputs = Mock()
        mock_outputs.logits = torch.randn(1, 10, 3)
        mock_model.return_value = mock_outputs
        mock_model.eval = Mock()
        
        # Create mock tokenizer
        mock_tokenizer = Mock()
        mock_tokenizer.vocab_size = 50000
        
        # Test validation
        result = validate_model_compatibility(
            mock_model, 
            mock_tokenizer, 
            expected_labels=["O", "B-PER", "I-PER"]
        )
        
        assert result is True
    
    @pytest.mark.unit
    def test_validate_compatibility_wrong_model_type(self):
        """Test validation failure with wrong model type."""
        mock_model = Mock()  # Not RobertaForTokenClassification
        mock_tokenizer = Mock()
        mock_tokenizer.vocab_size = 50000
        
        with pytest.raises(ModelLoadingError) as exc_info:
            validate_model_compatibility(mock_model, mock_tokenizer)
        
        assert "not RobertaForTokenClassification instance" in str(exc_info.value)
    
    @pytest.mark.unit
    def test_validate_compatibility_missing_tokenizer_attr(self):
        """Test validation failure with invalid tokenizer."""
        mock_model = Mock(spec=RobertaForTokenClassification)
        mock_model.config = Mock()
        mock_model.config.num_labels = 3
        
        # Create tokenizer without vocab_size using spec
        mock_tokenizer = Mock(spec=[])  # Empty spec prevents attribute creation
        
        with pytest.raises(ModelLoadingError) as exc_info:
            validate_model_compatibility(mock_model, mock_tokenizer)
        
        assert "Tokenizer missing vocab_size attribute" in str(exc_info.value)
    
    @pytest.mark.unit
    def test_validate_compatibility_label_mismatch(self):
        """Test validation failure with label mismatch."""
        mock_model = Mock(spec=RobertaForTokenClassification)
        mock_model.config = Mock()
        mock_model.config.num_labels = 3
        mock_model.config.id2label = {0: "O", 1: "B-LOC", 2: "I-LOC"}  # Wrong labels
        mock_model.config.label2id = {"O": 0, "B-LOC": 1, "I-LOC": 2}
        
        mock_tokenizer = Mock()
        mock_tokenizer.vocab_size = 50000
        
        with pytest.raises(ModelLoadingError) as exc_info:
            validate_model_compatibility(
                mock_model, 
                mock_tokenizer, 
                expected_labels=["O", "B-PER", "I-PER"]
            )
        
        assert "Label mismatch" in str(exc_info.value)
    
    @pytest.mark.unit
    def test_validate_compatibility_forward_pass_failure(self):
        """Test validation failure during forward pass."""
        mock_model = Mock(spec=RobertaForTokenClassification)
        mock_model.config = Mock()
        mock_model.config.num_labels = 3
        mock_model.config.id2label = {0: "O", 1: "B-PER", 2: "I-PER"}
        mock_model.config.label2id = {"O": 0, "B-PER": 1, "I-PER": 2}
        mock_model.eval = Mock()
        
        # Forward pass raises exception
        mock_model.side_effect = Exception("Forward pass failed")
        
        mock_tokenizer = Mock()
        mock_tokenizer.vocab_size = 50000
        
        with pytest.raises(ModelLoadingError) as exc_info:
            validate_model_compatibility(mock_model, mock_tokenizer)
        
        assert "Model compatibility validation failed" in str(exc_info.value)


class TestModelForwardPass:
    """Test test_model_forward_pass function."""
    
    @pytest.mark.unit
    def test_forward_pass_success(self):
        """Test successful forward pass."""
        # Create mock model
        mock_model = Mock(spec=RobertaForTokenClassification)
        mock_model.config = Mock()
        mock_model.config.id2label = {0: "O", 1: "B-PER", 2: "I-PER"}
        mock_model.eval = Mock()
        
        # Mock forward pass output
        mock_outputs = Mock()
        mock_outputs.logits = torch.tensor([[[0.1, 0.8, 0.1], [0.9, 0.05, 0.05], [0.1, 0.1, 0.8]]])
        mock_model.return_value = mock_outputs
        
        # Create mock tokenizer
        mock_tokenizer = Mock()
        mock_tokenizer.return_value = {
            "input_ids": torch.tensor([[1, 2, 3]]),
            "attention_mask": torch.tensor([[1, 1, 1]])
        }
        mock_tokenizer.convert_ids_to_tokens.return_value = ["<s>", "Jan", "</s>"]
        
        # Test forward pass
        result = test_model_forward_pass(mock_model, mock_tokenizer, "Jan Jansen")
        
        # Verify results
        assert "logits" in result
        assert "predictions" in result
        assert "predicted_labels" in result
        assert "input_ids" in result
        assert "attention_mask" in result
        
        assert result["predicted_labels"] == ["B-PER", "O", "I-PER"]
    
    @pytest.mark.unit
    def test_forward_pass_failure(self):
        """Test forward pass failure handling."""
        mock_model = Mock(spec=RobertaForTokenClassification)
        mock_model.eval = Mock()
        mock_model.side_effect = Exception("Forward pass failed")
        
        mock_tokenizer = Mock()
        mock_tokenizer.return_value = {
            "input_ids": torch.tensor([[1, 2, 3]]),
            "attention_mask": torch.tensor([[1, 1, 1]])
        }
        
        with pytest.raises(ModelLoadingError) as exc_info:
            test_model_forward_pass(mock_model, mock_tokenizer, "Test text")
        
        assert "Model forward pass test failed" in str(exc_info.value)
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_forward_pass_real_model(self):
        """Test forward pass with real model (requires internet connection)."""
        try:
            model, tokenizer, label2id, id2label = create_ner_model()
            
            # Test forward pass
            result = test_model_forward_pass(
                model, 
                tokenizer, 
                "Jan Jansen woont in Amsterdam."
            )
            
            # Verify results structure
            assert "logits" in result
            assert "predictions" in result
            assert "predicted_labels" in result
            assert "input_ids" in result
            assert "attention_mask" in result
            
            # Verify shapes
            batch_size, seq_len, num_labels = result["logits"].shape
            assert batch_size == 1
            assert num_labels == 3
            assert len(result["predicted_labels"]) == seq_len
            
            # Verify all labels are valid
            valid_labels = {"O", "B-PER", "I-PER"}
            for label in result["predicted_labels"]:
                assert label in valid_labels
            
        except Exception as e:
            pytest.skip(f"Real model forward pass test failed (likely network issue): {e}")


class TestModelIntegration:
    """Integration tests for complete model setup workflow."""
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_complete_model_setup_workflow(self):
        """Test complete model setup and validation workflow."""
        try:
            # Step 1: Create model
            model, tokenizer, label2id, id2label = create_ner_model()
            
            # Step 2: Validate compatibility
            is_compatible = validate_model_compatibility(
                model, 
                tokenizer, 
                expected_labels=["O", "B-PER", "I-PER"]
            )
            assert is_compatible is True
            
            # Step 3: Test forward pass
            result = test_model_forward_pass(model, tokenizer)
            
            # Verify complete workflow
            assert isinstance(model, RobertaForTokenClassification)
            assert hasattr(model, 'roberta')
            assert hasattr(model, 'classifier')
            assert model.config.num_labels == 3
            assert len(result["predicted_labels"]) > 0
            
        except Exception as e:
            pytest.skip(f"Complete model setup workflow failed (likely network issue): {e}")
    
    @pytest.mark.unit
    def test_model_setup_with_custom_cache_dir(self):
        """Test model setup with custom cache directory."""
        with patch('src.training.hf_model_setup.AutoTokenizer') as mock_tokenizer, \
             patch('src.training.hf_model_setup.AutoConfig') as mock_config, \
             patch('src.training.hf_model_setup.RobertaForTokenClassification') as mock_model:
            
            # Setup mocks
            mock_tokenizer.from_pretrained.return_value = Mock()
            mock_config.from_pretrained.return_value = Mock()
            mock_model_instance = Mock()
            mock_model_instance.roberta = Mock()
            mock_model_instance.classifier = Mock()
            mock_model_instance.classifier.out_features = 3
            mock_model_instance.parameters.return_value = [torch.randn(10, 10)]
            mock_model.from_pretrained.return_value = mock_model_instance
            
            # Test with custom cache directory
            cache_dir = "/custom/cache/dir"
            create_ner_model(cache_dir=cache_dir)
            
            # Verify cache_dir was passed to all from_pretrained calls
            mock_tokenizer.from_pretrained.assert_called_with(
                "DTAI-KULeuven/robbert-2023-dutch-base",
                cache_dir=cache_dir,
                use_fast=True
            )
            mock_config.from_pretrained.assert_called_with(
                "DTAI-KULeuven/robbert-2023-dutch-base",
                cache_dir=cache_dir
            )
            # Model call includes config parameter, so we check it was called
            mock_model.from_pretrained.assert_called_once()