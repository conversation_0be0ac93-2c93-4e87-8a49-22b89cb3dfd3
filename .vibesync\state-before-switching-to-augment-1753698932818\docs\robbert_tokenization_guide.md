# RobBERT-2023 Tokenization and Label Alignment Guide

This document provides a comprehensive guide to RobBERT-2023's tokenization behavior and the label alignment system implemented for Dutch NER tasks.

## Overview

The transition from BERTje to RobBERT-2023 involves a fundamental change in tokenization approach:

- **BERTje**: WordPiece tokenization with 30,000 vocabulary
- **RobBERT-2023**: Byte-level BPE tokenization with 50,000 vocabulary

This change significantly improves handling of Dutch text, especially compound names, diacritics, and OCR-noisy text.

## Tokenization Comparison

### BERTje (WordPiece) vs RobBERT-2023 (Byte-level BPE)

| Aspect | BERTje (WordPiece) | RobBERT-2023 (Byte-level BPE) |
|--------|-------------------|--------------------------------|
| Vocabulary Size | 30,000 tokens | 50,000 tokens |
| OOV Handling | `[UNK]` tokens | No OOV (byte-level) |
| Subword Prefix | `##` for continuations | `Ġ` for word-initial |
| Unicode Support | Limited | Full Unicode |
| Dutch Compounds | Good | Excellent |
| Diacritics | Basic | Full support |

### Tokenization Examples

#### Simple Dutch Text
```python
text = "Jan Jansen woont in Amsterdam"

# BERTje tokenization
bertje_tokens = ["Jan", "Jansen", "wo", "##ont", "in", "Amsterdam"]

# RobBERT-2023 tokenization  
robbert_tokens = ["Jan", "ĠJansen", "Ġwoont", "Ġin", "ĠAmsterdam"]
```

#### Compound Dutch Names
```python
text = "Jan-Willem van der Berg"

# BERTje tokenization
bertje_tokens = ["Jan", "-", "Willem", "van", "der", "Berg"]

# RobBERT-2023 tokenization
robbert_tokens = ["Jan", "-", "Willem", "Ġvan", "Ġder", "ĠBerg"]
```

#### Names with Diacritics
```python
text = "José François Müller"

# BERTje tokenization (may use [UNK])
bertje_tokens = ["Jos", "##é", "François", "M", "##ü", "##ller"]

# RobBERT-2023 tokenization (handles all Unicode)
robbert_tokens = ["José", "ĠFrançois", "ĠMüller"]
```

## Label Alignment System

### The Challenge

NER models require token-level labels, but training data typically has word-level labels. When tokenizers split words into subwords, we need to align labels correctly.

### Alignment Rules

The `RobBERTTokenizerWithAlignment` class implements these rules:

1. **First subword of a word**: Gets the original word's label
2. **Continuation subwords**: 
   - If original label was `B-PER` or `I-PER` → `I-PER`
   - If original label was `O` → `O`
3. **Special tokens** (`[CLS]`, `[SEP]`, `[PAD]`): Always `O`

### Alignment Examples

#### Example 1: Simple Name
```python
words = ["Jan", "Jansen", "woont"]
labels = ["B-PER", "I-PER", "O"]

# After tokenization and alignment
tokens = ["[CLS]", "Jan", "ĠJansen", "Ġwoont", "[SEP]"]
aligned_labels = ["O", "B-PER", "I-PER", "O", "O"]
```

#### Example 2: Compound Name Split
```python
words = ["Jan-Willem", "woont"]
labels = ["B-PER", "O"]

# Tokenization splits compound name
tokens = ["[CLS]", "Jan", "-", "Willem", "Ġwoont", "[SEP]"]
aligned_labels = ["O", "B-PER", "I-PER", "I-PER", "O", "O"]
```

#### Example 3: Long Surname
```python
words = ["van", "der", "Berg", "woont"]
labels = ["B-PER", "I-PER", "I-PER", "O"]

# Each word becomes one token
tokens = ["[CLS]", "van", "Ġder", "ĠBerg", "Ġwoont", "[SEP]"]
aligned_labels = ["O", "B-PER", "I-PER", "I-PER", "O", "O"]
```

## Implementation Details

### TokenAlignment Class

```python
@dataclass
class TokenAlignment:
    """Represents alignment between original words and subword tokens."""
    word_ids: List[Optional[int]]  # Maps each token to original word index
    token_labels: List[str]        # Labels aligned to subword tokens
    original_labels: List[str]     # Original word-level labels
    tokens: List[str]              # The actual tokens
```

### RobBERTTokenizerWithAlignment

The main tokenizer class provides:

```python
class RobBERTTokenizerWithAlignment:
    def tokenize_with_alignment(
        self, 
        words: List[str], 
        labels: Optional[List[str]] = None,
        max_length: int = 512,
        return_tensors: Optional[str] = None
    ) -> TokenAlignment:
        """Tokenize words and align labels to subword tokens."""
```

### Usage Example

```python
from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment

# Initialize tokenizer
tokenizer = RobBERTTokenizerWithAlignment()

# Prepare input
words = ["Jan", "Jansen", "en", "Marie", "de", "Wit"]
labels = ["B-PER", "I-PER", "O", "B-PER", "I-PER", "I-PER"]

# Tokenize with alignment
alignment = tokenizer.tokenize_with_alignment(words, labels)

print("Original words:", words)
print("Original labels:", labels)
print("Tokens:", alignment.tokens)
print("Token labels:", alignment.token_labels)
print("Word IDs:", alignment.word_ids)
```

## Advanced Features

### Handling Edge Cases

#### Empty Strings
```python
words = ["Jan", "", "Jansen"]  # Empty string in middle
labels = ["B-PER", "O", "I-PER"]

# System handles gracefully by skipping empty strings
```

#### Very Long Words
```python
words = ["supercalifragilisticexpialidocious"]
labels = ["O"]

# May be split into multiple subwords, all get "O" label
```

#### Special Characters
```python
words = ["<EMAIL>", "woont"]
labels = ["B-PER", "O"]

# Byte-level BPE handles all characters
tokens = ["Jan", "@", "email", ".", "com", "Ġwoont"]
aligned_labels = ["B-PER", "I-PER", "I-PER", "I-PER", "I-PER", "O"]
```

### Performance Optimizations

#### Batch Processing
```python
# Process multiple examples efficiently
batch_words = [
    ["Jan", "Jansen"],
    ["Marie", "de", "Wit"]
]
batch_labels = [
    ["B-PER", "I-PER"],
    ["B-PER", "I-PER", "I-PER"]
]

alignments = [
    tokenizer.tokenize_with_alignment(words, labels)
    for words, labels in zip(batch_words, batch_labels)
]
```

#### Memory Management
```python
# For large datasets, process in chunks
def process_large_dataset(dataset, chunk_size=1000):
    for i in range(0, len(dataset), chunk_size):
        chunk = dataset[i:i+chunk_size]
        # Process chunk
        yield [tokenizer.tokenize_with_alignment(words, labels) 
               for words, labels in chunk]
```

## Validation and Testing

### Alignment Validation

The system includes built-in validation:

```python
def validate_alignment(alignment: TokenAlignment) -> bool:
    """Validate that alignment is consistent."""
    # Check lengths match
    if len(alignment.word_ids) != len(alignment.token_labels) != len(alignment.tokens):
        return False
    
    # Check word_ids are valid
    max_word_id = len(alignment.original_labels) - 1
    for word_id in alignment.word_ids:
        if word_id is not None and (word_id < 0 or word_id > max_word_id):
            return False
    
    return True
```

### Testing Scripts

#### Basic Tokenization Test
```bash
python scripts/test_robbert_tokenizer.py
```

#### Compound Name Testing
```bash
python scripts/test_compound_names.py
```

#### OOV Analysis
```bash
python scripts/oov_analysis_and_tokenization_verification.py
```

### Manual Testing

```python
# Test specific cases
test_cases = [
    (["Jan-Willem"], ["B-PER"]),
    (["van", "der", "Berg"], ["B-PER", "I-PER", "I-PER"]),
    (["José"], ["B-PER"]),
    (["<EMAIL>"], ["O"])
]

for words, labels in test_cases:
    alignment = tokenizer.tokenize_with_alignment(words, labels)
    print(f"Words: {words}")
    print(f"Labels: {labels}")
    print(f"Tokens: {alignment.tokens}")
    print(f"Token labels: {alignment.token_labels}")
    print("---")
```

## Migration from BERTje

### Key Changes

1. **Import Changes**
```python
# Old (BERTje)
from transformers import BertTokenizer
tokenizer = BertTokenizer.from_pretrained("wietsedv/bert-base-dutch-cased")

# New (RobBERT-2023)
from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment
tokenizer = RobBERTTokenizerWithAlignment()
```

2. **Tokenization Method Changes**
```python
# Old (BERTje)
tokens = tokenizer.tokenize(text)
input_ids = tokenizer.convert_tokens_to_ids(tokens)

# New (RobBERT-2023)
alignment = tokenizer.tokenize_with_alignment(words, labels)
input_ids = tokenizer.tokenizer.convert_tokens_to_ids(alignment.tokens)
```

3. **Label Handling Changes**
```python
# Old (manual alignment)
def align_labels_manually(tokens, word_labels):
    # Complex manual alignment logic
    pass

# New (automatic alignment)
alignment = tokenizer.tokenize_with_alignment(words, labels)
aligned_labels = alignment.token_labels
```

### Migration Checklist

- [ ] Update tokenizer initialization
- [ ] Replace manual alignment with automatic system
- [ ] Update label mapping (9 labels → 3 labels for PER-only NER)
- [ ] Test with compound Dutch names
- [ ] Validate diacritic handling
- [ ] Check memory usage (slightly higher with larger vocabulary)
- [ ] Update training scripts to use new alignment
- [ ] Update inference scripts for new tokenization

## Troubleshooting

### Common Issues

#### Alignment Length Mismatch
```python
# Problem: Lengths don't match
if len(alignment.word_ids) != len(alignment.tokens):
    print("Alignment error detected")
    
# Solution: Check input validation
if not words or (labels and len(words) != len(labels)):
    raise ValueError("Invalid input")
```

#### Unexpected Token Splits
```python
# Debug tokenization
text = "problematic-text"
tokens = tokenizer.tokenizer.tokenize(text)
print(f"'{text}' → {tokens}")

# Check encoding
encoding = tokenizer.tokenizer(text, return_offsets_mapping=True)
print("Offsets:", encoding['offset_mapping'])
```

#### Memory Issues with Large Texts
```python
# Solution: Use chunking
def tokenize_long_text(text, max_chunk_length=400):
    words = text.split()
    chunks = [words[i:i+max_chunk_length] for i in range(0, len(words), max_chunk_length)]
    
    alignments = []
    for chunk in chunks:
        alignment = tokenizer.tokenize_with_alignment(chunk)
        alignments.append(alignment)
    
    return alignments
```

### Performance Tips

1. **Batch Processing**: Process multiple examples together
2. **Caching**: Cache tokenizer instance (expensive to initialize)
3. **Memory Management**: Use generators for large datasets
4. **Validation**: Enable validation only during development/testing

## Related Documentation

- [RobBERT-2023 Model Documentation](../models/robbert2023-per/README.md)
- [API Documentation](../README_API_ROBBERT.md)
- [Token Management Guide](../README_TOKEN_MANAGEMENT.md)
- [Tokenization Analysis Results](../assessment_results/tokenization_analysis.md)
- [Performance Baseline](performance_baseline.md)

## References

- [RobBERT-2023 Paper](https://arxiv.org/abs/1912.00179)
- [Byte-Pair Encoding](https://arxiv.org/abs/1508.07909)
- [BERT Tokenization](https://arxiv.org/abs/1810.04805)
- [Dutch NER Datasets](https://www.clips.uantwerpen.be/conll2002/ner/)