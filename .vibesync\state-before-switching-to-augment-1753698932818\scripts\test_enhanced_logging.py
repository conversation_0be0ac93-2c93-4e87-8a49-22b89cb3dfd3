#!/usr/bin/env python3
"""
Demonstration script for enhanced logging and error handling in RobBERT-2023 pipeline.
"""

import sys
import torch
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.logging_utils import get_logger, log_execution_time, log_memory_usage
from src.exceptions import TokenizationError, ModelLoadingError, InferenceError
from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment


def demonstrate_logging():
    """Demonstrate enhanced logging capabilities."""
    print("=== Enhanced Logging Demonstration ===\n")
    
    # Create logger
    logger = get_logger("demo_logger", level="DEBUG")
    
    # Log system information
    print("1. System Information Logging:")
    logger.log_system_info()
    print()
    
    # Log memory usage
    print("2. Memory Usage Logging:")
    logger.log_memory_usage("Demo start")
    print()
    
    # Demonstrate execution time logging
    print("3. Execution Time Logging:")
    
    @log_execution_time(logger=logger, operation_name="sample_computation")
    def sample_computation():
        """Sample computation for timing demonstration."""
        result = sum(i**2 for i in range(1000))
        return result
    
    result = sample_computation()
    print(f"Computation result: {result}")
    print()
    
    # Demonstrate tensor logging
    print("4. Tensor Statistics Logging:")
    from src.utils.logging_utils import log_tensor_stats, validate_tensor_shape
    
    sample_tensor = torch.randn(2, 512, 768)
    log_tensor_stats(sample_tensor, "sample_hidden_states", logger)
    
    # Validate tensor shape
    try:
        validate_tensor_shape(sample_tensor, (2, 512, 768), "sample_tensor", logger)
        print("✓ Tensor shape validation passed")
    except Exception as e:
        print(f"✗ Tensor shape validation failed: {e}")
    print()


def demonstrate_error_handling():
    """Demonstrate enhanced error handling."""
    print("=== Enhanced Error Handling Demonstration ===\n")
    
    logger = get_logger("error_demo_logger", level="DEBUG")
    
    # 1. Demonstrate TokenizationError
    print("1. TokenizationError Handling:")
    try:
        # This will fail because we're using a mock scenario
        raise TokenizationError(
            "Failed to tokenize text",
            text="Sample problematic text with special characters: ñáéíóú",
            tokenizer_name="robbert-2023"
        )
    except TokenizationError as e:
        logger.log_error(e, {"function": "demonstrate_error_handling", "step": "tokenization"})
        print(f"✓ Caught and logged TokenizationError: {e.message}")
    print()
    
    # 2. Demonstrate ModelLoadingError
    print("2. ModelLoadingError Handling:")
    try:
        raise ModelLoadingError(
            "Failed to load model checkpoint",
            model_name="robbert-2023",
            checkpoint_path="/invalid/path/to/model"
        )
    except ModelLoadingError as e:
        logger.log_error(e, {"function": "demonstrate_error_handling", "step": "model_loading"})
        print(f"✓ Caught and logged ModelLoadingError: {e.message}")
    print()
    
    # 3. Demonstrate InferenceError
    print("3. InferenceError Handling:")
    try:
        raise InferenceError(
            "Inference failed due to dimension mismatch",
            input_shape=(1, 512),
            model_name="robbert-2023",
            heads=["ner", "compliance"]
        )
    except InferenceError as e:
        logger.log_error(e, {"function": "demonstrate_error_handling", "step": "inference"})
        print(f"✓ Caught and logged InferenceError: {e.message}")
    print()


def demonstrate_safe_execution():
    """Demonstrate safe execution patterns."""
    print("=== Safe Execution Patterns ===\n")
    
    logger = get_logger("safe_demo_logger", level="DEBUG")
    
    from src.utils.logging_utils import safe_execute
    
    print("1. Safe execution with default return value:")
    
    @safe_execute(logger=logger, default_return="fallback_value", reraise=False)
    def potentially_failing_function():
        """Function that might fail."""
        raise ValueError("Simulated failure")
    
    result = potentially_failing_function()
    print(f"✓ Function returned: {result}")
    print()
    
    print("2. Safe execution with reraise:")
    
    @safe_execute(logger=logger, reraise=True)
    def another_failing_function():
        """Another function that might fail."""
        raise RuntimeError("Another simulated failure")
    
    try:
        another_failing_function()
    except RuntimeError as e:
        print(f"✓ Exception was reraised: {e}")
    print()


def demonstrate_tokenizer_error_handling():
    """Demonstrate tokenizer error handling (with mocked failures)."""
    print("=== Tokenizer Error Handling ===\n")
    
    logger = get_logger("tokenizer_demo_logger", level="DEBUG")
    
    # Simulate tokenizer errors without actually loading the model
    print("1. Simulating empty word list error:")
    try:
        # This would normally create a tokenizer, but we'll simulate the error
        raise TokenizationError("Empty word list provided")
    except TokenizationError as e:
        logger.log_error(e)
        print(f"✓ Handled empty word list error: {e.message}")
    print()
    
    print("2. Simulating label alignment error:")
    try:
        from src.exceptions import LabelAlignmentError
        raise LabelAlignmentError(
            "Words and labels must have the same length",
            num_words=5,
            num_labels=3,
            num_tokens=8
        )
    except LabelAlignmentError as e:
        logger.log_error(e)
        print(f"✓ Handled label alignment error: {e.message}")
        print(f"  Details: {e.details}")
    print()


def main():
    """Main demonstration function."""
    print("RobBERT-2023 Enhanced Logging and Error Handling Demo")
    print("=" * 60)
    print()
    
    try:
        # Run demonstrations
        demonstrate_logging()
        demonstrate_error_handling()
        demonstrate_safe_execution()
        demonstrate_tokenizer_error_handling()
        
        print("=== Demo Complete ===")
        print("✓ All logging and error handling features demonstrated successfully!")
        
    except Exception as e:
        print(f"✗ Demo failed with unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()