#!/usr/bin/env python3
"""
Batch training script for multiple entity type heads.

This script provides functionality to train multiple entity types either
sequentially or in parallel, with consolidated reporting and monitoring.
"""

import os
import sys
import json
import time
import argparse
import multiprocessing as mp
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor, as_completed

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.training.hf_config import HFTrainingConfig
from src.training.hf_single_head_trainer import train_single_head
from src.utils.logging_utils import get_logger


class BatchHeadTrainer:
    """
    Batch trainer for multiple entity type heads.
    
    Supports both sequential and parallel training with progress monitoring
    and consolidated reporting.
    """
    
    def __init__(self, config: HFTrainingConfig, base_output_dir: Optional[str] = None):
        """
        Initialize batch trainer.
        
        Args:
            config: Base training configuration
            base_output_dir: Base output directory for all heads
        """
        self.config = config
        self.base_output_dir = base_output_dir or config.output_dir
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        # Training state
        self.training_results = {}
        self.training_start_time = None
        self.training_end_time = None
    
    def train_heads_sequential(
        self,
        entity_types: List[str],
        data_path: str,
        filtering_strategy: str = "strict"
    ) -> Dict[str, Any]:
        """
        Train multiple heads sequentially.
        
        Args:
            entity_types: List of entity types to train
            data_path: Path to training data
            filtering_strategy: Dataset filtering strategy
            
        Returns:
            Dictionary with results for each head
        """
        self.logger.logger.info(f"Starting sequential training for {len(entity_types)} heads")
        self.logger.logger.info(f"Entity types: {entity_types}")
        
        self.training_start_time = datetime.now()
        results = {}
        
        for i, entity_type in enumerate(entity_types, 1):
            self.logger.logger.info(f"Training head {i}/{len(entity_types)}: {entity_type}")
            
            try:
                # Create head-specific output directory
                head_output_dir = self._create_head_output_dir(entity_type)
                
                # Train single head
                head_result = train_single_head(
                    entity_type=entity_type,
                    data_path=data_path,
                    filtering_strategy=filtering_strategy,
                    config=self.config,
                    output_dir=head_output_dir
                )
                
                results[entity_type] = {
                    "status": "completed",
                    "result": head_result,
                    "training_time": self._calculate_training_time(head_result),
                    "output_dir": head_result["output_dir"]
                }
                
                self.logger.logger.info(f"Completed training for {entity_type}")
                
            except Exception as e:
                error_msg = f"Failed to train {entity_type}: {e}"
                self.logger.logger.error(error_msg)
                
                results[entity_type] = {
                    "status": "failed",
                    "error": str(e),
                    "training_time": 0,
                    "output_dir": None
                }
        
        self.training_end_time = datetime.now()
        self.training_results = results
        
        # Generate consolidated report
        self._generate_batch_report(results, "sequential")
        
        return results
    
    def train_heads_parallel(
        self,
        entity_types: List[str],
        data_path: str,
        filtering_strategy: str = "strict",
        max_workers: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Train multiple heads in parallel.
        
        Args:
            entity_types: List of entity types to train
            data_path: Path to training data
            filtering_strategy: Dataset filtering strategy
            max_workers: Maximum number of parallel workers (auto-detect if None)
            
        Returns:
            Dictionary with results for each head
        """
        if max_workers is None:
            # Use number of available CPUs, but limit to reasonable number
            max_workers = min(len(entity_types), mp.cpu_count(), 4)
        
        self.logger.logger.info(f"Starting parallel training for {len(entity_types)} heads")
        self.logger.logger.info(f"Entity types: {entity_types}")
        self.logger.logger.info(f"Max workers: {max_workers}")
        
        self.training_start_time = datetime.now()
        results = {}
        
        # Create training tasks
        training_tasks = []
        for entity_type in entity_types:
            head_output_dir = self._create_head_output_dir(entity_type)
            task = {
                "entity_type": entity_type,
                "data_path": data_path,
                "filtering_strategy": filtering_strategy,
                "config": self.config,
                "output_dir": head_output_dir
            }
            training_tasks.append(task)
        
        # Execute parallel training
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_entity = {
                executor.submit(self._train_single_head_worker, task): task["entity_type"]
                for task in training_tasks
            }
            
            # Collect results as they complete
            completed = 0
            for future in as_completed(future_to_entity):
                entity_type = future_to_entity[future]
                completed += 1
                
                try:
                    head_result = future.result()
                    results[entity_type] = {
                        "status": "completed",
                        "result": head_result,
                        "training_time": self._calculate_training_time(head_result),
                        "output_dir": head_result["output_dir"]
                    }
                    
                    self.logger.logger.info(f"Completed training for {entity_type} ({completed}/{len(entity_types)})")
                    
                except Exception as e:
                    error_msg = f"Failed to train {entity_type}: {e}"
                    self.logger.logger.error(error_msg)
                    
                    results[entity_type] = {
                        "status": "failed",
                        "error": str(e),
                        "training_time": 0,
                        "output_dir": None
                    }
        
        self.training_end_time = datetime.now()
        self.training_results = results
        
        # Generate consolidated report
        self._generate_batch_report(results, "parallel")
        
        return results
    
    def _train_single_head_worker(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Worker function for parallel training."""
        return train_single_head(
            entity_type=task["entity_type"],
            data_path=task["data_path"],
            filtering_strategy=task["filtering_strategy"],
            config=task["config"],
            output_dir=task["output_dir"]
        )
    
    def _create_head_output_dir(self, entity_type: str) -> str:
        """Create output directory for specific head."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        version = self.config.run_version
        
        dir_name = f"{entity_type}_{timestamp}_{version}"
        output_path = Path(self.base_output_dir) / dir_name
        output_path.mkdir(parents=True, exist_ok=True)
        
        return str(output_path)
    
    def _calculate_training_time(self, result: Dict[str, Any]) -> float:
        """Calculate training time from result."""
        if "train_result" in result and hasattr(result["train_result"], "metrics"):
            metrics = result["train_result"].metrics
            return metrics.get("train_runtime", 0.0)
        return 0.0
    
    def _generate_batch_report(self, results: Dict[str, Any], training_mode: str) -> None:
        """Generate consolidated batch training report."""
        try:
            # Create batch report directory
            batch_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_dir = Path(self.base_output_dir) / f"batch_report_{batch_timestamp}"
            report_dir.mkdir(parents=True, exist_ok=True)
            
            # Calculate overall statistics
            total_heads = len(results)
            successful_heads = sum(1 for r in results.values() if r["status"] == "completed")
            failed_heads = total_heads - successful_heads
            total_training_time = sum(r["training_time"] for r in results.values())
            
            if self.training_start_time and self.training_end_time:
                wall_clock_time = (self.training_end_time - self.training_start_time).total_seconds()
            else:
                wall_clock_time = 0
            
            # Create summary report
            summary = {
                "batch_training_summary": {
                    "training_mode": training_mode,
                    "timestamp": batch_timestamp,
                    "total_heads": total_heads,
                    "successful_heads": successful_heads,
                    "failed_heads": failed_heads,
                    "success_rate": successful_heads / total_heads if total_heads > 0 else 0,
                    "total_training_time_seconds": total_training_time,
                    "wall_clock_time_seconds": wall_clock_time,
                    "time_efficiency": total_training_time / wall_clock_time if wall_clock_time > 0 else 0,
                    "entity_types": list(results.keys()),
                    "config_version": self.config.run_version
                },
                "head_results": {}
            }
            
            # Add individual head results
            for entity_type, result in results.items():
                head_summary = {
                    "status": result["status"],
                    "training_time_seconds": result["training_time"],
                    "output_dir": result["output_dir"]
                }
                
                if result["status"] == "completed":
                    # Extract key metrics
                    head_result = result["result"]
                    if "eval_result" in head_result:
                        eval_metrics = head_result["eval_result"]
                        head_summary["metrics"] = {
                            key: value for key, value in eval_metrics.items()
                            if isinstance(value, (int, float))
                        }
                    
                    if "dataset_statistics" in head_result:
                        stats = head_result["dataset_statistics"]
                        head_summary["dataset_stats"] = {
                            "train_size": stats["train_size"],
                            "val_size": stats["val_size"],
                            "entity_coverage": stats["entity_coverage"]
                        }
                else:
                    head_summary["error"] = result.get("error", "Unknown error")
                
                summary["head_results"][entity_type] = head_summary
            
            # Save summary report
            summary_path = report_dir / "batch_training_summary.json"
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            
            # Generate markdown report
            self._generate_markdown_report(summary, report_dir)
            
            self.logger.logger.info(f"Batch training report generated: {report_dir}")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to generate batch report: {e}")
    
    def _generate_markdown_report(self, summary: Dict[str, Any], report_dir: Path) -> None:
        """Generate markdown batch training report."""
        batch_summary = summary["batch_training_summary"]
        head_results = summary["head_results"]
        
        markdown_content = f"""# Batch Training Report

## Summary

- **Training Mode**: {batch_summary['training_mode'].title()}
- **Timestamp**: {batch_summary['timestamp']}
- **Total Heads**: {batch_summary['total_heads']}
- **Successful**: {batch_summary['successful_heads']}
- **Failed**: {batch_summary['failed_heads']}
- **Success Rate**: {batch_summary['success_rate']:.1%}
- **Total Training Time**: {batch_summary['total_training_time_seconds']:.1f}s
- **Wall Clock Time**: {batch_summary['wall_clock_time_seconds']:.1f}s
- **Time Efficiency**: {batch_summary['time_efficiency']:.2f}x

## Entity Types

{', '.join(batch_summary['entity_types'])}

## Individual Head Results

"""
        
        for entity_type, result in head_results.items():
            markdown_content += f"### {entity_type}\n\n"
            
            if result["status"] == "completed":
                markdown_content += f"- **Status**: ✅ Completed\n"
                markdown_content += f"- **Training Time**: {result['training_time_seconds']:.1f}s\n"
                markdown_content += f"- **Output Directory**: `{result['output_dir']}`\n"
                
                if "metrics" in result:
                    markdown_content += "\n**Evaluation Metrics:**\n"
                    for metric, value in result["metrics"].items():
                        markdown_content += f"- {metric}: {value:.4f}\n"
                
                if "dataset_stats" in result:
                    stats = result["dataset_stats"]
                    markdown_content += f"\n**Dataset Statistics:**\n"
                    markdown_content += f"- Train size: {stats['train_size']}\n"
                    markdown_content += f"- Validation size: {stats['val_size']}\n"
                    markdown_content += f"- Entity coverage: {stats['entity_coverage']:.1f}%\n"
            else:
                markdown_content += f"- **Status**: ❌ Failed\n"
                markdown_content += f"- **Error**: {result.get('error', 'Unknown error')}\n"
            
            markdown_content += "\n"
        
        # Save markdown report
        markdown_path = report_dir / "batch_training_report.md"
        with open(markdown_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)


def create_parser() -> argparse.ArgumentParser:
    """Create command-line argument parser."""
    parser = argparse.ArgumentParser(
        description="Batch train multiple entity type heads for RobBERT-2023 NER",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Train all heads sequentially
  python -m src.training.batch_train_heads --entity-types PER LOC ORG --data data_sets/dutch_ner.json

  # Train heads in parallel
  python -m src.training.batch_train_heads --entity-types PER LOC --data data_sets/dutch_ner.json --parallel --max-workers 2

  # Train with custom config and mixed filtering
  python -m src.training.batch_train_heads --entity-types PER LOC ORG --data data_sets/dutch_ner.json --config config/training.yaml --filtering-strategy mixed
        """
    )
    
    # Required arguments
    parser.add_argument(
        "--entity-types",
        nargs="+",
        required=True,
        choices=["PER", "LOC", "ORG", "MISC"],
        help="Entity types to train (space-separated)"
    )
    
    parser.add_argument(
        "--data",
        type=str,
        required=True,
        help="Path to training data (JSON/JSONL file)"
    )
    
    # Training mode
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Train heads in parallel (default: sequential)"
    )
    
    parser.add_argument(
        "--max-workers",
        type=int,
        help="Maximum parallel workers (auto-detect if not specified)"
    )
    
    # Configuration
    parser.add_argument(
        "--config",
        type=str,
        help="Path to YAML configuration file"
    )
    
    parser.add_argument(
        "--filtering-strategy",
        type=str,
        default="strict",
        choices=["strict", "mixed"],
        help="Dataset filtering strategy (default: strict)"
    )
    
    parser.add_argument(
        "--output-dir",
        type=str,
        help="Base output directory for all heads"
    )
    
    # Quick overrides
    parser.add_argument(
        "--test-mode",
        action="store_true",
        help="Enable test mode for all heads"
    )
    
    parser.add_argument(
        "--epochs",
        type=int,
        help="Number of epochs for all heads"
    )
    
    return parser


def main():
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    # Setup logging
    logger = get_logger("batch_train_heads")
    
    try:
        # Validate data file
        data_path = Path(args.data)
        if not data_path.exists():
            raise FileNotFoundError(f"Data file not found: {args.data}")
        
        # Load configuration
        if args.config:
            config = HFTrainingConfig.from_yaml(args.config)
        else:
            config = HFTrainingConfig()
        
        # Apply overrides
        if args.test_mode:
            config.test_mode = True
            config.test_epochs = 1
            config.test_sample_limit = 50
            config.test_disable_wandb = True
        
        if args.epochs is not None:
            config.epochs = args.epochs
        
        # Create batch trainer
        batch_trainer = BatchHeadTrainer(config, args.output_dir)
        
        # Log training setup
        logger.logger.info("Starting batch head training:")
        logger.logger.info(f"  - Entity types: {args.entity_types}")
        logger.logger.info(f"  - Data path: {args.data}")
        logger.logger.info(f"  - Filtering strategy: {args.filtering_strategy}")
        logger.logger.info(f"  - Training mode: {'parallel' if args.parallel else 'sequential'}")
        logger.logger.info(f"  - Test mode: {config.test_mode}")
        
        if args.parallel:
            logger.logger.info(f"  - Max workers: {args.max_workers or 'auto-detect'}")
        
        # Run batch training
        start_time = time.time()
        
        if args.parallel:
            results = batch_trainer.train_heads_parallel(
                entity_types=args.entity_types,
                data_path=args.data,
                filtering_strategy=args.filtering_strategy,
                max_workers=args.max_workers
            )
        else:
            results = batch_trainer.train_heads_sequential(
                entity_types=args.entity_types,
                data_path=args.data,
                filtering_strategy=args.filtering_strategy
            )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Log results
        successful = sum(1 for r in results.values() if r["status"] == "completed")
        failed = len(results) - successful
        
        logger.logger.info("Batch training completed!")
        logger.logger.info(f"  - Total time: {total_time:.1f}s")
        logger.logger.info(f"  - Successful heads: {successful}/{len(results)}")
        logger.logger.info(f"  - Failed heads: {failed}")
        
        # Print summary
        print("\nBatch Training Summary:")
        print(f"  Total heads: {len(results)}")
        print(f"  Successful: {successful}")
        print(f"  Failed: {failed}")
        print(f"  Success rate: {successful/len(results):.1%}")
        print(f"  Total time: {total_time:.1f}s")
        
        print("\nIndividual Results:")
        for entity_type, result in results.items():
            status_icon = "✅" if result["status"] == "completed" else "❌"
            print(f"  {status_icon} {entity_type}: {result['status']}")
            if result["status"] == "completed":
                print(f"    Output: {result['output_dir']}")
        
        return 0 if failed == 0 else 1
        
    except KeyboardInterrupt:
        logger.logger.info("Batch training interrupted by user")
        return 1
        
    except Exception as e:
        logger.logger.error(f"Batch training failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())