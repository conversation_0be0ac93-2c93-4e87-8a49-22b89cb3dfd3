"""
Multi-task BERTje model with multiple classification heads.
"""

import os
import torch
import torch.nn as nn
from typing import Dict, Optional, List, Any
from transformers import AutoModel, AutoConfig, AutoTokenizer, BertForTokenClassification
from pathlib import Path

from ..heads.ner_head import NERHead


class MultiTaskBERTje(nn.Module):
    """Multi-task BERTje model with configurable heads."""
    
    def __init__(self, model_path: str, head_configs: Optional[Dict[str, Dict]] = None):
        super().__init__()

        # Load configuration and tokenizer
        self.config = AutoConfig.from_pretrained(model_path)
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)

        # Check if this is a token classification model (NER)
        if hasattr(self.config, 'architectures') and 'BertForTokenClassification' in self.config.architectures:
            # Load the complete pre-trained NER model
            self.ner_model = BertForTokenClassification.from_pretrained(model_path)
            # Extract the encoder from the NER model
            self.encoder = self.ner_model.bert
        else:
            # Load base encoder for other tasks
            self.encoder = AutoModel.from_pretrained(model_path)
            self.ner_model = None

        # Initialize heads
        self.heads = nn.ModuleDict()

        # Add NER head - use pre-trained classifier if available
        if self.ner_model is not None:
            # Use the pre-trained NER classifier
            self.heads['ner'] = self.ner_model.classifier
        elif hasattr(self.config, 'num_labels'):
            # Fallback to custom NER head
            self.heads['ner'] = NERHead(
                hidden_size=self.config.hidden_size,
                num_labels=self.config.num_labels
            )
        
        # Add placeholder heads for other tasks
        default_heads = {
            'compliance': {'num_labels': 2},
            'label': {'num_labels': 15}, 
            'reason': {'num_labels': 10},
            'topic': {'num_labels': 8}
        }
        
        if head_configs:
            default_heads.update(head_configs)
            
        for head_name, config in default_heads.items():
            if head_name not in self.heads:
                self.heads[head_name] = nn.Linear(
                    self.config.hidden_size, 
                    config['num_labels']
                )
    
    def forward(self, input_ids: torch.Tensor, attention_mask: Optional[torch.Tensor] = None,
                heads: Optional[List[str]] = None, labels: Optional[Dict[str, torch.Tensor]] = None):
        """Forward pass through encoder and specified heads."""
        
        # Encode input
        encoder_outputs = self.encoder(
            input_ids=input_ids,
            attention_mask=attention_mask,
            return_dict=True
        )
        
        hidden_states = encoder_outputs.last_hidden_state
        pooled_output = encoder_outputs.pooler_output if hasattr(encoder_outputs, 'pooler_output') else hidden_states.mean(dim=1)
        
        outputs = {}
        total_loss = 0.0
        
        # Run specified heads (or all if none specified)
        active_heads = heads if heads else list(self.heads.keys())
        
        for head_name in active_heads:
            if head_name in self.heads:
                head = self.heads[head_name]

                if head_name == 'ner':
                    # Token-level classification
                    head_labels = labels.get(head_name) if labels else None

                    if self.ner_model is not None:
                        # Use pre-trained NER model's classifier
                        logits = head(hidden_states)
                        head_output = {"logits": logits}

                        # Add loss if labels provided
                        if head_labels is not None:
                            loss_fct = nn.CrossEntropyLoss()
                            # Flatten for loss calculation
                            active_loss = attention_mask.view(-1) == 1
                            active_logits = logits.view(-1, logits.shape[-1])
                            active_labels = torch.where(
                                active_loss,
                                head_labels.view(-1),
                                torch.tensor(loss_fct.ignore_index).type_as(head_labels)
                            )
                            loss = loss_fct(active_logits, active_labels)
                            head_output["loss"] = loss
                    else:
                        # Use custom NER head
                        head_output = head(hidden_states, head_labels)
                else:
                    # Sequence-level classification
                    logits = head(pooled_output)
                    head_output = {"logits": logits}
                    
                    if labels and head_name in labels:
                        loss_fct = nn.CrossEntropyLoss()
                        loss = loss_fct(logits, labels[head_name])
                        head_output["loss"] = loss
                        total_loss += loss
                
                outputs[head_name] = head_output
                if "loss" in head_output:
                    total_loss += head_output["loss"]
        
        if total_loss > 0:
            outputs["loss"] = total_loss
            
        return outputs
    
    def get_head(self, head_name: str):
        """Get specific head by name."""
        return self.heads[head_name] if head_name in self.heads else None
    
    def add_head(self, head_name: str, head: nn.Module):
        """Add new head to the model."""
        self.heads[head_name] = head
    
    def save_pretrained(self, save_directory: str):
        """Save model and tokenizer."""
        save_path = Path(save_directory)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # Save model state
        torch.save(self.state_dict(), save_path / "pytorch_model.bin")
        
        # Save config and tokenizer
        self.config.save_pretrained(save_directory)
        self.tokenizer.save_pretrained(save_directory)
    
    @classmethod
    def load_pretrained(cls, model_directory: str, **kwargs):
        """Load pretrained multi-task model."""
        model = cls(model_directory, **kwargs)
        
        # Load model weights if available
        weights_path = Path(model_directory) / "pytorch_model.bin"
        if weights_path.exists():
            state_dict = torch.load(weights_path, map_location='cpu')
            model.load_state_dict(state_dict, strict=False)
            
        return model
    
    @classmethod
    def from_pretrained(cls, model_path: Optional[str] = None, **kwargs):
        """Create model from pretrained checkpoint."""
        if model_path is None:
            model_path = os.getenv('BERTJE_WEIGHTS_DIR', 'src/models/weights/bertje_conll')
        
        return cls(model_path, **kwargs)