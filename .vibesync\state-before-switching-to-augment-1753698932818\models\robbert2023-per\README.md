# RobBERT-2023 Person NER Model

This directory contains RobBERT-2023-based model artifacts specifically configured for Dutch Person Named Entity Recognition (PER NER).

## Model Details

### Base Model
- **Model**: `DTAI-KULeuven/robbert-2023-dutch-base`
- **Architecture**: BERT-base (12 layers, 768 hidden, 12 attention heads)
- **Parameters**: ~110M parameters
- **Tokenizer**: Byte-level BPE with 50,000 vocabulary
- **Max Sequence Length**: 512 tokens

### Task Configuration
- **Task**: Named Entity Recognition (NER)
- **Entity Types**: Person names only (PER)
- **Label Scheme**: BIO tagging (B-PER, I-PER, O)
- **Output**: 3-class classification per token

## Directory Structure

```
robbert2023-per/
├── README.md                   # This documentation
├── backups/                    # Model backups before modifications
├── base-models/               # Downloaded base model checkpoints
├── experiments/               # Experimental model variants
└── fine-tuned/               # Fine-tuned model checkpoints
```

### Subdirectory Purposes

#### `backups/`
Contains backup copies of models before significant modifications:
- Pre-token addition backups
- Pre-fine-tuning snapshots
- Recovery checkpoints

#### `base-models/`
Stores downloaded base model files:
- `config.json` - Model configuration
- `pytorch_model.bin` - Model weights
- `tokenizer.json` - Tokenizer configuration
- `vocab.json` - Vocabulary mappings

#### `experiments/`
Experimental model variants and research checkpoints:
- Different hyperparameter configurations
- Alternative training approaches
- A/B testing models

#### `fine-tuned/`
Production-ready fine-tuned models:
- Domain-specific adaptations
- Performance-optimized checkpoints
- Validated model releases

## Tokenization Behavior

### RobBERT-2023 Tokenization Features

#### Byte-level BPE
- **Advantage**: Handles any Unicode character, no OOV tokens
- **Behavior**: Splits text into subword units based on byte pairs
- **Example**: `"Jan-Willem"` → `["Jan", "-", "Willem"]`

#### Space Handling
- **Prefix**: `Ġ` character indicates word-initial tokens
- **Example**: `"Jan Jansen"` → `["Jan", "ĠJansen"]`

#### Dutch-Specific Improvements
- **Compound Names**: Better segmentation of surnames like `"van der Berg"`
- **Diacritics**: Proper handling of `"José"`, `"François"`
- **Hyphenated Names**: Correct tokenization of `"Anne-Marie"`

### Label Alignment

The tokenizer includes automatic label alignment for NER:

```python
from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment

tokenizer = RobBERTTokenizerWithAlignment()

# Word-level input
words = ["Jan", "Jansen", "woont", "in", "Amsterdam"]
labels = ["B-PER", "I-PER", "O", "O", "O"]

# Tokenize with alignment
alignment = tokenizer.tokenize_with_alignment(words, labels)

# Results in subword-level labels
print(alignment.tokens)        # ["Jan", "ĠJansen", "Ġwoont", "Ġin", "ĠAmsterdam"]
print(alignment.token_labels)  # ["B-PER", "I-PER", "O", "O", "O"]
```

#### Alignment Rules
1. **First subword**: Gets the original word's label
2. **Continuation subwords**: Get `I-PER` if original was `B-PER` or `I-PER`, otherwise `O`
3. **Special tokens**: Always labeled as `O`

## Model Usage

### Loading the Model

```python
from src.models.multitask_robbert import MultiTaskRobBERT

# Load default model
model = MultiTaskRobBERT.from_pretrained()

# Load specific checkpoint
model = MultiTaskRobBERT.from_pretrained("models/robbert2023-per/fine-tuned/best-model")
```

### Inference Example

```python
import torch

# Prepare input
text = "Jan Jansen woont in Amsterdam."
inputs = model.tokenizer(text, return_tensors="pt", truncation=True, padding=True)

# Run inference
model.eval()
with torch.no_grad():
    outputs = model(
        input_ids=inputs['input_ids'],
        attention_mask=inputs['attention_mask'],
        heads=['ner']
    )

# Get predictions
logits = outputs['ner']['logits']
predictions = torch.argmax(logits, dim=-1)

# Convert to labels
predicted_labels = [model.id2label[pred.item()] for pred in predictions[0]]
```

### Entity Extraction

```python
from src.inference.api_fastapi import extract_entities

# Extract entities from text
text = "Jan Jansen en Marie de Wit wonen in Amsterdam."
entities = extract_entities(model, text)

# Results
for entity in entities:
    print(f"{entity['text']} ({entity['label']}) - confidence: {entity['confidence']:.2f}")
```

## Performance Characteristics

### Tokenization Quality
- **OOV Rate**: 0% (byte-level BPE handles all input)
- **Compound Name Handling**: Excellent for Dutch surnames
- **Diacritics**: Full Unicode support
- **OCR Robustness**: Better handling of scanning artifacts

### Model Performance
- **Inference Speed**: ~100 tokens/second (CPU)
- **Memory Usage**: ~1.2GB RAM
- **Batch Processing**: Supports efficient batching
- **Accuracy**: High precision on Dutch person names

### Comparison with BERTje

| Metric | BERTje | RobBERT-2023 |
|--------|--------|--------------|
| Vocabulary Size | 30,000 | 50,000 |
| Tokenization | WordPiece | Byte-level BPE |
| OOV Rate | 2-5% | 0% |
| Dutch Compound Names | Good | Excellent |
| Diacritics | Limited | Full Support |
| Memory Usage | ~1.0GB | ~1.2GB |

## Training and Fine-tuning

### Fine-tuning Command
```bash
python -m src.training.train_multitask \
    --heads ner \
    --epochs 5 \
    --batch-size 8 \
    --learning-rate 2e-5 \
    --output-dir models/robbert2023-per/fine-tuned/custom-model
```

### Training Data Format
```json
{"text": "Jan Jansen woont in Amsterdam.", "ner": ["B-PER", "I-PER", "O", "O", "O"]}
{"text": "Marie de Wit werkt bij Google.", "ner": ["B-PER", "I-PER", "I-PER", "O", "O", "O"]}
```

### Hyperparameter Recommendations
- **Learning Rate**: 2e-5 (standard for BERT fine-tuning)
- **Batch Size**: 8-16 (depending on available memory)
- **Epochs**: 3-5 (monitor validation loss)
- **Warmup Steps**: 10% of total steps
- **Weight Decay**: 0.01

## Checkpoint Management

### Saving Checkpoints
```python
from src.utils.checkpoint_manager import CheckpointManager

manager = CheckpointManager("models/robbert2023-per")

# Save with metadata
manager.save_checkpoint(model, "experiment-1", metadata={
    "epoch": 3,
    "train_loss": 0.12,
    "val_loss": 0.15,
    "f1_score": 0.91,
    "precision": 0.89,
    "recall": 0.93
})
```

### Loading Checkpoints
```python
# List available checkpoints
checkpoints = manager.list_checkpoints()
print("Available checkpoints:", checkpoints)

# Load best checkpoint
best_model = manager.load_best_checkpoint(metric="f1_score")
```

## Troubleshooting

### Common Issues

#### Model Loading Errors
```python
# Check if model files exist
import os
model_path = "models/robbert2023-per/base-models"
required_files = ["config.json", "pytorch_model.bin", "tokenizer.json"]
for file in required_files:
    if not os.path.exists(os.path.join(model_path, file)):
        print(f"Missing: {file}")
```

#### Tokenization Issues
```python
# Test tokenizer directly
from transformers import AutoTokenizer
tokenizer = AutoTokenizer.from_pretrained("DTAI-KULeuven/robbert-2023-dutch-base")

# Test problematic text
text = "Jan-Willem van der Berg"
tokens = tokenizer.tokenize(text)
print("Tokens:", tokens)
```

#### Memory Issues
```python
# Reduce batch size or use CPU-only mode
import torch
torch.set_default_tensor_type('torch.FloatTensor')  # Force CPU
```

#### Label Alignment Problems
```python
# Debug alignment
from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment

tokenizer = RobBERTTokenizerWithAlignment()
words = ["Jan-Willem", "woont", "hier"]
labels = ["B-PER", "O", "O"]

alignment = tokenizer.tokenize_with_alignment(words, labels)
print("Word IDs:", alignment.word_ids)
print("Tokens:", alignment.tokens)
print("Token Labels:", alignment.token_labels)
```

## Validation Scripts

### Basic Functionality
```bash
# Test model loading and basic inference
python scripts/smoke_test.py

# Enhanced NER testing
python scripts/enhanced_ner_test.py

# Tokenization analysis
python scripts/test_robbert_tokenizer.py
```

### Performance Testing
```bash
# Performance baseline
python scripts/performance_baseline.py

# End-to-end pipeline validation
python scripts/test_end_to_end_pipeline.py
```

### Tokenization Quality
```bash
# OOV analysis
python scripts/oov_analysis_and_tokenization_verification.py

# Compound name testing
python scripts/test_compound_names.py
```

## Related Documentation

- [Main Project README](../../README.md)
- [API Documentation](../../README_API_ROBBERT.md)
- [Token Management](../../README_TOKEN_MANAGEMENT.md)
- [Tokenization Analysis](../../assessment_results/tokenization_analysis.md)
- [Performance Baseline](../../docs/performance_baseline.md)

## Model Artifacts

This directory is managed by the checkpoint management system. Key files include:

- **Model Weights**: PyTorch `.bin` files containing trained parameters
- **Configuration**: JSON files with model architecture and hyperparameters
- **Tokenizer**: Vocabulary and tokenization configuration
- **Metadata**: Training logs, performance metrics, and validation results

For detailed checkpoint management, see the [Checkpoint Management Documentation](../../docs/checkpoint_management.md).