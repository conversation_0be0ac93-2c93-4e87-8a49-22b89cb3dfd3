#!/usr/bin/env python3
"""
Example usage of HFTrainingConfig for Hugging Face Trainer integration.

This script demonstrates how to:
1. Create HFTrainingConfig instances
2. Load configuration from YAML files
3. Convert to TrainingArguments
4. Use class weights and advanced features
"""

import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from training.hf_config import (
    HFTrainingConfig,
    create_default_hf_config,
    create_test_hf_config
)


def example_basic_usage():
    """Demonstrate basic HFTrainingConfig usage."""
    print("=== Basic Usage Example ===")
    
    # Create default configuration
    config = create_default_hf_config()
    print(f"Default config - Epochs: {config.epochs}, Batch size: {config.batch_size}")
    
    # Create custom configuration
    custom_config = HFTrainingConfig(
        epochs=5,
        batch_size=16,
        learning_rate=3e-5,
        use_class_weights=True,
        class_weights={"O": 0.5, "B-PER": 2.0, "I-PER": 1.5},
        experiment_name="custom_ner_training"
    )
    print(f"Custom config - Epochs: {custom_config.epochs}, Use class weights: {custom_config.use_class_weights}")
    
    # Convert to TrainingArguments
    training_args = custom_config.to_training_args(output_dir="./test_output")
    print(f"TrainingArguments - Output dir: {training_args.output_dir}")
    print(f"TrainingArguments - Learning rate: {training_args.learning_rate}")
    print()


def example_yaml_loading():
    """Demonstrate YAML configuration loading."""
    print("=== YAML Loading Example ===")
    
    # Load default template
    try:
        config = HFTrainingConfig.from_yaml("src/config/hf_training_default.yaml")
        print(f"Loaded default template - Model: {config.model_name}")
        print(f"WandB project: {config.wandb_project}")
    except FileNotFoundError:
        print("Default template not found, creating programmatically...")
        config = create_default_hf_config()
    
    # Load test template
    try:
        test_config = HFTrainingConfig.from_yaml("src/config/hf_training_test.yaml")
        print(f"Loaded test template - Test mode: {test_config.test_mode}")
        print(f"Test epochs: {test_config.test_epochs}")
    except FileNotFoundError:
        print("Test template not found, creating programmatically...")
        test_config = create_test_hf_config()
    
    # Load class weights template
    try:
        class_weights_config = HFTrainingConfig.from_yaml("src/config/hf_training_class_weights.yaml")
        print(f"Loaded class weights template - Use class weights: {class_weights_config.use_class_weights}")
        if class_weights_config.class_weights:
            print(f"Class weights: {class_weights_config.class_weights}")
    except FileNotFoundError:
        print("Class weights template not found")
    
    print()


def example_environment_variables():
    """Demonstrate environment variable substitution."""
    print("=== Environment Variables Example ===")
    
    # Set some environment variables
    os.environ["ROBBERT_MODEL_NAME"] = "custom-robbert-model"
    os.environ["WANDB_PROJECT"] = "custom-project"
    os.environ["WANDB_ENTITY"] = "custom-entity"
    
    # Create YAML content with environment variables
    yaml_content = """
hf_training:
  model_name: "${ROBBERT_MODEL_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}"
  wandb_project: "${WANDB_PROJECT:-robbert2023-ner}"
  wandb_entity: "${WANDB_ENTITY:-slippydongle}"
  epochs: 3
  batch_size: 8
"""
    
    # Write to temporary file
    temp_yaml = Path("temp_config.yaml")
    with open(temp_yaml, "w") as f:
        f.write(yaml_content)
    
    try:
        # Load configuration with environment variable substitution
        config = HFTrainingConfig.from_yaml(str(temp_yaml))
        print(f"Model name from env: {config.model_name}")
        print(f"WandB project from env: {config.wandb_project}")
        print(f"WandB entity from env: {config.wandb_entity}")
    finally:
        # Cleanup
        temp_yaml.unlink()
    
    print()


def example_class_weights():
    """Demonstrate class weights functionality."""
    print("=== Class Weights Example ===")
    
    # Create configuration with class weights
    config = HFTrainingConfig(
        use_class_weights=True,
        class_weights={
            "O": 0.3,      # Reduce weight for majority class
            "B-PER": 3.0,  # Increase weight for minority class
            "I-PER": 2.5   # Increase weight for minority class
        }
    )
    
    print(f"Use class weights: {config.use_class_weights}")
    print(f"Class weights: {config.class_weights}")
    
    # Validate class weights
    label_list = ["O", "B-PER", "I-PER"]
    try:
        config.validate_class_weights(label_list)
        print("✅ Class weights validation passed")
    except ValueError as e:
        print(f"❌ Class weights validation failed: {e}")
    
    # Test with missing labels
    try:
        incomplete_config = HFTrainingConfig(
            use_class_weights=True,
            class_weights={"O": 0.5, "B-PER": 2.0}  # Missing I-PER
        )
        incomplete_config.validate_class_weights(label_list)
    except ValueError as e:
        print(f"✅ Expected validation error for missing labels: {e}")
    
    print()


def example_wandb_config():
    """Demonstrate WandB configuration extraction."""
    print("=== WandB Configuration Example ===")
    
    config = HFTrainingConfig(
        wandb_project="robbert-ner-experiment",
        wandb_entity="my-team",
        run_name="experiment-v1",
        wandb_tags=["robbert", "ner", "dutch"],
        wandb_notes="Testing HF Trainer integration",
        epochs=5,
        batch_size=16,
        learning_rate=3e-5
    )
    
    wandb_config = config.get_wandb_config()
    print("WandB configuration:")
    print(f"  Project: {wandb_config['project']}")
    print(f"  Entity: {wandb_config['entity']}")
    print(f"  Run name: {wandb_config['name']}")
    print(f"  Tags: {wandb_config['tags']}")
    print(f"  Training config: {wandb_config['config']}")
    
    print()


def example_output_directory():
    """Demonstrate output directory creation."""
    print("=== Output Directory Example ===")
    
    config = HFTrainingConfig(
        experiment_name="ner_experiment",
        run_version="v2.1",
        output_dir="checkpoints"
    )
    
    # Create timestamped output directory
    output_dir = config.create_output_dir("training")
    print(f"Created output directory: {output_dir}")
    
    # Cleanup
    import shutil
    if Path(output_dir).exists():
        shutil.rmtree(output_dir)
        print("Cleaned up output directory")
    
    print()


def example_test_mode():
    """Demonstrate test mode configuration."""
    print("=== Test Mode Example ===")
    
    # Create test configuration
    test_config = create_test_hf_config()
    print(f"Test mode: {test_config.test_mode}")
    print(f"Test epochs: {test_config.test_epochs}")
    print(f"Test sample limit: {test_config.test_sample_limit}")
    print(f"Test disable WandB: {test_config.test_disable_wandb}")
    
    # Convert to TrainingArguments
    training_args = test_config.to_training_args()
    print(f"Training epochs: {training_args.num_train_epochs}")
    print(f"Report to: {training_args.report_to}")
    
    print()


def main():
    """Run all examples."""
    print("HFTrainingConfig Examples")
    print("=" * 50)
    
    example_basic_usage()
    example_yaml_loading()
    example_environment_variables()
    example_class_weights()
    example_wandb_config()
    example_output_directory()
    example_test_mode()
    
    print("All examples completed successfully! ✅")


if __name__ == "__main__":
    main()