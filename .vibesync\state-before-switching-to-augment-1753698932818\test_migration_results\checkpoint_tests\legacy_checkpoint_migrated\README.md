# RobBERT-2023 NER Checkpoint

This checkpoint has been migrated to Hugging Face Trainer format.

## Usage

### Loading the model

```python
from transformers import RobertaForTokenClassification, AutoTokenizer

model = RobertaForTokenClassification.from_pretrained("test_migration_results\checkpoint_tests\legacy_checkpoint_migrated")
tokenizer = AutoTokenizer.from_pretrained("test_migration_results\checkpoint_tests\legacy_checkpoint_migrated")
```

### Using with Hugging Face Trainer

```python
from src.training.hf_trainer import HFNERTrainer
from src.training.hf_config import HFTrainingConfig

config = HFTrainingConfig()
trainer = HFNERTrainer(config)

# Resume training
trainer.train(
    data_path="path/to/data.json",
    resume_from_checkpoint="test_migration_results\checkpoint_tests\legacy_checkpoint_migrated"
)
```

## Migration Information

- Migrated on: 2025-07-28T17:16:33.825932
- Format: Hugging Face Trainer compatible
- Model: RobBERT-2023 for Dutch NER
- Labels: O, B-PER, I-PER

## Compatibility

This checkpoint is compatible with:
- Hugging Face Transformers library
- RobBERT-2023 tokenizer
- Current inference API endpoints
