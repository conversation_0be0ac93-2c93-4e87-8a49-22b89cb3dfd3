# Token Management Guide

This guide explains how to inspect, modify, and manage the tokenizer vocabulary and model token embeddings in the BERTje NER system.

## Overview

The BERTje model uses a tokenizer with a fixed vocabulary. When you need to add new tokens (e.g., domain-specific terms, new entity types, or special markers), you must:

1. Add tokens to the tokenizer vocabulary
2. Resize the model's token embeddings to accommodate new tokens
3. Optionally retrain or fine-tune the model on the new tokens

## Quick Start

### Activate Environment
```bash
# Windows
.\venv\Scripts\activate

# Linux/macOS  
source venv/bin/activate
```

### Basic Commands

```bash
# Show vocabulary information
python scripts/token_management.py info

# Search for existing tokens
python scripts/token_management.py search "gemeente"

# Check if specific tokens exist
python scripts/token_management.py check "Amsterdam" "Rotterdam" "[GEMEENTE]"

# Add new tokens and resize embeddings
python scripts/token_management.py add "[GEMEENTE]" "[POSTCODE]" --resize --save models/updated_bertje

# Export vocabulary to file
python scripts/token_management.py export vocab.json --format json
```

## Detailed Usage

### 1. Inspecting Current Vocabulary

#### Get Vocabulary Statistics
```bash
python scripts/token_management.py info
```

This shows:
- Total vocabulary size
- Model maximum sequence length
- Special tokens (PAD, UNK, CLS, SEP, MASK)
- Special token IDs

#### Search for Tokens
```bash
# Find tokens containing "gemeente"
python scripts/token_management.py search "gemeente" --limit 10

# Find tokens with "amsterdam"
python scripts/token_management.py search "amsterdam"
```

#### Check Specific Tokens
```bash
# Check if tokens exist
python scripts/token_management.py check "Amsterdam" "Rotterdam" "[GEMEENTE]"
```

### 2. Adding New Tokens

#### Add Regular Tokens
```bash
# Add domain-specific tokens
python scripts/token_management.py add "[GEMEENTE]" "[POSTCODE]" "[STRAAT]"
```

#### Add Special Tokens
```bash
# Add as special tokens (won't be split during tokenization)
python scripts/token_management.py add "[REDACTED]" "[GDPR]" --special
```

#### Add Tokens with Model Update
```bash
# Add tokens, resize embeddings, and save updated model
python scripts/token_management.py add "[GEMEENTE]" "[POSTCODE]" \
    --resize --save models/updated_bertje
```

### 3. Understanding Token Types

#### Regular Tokens
- Normal vocabulary tokens that can be split by the tokenizer
- Used for domain-specific terms, names, locations
- Example: `"gemeente"`, `"amsterdam"`, `"postcode"`

#### Special Tokens
- Never split during tokenization
- Useful for markers, redaction placeholders, special entity types
- Example: `"[GEMEENTE]"`, `"[REDACTED]"`, `"[GDPR_SENSITIVE]"`

### 4. Model Embedding Resize Process

When you add tokens, the model's embedding layer needs to be resized:

```python
# The process internally:
# 1. Get current embedding size: old_size = model.embeddings.word_embeddings.num_embeddings
# 2. Get new vocabulary size: new_size = len(tokenizer.get_vocab())
# 3. Resize embeddings: model.resize_token_embeddings(new_size)
# 4. New token embeddings are randomly initialized
```

#### Important Notes:
- New token embeddings start with random weights
- You should fine-tune the model after adding tokens
- The resize operation affects both the encoder and NER head embeddings

### 5. Programmatic Usage

#### Using TokenManager Class
```python
from scripts.token_management import TokenManager

# Initialize
tm = TokenManager('src/models/weights/bertje_conll')

# Get vocabulary info
info = tm.get_vocab_info()
print(f"Vocabulary size: {info['vocab_size']}")

# Check tokens
exists = tm.check_tokens_exist(['Amsterdam', '[GEMEENTE]'])
print(exists)  # {'Amsterdam': True, '[GEMEENTE]': False}

# Add tokens
result = tm.add_tokens(['[GEMEENTE]', '[POSTCODE]'])
print(f"Added {result['tokens_added']} tokens")

# Resize model embeddings
resize_result = tm.resize_model_embeddings('models/updated_bertje')
print(resize_result)
```

### 6. Common Use Cases

#### Adding GDPR Redaction Tokens
```bash
# Add tokens for GDPR compliance
python scripts/token_management.py add \
    "[REDACTED_PERSON]" "[REDACTED_ADDRESS]" "[REDACTED_PHONE]" \
    --special --resize --save models/gdpr_bertje
```

#### Adding Dutch Municipality Names
```bash
# First check which exist
python scripts/token_management.py check \
    "Amsterdam" "Rotterdam" "Utrecht" "Eindhoven" "Tilburg"

# Add missing ones
python scripts/token_management.py add \
    "Groningen" "Breda" "Nijmegen" "Apeldoorn" \
    --resize --save models/municipality_bertje
```

#### Adding Domain-Specific Terms
```bash
# Legal/compliance terms
python scripts/token_management.py add \
    "AVG" "GDPR" "verwerkingsverantwoordelijke" "betrokkene" \
    --resize --save models/legal_bertje
```

### 7. Best Practices

#### Token Naming Conventions
- Use `[UPPERCASE]` for special markers: `[REDACTED]`, `[GEMEENTE]`
- Use lowercase for regular domain terms: `gemeente`, `postcode`
- Be consistent with existing vocabulary style

#### When to Add Tokens
- **Add tokens when**: You have frequent out-of-vocabulary terms
- **Add tokens when**: You need special markers for processing
- **Don't add tokens for**: Rare terms that appear < 100 times in your data
- **Don't add tokens for**: Terms that are well-handled by subword tokenization

#### Model Retraining
After adding tokens, consider:
1. **Light fine-tuning**: 1-2 epochs on your domain data
2. **Embedding-only training**: Freeze encoder, train only new embeddings
3. **Full retraining**: If adding many tokens (>1000)

### 8. Troubleshooting

#### Common Issues

**Issue**: "Token already exists"
```bash
# Check first
python scripts/token_management.py check "your_token"
```

**Issue**: "Model embeddings size mismatch"
```bash
# Resize embeddings
python scripts/token_management.py add --resize
```

**Issue**: "Out of memory during resize"
- Use smaller batch sizes during training
- Consider gradient checkpointing
- Use CPU-only mode for embedding resize

#### Validation
```bash
# After adding tokens, validate the model still works
python scripts/smoke_test.py

# Test with new tokens
python scripts/enhanced_ner_test.py
```

### 9. Export and Backup

#### Export Current Vocabulary
```bash
# JSON format (with token IDs)
python scripts/token_management.py export vocab.json --format json

# Text format (tab-separated)
python scripts/token_management.py export vocab.txt --format txt
```

#### Backup Before Changes
```bash
# Always backup before major changes
cp -r src/models/weights/bertje_conll src/models/weights/bertje_conll_backup
```

## Advanced Usage

### Custom Token Addition Script
```python
#!/usr/bin/env python3
"""Custom token addition for specific domain."""

from scripts.token_management import TokenManager

def add_dutch_legal_tokens():
    """Add Dutch legal and GDPR-specific tokens."""
    
    tm = TokenManager('src/models/weights/bertje_conll')
    
    # Define tokens to add
    legal_tokens = [
        # GDPR/AVG terms
        '[REDACTED_PERSON]', '[REDACTED_ADDRESS]', '[REDACTED_PHONE]',
        '[REDACTED_EMAIL]', '[REDACTED_BSN]', '[REDACTED_IBAN]',
        
        # Legal entities
        'verwerkingsverantwoordelijke', 'verwerker', 'betrokkene',
        'persoonsgegevens', 'bijzondere_persoonsgegevens',
        
        # Dutch municipalities (common ones not in vocab)
        'Almere', 'Zoetermeer', 'Maastricht', 'Dordrecht'
    ]
    
    # Check existing
    existing = tm.check_tokens_exist(legal_tokens)
    new_tokens = [token for token, exists in existing.items() if not exists]
    
    print(f"Adding {len(new_tokens)} new legal tokens...")
    
    # Add tokens
    result = tm.add_tokens(new_tokens)
    print(f"Added {result['tokens_added']} tokens")
    
    # Resize and save
    resize_result = tm.resize_model_embeddings('models/legal_bertje')
    print(f"Model updated: {resize_result}")

if __name__ == '__main__':
    add_dutch_legal_tokens()
```

### Integration with Training Pipeline
```python
# In your training script
from scripts.token_management import TokenManager

def prepare_model_for_domain(model_path, domain_tokens):
    """Prepare model with domain-specific tokens."""
    
    tm = TokenManager(model_path)
    
    # Add domain tokens
    tm.add_tokens(domain_tokens)
    
    # Load model and resize
    model = tm.load_model()
    tm.resize_model_embeddings()
    
    return model, tm.tokenizer
```

This comprehensive token management system allows you to efficiently handle vocabulary expansion while maintaining model compatibility and performance.