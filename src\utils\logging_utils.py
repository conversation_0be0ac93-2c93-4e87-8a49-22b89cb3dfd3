"""
Enhanced logging utilities for RobBERT-2023 pipeline.
"""

import logging
import sys
import time
import functools
import traceback
from typing import Optional, Dict, Any, Callable
from pathlib import Path
import torch
import psutil
import os

from ..exceptions import RobBERTError


class RobBERTLogger:
    """Enhanced logger for RobBERT pipeline with structured logging."""
    
    def __init__(self, name: str, level: str = "INFO", log_file: Optional[str] = None):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler if specified
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def log_system_info(self):
        """Log system information for debugging."""
        try:
            # System info
            self.logger.info(f"Python version: {sys.version}")
            self.logger.info(f"PyTorch version: {torch.__version__}")
            self.logger.info(f"CUDA available: {torch.cuda.is_available()}")
            if torch.cuda.is_available():
                self.logger.info(f"CUDA version: {torch.version.cuda}")
                self.logger.info(f"GPU count: {torch.cuda.device_count()}")
                for i in range(torch.cuda.device_count()):
                    gpu_name = torch.cuda.get_device_name(i)
                    gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1e9
                    self.logger.info(f"GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
            
            # Memory info
            memory = psutil.virtual_memory()
            self.logger.info(f"System memory: {memory.total / 1e9:.1f}GB total, "
                           f"{memory.available / 1e9:.1f}GB available")
            
            # CPU info
            self.logger.info(f"CPU count: {psutil.cpu_count()} cores")
            
        except Exception as e:
            self.logger.warning(f"Could not log system info: {e}")
    
    def log_model_info(self, model_name: str, num_parameters: Optional[int] = None,
                      model_size_mb: Optional[float] = None):
        """Log model information."""
        self.logger.info(f"Model: {model_name}")
        if num_parameters:
            self.logger.info(f"Parameters: {num_parameters:,}")
        if model_size_mb:
            self.logger.info(f"Model size: {model_size_mb:.1f}MB")
    
    def log_tokenization_stats(self, text: str, tokens: list, 
                             alignment_stats: Optional[Dict[str, Any]] = None):
        """Log tokenization statistics."""
        self.logger.debug(f"Text length: {len(text)} characters")
        self.logger.debug(f"Token count: {len(tokens)}")
        self.logger.debug(f"Tokens per character: {len(tokens) / len(text):.3f}")
        
        if alignment_stats:
            self.logger.debug(f"Alignment stats: {alignment_stats}")
        
        # Log sample tokens for debugging
        if len(tokens) > 0:
            sample_tokens = tokens[:5] + (['...'] if len(tokens) > 5 else [])
            self.logger.debug(f"Sample tokens: {sample_tokens}")
    
    def log_inference_stats(self, input_shape: tuple, output_shape: tuple,
                          inference_time: float, heads: list):
        """Log inference statistics."""
        self.logger.info(f"Inference completed in {inference_time:.3f}s")
        self.logger.debug(f"Input shape: {input_shape}")
        self.logger.debug(f"Output shape: {output_shape}")
        self.logger.debug(f"Active heads: {heads}")
        
        # Calculate throughput
        batch_size = input_shape[0] if input_shape else 1
        seq_length = input_shape[1] if len(input_shape) > 1 else 1
        if inference_time > 0:
            tokens_per_second = (batch_size * seq_length) / inference_time
            self.logger.debug(f"Throughput: {tokens_per_second:.1f} tokens/second")
        else:
            self.logger.debug("Throughput: N/A (inference time too small to measure)")
    
    def log_memory_usage(self, operation: str = ""):
        """Log current memory usage."""
        try:
            # System memory
            memory = psutil.virtual_memory()
            memory_used_gb = (memory.total - memory.available) / 1e9
            memory_percent = memory.percent
            
            # GPU memory if available
            gpu_info = ""
            if torch.cuda.is_available():
                gpu_memory_used = torch.cuda.memory_allocated() / 1e9
                gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / 1e9
                gpu_percent = (gpu_memory_used / gpu_memory_total) * 100
                gpu_info = f", GPU: {gpu_memory_used:.1f}GB/{gpu_memory_total:.1f}GB ({gpu_percent:.1f}%)"
            
            operation_prefix = f"[{operation}] " if operation else ""
            self.logger.debug(f"{operation_prefix}Memory usage - "
                            f"RAM: {memory_used_gb:.1f}GB ({memory_percent:.1f}%){gpu_info}")
            
        except Exception as e:
            self.logger.warning(f"Could not log memory usage: {e}")
    
    def log_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """Log error with context and traceback."""
        error_msg = f"Error: {type(error).__name__}: {str(error)}"
        
        if isinstance(error, RobBERTError) and error.details:
            error_msg += f" | Details: {error.details}"
        
        if context:
            error_msg += f" | Context: {context}"
        
        self.logger.error(error_msg)
        self.logger.debug(f"Traceback:\n{traceback.format_exc()}")


def get_logger(name: str, level: str = "INFO", log_file: Optional[str] = None) -> RobBERTLogger:
    """Factory function to create RobBERT logger."""
    return RobBERTLogger(name, level, log_file)


def log_execution_time(logger: Optional[RobBERTLogger] = None, 
                      operation_name: str = "operation"):
    """Decorator to log execution time of functions."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal logger
            if logger is None:
                logger = get_logger(func.__module__)
            
            start_time = time.time()
            logger.logger.debug(f"Starting {operation_name}: {func.__name__}")
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.logger.info(f"Completed {operation_name}: {func.__name__} "
                                 f"in {execution_time:.3f}s")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.log_error(e, {
                    "function": func.__name__,
                    "operation": operation_name,
                    "execution_time": f"{execution_time:.3f}s"
                })
                raise
        
        return wrapper
    return decorator


def log_memory_usage(logger: Optional[RobBERTLogger] = None, 
                    operation_name: str = "operation"):
    """Decorator to log memory usage before and after function execution."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal logger
            if logger is None:
                logger = get_logger(func.__module__)
            
            # Log memory before
            logger.log_memory_usage(f"Before {operation_name}")
            
            try:
                result = func(*args, **kwargs)
                # Log memory after
                logger.log_memory_usage(f"After {operation_name}")
                return result
            except Exception as e:
                logger.log_memory_usage(f"After failed {operation_name}")
                raise
        
        return wrapper
    return decorator


def safe_execute(logger: Optional[RobBERTLogger] = None, 
                default_return: Any = None,
                reraise: bool = True):
    """Decorator for safe execution with error logging."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal logger
            if logger is None:
                logger = get_logger(func.__module__)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.log_error(e, {
                    "function": func.__name__,
                    "args_count": len(args),
                    "kwargs_keys": list(kwargs.keys())
                })
                
                if reraise:
                    raise
                else:
                    logger.logger.warning(f"Returning default value: {default_return}")
                    return default_return
        
        return wrapper
    return decorator


def validate_tensor_shape(tensor: torch.Tensor, expected_shape: tuple, 
                         tensor_name: str = "tensor", logger: Optional[RobBERTLogger] = None):
    """Validate tensor shape and log details."""
    if logger is None:
        logger = get_logger(__name__)
    
    actual_shape = tuple(tensor.shape)
    
    # Check if shapes match (allowing -1 for flexible dimensions)
    shape_match = True
    for i, (expected, actual) in enumerate(zip(expected_shape, actual_shape)):
        if expected != -1 and expected != actual:
            shape_match = False
            break
    
    if not shape_match:
        from ..exceptions import DimensionMismatchError
        error_msg = f"Shape mismatch for {tensor_name}"
        logger.log_error(DimensionMismatchError(
            error_msg,
            expected_shape=expected_shape,
            actual_shape=actual_shape,
            tensor_name=tensor_name
        ))
        raise DimensionMismatchError(
            error_msg,
            expected_shape=expected_shape,
            actual_shape=actual_shape,
            tensor_name=tensor_name
        )
    
    logger.logger.debug(f"Tensor {tensor_name} shape validation passed: {actual_shape}")


def log_tensor_stats(tensor: torch.Tensor, tensor_name: str = "tensor",
                    logger: Optional[RobBERTLogger] = None):
    """Log tensor statistics for debugging."""
    if logger is None:
        logger = get_logger(__name__)
    
    try:
        stats = {
            "shape": tuple(tensor.shape),
            "dtype": str(tensor.dtype),
            "device": str(tensor.device),
            "requires_grad": tensor.requires_grad,
            "mean": tensor.float().mean().item() if tensor.numel() > 0 else 0.0,
            "std": tensor.float().std().item() if tensor.numel() > 0 else 0.0,
            "min": tensor.min().item() if tensor.numel() > 0 else 0.0,
            "max": tensor.max().item() if tensor.numel() > 0 else 0.0,
            "num_elements": tensor.numel()
        }
        
        logger.logger.debug(f"Tensor {tensor_name} stats: {stats}")
        
    except Exception as e:
        logger.logger.warning(f"Could not compute stats for tensor {tensor_name}: {e}")