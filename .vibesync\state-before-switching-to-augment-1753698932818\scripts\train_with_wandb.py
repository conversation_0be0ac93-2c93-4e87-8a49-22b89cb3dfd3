#!/usr/bin/env python3
"""
Example script demonstrating WandB integration with RobBERT-2023 training.

This script shows how to:
1. Train individual heads with head-specific configurations
2. Train multiple heads with multi-task learning
3. Use WandB for experiment tracking and model artifacts
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Success!")
        if result.stdout:
            print("STDOUT:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print("❌ Failed!")
        print("STDERR:", e.stderr)
        if e.stdout:
            print("STDOUT:", e.stdout)
        return False

def main():
    """Main training examples."""
    
    # Ensure we're in the right directory
    if not Path("src/training/train_multitask.py").exists():
        print("❌ Please run this script from the project root directory")
        sys.exit(1)
    
    # Set environment variable for WandB API key (if not already set)
    if not os.getenv('WANDB_API_KEY'):
        os.environ['WANDB_API_KEY'] = '****************************************'
    
    print("🚀 RobBERT-2023 WandB Training Examples")
    print("=" * 60)
    
    # Example 1: Train NER head only with head-specific config
    print("\n📝 Example 1: Training NER head with head-specific configuration")
    ner_cmd = [
        sys.executable, "-m", "src.training.train_multitask",
        "--heads", "ner",
        "--use-head-configs",
        "--wandb-config", "src/config/wandb.yaml",
        "--epochs", "2",  # Reduced for demo
        "--data", "data_sets/synthetic1_cleaned.json",
        "--experiment", "ner_head_demo"
    ]
    
    if not run_command(ner_cmd, "NER Head Training"):
        print("⚠️  NER training failed, continuing with other examples...")
    
    # Example 2: Train compliance head with custom parameters
    print("\n🔒 Example 2: Training compliance head with custom parameters")
    compliance_cmd = [
        sys.executable, "-m", "src.training.train_multitask",
        "--heads", "compliance",
        "--use-head-configs",
        "--wandb-config", "src/config/wandb.yaml",
        "--epochs", "2",
        "--batch-size", "6",  # Smaller batch for demo
        "--learning-rate", "1e-5",
        "--experiment", "compliance_head_demo"
    ]
    
    if not run_command(compliance_cmd, "Compliance Head Training"):
        print("⚠️  Compliance training failed, continuing with other examples...")
    
    # Example 3: Multi-task training with multiple heads
    print("\n🎯 Example 3: Multi-task training with multiple heads")
    multitask_cmd = [
        sys.executable, "-m", "src.training.train_multitask",
        "--heads", "ner", "compliance", "topic",
        "--use-head-configs",
        "--wandb-config", "src/config/wandb.yaml",
        "--epochs", "2",
        "--batch-size", "4",  # Smaller batch for multi-task
        "--experiment", "multitask_demo"
    ]
    
    if not run_command(multitask_cmd, "Multi-task Training"):
        print("⚠️  Multi-task training failed, continuing...")
    
    # Example 4: Training with override parameters
    print("\n⚙️  Example 4: Training with parameter overrides")
    override_cmd = [
        sys.executable, "-m", "src.training.train_multitask",
        "--heads", "label",
        "--use-head-configs",
        "--wandb-config", "src/config/wandb.yaml",
        "--epochs", "1",
        "--epochs-override",  # Override head config epochs
        "--batch-size", "8",
        "--batch-size-override",  # Override head config batch size
        "--learning-rate", "3e-5",
        "--learning-rate-override",  # Override head config learning rate
        "--experiment", "override_demo"
    ]
    
    if not run_command(override_cmd, "Training with Overrides"):
        print("⚠️  Override training failed...")
    
    # Example 5: Training without WandB (fallback)
    print("\n🔄 Example 5: Training without WandB (fallback mode)")
    no_wandb_cmd = [
        sys.executable, "-m", "src.training.train_multitask",
        "--heads", "reason",
        "--epochs", "1",
        "--batch-size", "4",
        "--experiment", "no_wandb_demo"
    ]
    
    if not run_command(no_wandb_cmd, "Training without WandB"):
        print("⚠️  No-WandB training failed...")
    
    print("\n🎉 Training examples completed!")
    print("\n📊 Check your WandB dashboard at: https://wandb.ai/slippydongle")
    print("   - robbert2023-ner: NER head experiments")
    print("   - robbert2023-compliance: Compliance head experiments") 
    print("   - robbert2023-multitask: Multi-task experiments")
    print("   - robbert2023-label: Label classification experiments")
    
    print("\n💡 Tips:")
    print("   - Use --use-head-configs for head-specific parameters")
    print("   - Use --*-override flags to override head config values")
    print("   - Check src/config/heads/ for head-specific configurations")
    print("   - Modify src/config/wandb.yaml for WandB settings")

if __name__ == "__main__":
    main()