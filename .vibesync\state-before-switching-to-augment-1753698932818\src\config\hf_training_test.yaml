# Hugging Face Training Test Configuration
# Optimized for quick testing and CI/CD pipelines

hf_training:
  # Model configuration
  model_name: "${ROBBERT_MODEL_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}"
  
  # Training parameters (reduced for testing)
  epochs: 1
  batch_size: 4
  eval_batch_size: 4
  learning_rate: 5.0e-5
  weight_decay: 0.01
  warmup_steps: 10
  warmup_ratio: 0.0
  
  # Evaluation and logging (frequent for testing)
  eval_steps: 10
  logging_steps: 5
  save_steps: 20
  evaluation_strategy: "steps"
  logging_strategy: "steps"
  save_strategy: "steps"
  
  # Early stopping (aggressive for testing)
  early_stopping_patience: 1
  early_stopping_threshold: 0.01
  load_best_model_at_end: true
  metric_for_best_model: "eval_f1"
  greater_is_better: true
  
  # Class balancing
  use_class_weights: false
  class_weights: null
  
  # WandB integration (disabled for testing)
  wandb_project: "robbert2023-test"
  wandb_entity: "${WANDB_ENTITY:-slippydongle}"
  run_name: null
  wandb_tags:
    - "test"
    - "robbert-2023"
    - "hf-trainer"
  wandb_notes: "Test run for HF Trainer integration"
  report_to: null  # Disabled for testing
  
  # Hardware optimization (conservative for testing)
  use_gpu: false  # Force CPU for consistent testing
  fp16: false
  bf16: false
  dataloader_num_workers: 0  # Avoid multiprocessing issues in tests
  dataloader_pin_memory: false
  gradient_accumulation_steps: 1
  max_grad_norm: 1.0
  
  # Checkpointing (minimal for testing)
  save_total_limit: 1
  push_to_hub: false
  hub_model_id: null
  hub_strategy: "every_save"
  
  # Learning rate scheduling
  lr_scheduler_type: "constant"
  cosine_schedule_num_cycles: 0.5
  polynomial_decay_power: 1.0
  
  # Advanced LR scheduling options
  lr_scheduler_kwargs: null
  warmup_schedule_type: "linear"
  end_learning_rate: 0.0
  
  # Enhanced early stopping options
  early_stopping_monitor_metrics:
    - "eval_f1"
  early_stopping_threshold_type: "absolute"
  early_stopping_min_delta: 0.01  # More aggressive for testing
  early_stopping_restore_best_weights: true
  
  # Advanced training options
  gradient_checkpointing: false
  remove_unused_columns: true
  label_smoothing_factor: 0.0
  optim: "adamw_torch"
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_epsilon: 1.0e-8
  
  # Versioning and experiment tracking
  run_version: "test"
  version_description: "Test configuration"
  experiment_name: "test"
  
  # Test mode settings (enabled)
  test_mode: true
  test_sample_limit: 50
  test_epochs: 1
  test_disable_wandb: true
  
  # Output and prediction settings
  output_dir: "test_checkpoints"
  save_predictions: true
  generate_model_card: false  # Skip for testing
  prediction_loss_only: false
  
  # Data processing
  max_length: 256  # Reduced for faster testing
  truncation: true
  padding: "max_length"
  
  # Reproducibility
  seed: 42
  data_seed: 42
  
  # Advanced features
  resume_from_checkpoint: null
  ignore_data_skip: false
  ddp_find_unused_parameters: false
  ddp_bucket_cap_mb: null