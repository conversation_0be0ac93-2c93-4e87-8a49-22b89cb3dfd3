#!/usr/bin/env python3
"""
Minimal test for error handling classes.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Test minimal class definition
class TestErrorHandler:
    def __init__(self):
        self.name = "test"
    
    def test_method(self):
        return "working"

print("Testing minimal class...")
handler = TestErrorHandler()
print(f"✅ Class created: {handler.test_method()}")

# Now test with imports
try:
    from src.utils.logging_utils import get_logger
    print("✅ Logging utils imported")
    
    class TestWithLogging:
        def __init__(self):
            self.logger = get_logger("test")
        
        def test_method(self):
            return "working with logging"
    
    handler_with_logging = TestWithLogging()
    print(f"✅ Class with logging: {handler_with_logging.test_method()}")
    
except Exception as e:
    print(f"❌ Error with logging: {e}")
    import traceback
    traceback.print_exc()

# Test with torch
try:
    import torch
    print("✅ Torch imported")
    
    class TestWithTorch:
        def __init__(self):
            self.cuda_available = torch.cuda.is_available()
        
        def test_method(self):
            return f"CUDA available: {self.cuda_available}"
    
    handler_with_torch = TestWithTorch()
    print(f"✅ Class with torch: {handler_with_torch.test_method()}")
    
except Exception as e:
    print(f"❌ Error with torch: {e}")
    import traceback
    traceback.print_exc()