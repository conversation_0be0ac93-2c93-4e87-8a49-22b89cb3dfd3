"""
FastAPI inference server for multi-task RobBERT-2023 model.
"""

from fastapi import FastAPI, HTTPException, Query, Request
from fastapi.responses import J<PERSON><PERSON>esponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import torch
import logging
import time
from contextlib import asynccontextmanager

from ..models.multitask_robbert import MultiTaskRobBERT
from ..config import load_config
from ..data.tokenizer_utils import RobBERTTokenizerWithAlignment
from ..exceptions import RobBERTError, ModelLoadingError, InferenceError, TokenizationError
from ..utils.logging_utils import get_logger, log_execution_time
from ..utils.compatibility_layer import adapt_response_for_compatibility, validate_request_for_compatibility


# Global model instance
model = None
config = None
api_logger = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Load model on startup and cleanup on shutdown."""
    global model, config, api_logger
    
    # Initialize logger
    api_logger = get_logger("api_fastapi", level="INFO")
    api_logger.log_system_info()
    
    try:
        # Load configuration
        api_logger.logger.info("Loading configuration...")
        config = load_config('src/config/default.yaml')
        api_logger.logger.info("Configuration loaded successfully")
        
        # Load model
        api_logger.logger.info("Loading multi-task RobBERT model...")
        api_logger.log_memory_usage("Before model loading")
        
        model = MultiTaskRobBERT.from_pretrained()
        model.eval()
        
        api_logger.log_memory_usage("After model loading")
        
        # Move to GPU if available
        if torch.cuda.is_available():
            try:
                model = model.cuda()
                api_logger.logger.info("Model loaded on GPU")
                api_logger.log_memory_usage("After GPU transfer")
            except Exception as e:
                api_logger.logger.warning(f"Failed to move model to GPU: {e}")
                api_logger.logger.info("Using CPU instead")
        else:
            api_logger.logger.info("Model loaded on CPU")
        
        api_logger.logger.info("API startup complete")
        
    except Exception as e:
        api_logger.log_error(ModelLoadingError("Failed to initialize API", model_name="RobBERT-2023"))
        raise
    
    yield
    
    # Cleanup
    api_logger.logger.info("Shutting down API...")
    model = None
    config = None
    api_logger.logger.info("API shutdown complete")


app = FastAPI(
    title="Multi-task RobBERT-2023 API",
    description="Dutch NLP inference API with RobBERT-2023 and multiple task heads",
    version="2.0.0",
    lifespan=lifespan
)


# Error handlers
@app.exception_handler(RobBERTError)
async def robbert_error_handler(request: Request, exc: RobBERTError):
    """Handle RobBERT-specific errors."""
    if api_logger:
        api_logger.log_error(exc, {"endpoint": str(request.url), "method": request.method})
    
    return JSONResponse(
        status_code=500,
        content={
            "error": type(exc).__name__,
            "message": exc.message,
            "details": exc.details
        }
    )


@app.exception_handler(ModelLoadingError)
async def model_loading_error_handler(request: Request, exc: ModelLoadingError):
    """Handle model loading errors."""
    if api_logger:
        api_logger.log_error(exc, {"endpoint": str(request.url), "method": request.method})
    
    return JSONResponse(
        status_code=503,
        content={
            "error": "ModelLoadingError",
            "message": "Model is not available",
            "details": exc.details
        }
    )


@app.exception_handler(InferenceError)
async def inference_error_handler(request: Request, exc: InferenceError):
    """Handle inference errors."""
    if api_logger:
        api_logger.log_error(exc, {"endpoint": str(request.url), "method": request.method})
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "InferenceError",
            "message": "Inference failed",
            "details": exc.details
        }
    )


@app.exception_handler(TokenizationError)
async def tokenization_error_handler(request: Request, exc: TokenizationError):
    """Handle tokenization errors."""
    if api_logger:
        api_logger.log_error(exc, {"endpoint": str(request.url), "method": request.method})
    
    return JSONResponse(
        status_code=400,
        content={
            "error": "TokenizationError",
            "message": "Text processing failed",
            "details": exc.details
        }
    )


class PredictionRequest(BaseModel):
    """Request model for predictions."""
    text: str = Field(..., description="Input text for processing")
    tasks: Optional[List[str]] = Field(
        default=None, 
        description="List of tasks to run (default: all available)"
    )


class NEREntity(BaseModel):
    """Named entity with position and confidence."""
    text: str
    label: str
    start: int
    end: int
    confidence: float


class PredictionResponse(BaseModel):
    """Response model for predictions."""
    text: str
    results: Dict[str, Any]


class AnnotatedTextResponse(BaseModel):
    """Response model for annotated text with highlighted entities."""
    original_text: str
    annotated_text: str
    entities: List[NEREntity]
    html_output: str


@app.get("/ping")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "model_loaded": model is not None,
        "available_heads": list(model.heads.keys()) if model else []
    }


@app.post("/predict", response_model=PredictionResponse)
async def predict(
    request: PredictionRequest,
    tasks: Optional[str] = Query(None, description="Comma-separated list of tasks")
):
    """Run inference on input text."""
    start_time = time.time()
    
    if model is None:
        raise ModelLoadingError("Model not loaded", model_name="RobBERT-2023")

    try:
        # Validate and adapt request for compatibility
        request_data = request.model_dump()
        validated_request = validate_request_for_compatibility(request_data)
        
        # Validate input
        if not validated_request["text"] or not validated_request["text"].strip():
            raise TokenizationError("Empty text provided")
        
        if len(validated_request["text"]) > 10000:  # Reasonable limit
            api_logger.logger.warning(f"Long text provided: {len(validated_request['text'])} characters")
        
        # Parse tasks
        active_tasks = None
        if tasks:
            active_tasks = [t.strip() for t in tasks.split(',')]
        elif request.tasks:
            active_tasks = request.tasks
        
        api_logger.logger.info(f"Prediction request - text length: {len(request.text)}, tasks: {active_tasks}")

        # Tokenize input using RobBERT tokenizer
        try:
            inputs = model.tokenizer.tokenizer(
                request.text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )
            api_logger.logger.debug(f"Tokenized input shape: {inputs['input_ids'].shape}")
            
        except Exception as e:
            raise TokenizationError(f"Failed to tokenize input text: {e}", text=request.text[:100])

        # Move to device
        device = "cuda" if torch.cuda.is_available() else "cpu"
        try:
            if torch.cuda.is_available():
                inputs = {k: v.cuda() for k, v in inputs.items()}
            api_logger.logger.debug(f"Moved inputs to device: {device}")
            
        except Exception as e:
            api_logger.logger.warning(f"Failed to move inputs to GPU, using CPU: {e}")
            device = "cpu"

        # Run inference
        try:
            api_logger.log_memory_usage("Before inference")
            with torch.no_grad():
                outputs = model(
                    input_ids=inputs['input_ids'],
                    attention_mask=inputs['attention_mask'],
                    heads=active_tasks
                )
            api_logger.log_memory_usage("After inference")
            
        except Exception as e:
            raise InferenceError(f"Model inference failed: {e}", 
                               input_shape=inputs['input_ids'].shape,
                               heads=active_tasks)

        # Process results
        results = {}
        for task_name, task_output in outputs.items():
            if task_name == 'loss':
                continue

            try:
                logits = task_output['logits']
                api_logger.logger.debug(f"Processing {task_name} output shape: {logits.shape}")

                if task_name == 'ner':
                    # Process NER results
                    predictions = torch.argmax(logits, dim=-1)
                    entities = extract_ner_entities(
                        request.text,
                        predictions[0],
                        model.tokenizer,
                        logits[0]
                    )
                    results[task_name] = entities
                    api_logger.logger.debug(f"Extracted {len(entities)} NER entities")
                else:
                    # Process classification results
                    probabilities = torch.softmax(logits, dim=-1)
                    predicted_class = torch.argmax(probabilities, dim=-1)
                    confidence = probabilities.max().item()

                    results[task_name] = {
                        'predicted_class': predicted_class.item(),
                        'confidence': confidence,
                        'probabilities': probabilities[0].tolist()
                    }
                    api_logger.logger.debug(f"{task_name} prediction: class={predicted_class.item()}, confidence={confidence:.3f}")
                    
            except Exception as e:
                api_logger.logger.error(f"Failed to process {task_name} output: {e}")
                # Continue with other tasks instead of failing completely
                continue

        # Log timing
        inference_time = time.time() - start_time
        api_logger.logger.info(f"Prediction completed in {inference_time:.3f}s")

        # Apply compatibility layer to ensure backward compatibility
        response_data = {"text": request.text, "results": results}
        compatible_response = adapt_response_for_compatibility(response_data)

        return PredictionResponse(**compatible_response)

    except (TokenizationError, InferenceError, ModelLoadingError):
        raise
    except Exception as e:
        api_logger.log_error(InferenceError(f"Unexpected prediction error: {e}"))
        raise InferenceError(f"Unexpected prediction error: {e}") from e


def extract_ner_entities(text: str, predictions: torch.Tensor,
                        tokenizer: RobBERTTokenizerWithAlignment, logits: torch.Tensor) -> List[NEREntity]:
    """Extract named entities from RobBERT NER predictions."""

    # RobBERT-2023 3-label mapping (O, B-PER, I-PER)
    id2label = {0: "O", 1: "B-PER", 2: "I-PER"}

    try:
        # Use the tokenizer's alignment functionality
        encoding = tokenizer.tokenizer(
            text, 
            return_tensors="pt", 
            add_special_tokens=True,
            return_offsets_mapping=True
        )
        
        input_ids = encoding["input_ids"].squeeze()
        offset_mapping = encoding["offset_mapping"].squeeze()
        tokens = tokenizer.tokenizer.convert_ids_to_tokens(input_ids)
        
        # Ensure predictions match token length
        predictions = predictions[:len(tokens)]
        logits = logits[:len(tokens)]
        
        # Convert predictions to labels
        pred_labels = [id2label.get(pred_id.item(), "O") for pred_id in predictions]
        
        # Extract entities using BIO tagging
        entities = []
        current_entity = None
        
        for i, (token, (start_offset, end_offset), label) in enumerate(zip(tokens, offset_mapping, pred_labels)):
            # Skip special tokens (they have offset (0, 0))
            if start_offset == end_offset:
                continue
                
            if label.startswith("B-"):
                # Start of new entity
                if current_entity:
                    entities.append(current_entity)
                
                current_entity = {
                    "label": label[2:],  # Remove B- prefix
                    "start_offset": start_offset.item(),
                    "end_offset": end_offset.item(),
                    "token_indices": [i],
                    "tokens": [token]
                }
                
            elif label.startswith("I-") and current_entity and current_entity["label"] == label[2:]:
                # Continuation of current entity
                current_entity["end_offset"] = end_offset.item()
                current_entity["token_indices"].append(i)
                current_entity["tokens"].append(token)
                
            else:
                # End of entity or O label
                if current_entity:
                    entities.append(current_entity)
                    current_entity = None
        
        # Add final entity if exists
        if current_entity:
            entities.append(current_entity)
        
        # Convert to NEREntity objects
        result_entities = []
        for entity_data in entities:
            start_char = entity_data["start_offset"]
            end_char = entity_data["end_offset"]
            
            # Extract entity text from original text
            entity_text = text[start_char:end_char].strip()
            
            # Skip empty entities
            if not entity_text:
                continue
            
            # Calculate confidence from logits
            token_indices = entity_data["token_indices"]
            if token_indices and all(idx < len(logits) for idx in token_indices):
                entity_logits = logits[token_indices]
                confidences = torch.softmax(entity_logits, dim=-1).max(dim=-1)[0]
                confidence = confidences.mean().item()
            else:
                confidence = 0.5  # Default confidence
            
            # Ensure confidence is in valid range
            confidence = max(0.0, min(1.0, confidence))
            
            result_entities.append(NEREntity(
                text=entity_text,
                label=entity_data["label"],
                start=start_char,
                end=end_char,
                confidence=confidence
            ))
        
        return result_entities
        
    except Exception as e:
        # Log error and return empty list to maintain API stability
        if api_logger:
            api_logger.logger.error(f"Entity extraction failed: {e}")
        return []


@app.post("/annotate", response_model=AnnotatedTextResponse)
async def annotate_text(request: PredictionRequest):
    """Run NER inference and return annotated text with highlighted entities."""
    if model is None:
        raise HTTPException(status_code=503, detail="Model not loaded")

    try:
        # Tokenize input using RobBERT tokenizer
        inputs = model.tokenizer.tokenizer(
            request.text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        )

        # Move to device
        if torch.cuda.is_available():
            inputs = {k: v.cuda() for k, v in inputs.items()}

        # Run NER inference only
        with torch.no_grad():
            outputs = model(
                input_ids=inputs['input_ids'],
                attention_mask=inputs['attention_mask'],
                heads=['ner']
            )

        # Extract entities
        logits = outputs['ner']['logits']
        predictions = torch.argmax(logits, dim=-1)
        entities = extract_ner_entities(
            request.text,
            predictions[0],
            model.tokenizer,
            logits[0]
        )

        # Create annotated text
        annotated_text = create_annotated_text(request.text, entities)
        html_output = create_html_highlighted_text(request.text, entities)

        return AnnotatedTextResponse(
            original_text=request.text,
            annotated_text=annotated_text,
            entities=entities,
            html_output=html_output
        )

    except Exception as e:
        logging.error(f"Annotation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Annotation failed: {str(e)}")


def create_annotated_text(text: str, entities: List[NEREntity]) -> str:
    """Create annotated text with entity labels in brackets."""
    if not entities:
        return text
    
    # Sort entities by start position (reverse order for replacement)
    sorted_entities = sorted(entities, key=lambda x: x.start, reverse=True)
    
    annotated = text
    for entity in sorted_entities:
        # Replace entity text with annotated version
        before = annotated[:entity.start]
        after = annotated[entity.end:]
        entity_text = annotated[entity.start:entity.end]
        
        # Create annotation
        annotation = f"[{entity_text}]({entity.label})"
        annotated = before + annotation + after
    
    return annotated


def create_html_highlighted_text(text: str, entities: List[NEREntity]) -> str:
    """Create HTML with highlighted entities."""
    if not entities:
        return f"<p>{text}</p>"
    
    # Sort entities by start position (reverse order for replacement)
    sorted_entities = sorted(entities, key=lambda x: x.start, reverse=True)
    
    html_text = text
    for entity in sorted_entities:
        # Replace entity text with highlighted HTML
        before = html_text[:entity.start]
        after = html_text[entity.end:]
        entity_text = html_text[entity.start:entity.end]
        
        # Create HTML highlight with confidence info
        confidence_percent = int(entity.confidence * 100)
        highlight = (
            f'<span class="entity entity-{entity.label.lower()}" '
            f'title="{entity.label} (confidence: {confidence_percent}%)">'
            f'{entity_text}</span>'
        )
        html_text = before + highlight + after
    
    # Wrap in paragraph with CSS
    css_styles = """
    <style>
        .entity {
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
            cursor: help;
        }
        .entity-per {
            background-color: #ffeb3b;
            color: #333;
        }
    </style>
    """
    
    return f"{css_styles}<p>{html_text}</p>"


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)