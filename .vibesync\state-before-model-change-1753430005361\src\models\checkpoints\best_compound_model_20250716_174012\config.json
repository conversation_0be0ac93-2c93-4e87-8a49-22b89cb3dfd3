{"_name_or_path": "src/models/weights/bertje_conll", "_num_labels": 9, "architectures": ["BertForTokenClassification"], "attention_probs_dropout_prob": 0.2, "classifier_dropout": null, "hidden_act": "gelu", "hidden_dropout_prob": 0.3, "hidden_size": 768, "id2label": {"0": "B-loc", "1": "B-misc", "2": "B-org", "3": "B-per", "4": "I-loc", "5": "I-misc", "6": "I-org", "7": "I-per", "8": "O"}, "initializer_range": 0.02, "intermediate_size": 3072, "label2id": {"B-loc": 0, "B-misc": 1, "B-org": 2, "B-per": 3, "I-loc": 4, "I-misc": 5, "I-org": 6, "I-per": 7, "O": 8}, "layer_norm_eps": 1e-12, "max_position_embeddings": 512, "model_type": "bert", "num_attention_heads": 12, "num_hidden_layers": 12, "output_past": true, "pad_token_id": 0, "position_embedding_type": "absolute", "transformers_version": "4.41.2", "type_vocab_size": 2, "use_cache": true, "vocab_size": 30000}