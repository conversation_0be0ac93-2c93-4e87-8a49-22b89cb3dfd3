"""
Unit tests for character validator functionality.
"""

import pytest
from unittest.mock import <PERSON><PERSON>, patch, MagicMock
from transformers import AutoTokenizer

from src.validation.character_validator import CharacterValidator, ValidationResult
from src.exceptions import ModelLoadingError


class TestCharacterValidator:
    """Test cases for CharacterValidator class."""
    
    @pytest.fixture
    def mock_tokenizer(self):
        """Create a mock tokenizer for testing."""
        tokenizer = Mock()
        tokenizer.__len__ = Mock(return_value=50000)
        return tokenizer
    
    @pytest.fixture
    def validator(self, mock_tokenizer):
        """Create a CharacterValidator instance with mocked tokenizer."""
        with patch('src.validation.character_validator.AutoTokenizer.from_pretrained') as mock_from_pretrained:
            mock_from_pretrained.return_value = mock_tokenizer
            validator = CharacterValidator()
            return validator
    
    def test_init_success(self, mock_tokenizer):
        """Test successful initialization of CharacterValidator."""
        with patch('src.validation.character_validator.AutoTokenizer.from_pretrained') as mock_from_pretrained:
            mock_from_pretrained.return_value = mock_tokenizer
            
            validator = CharacterValidator("test-model")
            
            assert validator.model_name == "test-model"
            assert validator.tokenizer == mock_tokenizer
            mock_from_pretrained.assert_called_once_with("test-model")
    
    def test_init_failure(self):
        """Test initialization failure when tokenizer loading fails."""
        with patch('src.validation.character_validator.AutoTokenizer.from_pretrained') as mock_from_pretrained:
            mock_from_pretrained.side_effect = Exception("Failed to load")
            
            with pytest.raises(ModelLoadingError):
                CharacterValidator("invalid-model")
    
    def test_get_all_required_chars(self, validator):
        """Test getting all required characters as a set."""
        all_chars = validator.get_all_required_chars()
        
        # Should contain characters from all categories
        assert 'a' in all_chars  # latin_base
        assert 'Z' in all_chars  # latin_base
        assert 'ë' in all_chars  # latin_diacritics
        assert 'ô' in all_chars  # latin_diacritics
        assert '!' in all_chars  # special_chars
        assert '€' in all_chars  # special_chars
        
        # Should be a set (no duplicates)
        assert isinstance(all_chars, set)
        
        # Should have reasonable size
        assert len(all_chars) > 50  # At least 26*2 + diacritics + special chars
    
    def test_character_properly_tokenized_single_token(self, validator):
        """Test character properly tokenized when it produces a single token."""
        tokens = ['a']
        assert validator._is_character_properly_tokenized('a', tokens) == True
        
        tokens = ['ë']
        assert validator._is_character_properly_tokenized('ë', tokens) == True
    
    def test_character_properly_tokenized_with_bpe_markers(self, validator):
        """Test character properly tokenized with BPE markers."""
        tokens = ['▁a']  # RoBERTa-style BPE marker
        assert validator._is_character_properly_tokenized('a', tokens) == True
        
        tokens = ['Ġa']  # GPT-style BPE marker
        assert validator._is_character_properly_tokenized('a', tokens) == True
    
    def test_character_properly_tokenized_empty_tokens(self, validator):
        """Test character not properly tokenized when no tokens produced."""
        tokens = []
        assert validator._is_character_properly_tokenized('a', tokens) == False
    
    def test_character_properly_tokenized_missing_char(self, validator):
        """Test character not properly tokenized when char missing from tokens."""
        tokens = ['b', 'c']
        assert validator._is_character_properly_tokenized('a', tokens) == False
    
    def test_character_in_tokens(self, validator):
        """Test character detection in token list."""
        tokens = ['test', 'a', 'word']
        assert validator._character_in_tokens('a', tokens) == True
        
        tokens = ['test', 'word']
        assert validator._character_in_tokens('a', tokens) == False
        
        # Test with BPE markers
        tokens = ['▁test', '▁a▁word']
        assert validator._character_in_tokens('a', tokens) == True
    
    def test_test_character_set_all_supported(self, validator):
        """Test character set validation when all characters are supported."""
        # Mock tokenizer to support all characters
        validator.tokenizer.tokenize = Mock(side_effect=lambda x: [x])
        
        char_set = "abc"
        result = validator._test_character_set(validator.tokenizer, char_set, "test")
        
        assert result['supported'] == {'a', 'b', 'c'}
        assert result['unsupported'] == set()
        assert result['total'] == 3
        assert result['coverage_percentage'] == 100.0
        assert len(result['issues']) == 0
    
    def test_test_character_set_some_unsupported(self, validator):
        """Test character set validation with some unsupported characters."""
        # Mock tokenizer to fail on 'b'
        def mock_tokenize(char):
            if char == 'b':
                return []  # Empty tokens indicate failure
            return [char]
        
        validator.tokenizer.tokenize = Mock(side_effect=mock_tokenize)
        
        char_set = "abc"
        result = validator._test_character_set(validator.tokenizer, char_set, "test")
        
        assert result['supported'] == {'a', 'c'}
        assert result['unsupported'] == {'b'}
        assert result['total'] == 3
        assert result['coverage_percentage'] == pytest.approx(66.67, rel=1e-2)
        assert len(result['issues']) == 1
        assert result['issues'][0]['char'] == 'b'
    
    def test_test_character_set_tokenization_error(self, validator):
        """Test character set validation with tokenization errors."""
        # Mock tokenizer to raise exception on 'b'
        def mock_tokenize(char):
            if char == 'b':
                raise Exception("Tokenization failed")
            return [char]
        
        validator.tokenizer.tokenize = Mock(side_effect=mock_tokenize)
        
        char_set = "abc"
        result = validator._test_character_set(validator.tokenizer, char_set, "test")
        
        assert result['supported'] == {'a', 'c'}
        assert result['unsupported'] == {'b'}
        assert len(result['issues']) == 1
        assert result['issues'][0]['char'] == 'b'
        assert result['issues'][0]['issue'] == 'tokenization_error'
    
    def test_validate_tokenizer_coverage_success(self, validator):
        """Test successful tokenizer coverage validation."""
        # Mock successful tokenization for all characters
        validator.tokenizer.tokenize = Mock(side_effect=lambda x: [x])
        
        result = validator.validate_tokenizer_coverage()
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid == True
        assert result.coverage_percentage == 100.0
        assert len(result.unsupported_chars) == 0
        assert result.error_message is None
        
        # Should test all character categories
        assert 'latin_base' in result.test_results
        assert 'latin_diacritics' in result.test_results
        assert 'special_chars' in result.test_results
    
    def test_validate_tokenizer_coverage_partial_failure(self, validator):
        """Test tokenizer coverage validation with some failures."""
        # Mock tokenizer to fail on diacritics
        def mock_tokenize(char):
            if char in 'ëïéèöêüçàûîñäô':
                return []  # Fail on diacritics
            return [char]
        
        validator.tokenizer.tokenize = Mock(side_effect=mock_tokenize)
        
        result = validator.validate_tokenizer_coverage()
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid == False  # Should fail due to unsupported chars
        assert result.coverage_percentage < 100.0
        assert len(result.unsupported_chars) > 0
        assert result.error_message is None
        
        # Diacritics should be unsupported
        diacritics = set('ëïéèöêüçàûîñäô')
        assert diacritics.issubset(result.unsupported_chars)
    
    def test_validate_tokenizer_coverage_no_tokenizer(self, validator):
        """Test tokenizer coverage validation with no tokenizer."""
        validator.tokenizer = None
        
        result = validator.validate_tokenizer_coverage()
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid == False
        assert result.coverage_percentage == 0.0
        assert len(result.supported_chars) == 0
        assert result.error_message == "No tokenizer available for validation"
    
    def test_validate_tokenizer_coverage_exception(self, validator):
        """Test tokenizer coverage validation with exception."""
        validator.tokenizer.tokenize = Mock(side_effect=Exception("Tokenizer error"))
        
        result = validator.validate_tokenizer_coverage()
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid == False
        assert result.coverage_percentage == 0.0
        assert result.error_message is not None
        assert "Error during character validation" in result.error_message
    
    def test_test_character_encoding_success(self, validator):
        """Test successful character encoding."""
        test_text = "test ëxample"
        
        # Mock successful encoding/decoding
        validator.tokenizer.encode = Mock(return_value=[1, 2, 3, 4, 5])
        validator.tokenizer.decode = Mock(return_value=test_text)
        
        result = validator.test_character_encoding(test_text)
        
        assert result == True
        validator.tokenizer.encode.assert_called_once_with(test_text, add_special_tokens=False)
        validator.tokenizer.decode.assert_called_once_with([1, 2, 3, 4, 5], skip_special_tokens=True)
    
    def test_test_character_encoding_mismatch(self, validator):
        """Test character encoding with decode mismatch."""
        test_text = "test ëxample"
        
        # Mock encoding/decoding with mismatch
        validator.tokenizer.encode = Mock(return_value=[1, 2, 3, 4, 5])
        validator.tokenizer.decode = Mock(return_value="test example")  # Missing diacritic
        
        result = validator.test_character_encoding(test_text)
        
        assert result == False
    
    def test_test_character_encoding_no_tokenizer(self, validator):
        """Test character encoding with no tokenizer."""
        validator.tokenizer = None
        
        result = validator.test_character_encoding("test")
        
        assert result == False
    
    def test_test_character_encoding_exception(self, validator):
        """Test character encoding with exception."""
        validator.tokenizer.encode = Mock(side_effect=Exception("Encoding failed"))
        
        result = validator.test_character_encoding("test")
        
        assert result == False
    
    def test_generate_character_test_cases(self, validator):
        """Test generation of character test cases."""
        test_cases = validator.generate_character_test_cases()
        
        assert isinstance(test_cases, list)
        assert len(test_cases) > 0
        
        # Should include individual characters
        assert 'a' in test_cases
        assert 'ë' in test_cases
        assert '!' in test_cases
        assert '€' in test_cases
        
        # Should include Dutch-specific cases
        assert any('café' in case for case in test_cases)
        assert any('naïef' in case for case in test_cases)
        
        # Should include mixed character cases
        assert any('@' in case and '.' in case for case in test_cases)  # Email-like
        assert any('€' in case for case in test_cases)  # Currency
        
        # Should include context cases
        assert any(len(case.split()) > 2 for case in test_cases)  # Multi-word sentences
    
    def test_run_comprehensive_validation_success(self, validator):
        """Test comprehensive validation with all tests passing."""
        # Mock successful tokenization and encoding
        validator.tokenizer.tokenize = Mock(side_effect=lambda x: [x])
        validator.tokenizer.encode = Mock(return_value=[1, 2, 3])
        
        # Mock decode to return the original text (simulate successful encoding/decoding)
        def mock_decode(token_ids, **kwargs):
            # For the test, we'll simulate that decode returns the original text
            # In reality, this would be more complex, but for testing we just need consistency
            return validator._current_test_text if hasattr(validator, '_current_test_text') else "test"
        
        validator.tokenizer.decode = Mock(side_effect=mock_decode)
        
        # Override test_character_encoding to always return True for this test
        original_test_encoding = validator.test_character_encoding
        validator.test_character_encoding = Mock(return_value=True)
        
        result = validator.run_comprehensive_validation()
        
        assert isinstance(result, dict)
        assert 'tokenizer_coverage' in result
        assert 'encoding_tests' in result
        assert 'overall_valid' in result
        assert 'summary' in result
        
        # Should pass all tests
        assert result['tokenizer_coverage'].is_valid == True
        assert result['encoding_tests']['success_rate'] == 100.0
        assert result['overall_valid'] == True
    
    def test_run_comprehensive_validation_partial_failure(self, validator):
        """Test comprehensive validation with some failures."""
        # Mock partial tokenization success and encoding failures
        def mock_tokenize(char):
            if char in 'ëï':  # Fail on some diacritics
                return []
            return [char]
        
        def mock_encode_decode(text):
            if 'ë' in text or 'ï' in text:
                return False  # Fail encoding for texts with problematic chars
            return True
        
        validator.tokenizer.tokenize = Mock(side_effect=mock_tokenize)
        
        # Mock encoding test to fail on some cases
        original_test_encoding = validator.test_character_encoding
        validator.test_character_encoding = Mock(side_effect=lambda x: not any(c in x for c in 'ëï'))
        
        result = validator.run_comprehensive_validation()
        
        assert isinstance(result, dict)
        assert result['tokenizer_coverage'].is_valid == False  # Some chars unsupported
        assert result['encoding_tests']['success_rate'] < 100.0  # Some encoding failures
        assert result['overall_valid'] == False  # Overall failure
        
        # Should have unsupported characters
        assert len(result['summary']['unsupported_characters']) > 0
        assert 'ë' in result['summary']['unsupported_characters']
        assert 'ï' in result['summary']['unsupported_characters']


class TestCharacterValidatorIntegration:
    """Integration tests with real tokenizer."""
    
    @pytest.mark.slow
    def test_real_tokenizer_validation(self):
        """Test character validation with real RobBERT tokenizer."""
        try:
            validator = CharacterValidator()
            result = validator.validate_tokenizer_coverage()
            
            # Real RobBERT should support most Dutch characters
            assert isinstance(result, ValidationResult)
            assert result.coverage_percentage > 90.0  # Should support most characters
            
            # Test some specific Dutch characters
            dutch_chars = ['ë', 'ï', 'é', 'è', 'ö', 'ê', 'ü', 'ç']
            for char in dutch_chars:
                encoding_result = validator.test_character_encoding(char)
                # Most should work, but we don't require 100% for this test
                
        except Exception as e:
            pytest.skip(f"Real tokenizer test skipped due to: {e}")
    
    @pytest.mark.slow
    def test_comprehensive_validation_real_tokenizer(self):
        """Test comprehensive validation with real tokenizer."""
        try:
            validator = CharacterValidator()
            result = validator.run_comprehensive_validation()
            
            assert isinstance(result, dict)
            assert 'tokenizer_coverage' in result
            assert 'encoding_tests' in result
            assert 'summary' in result
            
            # Should have reasonable performance
            assert result['summary']['character_coverage'] > 80.0
            assert result['summary']['encoding_success_rate'] > 80.0
            
        except Exception as e:
            pytest.skip(f"Comprehensive validation test skipped due to: {e}")


class TestValidationResult:
    """Test ValidationResult dataclass."""
    
    def test_validation_result_creation(self):
        """Test creating ValidationResult instance."""
        result = ValidationResult(
            is_valid=True,
            supported_chars={'a', 'b', 'c'},
            unsupported_chars=set(),
            coverage_percentage=100.0,
            test_results={'test': 'data'}
        )
        
        assert result.is_valid == True
        assert result.supported_chars == {'a', 'b', 'c'}
        assert result.unsupported_chars == set()
        assert result.coverage_percentage == 100.0
        assert result.test_results == {'test': 'data'}
        assert result.error_message is None
    
    def test_validation_result_with_error(self):
        """Test ValidationResult with error message."""
        result = ValidationResult(
            is_valid=False,
            supported_chars=set(),
            unsupported_chars={'x', 'y'},
            coverage_percentage=0.0,
            test_results={},
            error_message="Test error"
        )
        
        assert result.is_valid == False
        assert result.error_message == "Test error"
        assert result.unsupported_chars == {'x', 'y'}