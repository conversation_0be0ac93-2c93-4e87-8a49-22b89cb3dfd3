transformers>=4.38.0,<4.42
tokenizers>=0.15.0,<0.20.0
torch>=2.0.0,<2.6.0
datasets>=2.20.0,<2.21.0
accelerate>=0.29.0,<0.30.0
evaluate>=0.4.2,<0.5.0
optimum>=1.20.0,<1.21.0
sentencepiece>=0.1.99,<0.2.0
pydantic>=2.7,<3.0
fastapi>=0.111,<0.112
uvicorn[standard]>=0.30,<0.31
python-dotenv>=1.0,<2.0
pytest>=8.2,<9.0
huggingface-hub>=0.23.0,<0.24.0
pyyaml>=6.0,<7.0
scikit-learn>=1.3.0,<1.4.0
seqeval>=1.2.2,<2.0.0
wandb>=0.16.0,<0.17.0
typer[all]>=0.9.0,<0.13.0
rich>=13.0.0,<14.0.0
