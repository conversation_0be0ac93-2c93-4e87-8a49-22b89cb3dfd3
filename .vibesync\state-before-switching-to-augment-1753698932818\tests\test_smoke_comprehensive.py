"""
Smoke test for comprehensive HF integration test suite.

This module provides a quick smoke test to verify that the comprehensive
test suite is properly configured and can run without major issues.
"""

import pytest
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


class TestSmokeTest:
    """Basic smoke tests to verify test suite functionality."""
    
    def test_imports_work(self):
        """Test that all required imports work."""
        try:
            # Test core imports
            from src.training.hf_config import HFTrainingConfig, create_test_hf_config
            from src.training.hf_trainer import HFNERTrainer
            from src.data.hf_dataset_preparation import HFDatasetPreparator
            
            # Test that classes can be instantiated
            config = create_test_hf_config()
            assert config is not None
            assert config.test_mode == True
            
            trainer = HFNERTrainer(config)
            assert trainer is not None
            
            print("✅ All imports successful")
            
        except ImportError as e:
            pytest.fail(f"Import failed: {e}")
    
    def test_test_mode_configuration(self):
        """Test that test mode configuration works."""
        from src.training.hf_config import create_test_hf_config
        
        config = create_test_hf_config()
        
        # Verify test mode settings
        assert config.test_mode == True
        assert config.epochs == 1
        assert config.test_sample_limit == 50
        assert config.test_disable_wandb == True
        
        print("✅ Test mode configuration works")
    
    def test_mock_training_pipeline(self):
        """Test that mock training pipeline works."""
        from unittest.mock import Mock, patch
        from src.training.hf_trainer import train_ner_model
        from src.training.hf_config import create_test_hf_config
        
        with patch('src.training.hf_trainer.HFNERTrainer') as mock_trainer_class:
            # Mock trainer
            mock_trainer = Mock()
            mock_trainer.train.return_value = {
                "eval_f1": 0.85,
                "eval_precision": 0.87,
                "eval_recall": 0.83
            }
            mock_trainer_class.return_value = mock_trainer
            
            config = create_test_hf_config()
            
            # This should work without actual data
            result = train_ner_model(
                data_path="dummy_path.json",  # Won't be used due to mocking
                config=config
            )
            
            assert "eval_f1" in result
            assert result["eval_f1"] == 0.85
            
            print("✅ Mock training pipeline works")
    
    def test_pytest_markers_configured(self):
        """Test that pytest markers are properly configured."""
        import pytest
        
        # These should not raise errors if markers are configured
        pytest.mark.unit
        pytest.mark.integration
        pytest.mark.slow
        
        print("✅ Pytest markers configured")
    
    def test_test_data_creation(self):
        """Test that test data can be created."""
        import json
        import tempfile
        from pathlib import Path
        
        # Create sample test data
        test_data = [
            {
                "id": 1,
                "sentence": "Jan Jansen woont in Amsterdam.",
                "entities": [
                    {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10}
                ]
            }
        ]
        
        # Write to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_path = f.name
        
        try:
            # Verify file was created and can be read
            with open(temp_path, 'r') as f:
                loaded_data = json.load(f)
            
            assert len(loaded_data) == 1
            assert loaded_data[0]["sentence"] == "Jan Jansen woont in Amsterdam."
            
            print("✅ Test data creation works")
            
        finally:
            Path(temp_path).unlink()


@pytest.mark.unit
class TestUnitSmokeTest:
    """Unit test smoke tests."""
    
    def test_unit_marker_works(self):
        """Test that unit marker works."""
        assert True
        print("✅ Unit marker works")


@pytest.mark.integration
class TestIntegrationSmokeTest:
    """Integration test smoke tests."""
    
    def test_integration_marker_works(self):
        """Test that integration marker works."""
        assert True
        print("✅ Integration marker works")


@pytest.mark.slow
class TestSlowSmokeTest:
    """Slow test smoke tests."""
    
    def test_slow_marker_works(self):
        """Test that slow marker works."""
        assert True
        print("✅ Slow marker works")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])