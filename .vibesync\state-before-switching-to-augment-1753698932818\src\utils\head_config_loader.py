#!/usr/bin/env python3
"""
Head-specific configuration loader for RobBERT-2023 multi-task training.
"""

import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
from ..config import substitute_env_vars

logger = logging.getLogger(__name__)


class HeadConfigLoader:
    """
    Loader for head-specific configuration files.
    
    Supports loading individual head configurations and merging them
    for multi-task training scenarios.
    """
    
    def __init__(self, config_dir: str = "src/config/heads"):
        """
        Initialize head config loader.
        
        Args:
            config_dir: Directory containing head configuration files
        """
        self.config_dir = Path(config_dir)
        self._validate_config_dir()
    
    def _validate_config_dir(self):
        """Validate that config directory exists."""
        if not self.config_dir.exists():
            raise FileNotFoundError(f"Head config directory not found: {self.config_dir}")
        
        if not self.config_dir.is_dir():
            raise NotADirectoryError(f"Head config path is not a directory: {self.config_dir}")
    
    def load_head_config(self, head_name: str) -> Dict[str, Any]:
        """
        Load configuration for a specific head.
        
        Args:
            head_name: Name of the head (e.g., 'ner', 'compliance')
            
        Returns:
            Head configuration dictionary
            
        Raises:
            FileNotFoundError: If head config file doesn't exist
            yaml.YAMLError: If YAML parsing fails
        """
        config_file = self.config_dir / f"{head_name}.yaml"
        
        if not config_file.exists():
            raise FileNotFoundError(f"Head config file not found: {config_file}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                raw_config = yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"Failed to parse head config {config_file}: {e}")
        
        # Substitute environment variables
        processed_config = substitute_env_vars(raw_config)
        
        # Extract head_config section
        head_config = processed_config.get('head_config', {})
        
        # Validate required fields
        self._validate_head_config(head_config, head_name)
        
        return head_config
    
    def _validate_head_config(self, config: Dict[str, Any], head_name: str):
        """
        Validate head configuration structure.
        
        Args:
            config: Head configuration dictionary
            head_name: Name of the head for error messages
        """
        required_sections = ['name', 'type', 'model', 'training']
        
        for section in required_sections:
            if section not in config:
                raise ValueError(f"Missing required section '{section}' in {head_name} config")
        
        # Validate head name matches file
        if config['name'] != head_name:
            logger.warning(f"Head name mismatch: file={head_name}, config={config['name']}")
        
        # Validate head type
        valid_types = ['token_classification', 'sequence_classification']
        if config['type'] not in valid_types:
            raise ValueError(f"Invalid head type '{config['type']}' for {head_name}. "
                           f"Must be one of: {valid_types}")
    
    def load_multiple_heads(self, head_names: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Load configurations for multiple heads.
        
        Args:
            head_names: List of head names to load
            
        Returns:
            Dictionary mapping head names to their configurations
        """
        configs = {}
        
        for head_name in head_names:
            try:
                configs[head_name] = self.load_head_config(head_name)
                logger.info(f"Loaded config for head: {head_name}")
            except Exception as e:
                logger.error(f"Failed to load config for head {head_name}: {e}")
                raise
        
        return configs
    
    def get_available_heads(self) -> List[str]:
        """
        Get list of available head configurations.
        
        Returns:
            List of available head names
        """
        head_files = list(self.config_dir.glob("*.yaml"))
        return [f.stem for f in head_files]
    
    def merge_training_configs(self, head_configs: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Merge training configurations from multiple heads for multi-task training.
        
        Args:
            head_configs: Dictionary of head configurations
            
        Returns:
            Merged training configuration
        """
        merged_config = {
            'heads': {},
            'global_training': {},
            'wandb': {
                'projects': {},
                'metrics': []
            }
        }
        
        # Extract head-specific configs
        for head_name, config in head_configs.items():
            merged_config['heads'][head_name] = {
                'model': config.get('model', {}),
                'training': config.get('training', {}),
                'evaluation': config.get('evaluation', {}),
                'inference': config.get('inference', {})
            }
            
            # Collect WandB configs
            if 'wandb' in config:
                wandb_config = config['wandb']
                merged_config['wandb']['projects'][head_name] = {
                    'project_suffix': wandb_config.get('project_suffix', f'-{head_name}'),
                    'tags': wandb_config.get('tags', []),
                    'notes': wandb_config.get('notes', '')
                }
                
                # Collect metrics
                track_metrics = wandb_config.get('track_metrics', [])
                merged_config['wandb']['metrics'].extend(track_metrics)
        
        # Calculate global training parameters (averages/medians)
        self._calculate_global_training_params(merged_config, head_configs)
        
        return merged_config
    
    def _calculate_global_training_params(self, merged_config: Dict[str, Any], 
                                        head_configs: Dict[str, Dict[str, Any]]):
        """
        Calculate global training parameters from individual head configs.
        
        Args:
            merged_config: Merged configuration to update
            head_configs: Individual head configurations
        """
        # Collect training parameters with type conversion
        epochs = []
        batch_sizes = []
        learning_rates = []
        weight_decays = []
        
        for config in head_configs.values():
            training = config.get('training', {})
            
            # Convert to appropriate types with error handling
            try:
                epochs.append(int(training.get('epochs', 3)))
            except (ValueError, TypeError):
                epochs.append(3)
                
            try:
                batch_sizes.append(int(training.get('batch_size', 8)))
            except (ValueError, TypeError):
                batch_sizes.append(8)
                
            try:
                lr_value = training.get('learning_rate', 2e-5)
                # Handle both string and numeric learning rates
                if isinstance(lr_value, str):
                    learning_rates.append(float(lr_value))
                else:
                    learning_rates.append(float(lr_value))
            except (ValueError, TypeError):
                learning_rates.append(2e-5)
                
            try:
                wd_value = training.get('weight_decay', 0.01)
                if isinstance(wd_value, str):
                    weight_decays.append(float(wd_value))
                else:
                    weight_decays.append(float(wd_value))
            except (ValueError, TypeError):
                weight_decays.append(0.01)
        
        # Use median for most parameters, max for epochs
        # Ensure all lists have at least one element
        epochs = epochs if epochs else [3]
        batch_sizes = batch_sizes if batch_sizes else [8]
        learning_rates = learning_rates if learning_rates else [2e-5]
        weight_decays = weight_decays if weight_decays else [0.01]
        
        merged_config['global_training'] = {
            'epochs': max(epochs),
            'batch_size': int(sorted(batch_sizes)[len(batch_sizes)//2]),
            'learning_rate': sorted(learning_rates)[len(learning_rates)//2],
            'weight_decay': sorted(weight_decays)[len(weight_decays)//2]
        }
    
    def get_head_wandb_config(self, head_name: str) -> Optional[Dict[str, Any]]:
        """
        Get WandB configuration for a specific head.
        
        Args:
            head_name: Name of the head
            
        Returns:
            WandB configuration dictionary or None if not found
        """
        try:
            head_config = self.load_head_config(head_name)
            return head_config.get('wandb')
        except Exception as e:
            logger.error(f"Failed to get WandB config for head {head_name}: {e}")
            return None


def load_head_configs(head_names: List[str], 
                     config_dir: str = "src/config/heads") -> Dict[str, Dict[str, Any]]:
    """
    Convenience function to load multiple head configurations.
    
    Args:
        head_names: List of head names to load
        config_dir: Directory containing head configuration files
        
    Returns:
        Dictionary mapping head names to their configurations
    """
    loader = HeadConfigLoader(config_dir)
    return loader.load_multiple_heads(head_names)


def get_available_heads(config_dir: str = "src/config/heads") -> List[str]:
    """
    Convenience function to get available head configurations.
    
    Args:
        config_dir: Directory containing head configuration files
        
    Returns:
        List of available head names
    """
    loader = HeadConfigLoader(config_dir)
    return loader.get_available_heads()