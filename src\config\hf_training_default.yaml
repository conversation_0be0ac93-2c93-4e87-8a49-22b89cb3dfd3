# Hugging Face Training Configuration Template
# This file provides default configuration for HF Trainer-based training

hf_training:
  # Model configuration
  model_name: "${ROBBERT_MODEL_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}"
  
  # Training parameters
  epochs: 3
  batch_size: 8
  eval_batch_size: 8
  learning_rate: 5.0e-5
  weight_decay: 0.01
  warmup_steps: 500
  warmup_ratio: 0.0
  
  # Evaluation and logging
  eval_steps: 100
  logging_steps: 50
  save_steps: 500
  evaluation_strategy: "steps"  # "no", "steps", "epoch"
  logging_strategy: "steps"
  save_strategy: "steps"
  
  # Early stopping
  early_stopping_patience: 3
  early_stopping_threshold: 0.001
  load_best_model_at_end: true
  metric_for_best_model: "eval_f1"
  greater_is_better: true
  
  # Class balancing (optional)
  use_class_weights: false
  class_weights: null
    # Example:
    # "O": 0.5
    # "B-PER": 2.0
    # "I-PER": 2.0
  
  # WandB integration
  wandb_project: "${WANDB_PROJECT:-robbert2023-ner}"
  wandb_entity: "${WANDB_ENTITY:-slippydongle}"
  run_name: null  # Auto-generated if null
  wandb_tags:
    - "robbert-2023"
    - "dutch-nlp"
    - "ner"
    - "hf-trainer"
  wandb_notes: "Hugging Face Trainer integration for RobBERT-2023 NER"
  report_to: "wandb"
  
  # Enhanced WandB logging options
  wandb_log_confusion_matrix: true
  wandb_log_per_class_metrics: true
  wandb_log_model_artifacts: true
  wandb_log_evaluation_tables: true
  wandb_confusion_matrix_frequency: "epoch"  # "epoch" or "step"
  wandb_confusion_matrix_steps: 500
  
  # Hardware optimization
  use_gpu: true
  fp16: true  # Mixed precision training (GPU only)
  bf16: false  # Brain float 16 (newer GPUs)
  dataloader_num_workers: 2
  dataloader_pin_memory: true
  gradient_accumulation_steps: 1
  max_grad_norm: 1.0
  
  # Checkpointing and model management
  save_total_limit: 3
  push_to_hub: false
  hub_model_id: null
  hub_strategy: "every_save"
  
  # Learning rate scheduling
  lr_scheduler_type: "linear"  # "linear", "cosine", "cosine_with_restarts", "polynomial", "constant", "constant_with_warmup"
  cosine_schedule_num_cycles: 0.5
  polynomial_decay_power: 1.0
  
  # Advanced LR scheduling options
  lr_scheduler_kwargs: null  # Additional scheduler-specific arguments
  warmup_schedule_type: "linear"  # "linear", "cosine", "constant"
  end_learning_rate: 0.0  # Final LR for polynomial decay
  
  # Enhanced early stopping options
  early_stopping_monitor_metrics:
    - "eval_f1"
    - "eval_loss"
  early_stopping_threshold_type: "absolute"  # "absolute" or "relative"
  early_stopping_min_delta: 0.0001
  early_stopping_restore_best_weights: true
  
  # Advanced training options
  gradient_checkpointing: false
  remove_unused_columns: true
  label_smoothing_factor: 0.0
  optim: "adamw_torch"  # "adamw_torch", "adamw_hf", "adafactor"
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_epsilon: 1.0e-8
  
  # Versioning and experiment tracking
  run_version: "v1.0"
  version_description: null
  experiment_name: null  # Used for output directory naming
  
  # Test mode settings
  test_mode: false
  test_sample_limit: 50
  test_epochs: 1
  test_disable_wandb: true
  
  # Output and prediction settings
  output_dir: "checkpoints"
  save_predictions: true
  generate_model_card: true
  prediction_loss_only: false
  
  # Data processing
  max_length: 512
  truncation: true
  padding: "max_length"
  
  # Reproducibility
  seed: 42
  data_seed: null  # Uses seed if null
  
  # Advanced features
  resume_from_checkpoint: null
  ignore_data_skip: false
  ddp_find_unused_parameters: false
  ddp_bucket_cap_mb: null