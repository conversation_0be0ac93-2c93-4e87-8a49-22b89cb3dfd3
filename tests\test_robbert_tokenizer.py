"""
Unit tests for RobBERT tokenizer with label alignment.
"""

import pytest
import torch
from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment, TokenAlignment, create_robbert_tokenizer


class TestRobBERTTokenizerWithAlignment:
    """Test suite for RobBERT tokenizer with alignment capabilities."""
    
    @pytest.fixture
    def tokenizer(self):
        """Create tokenizer instance for testing."""
        return RobBERTTokenizerWithAlignment()
    
    def test_tokenizer_initialization(self, tokenizer):
        """Test tokenizer initializes correctly."""
        assert tokenizer.model_name == "DTAI-KULeuven/robbert-2023-dutch-base"
        assert tokenizer.tokenizer is not None
        assert tokenizer.label2id == {"O": 0, "B-PER": 1, "I-PER": 2}
        assert tokenizer.id2label == {0: "O", 1: "B-PER", 2: "I-PER"}
    
    def test_basic_tokenization_without_labels(self, tokenizer):
        """Test basic tokenization without labels."""
        words = ["<PERSON>", "<PERSON><PERSON>", "woont", "in", "Amsterdam"]
        
        alignment = tokenizer.tokenize_with_alignment(words)
        
        assert isinstance(alignment, TokenAlignment)
        assert len(alignment.tokens) > 0
        assert len(alignment.word_ids) == len(alignment.tokens)
        assert len(alignment.token_labels) == len(alignment.tokens)
        assert alignment.original_labels == ["O"] * len(words)
        
        # Check that all token labels are "O" when no labels provided
        non_special_labels = [
            label for label, word_id in zip(alignment.token_labels, alignment.word_ids)
            if word_id is not None
        ]
        assert all(label == "O" for label in non_special_labels)
    
    def test_tokenization_with_labels(self, tokenizer):
        """Test tokenization with word-level labels."""
        words = ["Jan", "Jansen", "woont", "in", "Amsterdam"]
        labels = ["B-PER", "I-PER", "O", "O", "B-LOC"]
        
        alignment = tokenizer.tokenize_with_alignment(words, labels)
        
        assert alignment.original_labels == labels
        assert len(alignment.token_labels) == len(alignment.tokens)
        
        # Check that first tokens of words get original labels
        word_starts = {}
        for i, word_id in enumerate(alignment.word_ids):
            if word_id is not None and word_id not in word_starts:
                word_starts[word_id] = i
        
        # Verify first subword gets original label (for supported labels)
        for word_id, token_idx in word_starts.items():
            if word_id < len(labels):
                expected_label = labels[word_id] if labels[word_id] in tokenizer.label2id else "O"
                assert alignment.token_labels[token_idx] == expected_label
    
    def test_label_alignment_b_to_i_conversion(self, tokenizer):
        """Test B-PER to I-PER conversion for subword tokens."""
        # Use a compound name that will likely be split
        words = ["Jan-Willem", "van", "der", "Berg"]
        labels = ["B-PER", "I-PER", "I-PER", "I-PER"]
        
        alignment = tokenizer.tokenize_with_alignment(words, labels)
        
        # Find tokens for the first word (Jan-Willem)
        first_word_tokens = []
        for i, word_id in enumerate(alignment.word_ids):
            if word_id == 0:  # First word
                first_word_tokens.append((i, alignment.token_labels[i]))
        
        if len(first_word_tokens) > 1:
            # First subtoken should be B-PER
            assert first_word_tokens[0][1] == "B-PER"
            # Subsequent subtokens should be I-PER
            for _, label in first_word_tokens[1:]:
                assert label == "I-PER"
    
    def test_special_tokens_handling(self, tokenizer):
        """Test that special tokens get O labels."""
        words = ["Jan", "Jansen"]
        labels = ["B-PER", "I-PER"]
        
        alignment = tokenizer.tokenize_with_alignment(words, labels)
        
        # Special tokens (word_id=None) should have "O" labels
        for word_id, token_label in zip(alignment.word_ids, alignment.token_labels):
            if word_id is None:
                assert token_label == "O"
    
    def test_encode_for_model(self, tokenizer):
        """Test encoding for model input."""
        words = ["Jan", "Jansen", "woont"]
        labels = ["B-PER", "I-PER", "O"]
        
        encoding = tokenizer.encode_for_model(words, labels, max_length=128)
        
        assert "input_ids" in encoding
        assert "attention_mask" in encoding
        assert "labels" in encoding
        
        assert encoding["input_ids"].shape == (1, 128)
        assert encoding["attention_mask"].shape == (1, 128)
        assert encoding["labels"].shape == (1, 128)
        
        # Check that labels are properly converted to IDs
        label_ids = encoding["labels"].squeeze().tolist()
        assert all(isinstance(lid, int) for lid in label_ids)
        assert all(0 <= lid <= 2 for lid in label_ids)  # Valid label range
    
    def test_encode_for_model_without_labels(self, tokenizer):
        """Test encoding without labels."""
        words = ["Jan", "Jansen", "woont"]
        
        encoding = tokenizer.encode_for_model(words, max_length=128)
        
        assert "input_ids" in encoding
        assert "attention_mask" in encoding
        assert "labels" not in encoding
    
    def test_decode_predictions(self, tokenizer):
        """Test decoding model predictions to entities."""
        # Create mock input and predictions
        words = ["Jan", "Jansen", "woont", "in", "Amsterdam"]
        text = " ".join(words)
        
        # Encode text
        encoding = tokenizer.tokenizer(text, return_tensors="pt", padding="max_length", max_length=32)
        input_ids = encoding["input_ids"]
        
        # Create mock predictions (B-PER, I-PER, O, O, B-PER pattern)
        predictions = torch.zeros(1, 32, dtype=torch.long)
        # Set some predictions to simulate entity detection
        predictions[0, 1] = 1  # B-PER
        predictions[0, 2] = 2  # I-PER
        predictions[0, 5] = 1  # B-PER (for Amsterdam)
        
        entities = tokenizer.decode_predictions(input_ids, predictions)
        
        assert isinstance(entities, list)
        # Should find at least one entity
        assert len(entities) >= 0
        
        for entity in entities:
            assert "text" in entity
            assert "label" in entity
            assert "start_token" in entity
            assert "end_token" in entity
            assert "tokens" in entity
    
    def test_validate_alignment(self, tokenizer):
        """Test alignment validation functionality."""
        words = ["Jan-Willem", "van", "Amsterdam"]
        labels = ["B-PER", "I-PER", "B-LOC"]
        
        validation = tokenizer.validate_alignment(words, labels)
        
        assert "valid" in validation
        assert "stats" in validation
        assert "alignment" in validation
        
        stats = validation["stats"]
        assert "total_words" in stats
        assert "total_tokens" in stats
        assert "special_tokens" in stats
        assert "subword_splits" in stats
        
        assert stats["total_words"] == len(words)
        assert stats["total_tokens"] > 0
    
    def test_validate_alignment_verbose(self, tokenizer, capsys):
        """Test alignment validation with verbose output."""
        words = ["Jan-Willem", "van", "Amsterdam"]
        labels = ["B-PER", "I-PER", "B-LOC"]
        
        validation = tokenizer.validate_alignment(words, labels, verbose=True)
        
        captured = capsys.readouterr()
        assert "Alignment validation" in captured.out
        assert "Total tokens:" in captured.out
    
    def test_mismatched_words_and_labels_error(self, tokenizer):
        """Test error when words and labels have different lengths."""
        words = ["Jan", "Jansen"]
        labels = ["B-PER"]  # One less label
        
        with pytest.raises(ValueError, match="Words and labels must have the same length"):
            tokenizer.tokenize_with_alignment(words, labels)
    
    def test_token_alignment_validation_error(self):
        """Test TokenAlignment validation."""
        with pytest.raises(ValueError, match="All alignment lists must have the same length"):
            TokenAlignment(
                word_ids=[0, 1],
                token_labels=["B-PER"],  # Different length
                original_labels=["B-PER", "I-PER"],
                tokens=["Jan", "Jansen"]
            )
    
    def test_unsupported_labels_converted_to_o(self, tokenizer):
        """Test that unsupported labels are converted to O."""
        words = ["Jan", "Amsterdam", "Google"]
        labels = ["B-PER", "B-LOC", "B-ORG"]  # B-LOC and B-ORG not in label2id
        
        alignment = tokenizer.tokenize_with_alignment(words, labels)
        
        # Find first token of each word
        word_starts = {}
        for i, word_id in enumerate(alignment.word_ids):
            if word_id is not None and word_id not in word_starts:
                word_starts[word_id] = i
        
        # First word (Jan) should keep B-PER
        if 0 in word_starts:
            assert alignment.token_labels[word_starts[0]] == "B-PER"
        
        # Other words should be converted to O (unsupported labels)
        for word_id in [1, 2]:
            if word_id in word_starts:
                assert alignment.token_labels[word_starts[word_id]] == "O"
    
    def test_empty_input(self, tokenizer):
        """Test handling of empty input."""
        words = []
        labels = []
        
        alignment = tokenizer.tokenize_with_alignment(words, labels)
        
        # Should still have special tokens
        assert len(alignment.tokens) >= 2  # At least [CLS] and [SEP]
        assert all(label == "O" for label in alignment.token_labels)
    
    def test_long_sequence_truncation(self, tokenizer):
        """Test truncation of long sequences."""
        # Create a long sequence
        words = ["word"] * 600  # More than max_length
        labels = ["O"] * 600
        
        alignment = tokenizer.tokenize_with_alignment(words, labels, max_length=128)
        
        # Should be truncated to max_length
        assert len(alignment.tokens) <= 128
        assert len(alignment.token_labels) <= 128
        assert len(alignment.word_ids) <= 128


class TestFactoryFunction:
    """Test the factory function."""
    
    def test_create_robbert_tokenizer(self):
        """Test factory function creates tokenizer correctly."""
        tokenizer = create_robbert_tokenizer()
        
        assert isinstance(tokenizer, RobBERTTokenizerWithAlignment)
        assert tokenizer.model_name == "DTAI-KULeuven/robbert-2023-dutch-base"
    
    def test_create_robbert_tokenizer_custom_model(self):
        """Test factory function with custom model name."""
        custom_model = "DTAI-KULeuven/robbert-2023-dutch-base"
        tokenizer = create_robbert_tokenizer(custom_model)
        
        assert isinstance(tokenizer, RobBERTTokenizerWithAlignment)
        assert tokenizer.model_name == custom_model


class TestDutchSpecificCases:
    """Test Dutch-specific tokenization cases."""
    
    @pytest.fixture
    def tokenizer(self):
        """Create tokenizer instance for testing."""
        return RobBERTTokenizerWithAlignment()
    
    def test_compound_dutch_names(self, tokenizer):
        """Test handling of compound Dutch names."""
        words = ["Jan-Willem", "van", "der", "Berg"]
        labels = ["B-PER", "I-PER", "I-PER", "I-PER"]
        
        alignment = tokenizer.tokenize_with_alignment(words, labels)
        
        # Should handle compound names correctly
        assert len(alignment.tokens) > len(words)  # Likely to be split
        assert alignment.original_labels == labels
    
    def test_diacritics_handling(self, tokenizer):
        """Test handling of Dutch diacritics."""
        words = ["José", "Müller", "café"]
        labels = ["B-PER", "I-PER", "O"]
        
        alignment = tokenizer.tokenize_with_alignment(words, labels)
        
        # Should handle diacritics without errors
        assert len(alignment.tokens) > 0
        assert len(alignment.token_labels) == len(alignment.tokens)
    
    def test_dutch_prefixes(self, tokenizer):
        """Test handling of Dutch name prefixes."""
        words = ["van", "der", "Berg"]
        labels = ["I-PER", "I-PER", "I-PER"]
        
        alignment = tokenizer.tokenize_with_alignment(words, labels)
        
        # Should handle prefixes correctly
        assert len(alignment.tokens) >= len(words)
        
        # Check that I-PER labels are preserved for continuation tokens
        word_starts = {}
        for i, word_id in enumerate(alignment.word_ids):
            if word_id is not None and word_id not in word_starts:
                word_starts[word_id] = i
        
        # First tokens should get I-PER labels
        for word_id, token_idx in word_starts.items():
            if word_id < len(labels):
                assert alignment.token_labels[token_idx] == "I-PER"
    
    def test_email_like_text(self, tokenizer):
        """Test handling of email-like text."""
        words = ["<EMAIL>", "werkt", "hier"]
        labels = ["B-PER", "O", "O"]
        
        alignment = tokenizer.tokenize_with_alignment(words, labels)
        
        # Should handle email-like text without errors
        assert len(alignment.tokens) > 0
        # Email will likely be split into multiple tokens
        assert len(alignment.tokens) >= len(words)