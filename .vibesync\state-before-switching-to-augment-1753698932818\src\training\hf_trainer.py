"""
Hugging Face Trainer integration for RobBERT-2023 NER training.

This module provides the main training function using Hugging Face Trainer class
with WandB integration, automatic checkpointing, and hardware optimization.
"""

import os
import torch
import wandb
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path
from datetime import datetime

from datasets import DatasetDict
from transformers import (
    Trainer,
    TrainingArguments,
    DataCollatorForTokenClassification,
    EarlyStoppingCallback,
    RobertaForTokenClassification,
    AutoTokenizer
)
from transformers.integrations import WandbCallback

from .hf_config import HFTrainingConfig
from .hf_model_setup import create_ner_model, validate_model_compatibility
from .hf_evaluation_metrics import compute_metrics, NERMetricsCallback
from .hf_wandb_integration import CustomWandbCallback, create_enhanced_wandb_callback
from .hf_callbacks import create_enhanced_callbacks
from .error_handling import TrainingErrorHandler, handle_training_errors
from ..data.hf_dataset_preparation import prepare_ner_dataset
from ..utils.logging_utils import get_logger
from ..exceptions import TrainingError, ModelLoadingError


class HFNERTrainer:
    """
    Hugging Face Trainer wrapper for RobBERT-2023 NER training.
    
    This class provides a complete training pipeline using Hugging Face Trainer
    with WandB integration, automatic checkpointing, and evaluation metrics.
    """
    
    def __init__(self, config: HFTrainingConfig):
        """
        Initialize HF NER Trainer.
        
        Args:
            config: Training configuration
        """
        self.config = config
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        # Initialize comprehensive error handler
        self.error_handler = TrainingErrorHandler(self.logger)
        
        # Training components
        self.model = None
        self.tokenizer = None
        self.dataset_dict = None
        self.dataset_statistics = None
        self.output_dir = None
        self.config = config
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        # Initialize components
        self.model = None
        self.tokenizer = None
        self.label2id = None
        self.id2label = None
        self.label_list = None
        self.trainer = None
        
        # Training state
        self.output_dir = None
        self.dataset_dict = None
        self.dataset_statistics = None
    
    def setup_model_and_tokenizer(
        self,
        label_list: Optional[List[str]] = None
    ) -> Tuple[RobertaForTokenClassification, AutoTokenizer, Dict[str, int], Dict[int, str]]:
        """
        Set up model and tokenizer for training.
        
        Args:
            label_list: List of NER labels. If None, uses default ["O", "B-PER", "I-PER"]
            
        Returns:
            Tuple of (model, tokenizer, label2id, id2label)
        """
        self.logger.logger.info("Setting up model and tokenizer")
        
        try:
            # Create model and tokenizer
            model, tokenizer, label2id, id2label = create_ner_model(
                model_name=self.config.model_name,
                label_list=label_list
            )
            
            # Validate model compatibility
            validate_model_compatibility(model, tokenizer, label_list)
            
            # Store components
            self.model = model
            self.tokenizer = tokenizer
            self.label2id = label2id
            self.id2label = id2label
            self.label_list = list(label2id.keys())
            
            # Validate class weights if configured
            if self.config.use_class_weights:
                self.config.validate_class_weights(self.label_list)
            
            self.logger.logger.info(f"Model setup completed with {len(self.label_list)} labels")
            
            return model, tokenizer, label2id, id2label
            
        except Exception as e:
            error_msg = f"Failed to setup model and tokenizer: {e}"
            self.logger.logger.error(error_msg)
            raise ModelLoadingError(error_msg) from e
    
    def prepare_dataset(
        self,
        data_path: Union[str, Path],
        train_split: float = 0.8,
        max_length: Optional[int] = None
    ) -> DatasetDict:
        """
        Prepare dataset for training with comprehensive error handling.
        
        Args:
            data_path: Path to training data
            train_split: Training split ratio
            max_length: Maximum sequence length (uses config default if None)
            
        Returns:
            Prepared DatasetDict
        """
        self.logger.logger.info(f"Preparing dataset from: {data_path}")
        
        if max_length is None:
            max_length = self.config.max_length
        
        try:
            # Validate data file exists and is readable
            data_records = self.error_handler.data_handler.safe_json_load(str(data_path))
            self.logger.logger.info(f"Loaded {len(data_records)} records from {data_path}")
            
            # Prepare dataset using HF datasets with error handling
            dataset_dict, label_list, label2id, id2label, statistics = prepare_ner_dataset(
                data_path=data_path,
                model_name=self.config.model_name,
                train_split=train_split,
                max_length=max_length,
                stratify_by_entities=True,
                random_state=self.config.seed
            )
            
            # Store dataset information
            self.dataset_dict = dataset_dict
            self.dataset_statistics = statistics
            
            # Update model if not already set up
            if self.model is None:
                self.setup_model_and_tokenizer(label_list)
            
            # Log dataset statistics
            self.logger.logger.info(f"Dataset preparation completed:")
            self.logger.logger.info(f"  - Train samples: {len(dataset_dict['train'])}")
            self.logger.logger.info(f"  - Validation samples: {len(dataset_dict['validation'])}")
            self.logger.logger.info(f"  - Label count: {len(label_list)}")
            self.logger.logger.info(f"  - Labels: {label_list}")
            
            # Log data processing errors summary
            error_summary = self.error_handler.data_handler.get_error_summary()
            if error_summary:
                self.logger.logger.warning(f"Data processing errors encountered: {error_summary}")
            
            return dataset_dict
            
        except Exception as e:
            # Enhanced error logging with context
            self.logger.log_error(e, {
                "data_path": str(data_path),
                "train_split": train_split,
                "max_length": max_length
            })
            raise TrainingError(f"Failed to prepare dataset: {e}") from e
    
    def create_data_collator(self) -> DataCollatorForTokenClassification:
        """
        Create data collator for token classification.
        
        Returns:
            DataCollatorForTokenClassification instance
        """
        if self.tokenizer is None:
            raise TrainingError("Tokenizer not initialized. Call setup_model_and_tokenizer() first.")
        
        self.logger.logger.info("Creating data collator for token classification")
        
        return DataCollatorForTokenClassification(
            tokenizer=self.tokenizer,
            padding=True,
            max_length=self.config.max_length,
            pad_to_multiple_of=8 if torch.cuda.is_available() else None,
            return_tensors="pt"
        )
    
    def setup_wandb(self) -> None:
        """Set up WandB experiment tracking."""
        if self.config.test_mode and self.config.test_disable_wandb:
            self.logger.logger.info("WandB disabled in test mode")
            return
        
        if not self.config.wandb_project:
            self.logger.logger.info("WandB project not configured, skipping WandB setup")
            return
        
        try:
            self.logger.logger.info("Initializing WandB experiment tracking")
            
            # Get WandB configuration
            wandb_config = self.config.get_wandb_config()
            
            # Add dataset statistics to config
            if self.dataset_statistics:
                wandb_config["config"]["dataset_stats"] = self.dataset_statistics.to_dict()
            
            # Initialize WandB
            wandb.init(
                project=wandb_config["project"],
                entity=wandb_config["entity"],
                name=wandb_config["name"],
                tags=wandb_config["tags"],
                notes=wandb_config["notes"],
                config=wandb_config["config"]
            )
            
            self.logger.logger.info(f"WandB initialized: {wandb.run.name}")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to initialize WandB: {e}")
            # Continue without WandB rather than failing
    
    def create_training_arguments(self, output_dir: str) -> TrainingArguments:
        """
        Create TrainingArguments with WandB integration and optimization.
        
        Args:
            output_dir: Output directory for checkpoints
            
        Returns:
            TrainingArguments instance
        """
        self.logger.logger.info("Creating training arguments")
        
        # Convert config to TrainingArguments
        training_args = self.config.to_training_args(output_dir)
        
        self.logger.logger.info(f"Training arguments created:")
        self.logger.logger.info(f"  - Output dir: {training_args.output_dir}")
        self.logger.logger.info(f"  - Epochs: {training_args.num_train_epochs}")
        self.logger.logger.info(f"  - Batch size: {training_args.per_device_train_batch_size}")
        self.logger.logger.info(f"  - Learning rate: {training_args.learning_rate}")
        self.logger.logger.info(f"  - WandB reporting: {training_args.report_to}")
        self.logger.logger.info(f"  - Mixed precision: FP16={training_args.fp16}, BF16={training_args.bf16}")
        
        return training_args
    
    def create_callbacks(self, num_training_steps: Optional[int] = None) -> List:
        """
        Create training callbacks including enhanced early stopping, LR scheduling, and monitoring.
        
        Args:
            num_training_steps: Total number of training steps for LR scheduling
        
        Returns:
            List of callback instances
        """
        callbacks = []
        
        # Create enhanced callbacks (early stopping, LR scheduling, monitoring)
        enhanced_callbacks = create_enhanced_callbacks(
            config=self.config,
            label_list=self.label_list,
            num_training_steps=num_training_steps
        )
        callbacks.extend(enhanced_callbacks)
        
        # Custom NER metrics callback
        if self.label_list:
            metrics_callback = NERMetricsCallback(self.label_list)
            callbacks.append(metrics_callback)
            self.logger.logger.info("Added NER metrics callback")
        
        # Enhanced WandB callback (replaces default WandB integration)
        if self.config.wandb_project and not (self.config.test_mode and self.config.test_disable_wandb):
            wandb_config = {
                'log_confusion_matrix': self.config.wandb_log_confusion_matrix,
                'log_per_class_metrics': self.config.wandb_log_per_class_metrics,
                'log_model_artifacts': self.config.wandb_log_model_artifacts,
                'log_evaluation_tables': self.config.wandb_log_evaluation_tables,
                'confusion_matrix_frequency': self.config.wandb_confusion_matrix_frequency,
                'confusion_matrix_steps': self.config.wandb_confusion_matrix_steps
            }
            
            enhanced_wandb_callback = create_enhanced_wandb_callback(
                label_list=self.label_list,
                config=wandb_config
            )
            callbacks.append(enhanced_wandb_callback)
            self.logger.logger.info("Added enhanced WandB callback with advanced logging")
        
        return callbacks
    
    def create_trainer(
        self,
        dataset_dict: DatasetDict,
        output_dir: str
    ) -> Trainer:
        """
        Create Hugging Face Trainer instance.
        
        Args:
            dataset_dict: Prepared dataset
            output_dir: Output directory for checkpoints
            
        Returns:
            Trainer instance
        """
        self.logger.logger.info("Creating Hugging Face Trainer")
        
        if self.model is None or self.tokenizer is None:
            raise TrainingError("Model and tokenizer must be initialized before creating trainer")
        
        # Create components
        training_args = self.create_training_arguments(output_dir)
        data_collator = self.create_data_collator()
        
        # Calculate total training steps for LR scheduling
        num_training_steps = training_args.max_steps if training_args.max_steps > 0 else None
        if num_training_steps is None:
            # Estimate from dataset size and training args
            train_dataset_size = len(dataset_dict['train'])
            steps_per_epoch = train_dataset_size // (training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps)
            num_training_steps = int(steps_per_epoch * training_args.num_train_epochs)
        
        callbacks = self.create_callbacks(num_training_steps)
        
        # Create compute_metrics function
        def compute_metrics_fn(eval_pred):
            return compute_metrics(eval_pred, self.label_list)
        
        # Create trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=dataset_dict['train'],
            eval_dataset=dataset_dict['validation'],
            tokenizer=self.tokenizer,
            data_collator=data_collator,
            compute_metrics=compute_metrics_fn,
            callbacks=callbacks
        )
        
        self.trainer = trainer
        
        self.logger.logger.info("Trainer created successfully")
        self.logger.logger.info(f"  - Train dataset size: {len(dataset_dict['train'])}")
        self.logger.logger.info(f"  - Validation dataset size: {len(dataset_dict['validation'])}")
        
        return trainer
    
    @handle_training_errors()
    def train(
        self,
        data_path: Union[str, Path],
        output_dir: Optional[str] = None,
        resume_from_checkpoint: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Run complete training pipeline with comprehensive error handling.
        
        Args:
            data_path: Path to training data
            output_dir: Output directory (auto-generated if None)
            resume_from_checkpoint: Path to checkpoint to resume from
            
        Returns:
            Training results dictionary
        """
        self.logger.logger.info("Starting Hugging Face Trainer training pipeline")
        
        # Log system information for debugging
        self.logger.log_system_info()
        
        try:
            # Create output directory
            if output_dir is None:
                output_dir = self.config.create_output_dir("hf_training")
            else:
                Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            self.output_dir = output_dir
            self.logger.logger.info(f"Using output directory: {output_dir}")
            
            # Setup WandB with error handling
            with self.error_handler.wandb_handler.safe_wandb_context(
                project=self.config.wandb_project,
                entity=self.config.wandb_entity,
                name=self.config.run_name
            ) as wandb_run:
                
                # Prepare dataset with error handling
                dataset_dict = self.prepare_dataset(data_path)
                
                # Create trainer with CUDA memory monitoring
                with self.error_handler.cuda_manager.cuda_memory_context("trainer_creation"):
                    trainer = self.create_trainer(dataset_dict, output_dir)
                
                # Override resume_from_checkpoint if provided
                if resume_from_checkpoint:
                    trainer.args.resume_from_checkpoint = resume_from_checkpoint
                    self.logger.logger.info(f"Resuming from checkpoint: {resume_from_checkpoint}")
                
                # Start training with memory monitoring
                self.logger.logger.info("Starting training...")
                with self.error_handler.cuda_manager.cuda_memory_context("training"):
                    train_result = trainer.train(resume_from_checkpoint=resume_from_checkpoint)
                
                # Save final model
                self.logger.logger.info("Saving final model...")
                trainer.save_model()
                trainer.save_state()
                
                # Run final evaluation
                self.logger.logger.info("Running final evaluation...")
                with self.error_handler.cuda_manager.cuda_memory_context("evaluation"):
                    eval_result = trainer.evaluate()
                
                # Compile results
                results = {
                    "train_result": train_result,
                    "eval_result": eval_result,
                    "output_dir": output_dir,
                    "model_path": os.path.join(output_dir, "pytorch_model.bin"),
                    "config_path": os.path.join(output_dir, "config.json"),
                    "tokenizer_path": output_dir,
                    "dataset_statistics": self.dataset_statistics.to_dict() if self.dataset_statistics else None,
                    "error_summary": self.error_handler.get_error_summary()
                }
                
                # Log final results
                self._log_training_results(results)
                
                # Save training configuration
                self._save_training_config(output_dir)
                
                # Generate model card if enabled
                if self.config.generate_model_card:
                    self._generate_model_card(output_dir, results)
                
                # Save predictions if enabled
                if self.config.save_predictions:
                    self._save_predictions(trainer, dataset_dict, output_dir)
                
                # Save error report
                error_report_path = os.path.join(output_dir, "error_report.json")
                self.error_handler.save_error_report(error_report_path)
                
                # Save fallback metrics if WandB was unavailable
                if not wandb_run:
                    fallback_metrics_path = os.path.join(output_dir, "fallback_metrics.json")
                    self.error_handler.wandb_handler.save_fallback_metrics(fallback_metrics_path)
                
                self.logger.logger.info("Training completed successfully")
                
                return results
                
        except Exception as e:
            # Let the error handler try to recover
            if not self.error_handler.handle_training_error(e):
                # If recovery failed, log comprehensive error info and re-raise
                self.logger.log_error(e, {
                    "data_path": str(data_path),
                    "output_dir": output_dir,
                    "resume_from_checkpoint": resume_from_checkpoint
                })
                
                # Save error report if output directory exists
                if hasattr(self, 'output_dir') and self.output_dir:
                    error_report_path = os.path.join(self.output_dir, "error_report.json")
                    self.error_handler.save_error_report(error_report_path)
                
                raise TrainingError(f"Training failed: {e}") from e
    
    def _log_training_results(self, results: Dict[str, Any]) -> None:
        """Log comprehensive training results."""
        self.logger.logger.info("Training Results Summary:")
        
        # Training metrics
        if "train_result" in results:
            train_result = results["train_result"]
            if hasattr(train_result, 'training_loss'):
                self.logger.logger.info(f"  - Final training loss: {train_result.training_loss:.4f}")
            if hasattr(train_result, 'metrics'):
                for key, value in train_result.metrics.items():
                    self.logger.logger.info(f"  - {key}: {value}")
        
        # Evaluation metrics
        if "eval_result" in results:
            eval_result = results["eval_result"]
            for key, value in eval_result.items():
                if isinstance(value, (int, float)):
                    self.logger.logger.info(f"  - {key}: {value:.4f}")
                else:
                    self.logger.logger.info(f"  - {key}: {value}")
        
        # Output paths
        self.logger.logger.info(f"  - Model saved to: {results.get('model_path', 'N/A')}")
        self.logger.logger.info(f"  - Output directory: {results.get('output_dir', 'N/A')}")
    
    def _save_training_config(self, output_dir: str) -> None:
        """Save training configuration to output directory."""
        try:
            config_path = Path(output_dir) / "training_config.yaml"
            self.config.save_yaml(str(config_path))
            self.logger.logger.info(f"Training configuration saved to: {config_path}")
        except Exception as e:
            self.logger.logger.warning(f"Failed to save training configuration: {e}")
    
    def _generate_model_card(self, output_dir: str, results: Dict[str, Any]) -> None:
        """Generate model card with training information."""
        try:
            from .model_card_generator import generate_model_card
            
            model_card_path = Path(output_dir) / "model_card.md"
            generate_model_card(
                output_path=str(model_card_path),
                config=self.config,
                results=results,
                dataset_statistics=self.dataset_statistics,
                label_list=self.label_list
            )
            self.logger.logger.info(f"Model card generated: {model_card_path}")
            
        except ImportError:
            self.logger.logger.warning("Model card generator not available")
        except Exception as e:
            self.logger.logger.warning(f"Failed to generate model card: {e}")
    
    def _save_predictions(
        self,
        trainer: Trainer,
        dataset_dict: DatasetDict,
        output_dir: str
    ) -> None:
        """Save validation predictions for analysis."""
        try:
            predictions_path = Path(output_dir) / "predictions.jsonl"
            
            # Get predictions on validation set
            predictions = trainer.predict(dataset_dict['validation'])
            
            # Save predictions in JSONL format
            import json
            with open(predictions_path, 'w', encoding='utf-8') as f:
                for i, (pred, label) in enumerate(zip(predictions.predictions, predictions.label_ids)):
                    # Convert to predicted labels
                    pred_labels = [self.id2label[p] for p in pred.argmax(axis=-1)]
                    true_labels = [self.id2label[l] if l != -100 else 'PAD' for l in label]
                    
                    prediction_record = {
                        "example_id": i,
                        "predicted_labels": pred_labels,
                        "true_labels": true_labels,
                        "logits": pred.tolist()
                    }
                    f.write(json.dumps(prediction_record) + '\n')
            
            self.logger.logger.info(f"Predictions saved to: {predictions_path}")
            
        except Exception as e:
            self.logger.logger.warning(f"Failed to save predictions: {e}")


def train_ner_model(
    data_path: Union[str, Path],
    config_path: Optional[str] = None,
    config: Optional[HFTrainingConfig] = None,
    output_dir: Optional[str] = None,
    resume_from_checkpoint: Optional[str] = None
) -> Dict[str, Any]:
    """
    Convenience function to train NER model with Hugging Face Trainer.
    
    Args:
        data_path: Path to training data
        config_path: Path to YAML configuration file
        config: HFTrainingConfig instance (overrides config_path)
        output_dir: Output directory for checkpoints
        resume_from_checkpoint: Path to checkpoint to resume from
        
    Returns:
        Training results dictionary
    """
    # Load configuration
    if config is None:
        if config_path:
            config = HFTrainingConfig.from_yaml(config_path)
        else:
            config = HFTrainingConfig()
    
    # Create trainer and run training
    trainer = HFNERTrainer(config)
    return trainer.train(
        data_path=data_path,
        output_dir=output_dir,
        resume_from_checkpoint=resume_from_checkpoint
    )


# Export main classes and functions
__all__ = [
    'HFNERTrainer',
    'train_ner_model'
]