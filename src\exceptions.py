"""
Custom exception classes for RobBERT-2023 pipeline.
"""

from typing import Optional, Dict, Any


class RobBERTError(Exception):
    """Base exception class for RobBERT pipeline errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}
    
    def __str__(self):
        if self.details:
            return f"{self.message} | Details: {self.details}"
        return self.message


class TokenizationError(RobBERTError):
    """Exception raised during tokenization operations."""
    
    def __init__(self, message: str, text: Optional[str] = None, 
                 tokenizer_name: Optional[str] = None, **kwargs):
        details = {
            "text_length": len(text) if text else None,
            "text_preview": text[:100] + "..." if text and len(text) > 100 else text,
            "tokenizer_name": tokenizer_name,
            **kwargs
        }
        super().__init__(message, details)


class ModelLoadingError(RobBERTError):
    """Exception raised during model loading operations."""
    
    def __init__(self, message: str, model_name: Optional[str] = None,
                 checkpoint_path: Optional[str] = None, **kwargs):
        details = {
            "model_name": model_name,
            "checkpoint_path": checkpoint_path,
            **kwargs
        }
        super().__init__(message, details)


class InferenceError(RobBERTError):
    """Exception raised during inference operations."""
    
    def __init__(self, message: str, input_shape: Optional[tuple] = None,
                 model_name: Optional[str] = None, heads: Optional[list] = None, **kwargs):
        details = {
            "input_shape": input_shape,
            "model_name": model_name,
            "heads": heads,
            **kwargs
        }
        super().__init__(message, details)


class DimensionMismatchError(InferenceError):
    """Exception raised when tensor dimensions don't match expected values."""
    
    def __init__(self, message: str, expected_shape: Optional[tuple] = None,
                 actual_shape: Optional[tuple] = None, tensor_name: Optional[str] = None, **kwargs):
        details = {
            "expected_shape": expected_shape,
            "actual_shape": actual_shape,
            "tensor_name": tensor_name,
            **kwargs
        }
        super().__init__(message, **details)


class LabelAlignmentError(TokenizationError):
    """Exception raised during label alignment operations."""
    
    def __init__(self, message: str, num_words: Optional[int] = None,
                 num_labels: Optional[int] = None, num_tokens: Optional[int] = None, **kwargs):
        details = {
            "num_words": num_words,
            "num_labels": num_labels,
            "num_tokens": num_tokens,
            **kwargs
        }
        super().__init__(message, **details)


class ConfigurationError(RobBERTError):
    """Exception raised for configuration-related errors."""
    
    def __init__(self, message: str, config_path: Optional[str] = None,
                 config_section: Optional[str] = None, **kwargs):
        details = {
            "config_path": config_path,
            "config_section": config_section,
            **kwargs
        }
        super().__init__(message, details)


class MemoryError(RobBERTError):
    """Exception raised for memory-related errors."""
    
    def __init__(self, message: str, required_memory: Optional[str] = None,
                 available_memory: Optional[str] = None, operation: Optional[str] = None, **kwargs):
        details = {
            "required_memory": required_memory,
            "available_memory": available_memory,
            "operation": operation,
            **kwargs
        }
        super().__init__(message, details)


class ValidationError(RobBERTError):
    """Exception raised for validation errors."""
    
    def __init__(self, message: str, validation_type: Optional[str] = None,
                 expected_value: Optional[Any] = None, actual_value: Optional[Any] = None, **kwargs):
        details = {
            "validation_type": validation_type,
            "expected_value": expected_value,
            "actual_value": actual_value,
            **kwargs
        }
        super().__init__(message, details)


class TrainingError(RobBERTError):
    """Exception raised during training operations."""
    
    def __init__(self, message: str, epoch: Optional[int] = None,
                 step: Optional[int] = None, loss: Optional[float] = None, **kwargs):
        details = {
            "epoch": epoch,
            "step": step,
            "loss": loss,
            **kwargs
        }
        super().__init__(message, details)


class DataProcessingError(RobBERTError):
    """Exception raised during data processing operations."""
    
    def __init__(self, message: str, data_path: Optional[str] = None,
                 processing_step: Optional[str] = None, data_size: Optional[int] = None, **kwargs):
        details = {
            "data_path": data_path,
            "processing_step": processing_step,
            "data_size": data_size,
            **kwargs
        }
        super().__init__(message, details)