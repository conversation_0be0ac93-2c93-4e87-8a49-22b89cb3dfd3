# Current BERTje Model Assessment - Compound Name Analysis

**Assessment Date:** July 16, 2025  
**Model:** BERTje Multi-task NER (`wietsedv/bert-base-dutch-cased-finetuned-conll2002-ner`)  
**CUDA Support:** ✅ Enabled (NVIDIA GeForce RTX 3070 Laptop GPU)  
**PyTorch Version:** 2.5.1+cu121  

## Executive Summary

The current BERTje model shows **significant limitations** when processing compound Dutch names and long entities. While it performs well on simple, single-token names, it struggles with:

- Hyphenated compound names (<PERSON><PERSON><PERSON> → only detects "<PERSON>")
- Names with particles (van der, de, aan den)
- Long formal names with multiple components
- Organization names with abbreviations (N.V., B.V.)
- Location names with prepositions

**Overall Performance:**
- Simple names: ~90% F1 score
- **Compound names: 39.4% F1 score** ⚠️
- **Training required** to meet production standards

## Detailed Findings

### 1. Tokenization Analysis

**Overall Statistics:**
- Average tokens per text: 10.3
- Average subword ratio: 6.78%
- Average tokens per word: 1.48

**Key Issues:**
- Compound names are fragmented across multiple tokens
- Hyphens and particles break entity boundaries
- Subword tokenization creates alignment problems

### 2. Entity Recognition Limitations

#### 2.1 Compound Person Names
| Test Case | Expected | Detected | Status |
|-----------|----------|----------|---------|
| Jan-Willem van der Berg | Jan-Willem van der Berg | Jan | ❌ FAIL |
| Marie-Claire de Jong-Bakker | Marie-Claire de Jong-Bakker | Marie | ❌ FAIL |
| Johannes Wilhelmus van den Broek | Johannes Wilhelmus van den Broek | JohannesWilhelm | ❌ PARTIAL |

**Issue:** Model only detects the first component of compound names, missing particles and subsequent name parts.

#### 2.2 Organization Names with Abbreviations
| Test Case | Expected | Detected | Status |
|-----------|----------|----------|---------|
| Koninklijke Philips N.V. | Koninklijke Philips N.V. | KoninklijkePhilipsN + C (MISC) | ❌ FAIL |
| Rabobank Nederland Groep | Rabobank Nederland Groep | RabobankNederlandGroep | ✅ PASS |

**Issue:** Abbreviations like "N.V." are not properly handled, causing entity boundary errors.

#### 2.3 Location Names with Prepositions
| Test Case | Expected | Detected | Status |
|-----------|----------|----------|---------|
| Den Haag-Centrum | Den Haag-Centrum | De | ❌ FAIL |
| Alphen aan den Rijn | Alphen aan den Rijn | Alphenaande | ❌ PARTIAL |

**Issue:** Prepositions and compound location names are not properly recognized as single entities.

### 3. Tokenization Patterns

**Problematic Patterns Identified:**

1. **Hyphen Fragmentation:**
   ```
   "Jan-Willem" → ['Jan', '-', 'Willem']
   Model sees: Jan (B-PER), - (O), Willem (O)
   Should be: Jan (B-PER), - (I-PER), Willem (I-PER)
   ```

2. **Particle Handling:**
   ```
   "van der Berg" → ['van', 'de', '##r', 'Berg']
   Model sees: van (O), de (O), ##r (O), Berg (O)
   Should be: van (I-PER), de (I-PER), ##r (I-PER), Berg (I-PER)
   ```

3. **Abbreviation Splitting:**
   ```
   "N.V." → ['N', '.', 'V', '.']
   Model sees: N (I-ORG), . (O), V (O), . (O)
   Should be: N (I-ORG), . (I-ORG), V (I-ORG), . (I-ORG)
   ```

### 4. Performance Metrics by Category

| Category | Test Cases | Success Rate | F1 Score |
|----------|------------|--------------|----------|
| Basic Names | 2 | 100% | 1.000 |
| Compound Person Names | 3 | 0% | 0.000 |
| Compound Organizations | 2 | 50% | 0.500 |
| Compound Locations | 2 | 0% | 0.000 |
| Mixed Complexity | 2 | 50% | 0.667 |
| **Overall** | **11** | **45%** | **0.394** |

## Root Cause Analysis

### 1. Training Data Limitations
- **Insufficient compound name examples** in CoNLL-2002 training data
- **Lack of Dutch-specific patterns** (particles, hyphens, formal titles)
- **Imbalanced entity types** (more simple than compound names)

### 2. Tokenization Issues
- **BERTje tokenizer** not optimized for Dutch compound structures
- **Subword fragmentation** breaks entity coherence
- **Special character handling** (hyphens, periods) disrupts boundaries

### 3. Model Architecture Limitations
- **BIO tagging scheme** struggles with long, fragmented entities
- **Context window** may not capture full compound name patterns
- **Entity boundary detection** fails across token boundaries

## Recommendations for Training Improvement

### 1. High Priority - Training Data Enhancement

**Expand Dataset Size (5-10x current size):**
- Target: ~10,000 training examples (currently ~1,000)
- Focus on compound name patterns
- Include negative examples (sentences without entities)

**Add Compound Name Examples:**
```python
# Required training patterns:
compound_person_patterns = [
    "Jan-Willem van der Berg",
    "Marie-Claire de Jong-Bakker", 
    "Dr. Johannes Wilhelmus van den Broek",
    "Prof. dr. Anne-Marie van den Heuvel"
]

compound_org_patterns = [
    "Koninklijke Philips N.V.",
    "Royal Dutch Shell plc",
    "ABN AMRO Bank N.V.",
    "Rabobank Nederland Groep"
]

compound_location_patterns = [
    "Den Haag-Centrum",
    "Amsterdam-Noord", 
    "Alphen aan den Rijn",
    "Bergen op Zoom"
]
```

**Handle Dutch Particles:**
- Add examples with particles NOT tagged as entities: "bij", "of", "sive", "genaamd"
- Include particle variations in person names
- Train on edge cases with multiple particles

### 2. Medium Priority - Model Architecture

**Consider Extended Entity Types:**
- ADDRESS (full addresses)
- POSTCODE (Dutch postal codes)
- EMAIL, PHONE, IBAN (structured data)
- MONEY (Dutch currency expressions)

**Improve Loss Function:**
- Weight longer entities higher in loss calculation
- Add boundary detection loss component
- Implement focal loss for hard examples

### 3. Low Priority - Tokenization

**Evaluate Alternative Approaches:**
- Character-level models for compound names
- Custom Dutch tokenizer with compound awareness
- Hybrid word/subword tokenization

## Implementation Priority

### Phase 1: Critical Issues (Immediate)
1. ✅ **CUDA Support** - Completed
2. 🔄 **Training Data Expansion** - In Progress
   - Validate existing training data alignment
   - Add compound name examples
   - Include negative examples
   - Fix tag casing consistency

### Phase 2: Model Training (Next)
3. **Enhanced Training Pipeline**
   - Implement weighted loss for compound entities
   - Add evaluation metrics for compound names
   - Create reproducible training process

### Phase 3: Advanced Features (Future)
4. **Extended Entity Recognition**
   - Add new entity types (ADDRESS, EMAIL, etc.)
   - Implement regex validation for structured entities
   - Add GDPR compliance checking

## Test Cases for Validation

The following test cases should be used to validate training improvements:

```python
critical_test_cases = [
    # Must pass after training
    "Jan-Willem van der Berg werkt bij de gemeente.",
    "Marie-Claire de Jong-Bakker is advocaat.",
    "Dr. Johannes Wilhelmus van den Broek spreekt.",
    "Koninklijke Philips N.V. heeft een nieuwe CEO.",
    "De gemeente Alphen aan den Rijn organiseert een evenement.",
    
    # Edge cases
    "Jan bij de Vaart werkt bij Microsoft.",
    "De heer Van der Berg genaamd Piet spreekt.",
    "ING Bank N.V. en ABN AMRO Bank N.V. fuseren niet."
]
```

**Success Criteria:**
- F1 Score ≥ 85% on compound name test cases
- No regression on simple name performance
- Proper handling of Dutch particles and abbreviations

## Conclusion

The current BERTje model requires **significant training improvements** to handle Dutch compound names effectively. The 39.4% F1 score on compound names is well below production standards.

**Next Steps:**
1. ✅ Implement training data validation and expansion
2. 🔄 Create enhanced training pipeline with compound name focus
3. 📋 Validate improvements against critical test cases
4. 📋 Deploy improved model for production use

**Estimated Timeline:**
- Training data preparation: 1-2 weeks
- Model training and validation: 1-2 weeks  
- Testing and deployment: 1 week
- **Total: 3-5 weeks**