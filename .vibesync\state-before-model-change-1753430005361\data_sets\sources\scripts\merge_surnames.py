#!/usr/bin/env python
"""
merge_lastnames.py

Run from anywhere. Expects the following files **in the same folder as this script**:

    achternamen_org.csv   # messy source  (surname_part, combiner?, count)
    last_names.csv        # one clean surname per line (no counts)

Generates (UTF-8):

    last_names_enriched.csv   # surname,total_freq  (clean names with >0 count)
    zero_count_clean.csv      # clean surnames that ended with total_freq == 0
    unmatched_count.csv       # deduped enriched positive-count surnames not in clean list
    unmatched_raw.csv         # (optional) raw unmatched lines (any count) for audit
"""

import csv
import os
import re
import sys
from pathlib import Path
from collections import defaultdict

# ------------------------------------------------------------------
# Paths resolved against the folder containing this script
# ------------------------------------------------------------------
BASE_DIR = Path(os.path.dirname(__file__)).resolve()
RAW_CSV   = BASE_DIR / "achternamen_org.csv"
CLEAN_CSV = BASE_DIR / "last_names.csv"

OUT_ENR   = BASE_DIR / "last_names_enriched.csv"
OUT_ZERO  = BASE_DIR / "zero_count_clean.csv"
OUT_UNM   = BASE_DIR / "unmatched_count.csv"
OUT_UNRAW = BASE_DIR / "unmatched_raw.csv"

if not RAW_CSV.exists() or not CLEAN_CSV.exists():
    sys.exit("[ERROR] Required input files (achternamen_org.csv, last_names.csv) not found in script directory.")

# ------------------------------------------------------------------
# Normalisation helpers
# ------------------------------------------------------------------
RE_MULTI_SPACE = re.compile(r"\s+")
RE_EDGE_QUOTE  = re.compile(r'^\s+|\s+$|^"+|"+$')   # trim spaces and straight quotes only

def norm_space(s: str) -> str:
    """Trim outer spaces/straight quotes, collapse internal whitespace, preserve apostrophes."""
    s = RE_EDGE_QUOTE.sub("", s)
    return RE_MULTI_SPACE.sub(" ", s.strip())

def cap_first(s: str) -> str:
    return s[:1].upper() + s[1:] if s else s


def to_int(text: str) -> int:
    """
    Interpret the count column.
      "< 5" -> 5
      "-"   -> 0
      ""    -> 0
      "0"   -> 0
      "12"  -> 12
    Returns an *int* always (0 if unknown).
    """
    s = text.strip()
    if not s or s == "-" or s == "0":
        return 0
    if s.startswith("<"):
        return 5
    try:
        return int(s)
    except ValueError:
        return 0

def canonical_key(name: str) -> str:
    """lower-case key with single spaces for dictionary matching"""
    return norm_space(name).lower()

# ------------------------------------------------------------------
# Load clean surnames (canonical list)
# ------------------------------------------------------------------
clean_names = []
with CLEAN_CSV.open(encoding="utf-8") as f:
    for line in f:
        n = norm_space(line)
        if n:
            clean_names.append(n)

clean_map = {canonical_key(n): n for n in clean_names}  # canonical key -> canonical name

# ------------------------------------------------------------------
# FIRST PASS over raw data:
#   * merge into clean_map counts
#   * collect unmatched positive counts
#   * keep raw lines for audit
# ------------------------------------------------------------------
raw_freq_clean = defaultdict(int)   # mapped to clean names
raw_seen_clean = set()              # saw any row for that clean name
unmatched_freq = defaultdict(int)   # canonical unmatched surname -> total positive freq
unmatched_raw_lines = []            # raw unmatched rows (string, any count)

with RAW_CSV.open(encoding="utf-8") as f:
    reader = csv.reader(f)
    header = next(reader, None)  # skip header row if present

    for row in reader:
        if not row:
            continue

        # strip trailing empties
        while row and not row[-1].strip():
            row = row[:-1]
        if not row:
            continue

        # interpret count (last column)
        cnt_raw = row[-1].strip()
        freq = to_int(cnt_raw)  # -> int (0 if unknown)

        # everything but last column is the name region
        name_parts = row[:-1]
        if not name_parts:
            # whole row was count only? skip
            continue

        if len(name_parts) == 1:
            # already complete name (e.g., "Godoy van de Velde")
            surname = norm_space(name_parts[0])
        else:
            # first part = main surname; rest = combiner fragment(s)
            main      = norm_space(name_parts[0])
            combiner  = norm_space(" ".join(name_parts[1:])).lower()
            surname   = f"{combiner} {cap_first(main)}".strip()

        key = canonical_key(surname)

        if key in clean_map:
            raw_seen_clean.add(key)
            if freq > 0:
                raw_freq_clean[key] += freq
        else:
            # truly unmatched against clean list
            if freq > 0:
                unmatched_freq[key] += freq
            unmatched_raw_lines.append(",".join(row))

# ------------------------------------------------------------------
# Build enriched & zero-count lists for the *clean* surnames
# ------------------------------------------------------------------
enriched_rows = []  # (surname, total_freq>0)
zero_rows     = []  # surname (total_freq == 0)

for clean_name in clean_names:
    key = canonical_key(clean_name)
    total = raw_freq_clean.get(key, 0)
    if total > 0:
        enriched_rows.append((clean_name, total))
    else:
        zero_rows.append(clean_name)

# ------------------------------------------------------------------
# Prepare deduped unmatched_count rows
# ------------------------------------------------------------------
# Convert canonical key -> display name (cap first token of main part)
def display_from_key(key: str) -> str:
    # attempt to restore minimal formatting: split once at space before last token?
    # Actually 'key' already has combiner lower-case; we'll capitalise the last token's first char.
    parts = key.split(" ")
    if not parts:
        return key
    # find last element as main surname
    main = parts[-1]
    parts[-1] = cap_first(main)
    return " ".join(parts)

unmatched_rows_display = [
    (display_from_key(k), freq) for k, freq in unmatched_freq.items() if freq > 0
]
unmatched_rows_display.sort(key=lambda x: -x[1])

# ------------------------------------------------------------------
# Write outputs
# ------------------------------------------------------------------
# 1) Enriched clean surnames (>0)
with OUT_ENR.open("w", encoding="utf-8", newline="") as f:
    w = csv.writer(f)
    w.writerow(["surname", "total_freq"])
    for name, freq in enriched_rows:
        w.writerow([name, freq])

# 2) Zero-count clean surnames
with OUT_ZERO.open("w", encoding="utf-8") as f:
    for n in zero_rows:
        f.write(n + "\n")

# 3) Unmatched positive-count surnames (deduped/enriched)
with OUT_UNM.open("w", encoding="utf-8", newline="") as f:
    w = csv.writer(f)
    w.writerow(["surname", "total_freq"])
    for name, freq in unmatched_rows_display:
        w.writerow([name, freq])

# 4) Raw unmatched lines (optional, diagnostic)
with OUT_UNRAW.open("w", encoding="utf-8") as f:
    for line in unmatched_raw_lines:
        f.write(line + "\n")

# ------------------------------------------------------------------
# Summary
# ------------------------------------------------------------------
print(f"[OK] enriched file → {OUT_ENR}")
print(f"[OK] {len(zero_rows):,} clean surnames with zero counts → {OUT_ZERO.name}")
print(f"[OK] {len(unmatched_rows_display):,} unmatched surnames with positive counts → {OUT_UNM.name}")
print(f"[OK] {len(unmatched_raw_lines):,} raw unmatched lines (any count) → {OUT_UNRAW.name}")
