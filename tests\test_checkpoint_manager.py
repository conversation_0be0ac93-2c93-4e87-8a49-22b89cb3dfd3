"""
Tests for RobBERT-2023 checkpoint management system.
"""

import pytest
import tempfile
import shutil
import json
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import torch
import torch.nn as nn

from src.utils.checkpoint_manager import (
    CheckpointManager, 
    save_robbert_checkpoint, 
    load_robbert_checkpoint,
    list_robbert_checkpoints
)
from src.models.multitask_robbert import MultiTaskRobBERT
from src.exceptions import ModelLoadingError


class TestCheckpointManager:
    """Test cases for CheckpointManager class."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def checkpoint_manager(self, temp_dir):
        """Create CheckpointManager instance for testing."""
        return CheckpointManager(base_dir=temp_dir)
    
    @pytest.fixture
    def mock_model(self):
        """Create mock MultiTaskRobBERT model for testing."""
        model = Mock(spec=MultiTaskRobBERT)
        model.heads = {'ner': <PERSON>ck(), 'compliance': Mock()}
        model.parameters.return_value = [torch.randn(100, 100), torch.randn(50)]
        model.save_pretrained = Mock()
        return model
    
    def test_init_creates_directory_structure(self, temp_dir):
        """Test that initialization creates proper directory structure."""
        manager = CheckpointManager(base_dir=temp_dir)
        
        expected_dirs = [
            manager.robbert_dir,
            manager.robbert_dir / "fine-tuned",
            manager.robbert_dir / "base-models",
            manager.robbert_dir / "experiments",
            manager.robbert_dir / "backups"
        ]
        
        for directory in expected_dirs:
            assert directory.exists(), f"Directory not created: {directory}"
    
    def test_save_checkpoint_basic(self, checkpoint_manager, mock_model, temp_dir):
        """Test basic checkpoint saving functionality."""
        checkpoint_name = "test_checkpoint"
        metadata = {"test_key": "test_value"}
        
        # Mock the save_pretrained method
        def mock_save_pretrained(path):
            save_path = Path(path)
            save_path.mkdir(parents=True, exist_ok=True)
            
            # Create minimal required files
            (save_path / "config.json").write_text('{"model_type": "roberta"}')
            (save_path / "pytorch_model.bin").write_text("dummy_weights")
            (save_path / "tokenizer_config.json").write_text('{}')
            (save_path / "vocab.json").write_text('{}')
            (save_path / "merges.txt").write_text("")
        
        mock_model.save_pretrained.side_effect = mock_save_pretrained
        
        # Mock validation to pass
        with patch.object(checkpoint_manager, 'validate_checkpoint', return_value=True):
            result_path = checkpoint_manager.save_checkpoint(mock_model, checkpoint_name, metadata)
        
        # Verify checkpoint was saved
        assert result_path.exists()
        assert result_path.name == checkpoint_name
        
        # Verify metadata was saved
        metadata_path = result_path / "checkpoint_metadata.json"
        assert metadata_path.exists()
        
        with open(metadata_path, 'r') as f:
            saved_metadata = json.load(f)
        
        assert saved_metadata["test_key"] == "test_value"
        assert "created_at" in saved_metadata
        assert "model_type" in saved_metadata
    
    def test_save_checkpoint_with_experiment(self, checkpoint_manager, mock_model):
        """Test saving checkpoint with experiment organization."""
        checkpoint_name = "test_checkpoint"
        experiment_name = "test_experiment"
        
        def mock_save_pretrained(path):
            save_path = Path(path)
            save_path.mkdir(parents=True, exist_ok=True)
            (save_path / "config.json").write_text('{"model_type": "roberta"}')
            (save_path / "pytorch_model.bin").write_text("dummy_weights")
            (save_path / "tokenizer_config.json").write_text('{}')
            (save_path / "vocab.json").write_text('{}')
            (save_path / "merges.txt").write_text("")
        
        mock_model.save_pretrained.side_effect = mock_save_pretrained
        
        with patch.object(checkpoint_manager, 'validate_checkpoint', return_value=True):
            result_path = checkpoint_manager.save_checkpoint(
                mock_model, checkpoint_name, experiment_name=experiment_name
            )
        
        # Verify checkpoint was saved in experiments directory
        expected_path = checkpoint_manager.robbert_dir / "experiments" / experiment_name / checkpoint_name
        assert result_path == expected_path
        assert result_path.exists()
    
    def test_validate_checkpoint_missing_files(self, checkpoint_manager, temp_dir):
        """Test checkpoint validation with missing files."""
        checkpoint_dir = Path(temp_dir) / "invalid_checkpoint"
        checkpoint_dir.mkdir()
        
        # Create only some required files
        (checkpoint_dir / "config.json").write_text('{"model_type": "roberta"}')
        
        result = checkpoint_manager.validate_checkpoint(checkpoint_dir)
        assert result is False
    
    def test_validate_checkpoint_valid(self, checkpoint_manager, temp_dir):
        """Test checkpoint validation with valid checkpoint."""
        checkpoint_dir = Path(temp_dir) / "valid_checkpoint"
        checkpoint_dir.mkdir()
        
        # Create all required files
        (checkpoint_dir / "config.json").write_text(json.dumps({
            "model_type": "roberta",
            "hidden_size": 768,
            "vocab_size": 50265
        }))
        (checkpoint_dir / "pytorch_model.bin").write_text("dummy_weights")
        (checkpoint_dir / "tokenizer_config.json").write_text('{}')
        (checkpoint_dir / "vocab.json").write_text('{}')
        (checkpoint_dir / "merges.txt").write_text("")
        
        # Mock torch.load for model weights validation
        mock_state_dict = {
            'roberta.embeddings.word_embeddings.weight': torch.randn(50265, 768),
            'roberta.embeddings.position_embeddings.weight': torch.randn(514, 768),
            'roberta.encoder.layer.0.attention.self.query.weight': torch.randn(768, 768)
        }
        
        with patch('torch.load', return_value=mock_state_dict), \
             patch('transformers.AutoConfig.from_pretrained') as mock_config, \
             patch('transformers.AutoTokenizer.from_pretrained') as mock_tokenizer:
            
            # Mock config
            mock_config_obj = Mock()
            mock_config_obj.model_type = 'roberta'
            mock_config_obj.hidden_size = 768
            mock_config.return_value = mock_config_obj
            
            # Mock tokenizer
            mock_tokenizer_obj = Mock()
            mock_tokenizer_obj.is_fast = True
            mock_tokenizer_obj.get_vocab.return_value = {f"token_{i}": i for i in range(50265)}
            mock_tokenizer_obj.tokenize.return_value = ["test", "tokens"]
            mock_tokenizer.return_value = mock_tokenizer_obj
            
            result = checkpoint_manager.validate_checkpoint(checkpoint_dir)
            assert result is True
    
    def test_list_checkpoints_empty(self, checkpoint_manager):
        """Test listing checkpoints when none exist."""
        checkpoints = checkpoint_manager.list_checkpoints()
        assert checkpoints == []
    
    def test_list_checkpoints_with_data(self, checkpoint_manager, temp_dir):
        """Test listing checkpoints with existing data."""
        # Create mock checkpoint directories
        fine_tuned_dir = checkpoint_manager.robbert_dir / "fine-tuned" / "checkpoint1"
        fine_tuned_dir.mkdir(parents=True)
        
        experiments_dir = checkpoint_manager.robbert_dir / "experiments" / "exp1" / "checkpoint2"
        experiments_dir.mkdir(parents=True)
        
        # Add metadata to one checkpoint
        metadata = {
            "checkpoint_name": "checkpoint1",
            "model_heads": ["ner", "compliance"],
            "epochs": 3
        }
        
        metadata_path = fine_tuned_dir / "checkpoint_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f)
        
        # Mock validation
        with patch.object(checkpoint_manager, 'validate_checkpoint', return_value=True):
            checkpoints = checkpoint_manager.list_checkpoints()
        
        assert len(checkpoints) == 2
        
        # Find checkpoint1 in results
        checkpoint1 = next((c for c in checkpoints if c['name'] == 'checkpoint1'), None)
        assert checkpoint1 is not None
        assert checkpoint1['checkpoint_name'] == 'checkpoint1'
        assert checkpoint1['model_heads'] == ['ner', 'compliance']
        assert checkpoint1['epochs'] == 3
    
    def test_backup_checkpoint(self, checkpoint_manager, temp_dir):
        """Test checkpoint backup functionality."""
        # Create source checkpoint
        source_dir = checkpoint_manager.robbert_dir / "fine-tuned" / "source_checkpoint"
        source_dir.mkdir(parents=True)
        (source_dir / "config.json").write_text('{"test": "data"}')
        (source_dir / "model.bin").write_text("model_data")
        
        backup_path = checkpoint_manager.backup_checkpoint("source_checkpoint", "test_backup")
        
        # Verify backup was created
        assert backup_path.exists()
        assert backup_path.name == "test_backup"
        assert (backup_path / "config.json").exists()
        assert (backup_path / "model.bin").exists()
        assert (backup_path / "backup_metadata.json").exists()
        
        # Verify backup metadata
        with open(backup_path / "backup_metadata.json", 'r') as f:
            backup_metadata = json.load(f)
        
        assert backup_metadata["backup_name"] == "test_backup"
        assert "backup_created_at" in backup_metadata
    
    def test_cleanup_old_checkpoints(self, checkpoint_manager):
        """Test cleanup of old checkpoints."""
        # Create multiple checkpoints
        checkpoints_dir = checkpoint_manager.robbert_dir / "fine-tuned"
        
        checkpoint_names = [f"checkpoint_{i}" for i in range(7)]
        for name in checkpoint_names:
            checkpoint_dir = checkpoints_dir / name
            checkpoint_dir.mkdir(parents=True)
            (checkpoint_dir / "config.json").write_text('{}')
        
        # Mock list_checkpoints to return sorted list
        mock_checkpoints = [
            {"name": name, "path": str(checkpoints_dir / name), "created_at": f"2025-01-{i:02d}T10:00:00"}
            for i, name in enumerate(checkpoint_names, 1)
        ]
        
        with patch.object(checkpoint_manager, 'list_checkpoints', return_value=mock_checkpoints), \
             patch.object(checkpoint_manager, 'backup_checkpoint') as mock_backup, \
             patch('shutil.rmtree') as mock_rmtree:
            
            checkpoint_manager.cleanup_old_checkpoints(keep_count=3)
            
            # Should backup and remove 4 oldest checkpoints
            assert mock_backup.call_count == 4
            assert mock_rmtree.call_count == 4
    
    def test_resolve_checkpoint_path(self, checkpoint_manager):
        """Test checkpoint path resolution."""
        # Test absolute path (use platform-appropriate absolute path)
        if os.name == 'nt':  # Windows
            abs_path = Path("C:/absolute/path/checkpoint")
        else:  # Unix-like
            abs_path = Path("/absolute/path/checkpoint")
        result = checkpoint_manager._resolve_checkpoint_path(str(abs_path))
        assert result == abs_path
        
        # Test relative path with separators
        rel_path = "experiments/exp1/checkpoint"
        result = checkpoint_manager._resolve_checkpoint_path(rel_path)
        expected = checkpoint_manager.base_dir / rel_path
        assert result == expected
        
        # Test simple name (should default to fine-tuned)
        simple_name = "checkpoint1"
        result = checkpoint_manager._resolve_checkpoint_path(simple_name)
        expected = checkpoint_manager.robbert_dir / "fine-tuned" / simple_name
        assert result == expected


class TestConvenienceFunctions:
    """Test convenience functions for checkpoint management."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_model(self):
        """Create mock model for testing."""
        model = Mock(spec=MultiTaskRobBERT)
        model.heads = {'ner': Mock()}
        model.parameters.return_value = [torch.randn(100, 100)]
        return model
    
    def test_save_robbert_checkpoint(self, temp_dir, mock_model):
        """Test save_robbert_checkpoint convenience function."""
        def mock_save_pretrained(path):
            save_path = Path(path)
            save_path.mkdir(parents=True, exist_ok=True)
            (save_path / "config.json").write_text('{"model_type": "roberta"}')
            (save_path / "pytorch_model.bin").write_text("dummy")
            (save_path / "tokenizer_config.json").write_text('{}')
            (save_path / "vocab.json").write_text('{}')
            (save_path / "merges.txt").write_text("")
        
        mock_model.save_pretrained.side_effect = mock_save_pretrained
        
        with patch('src.utils.checkpoint_manager.CheckpointManager.validate_checkpoint', return_value=True):
            result = save_robbert_checkpoint(
                model=mock_model,
                checkpoint_name="test_checkpoint",
                base_dir=temp_dir
            )
        
        assert result.exists()
        assert result.name == "test_checkpoint"
    
    def test_load_robbert_checkpoint(self, temp_dir):
        """Test load_robbert_checkpoint convenience function."""
        # Create mock checkpoint directory
        checkpoint_dir = Path(temp_dir) / "robbert2023-per" / "fine-tuned" / "test_checkpoint"
        checkpoint_dir.mkdir(parents=True)
        
        # Create required files
        (checkpoint_dir / "config.json").write_text('{"model_type": "roberta"}')
        (checkpoint_dir / "pytorch_model.bin").write_text("dummy")
        (checkpoint_dir / "tokenizer_config.json").write_text('{}')
        (checkpoint_dir / "vocab.json").write_text('{}')
        (checkpoint_dir / "merges.txt").write_text("")
        
        with patch('src.utils.checkpoint_manager.MultiTaskRobBERT.load_pretrained') as mock_load:
            mock_model = Mock(spec=MultiTaskRobBERT)
            mock_load.return_value = mock_model
            
            with patch('src.utils.checkpoint_manager.CheckpointManager.validate_checkpoint', return_value=True):
                result = load_robbert_checkpoint("test_checkpoint", base_dir=temp_dir)
            
            assert result == mock_model
            mock_load.assert_called_once()
    
    def test_list_robbert_checkpoints(self, temp_dir):
        """Test list_robbert_checkpoints convenience function."""
        with patch('src.utils.checkpoint_manager.CheckpointManager.list_checkpoints') as mock_list:
            mock_list.return_value = [{"name": "test_checkpoint"}]
            
            result = list_robbert_checkpoints(base_dir=temp_dir)
            
            assert result == [{"name": "test_checkpoint"}]
            mock_list.assert_called_once_with(None)


class TestErrorHandling:
    """Test error handling in checkpoint management."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    def test_save_checkpoint_failure(self, temp_dir):
        """Test handling of save checkpoint failures."""
        manager = CheckpointManager(base_dir=temp_dir)
        
        mock_model = Mock(spec=MultiTaskRobBERT)
        mock_model.save_pretrained.side_effect = Exception("Save failed")
        mock_model.heads = {'ner': Mock()}
        mock_model.parameters.return_value = [torch.randn(10, 10)]
        
        with pytest.raises(ModelLoadingError, match="Failed to save checkpoint"):
            manager.save_checkpoint(mock_model, "test_checkpoint")
    
    def test_load_checkpoint_not_found(self, temp_dir):
        """Test loading non-existent checkpoint."""
        manager = CheckpointManager(base_dir=temp_dir)
        
        with pytest.raises(ModelLoadingError, match="Checkpoint not found"):
            manager.load_checkpoint("nonexistent_checkpoint")
    
    def test_backup_checkpoint_not_found(self, temp_dir):
        """Test backing up non-existent checkpoint."""
        manager = CheckpointManager(base_dir=temp_dir)
        
        with pytest.raises(ModelLoadingError, match="Failed to backup checkpoint"):
            manager.backup_checkpoint("nonexistent_checkpoint")


if __name__ == '__main__':
    pytest.main([__file__])
