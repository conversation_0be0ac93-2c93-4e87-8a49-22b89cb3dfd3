"""
Tests for dynamic label discovery and BIO tagging system.
"""

import pytest
import json
import tempfile
from pathlib import Path
from typing import List, Dict, Any

from src.data.label_discovery import (
    EntitySpan,
    NERExample,
    LabelDiscovery,
    discover_labels_from_dataset,
    create_bio_scheme_for_labels,
    validate_bio_sequence
)


class TestEntitySpan:
    """Test EntitySpan Pydantic model."""
    
    def test_valid_entity_span(self):
        """Test creating valid EntitySpan."""
        entity = EntitySpan(
            text="<PERSON>",
            label="PER",
            start=0,
            end=10
        )
        
        assert entity.text == "<PERSON>"
        assert entity.label == "PER"
        assert entity.start == 0
        assert entity.end == 10
        assert entity.is_explicit_span is True
        assert entity.span_length == 10
    
    def test_entity_span_without_positions(self):
        """Test EntitySpan without explicit positions."""
        entity = EntitySpan(text="Amsterdam", label="LOC")
        
        assert entity.text == "Amsterdam"
        assert entity.label == "LOC"
        assert entity.start is None
        assert entity.end is None
        assert entity.is_explicit_span is False
        assert entity.span_length is None
    
    def test_entity_span_text_normalization(self):
        """Test text normalization (whitespace stripping)."""
        entity = EntitySpan(text="  Jan <PERSON><PERSON>  ", label="PER")
        assert entity.text == "<PERSON> <PERSON>sen"
    
    def test_entity_span_label_normalization(self):
        """Test label normalization (uppercase)."""
        entity = EntitySpan(text="Jan", label="per")
        assert entity.label == "PER"
        
        entity = EntitySpan(text="Amsterdam", label="location")
        assert entity.label == "LOCATION"
    
    def test_invalid_empty_text(self):
        """Test validation of empty text."""
        with pytest.raises(ValueError, match="Entity text cannot be empty"):
            EntitySpan(text="", label="PER")
        
        with pytest.raises(ValueError, match="Entity text cannot be empty"):
            EntitySpan(text="   ", label="PER")
    
    def test_invalid_empty_label(self):
        """Test validation of empty label."""
        with pytest.raises(ValueError, match="Entity label cannot be empty"):
            EntitySpan(text="Jan", label="")
        
        with pytest.raises(ValueError, match="Entity label cannot be empty"):
            EntitySpan(text="Jan", label="   ")
    
    def test_invalid_span_positions(self):
        """Test validation of span positions."""
        # Negative start
        with pytest.raises(ValueError, match="Start position cannot be negative"):
            EntitySpan(text="Jan", label="PER", start=-1, end=3)
        
        # End <= start
        with pytest.raises(ValueError, match="End position .* must be greater than start position"):
            EntitySpan(text="Jan", label="PER", start=5, end=5)
        
        with pytest.raises(ValueError, match="End position .* must be greater than start position"):
            EntitySpan(text="Jan", label="PER", start=5, end=3)
    
    def test_inconsistent_span_length(self):
        """Test validation of span length consistency."""
        with pytest.raises(ValueError, match="Text length .* doesn't match span length"):
            EntitySpan(text="Jan", label="PER", start=0, end=5)  # Text is 3 chars, span is 5
    
    def test_partial_span_positions(self):
        """Test validation when only start or end is provided."""
        with pytest.raises(ValueError, match="Both start and end must be provided together"):
            EntitySpan(text="Jan", label="PER", start=0)  # Missing end
        
        with pytest.raises(ValueError, match="Both start and end must be provided together"):
            EntitySpan(text="Jan", label="PER", end=3)  # Missing start


class TestNERExample:
    """Test NERExample Pydantic model."""
    
    def test_valid_ner_example(self):
        """Test creating valid NER example."""
        entities = [
            EntitySpan(text="Jan Jansen", label="PER", start=0, end=10),
            EntitySpan(text="Amsterdam", label="LOC", start=20, end=29)
        ]
        
        example = NERExample(
            id=1,
            sentence="Jan Jansen woont in Amsterdam.",
            entities=entities
        )
        
        assert example.id == 1
        assert example.sentence == "Jan Jansen woont in Amsterdam."
        assert len(example.entities) == 2
        assert example.entity_count == 2
        assert example.entity_labels == {"PER", "LOC"}
    
    def test_ner_example_without_entities(self):
        """Test NER example without entities."""
        example = NERExample(
            sentence="Dit is een gewone zin.",
            entities=[]
        )
        
        assert example.sentence == "Dit is een gewone zin."
        assert len(example.entities) == 0
        assert example.entity_count == 0
        assert example.entity_labels == set()
    
    def test_sentence_normalization(self):
        """Test sentence normalization (whitespace stripping)."""
        example = NERExample(sentence="  Test sentence  ")
        assert example.sentence == "Test sentence"
    
    def test_invalid_empty_sentence(self):
        """Test validation of empty sentence."""
        with pytest.raises(ValueError, match="Sentence cannot be empty"):
            NERExample(sentence="")
        
        with pytest.raises(ValueError, match="Sentence cannot be empty"):
            NERExample(sentence="   ")
    
    def test_entity_span_validation(self):
        """Test validation of entity spans against sentence."""
        sentence = "Jan Jansen woont in Amsterdam."
        
        # Valid entity
        valid_entity = EntitySpan(text="Jan Jansen", label="PER", start=0, end=10)
        example = NERExample(sentence=sentence, entities=[valid_entity])
        assert len(example.entities) == 1
        
        # Entity span extends beyond sentence (create valid EntitySpan first, then test NERExample validation)
        with pytest.raises(ValueError, match="Entity span extends beyond sentence length"):
            # Create a valid EntitySpan that would extend beyond the sentence
            long_text = "a" * 50  # 50 characters
            invalid_entity = EntitySpan(text=long_text, label="PER", start=0, end=50)
            NERExample(sentence=sentence, entities=[invalid_entity])
        
        # Entity text doesn't match span
        with pytest.raises(ValueError, match="Entity text .* doesn't match span"):
            invalid_entity = EntitySpan(text="Wrong Text", label="PER", start=0, end=10)
            NERExample(sentence=sentence, entities=[invalid_entity])
    
    def test_get_entities_by_label(self):
        """Test filtering entities by label."""
        entities = [
            EntitySpan(text="Jan", label="PER"),
            EntitySpan(text="Amsterdam", label="LOC"),
            EntitySpan(text="Marie", label="PER"),
            EntitySpan(text="Google", label="ORG")
        ]
        
        example = NERExample(sentence="Test sentence", entities=entities)
        
        per_entities = example.get_entities_by_label("PER")
        assert len(per_entities) == 2
        assert all(e.label == "PER" for e in per_entities)
        
        loc_entities = example.get_entities_by_label("LOC")
        assert len(loc_entities) == 1
        assert loc_entities[0].text == "Amsterdam"
        
        misc_entities = example.get_entities_by_label("MISC")
        assert len(misc_entities) == 0


class TestLabelDiscovery:
    """Test LabelDiscovery class."""
    
    def test_discover_labels_from_examples(self):
        """Test label discovery from NER examples."""
        examples = [
            NERExample(
                sentence="Jan woont in Amsterdam.",
                entities=[
                    EntitySpan(text="Jan", label="PER"),
                    EntitySpan(text="Amsterdam", label="LOC")
                ]
            ),
            NERExample(
                sentence="Marie werkt bij Google.",
                entities=[
                    EntitySpan(text="Marie", label="PER"),
                    EntitySpan(text="Google", label="ORG")
                ]
            ),
            NERExample(
                sentence="Piet is een persoon.",
                entities=[
                    EntitySpan(text="Piet", label="PER")
                ]
            )
        ]
        
        discovery = LabelDiscovery()
        labels = discovery.discover_labels_from_examples(examples)
        
        assert labels == {"PER", "LOC", "ORG"}
        assert discovery.discovered_labels == {"PER", "LOC", "ORG"}
        
        stats = discovery.get_label_statistics()
        assert stats["discovered_labels"] == ["LOC", "ORG", "PER"]  # Sorted
        assert stats["label_counts"]["PER"] == 3
        assert stats["label_counts"]["LOC"] == 1
        assert stats["label_counts"]["ORG"] == 1
        assert stats["total_entities"] == 5
    
    def test_create_bio_scheme(self):
        """Test BIO scheme creation."""
        discovery = LabelDiscovery()
        
        # Test with specific labels
        labels = {"PER", "LOC", "ORG"}
        bio_scheme = discovery.create_bio_scheme(labels)
        
        expected = ["O", "B-LOC", "I-LOC", "B-ORG", "I-ORG", "B-PER", "I-PER"]
        assert bio_scheme == expected
        assert discovery.bio_scheme == expected
    
    def test_create_bio_scheme_default(self):
        """Test BIO scheme creation with default PER label."""
        discovery = LabelDiscovery()
        bio_scheme = discovery.create_bio_scheme(set())  # Empty set
        
        expected = ["O", "B-PER", "I-PER"]
        assert bio_scheme == expected
    
    def test_create_label_mappings(self):
        """Test label mapping creation."""
        discovery = LabelDiscovery()
        bio_scheme = ["O", "B-PER", "I-PER", "B-LOC", "I-LOC"]
        
        label2id, id2label = discovery.create_label_mappings(bio_scheme)
        
        expected_label2id = {
            "O": 0,
            "B-PER": 1,
            "I-PER": 2,
            "B-LOC": 3,
            "I-LOC": 4
        }
        expected_id2label = {v: k for k, v in expected_label2id.items()}
        
        assert label2id == expected_label2id
        assert id2label == expected_id2label
        assert discovery.label2id == expected_label2id
        assert discovery.id2label == expected_id2label
    
    def test_validate_bio_tagging_valid(self):
        """Test BIO validation with valid sequence."""
        discovery = LabelDiscovery()
        
        valid_labels = ["O", "B-PER", "I-PER", "O", "B-LOC", "O"]
        result = discovery.validate_bio_tagging(valid_labels)
        
        assert result == valid_labels  # Should be unchanged
    
    def test_validate_bio_tagging_isolated_i(self):
        """Test BIO validation with isolated I- labels."""
        discovery = LabelDiscovery()
        
        # Isolated I- at beginning
        labels = ["I-PER", "O", "B-LOC"]
        result = discovery.validate_bio_tagging(labels)
        assert result == ["B-PER", "O", "B-LOC"]
        
        # Isolated I- in middle
        labels = ["O", "I-PER", "O"]
        result = discovery.validate_bio_tagging(labels)
        assert result == ["O", "B-PER", "O"]
    
    def test_validate_bio_tagging_invalid_continuation(self):
        """Test BIO validation with invalid I- continuation."""
        discovery = LabelDiscovery()
        
        # I-PER after B-LOC (different entity type)
        labels = ["B-LOC", "I-PER", "O"]
        result = discovery.validate_bio_tagging(labels)
        assert result == ["B-LOC", "B-PER", "O"]
        
        # I-PER after O
        labels = ["O", "I-PER", "O"]
        result = discovery.validate_bio_tagging(labels)
        assert result == ["O", "B-PER", "O"]
    
    def test_validate_bio_tagging_unknown_labels(self):
        """Test BIO validation with unknown labels."""
        discovery = LabelDiscovery()
        
        labels = ["O", "UNKNOWN", "B-PER"]
        result = discovery.validate_bio_tagging(labels)
        assert result == ["O", "O", "B-PER"]
    
    def test_discover_labels_from_file_json(self):
        """Test label discovery from JSON file."""
        data = [
            {
                "id": 1,
                "sentence": "Jan woont in Amsterdam.",
                "entities": [
                    {"text": "Jan", "label": "PER"},
                    {"text": "Amsterdam", "label": "LOC"}
                ]
            },
            {
                "id": 2,
                "sentence": "Marie werkt bij Google.",
                "entities": [
                    {"text": "Marie", "label": "PER"},
                    {"text": "Google", "label": "ORG"}
                ]
            }
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(data, f)
            temp_path = f.name
        
        try:
            discovery = LabelDiscovery()
            labels = discovery.discover_labels_from_data(temp_path)
            
            assert labels == {"PER", "LOC", "ORG"}
        finally:
            Path(temp_path).unlink()
    
    def test_discover_labels_from_file_jsonl(self):
        """Test label discovery from JSONL file."""
        data = [
            {"sentence": "Jan test.", "entities": [{"text": "Jan", "label": "PER"}]},
            {"sentence": "Amsterdam test.", "entities": [{"text": "Amsterdam", "label": "LOC"}]}
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
            for item in data:
                f.write(json.dumps(item) + '\n')
            temp_path = f.name
        
        try:
            discovery = LabelDiscovery()
            labels = discovery.discover_labels_from_data(temp_path)
            
            assert labels == {"PER", "LOC"}
        finally:
            Path(temp_path).unlink()
    
    def test_discover_labels_from_list(self):
        """Test label discovery from list of dictionaries."""
        data = [
            {
                "sentence": "Test sentence.",
                "entities": [{"text": "Test", "label": "MISC"}]
            }
        ]
        
        discovery = LabelDiscovery()
        labels = discovery.discover_labels_from_data(data)
        
        assert labels == {"MISC"}


class TestConvenienceFunctions:
    """Test convenience functions."""
    
    def test_discover_labels_from_dataset(self):
        """Test discover_labels_from_dataset function."""
        data = [
            {
                "sentence": "Jan woont in Amsterdam.",
                "entities": [
                    {"text": "Jan", "label": "PER"},
                    {"text": "Amsterdam", "label": "LOC"}
                ]
            }
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(data, f)
            temp_path = f.name
        
        try:
            labels, bio_scheme, label2id, id2label = discover_labels_from_dataset(temp_path)
            
            assert labels == {"PER", "LOC"}
            assert bio_scheme == ["O", "B-LOC", "I-LOC", "B-PER", "I-PER"]
            assert label2id is not None
            assert id2label is not None
            assert len(label2id) == 5
            
            # Test without mappings
            labels2, bio_scheme2, label2id2, id2label2 = discover_labels_from_dataset(
                temp_path, create_mappings=False
            )
            
            assert labels2 == labels
            assert bio_scheme2 == bio_scheme
            assert label2id2 is None
            assert id2label2 is None
            
        finally:
            Path(temp_path).unlink()
    
    def test_create_bio_scheme_for_labels(self):
        """Test create_bio_scheme_for_labels function."""
        entity_labels = ["PER", "LOC", "ORG"]
        
        bio_scheme, label2id, id2label = create_bio_scheme_for_labels(entity_labels)
        
        expected_bio = ["O", "B-LOC", "I-LOC", "B-ORG", "I-ORG", "B-PER", "I-PER"]
        assert bio_scheme == expected_bio
        assert len(label2id) == 7
        assert len(id2label) == 7
        assert label2id["O"] == 0
        assert label2id["B-PER"] == 5
        assert id2label[0] == "O"
        assert id2label[5] == "B-PER"
    
    def test_validate_bio_sequence(self):
        """Test validate_bio_sequence function."""
        # Valid sequence
        valid_labels = ["O", "B-PER", "I-PER", "O"]
        result = validate_bio_sequence(valid_labels)
        assert result == valid_labels
        
        # Invalid sequence with isolated I-
        invalid_labels = ["I-PER", "O", "I-LOC"]
        result = validate_bio_sequence(invalid_labels)
        assert result == ["B-PER", "O", "B-LOC"]


class TestIntegration:
    """Integration tests for the complete label discovery system."""
    
    def test_end_to_end_workflow(self):
        """Test complete workflow from data to BIO scheme."""
        # Sample data with multiple entity types
        data = [
            {
                "id": 1,
                "sentence": "Jan Jansen woont in Amsterdam en werkt bij Google.",
                "entities": [
                    {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10},
                    {"text": "Amsterdam", "label": "LOC", "start": 20, "end": 29},
                    {"text": "Google", "label": "ORG", "start": 43, "end": 49}
                ]
            },
            {
                "id": 2,
                "sentence": "Marie de Wit bezoekt Parijs.",
                "entities": [
                    {"text": "Marie de Wit", "label": "PER"},
                    {"text": "Parijs", "label": "LOC"}
                ]
            }
        ]
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(data, f)
            temp_path = f.name
        
        try:
            # Discover labels and create complete BIO scheme
            discovery = LabelDiscovery()
            
            # Step 1: Discover labels
            labels = discovery.discover_labels_from_data(temp_path)
            assert labels == {"PER", "LOC", "ORG"}
            
            # Step 2: Create BIO scheme
            bio_scheme = discovery.create_bio_scheme(labels)
            expected_bio = ["O", "B-LOC", "I-LOC", "B-ORG", "I-ORG", "B-PER", "I-PER"]
            assert bio_scheme == expected_bio
            
            # Step 3: Create mappings
            label2id, id2label = discovery.create_label_mappings(bio_scheme)
            assert len(label2id) == 7
            assert len(id2label) == 7
            
            # Step 4: Validate statistics
            stats = discovery.get_label_statistics()
            assert stats["total_labels"] == 3
            assert stats["total_entities"] == 5
            assert stats["label_counts"]["PER"] == 2
            assert stats["label_counts"]["LOC"] == 2
            assert stats["label_counts"]["ORG"] == 1
            
            # Step 5: Test BIO validation
            test_sequence = ["B-PER", "I-PER", "O", "I-LOC", "B-ORG"]  # Invalid I-LOC
            corrected = discovery.validate_bio_tagging(test_sequence)
            assert corrected == ["B-PER", "I-PER", "O", "B-LOC", "B-ORG"]
            
        finally:
            Path(temp_path).unlink()
    
    def test_multiple_entity_types_support(self):
        """Test support for multiple entity types including custom ones."""
        data = [
            {
                "sentence": "Dr. Jan Jansen van het UMC Amsterdam ontdekte een nieuwe behandeling.",
                "entities": [
                    {"text": "Dr. Jan Jansen", "label": "PER"},
                    {"text": "UMC Amsterdam", "label": "ORG"},
                    {"text": "Amsterdam", "label": "LOC"},
                    {"text": "behandeling", "label": "TREATMENT"}  # Custom entity type
                ]
            }
        ]
        
        discovery = LabelDiscovery()
        labels = discovery.discover_labels_from_data(data)
        
        assert labels == {"PER", "ORG", "LOC", "TREATMENT"}
        
        bio_scheme = discovery.create_bio_scheme(labels)
        expected = ["O", "B-LOC", "I-LOC", "B-ORG", "I-ORG", "B-PER", "I-PER", "B-TREATMENT", "I-TREATMENT"]
        assert bio_scheme == expected
        
        label2id, id2label = discovery.create_label_mappings(bio_scheme)
        assert "B-TREATMENT" in label2id
        assert "I-TREATMENT" in label2id
        assert label2id["B-TREATMENT"] == 7
        assert label2id["I-TREATMENT"] == 8