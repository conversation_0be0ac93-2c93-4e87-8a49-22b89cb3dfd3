#!/usr/bin/env python3
"""
Example script demonstrating single-head training for RobBERT-2023 NER.

This script shows how to use the per-head training isolation system to train
individual entity type heads with different filtering strategies.
"""

import sys
import json
import tempfile
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.training.hf_config import HFTrainingConfig
from src.training.hf_single_head_trainer import train_single_head, SingleHeadTrainer
from src.training.batch_train_heads import BatchHeadTrainer


def create_sample_data():
    """Create sample Dutch NER data for demonstration."""
    sample_data = [
        {
            "id": 1,
            "sentence": "<PERSON> woon<PERSON> in Amsterdam en werkt bij Google.",
            "entities": [
                {"text": "<PERSON>", "label": "PER", "start": 0, "end": 10},
                {"text": "Amsterdam", "label": "LOC", "start": 20, "end": 29},
                {"text": "Google", "label": "ORG", "start": 43, "end": 49}
            ]
        },
        {
            "id": 2,
            "sentence": "<PERSON> is geboren in Rotterdam.",
            "entities": [
                {"text": "<PERSON>it", "label": "PER", "start": 0, "end": 12},
                {"text": "Rotterdam", "label": "LOC", "start": 27, "end": 36}
            ]
        },
        {
            "id": 3,
            "sentence": "Het bedrijf Microsoft heeft kantoren in Nederland.",
            "entities": [
                {"text": "Microsoft", "label": "ORG", "start": 12, "end": 21},
                {"text": "Nederland", "label": "LOC", "start": 41, "end": 50}
            ]
        },
        {
            "id": 4,
            "sentence": "Piet Bakker werkt als ontwikkelaar.",
            "entities": [
                {"text": "Piet Bakker", "label": "PER", "start": 0, "end": 11}
            ]
        },
        {
            "id": 5,
            "sentence": "De hoofdstad van Frankrijk is Parijs.",
            "entities": [
                {"text": "Frankrijk", "label": "LOC", "start": 17, "end": 26},
                {"text": "Parijs", "label": "LOC", "start": 30, "end": 36}
            ]
        },
        {
            "id": 6,
            "sentence": "Dit is een zin zonder entiteiten.",
            "entities": []
        },
        {
            "id": 7,
            "sentence": "Apple en Samsung zijn grote technologiebedrijven.",
            "entities": [
                {"text": "Apple", "label": "ORG", "start": 0, "end": 5},
                {"text": "Samsung", "label": "ORG", "start": 9, "end": 16}
            ]
        },
        {
            "id": 8,
            "sentence": "Lisa van der Berg woont in Den Haag.",
            "entities": [
                {"text": "Lisa van der Berg", "label": "PER", "start": 0, "end": 17},
                {"text": "Den Haag", "label": "LOC", "start": 27, "end": 35}
            ]
        }
    ]
    
    return sample_data


def example_1_basic_single_head_training():
    """Example 1: Basic single-head training for PER entities."""
    print("=" * 60)
    print("Example 1: Basic Single-Head Training (PER entities)")
    print("=" * 60)
    
    # Create sample data
    sample_data = create_sample_data()
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(sample_data, f, indent=2)
        data_path = f.name
    
    try:
        # Create test configuration
        config = HFTrainingConfig(
            test_mode=True,
            test_epochs=1,
            test_sample_limit=8,
            test_disable_wandb=True,
            batch_size=2,
            eval_steps=2,
            logging_steps=1,
            save_steps=4,  # Make it a multiple of eval_steps
            load_best_model_at_end=False,  # Disable for example
            early_stopping_patience=0,  # Disable early stopping
            run_version="example_v1.0"
        )
        
        print(f"Training PER head with strict filtering...")
        print(f"Data: {len(sample_data)} examples")
        print(f"Configuration: {config.test_epochs} epoch, batch size {config.batch_size}")
        
        # Train PER head with strict filtering
        result = train_single_head(
            entity_type="PER",
            data_path=data_path,
            filtering_strategy="strict",
            config=config
        )
        
        print(f"\nTraining completed!")
        print(f"Entity type: {result['entity_type']}")
        print(f"Filtering strategy: {result['filtering_strategy']}")
        print(f"Label scheme: {result['head_label_scheme']}")
        print(f"Output directory: {result['output_dir']}")
        
        # Print dataset statistics
        stats = result['dataset_statistics']
        print(f"\nDataset Statistics:")
        print(f"  Train size: {stats['train_size']}")
        print(f"  Validation size: {stats['val_size']}")
        print(f"  Entity coverage: {stats['entity_coverage']:.1f}%")
        
        # Print filter statistics
        filter_stats = result['filter_statistics']['train']
        print(f"\nFiltering Statistics (Train):")
        print(f"  Original size: {filter_stats['original_size']}")
        print(f"  Filtered size: {filter_stats['filtered_size']}")
        print(f"  Examples removed: {filter_stats['examples_removed']}")
        print(f"  Removal rate: {filter_stats['removal_rate']:.1%}")
        
        # Print evaluation metrics
        if 'eval_result' in result:
            print(f"\nEvaluation Metrics:")
            for key, value in result['eval_result'].items():
                if isinstance(value, (int, float)):
                    print(f"  {key}: {value:.4f}")
        
    finally:
        # Clean up
        Path(data_path).unlink(missing_ok=True)


def example_2_filtering_strategy_comparison():
    """Example 2: Compare strict vs mixed filtering strategies."""
    print("\n" + "=" * 60)
    print("Example 2: Filtering Strategy Comparison (LOC entities)")
    print("=" * 60)
    
    # Create sample data
    sample_data = create_sample_data()
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(sample_data, f, indent=2)
        data_path = f.name
    
    try:
        # Create test configuration
        config = HFTrainingConfig(
            test_mode=True,
            test_epochs=1,
            test_sample_limit=8,
            test_disable_wandb=True,
            batch_size=2,
            eval_steps=2,
            logging_steps=1,
            save_steps=5,
            run_version="example_v1.0"
        )
        
        strategies = ["strict", "mixed"]
        results = {}
        
        for strategy in strategies:
            print(f"\nTraining LOC head with {strategy} filtering...")
            
            result = train_single_head(
                entity_type="LOC",
                data_path=data_path,
                filtering_strategy=strategy,
                config=config
            )
            
            results[strategy] = result
            
            # Print filtering statistics
            filter_stats = result['filter_statistics']['train']
            print(f"  Strategy: {strategy}")
            print(f"  Original size: {filter_stats['original_size']}")
            print(f"  Filtered size: {filter_stats['filtered_size']}")
            print(f"  Examples removed: {filter_stats['examples_removed']}")
            print(f"  Removal rate: {filter_stats['removal_rate']:.1%}")
        
        # Compare results
        print(f"\nFiltering Strategy Comparison:")
        print(f"{'Strategy':<10} {'Original':<10} {'Filtered':<10} {'Removed':<10} {'Rate':<10}")
        print("-" * 50)
        
        for strategy in strategies:
            filter_stats = results[strategy]['filter_statistics']['train']
            print(f"{strategy:<10} {filter_stats['original_size']:<10} "
                  f"{filter_stats['filtered_size']:<10} {filter_stats['examples_removed']:<10} "
                  f"{filter_stats['removal_rate']:.1%}")
        
    finally:
        # Clean up
        Path(data_path).unlink(missing_ok=True)


def example_3_single_head_trainer_class():
    """Example 3: Using SingleHeadTrainer class directly."""
    print("\n" + "=" * 60)
    print("Example 3: Using SingleHeadTrainer Class (ORG entities)")
    print("=" * 60)
    
    # Create sample data
    sample_data = create_sample_data()
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(sample_data, f, indent=2)
        data_path = f.name
    
    try:
        # Create test configuration
        config = HFTrainingConfig(
            test_mode=True,
            test_epochs=1,
            test_sample_limit=8,
            test_disable_wandb=True,
            batch_size=2,
            eval_steps=2,
            logging_steps=1,
            save_steps=5,
            run_version="example_v1.0",
            wandb_project="single_head_example",
            wandb_entity="test_user"
        )
        
        print(f"Creating SingleHeadTrainer for ORG entities...")
        
        # Create trainer instance
        trainer = SingleHeadTrainer(config, "ORG", "mixed")
        
        print(f"Entity type: {trainer.entity_type}")
        print(f"Filtering strategy: {trainer.filtering_strategy}")
        
        # Create WandB run name
        run_name = trainer.create_wandb_run_name()
        print(f"WandB run name: {run_name}")
        
        # Create output directory
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = trainer.create_head_output_dir(temp_dir)
            print(f"Output directory: {output_dir}")
            
            # Prepare dataset
            print(f"\nPreparing dataset...")
            dataset_result = trainer.prepare_head_dataset(data_path)
            dataset_dict, head_labels, head_label2id, head_id2label, statistics, filter_stats = dataset_result
            
            print(f"Head label scheme: {head_labels}")
            print(f"Label mappings: {head_label2id}")
            
            # Print dataset info
            print(f"\nDataset Information:")
            print(f"  Train examples: {len(dataset_dict['train'])}")
            print(f"  Validation examples: {len(dataset_dict['validation'])}")
            print(f"  Entity coverage: {statistics.entity_coverage:.1f}%")
            
            # Print filter statistics
            train_filter_stats = filter_stats['train']
            print(f"\nFilter Statistics:")
            print(f"  Strategy: {train_filter_stats['filtering_strategy']}")
            print(f"  Examples removed: {train_filter_stats['examples_removed']}")
            print(f"  Removal rate: {train_filter_stats['removal_rate']:.1%}")
            
            print(f"\nNote: Full training would continue here with trainer.train_single_head()")
        
    finally:
        # Clean up
        Path(data_path).unlink(missing_ok=True)


def example_4_batch_training_setup():
    """Example 4: Setting up batch training for multiple heads."""
    print("\n" + "=" * 60)
    print("Example 4: Batch Training Setup")
    print("=" * 60)
    
    # Create sample data
    sample_data = create_sample_data()
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(sample_data, f, indent=2)
        data_path = f.name
    
    try:
        # Create test configuration
        config = HFTrainingConfig(
            test_mode=True,
            test_epochs=1,
            test_sample_limit=8,
            test_disable_wandb=True,
            batch_size=2,
            eval_steps=2,
            logging_steps=1,
            save_steps=5,
            run_version="batch_example_v1.0"
        )
        
        print(f"Setting up batch training for multiple entity types...")
        
        # Create batch trainer
        with tempfile.TemporaryDirectory() as temp_dir:
            batch_trainer = BatchHeadTrainer(config, temp_dir)
            
            entity_types = ["PER", "LOC", "ORG"]
            print(f"Entity types to train: {entity_types}")
            
            # Show what would happen in sequential training
            print(f"\nSequential Training Plan:")
            for i, entity_type in enumerate(entity_types, 1):
                output_dir = batch_trainer._create_head_output_dir(entity_type)
                print(f"  {i}. {entity_type} -> {Path(output_dir).name}")
            
            print(f"\nNote: Actual batch training would be run with:")
            print(f"  batch_trainer.train_heads_sequential(entity_types, data_path, 'strict')")
            print(f"  or")
            print(f"  batch_trainer.train_heads_parallel(entity_types, data_path, 'strict')")
        
    finally:
        # Clean up
        Path(data_path).unlink(missing_ok=True)


def example_5_label_scheme_demonstration():
    """Example 5: Demonstrate 3-label scheme per head."""
    print("\n" + "=" * 60)
    print("Example 5: 3-Label Scheme Demonstration")
    print("=" * 60)
    
    from src.training.hf_single_head_trainer import SingleHeadDatasetFilter
    from datasets import Dataset
    
    # Create sample dataset with multiple entity types
    sample_dataset_data = [
        {
            'input_ids': [0, 1, 2, 3, 4, 5, 6, 7],
            'attention_mask': [1, 1, 1, 1, 1, 1, 1, 1],
            'labels': [0, 1, 2, 0, 3, 4, 5, 6]  # O, B-PER, I-PER, O, B-LOC, I-LOC, B-ORG, I-ORG
        }
    ]
    
    original_labels = ["O", "B-PER", "I-PER", "B-LOC", "I-LOC", "B-ORG", "I-ORG"]
    dataset = Dataset.from_list(sample_dataset_data)
    
    print(f"Original label scheme: {original_labels}")
    print(f"Original labels in example: {sample_dataset_data[0]['labels']}")
    print(f"Meaning: {[original_labels[i] for i in sample_dataset_data[0]['labels']]}")
    
    entity_types = ["PER", "LOC", "ORG"]
    
    for entity_type in entity_types:
        print(f"\n{entity_type} Head (Strict Filtering):")
        
        filter_obj = SingleHeadDatasetFilter(entity_type, "strict")
        filtered_dataset, head_labels, head_label2id, head_id2label, stats = \
            filter_obj.filter_dataset_for_entity_type(dataset, original_labels)
        
        print(f"  Head label scheme: {head_labels}")
        print(f"  Label mappings: {head_label2id}")
        
        if len(filtered_dataset) > 0:
            converted_labels = filtered_dataset[0]['labels']
            print(f"  Converted labels: {converted_labels}")
            print(f"  Meaning: {[head_labels[i] if i < len(head_labels) else 'UNK' for i in converted_labels]}")
        else:
            print(f"  No examples after filtering (entity not present)")


def main():
    """Run all examples."""
    print("Single-Head Training Examples for RobBERT-2023 NER")
    print("=" * 60)
    print("This script demonstrates the per-head training isolation system")
    print("with different filtering strategies and configurations.")
    
    try:
        # Run examples
        example_1_basic_single_head_training()
        example_2_filtering_strategy_comparison()
        example_3_single_head_trainer_class()
        example_4_batch_training_setup()
        example_5_label_scheme_demonstration()
        
        print("\n" + "=" * 60)
        print("All examples completed successfully!")
        print("=" * 60)
        
        print("\nKey Features Demonstrated:")
        print("1. Basic single-head training with train_single_head() function")
        print("2. Comparison of strict vs mixed filtering strategies")
        print("3. Direct use of SingleHeadTrainer class")
        print("4. Batch training setup for multiple entity types")
        print("5. 3-label scheme conversion per head")
        
        print("\nNext Steps:")
        print("- Use CLI: python -m src.training.cli_single_head --entity-type PER --data your_data.json")
        print("- Batch training: python -m src.training.batch_train_heads --entity-types PER LOC ORG --data your_data.json")
        print("- Custom config: Create YAML config file and use --config parameter")
        
    except Exception as e:
        print(f"\nError running examples: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())