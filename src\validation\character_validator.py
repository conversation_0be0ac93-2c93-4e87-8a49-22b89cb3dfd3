"""
Character validation for BERTje tokenizer to ensure proper support for Dutch text.
"""

from typing import List, Dict, Set, Optional, Any
from dataclasses import dataclass
from transformers import AutoTokenizer
import logging

from ..exceptions import TokenizationError, ModelLoadingError
from ..utils.logging_utils import get_logger


@dataclass
class ValidationResult:
    """Result of character validation."""
    is_valid: bool
    supported_chars: Set[str]
    unsupported_chars: Set[str]
    coverage_percentage: float
    test_results: Dict[str, Any]
    error_message: Optional[str] = None


class CharacterValidator:
    """Validates BERTje tokenizer character coverage for Dutch text processing."""
    
    # Character sets as specified in the design document
    REQUIRED_CHARS = {
        'latin_base': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
        'latin_diacritics': 'ëïéèöêüçàûîñäô',
        'special_chars': '!@#$%^&*()-_+/=:;"\',?.\\€'
    }
    
    def __init__(self, model_name: str = "DTAI-KULeuven/robbert-2023-dutch-base"):
        """Initialize character validator with tokenizer."""
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.model_name = model_name
        self.tokenizer = None
        
        try:
            self.logger.logger.info(f"Loading tokenizer for character validation: {model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.logger.logger.info(f"Successfully loaded tokenizer with vocab size: {len(self.tokenizer)}")
        except Exception as e:
            error_msg = f"Failed to load tokenizer for character validation: {model_name}"
            self.logger.log_error(ModelLoadingError(error_msg, model_name=model_name))
            raise ModelLoadingError(error_msg, model_name=model_name) from e
    
    def get_all_required_chars(self) -> Set[str]:
        """Get all required characters as a single set."""
        all_chars = set()
        for char_set in self.REQUIRED_CHARS.values():
            all_chars.update(char_set)
        return all_chars
    
    def validate_tokenizer_coverage(self, tokenizer=None) -> ValidationResult:
        """
        Validate that the tokenizer supports all required characters.
        
        Args:
            tokenizer: Optional tokenizer to validate (uses self.tokenizer if None)
            
        Returns:
            ValidationResult with coverage information
        """
        if tokenizer is None:
            tokenizer = self.tokenizer
        
        if tokenizer is None:
            return ValidationResult(
                is_valid=False,
                supported_chars=set(),
                unsupported_chars=set(),
                coverage_percentage=0.0,
                test_results={},
                error_message="No tokenizer available for validation"
            )
        
        try:
            self.logger.logger.info("Starting character coverage validation")
            
            all_required_chars = self.get_all_required_chars()
            supported_chars = set()
            unsupported_chars = set()
            test_results = {}
            
            # Test each character category
            for category, char_set in self.REQUIRED_CHARS.items():
                category_results = self._test_character_set(tokenizer, char_set, category)
                test_results[category] = category_results
                
                supported_chars.update(category_results['supported'])
                unsupported_chars.update(category_results['unsupported'])
            
            # Calculate coverage percentage
            total_chars = len(all_required_chars)
            supported_count = len(supported_chars)
            coverage_percentage = (supported_count / total_chars) * 100 if total_chars > 0 else 0.0
            
            # Determine if validation passes (require 100% coverage)
            is_valid = len(unsupported_chars) == 0
            
            self.logger.logger.info(f"Character validation complete: {coverage_percentage:.1f}% coverage")
            if unsupported_chars:
                self.logger.logger.warning(f"Unsupported characters found: {sorted(unsupported_chars)}")
            
            return ValidationResult(
                is_valid=is_valid,
                supported_chars=supported_chars,
                unsupported_chars=unsupported_chars,
                coverage_percentage=coverage_percentage,
                test_results=test_results
            )
            
        except Exception as e:
            error_msg = f"Error during character validation: {e}"
            self.logger.logger.error(error_msg)
            return ValidationResult(
                is_valid=False,
                supported_chars=set(),
                unsupported_chars=set(),
                coverage_percentage=0.0,
                test_results={},
                error_message=error_msg
            )
    
    def _test_character_set(self, tokenizer, char_set: str, category: str) -> Dict[str, Any]:
        """Test a specific character set for tokenizer support."""
        supported = set()
        unsupported = set()
        tokenization_issues = []
        
        self.logger.logger.debug(f"Testing {category} character set: {len(char_set)} characters")
        
        for char in char_set:
            try:
                # Test individual character tokenization
                tokens = tokenizer.tokenize(char)
                
                # Check if character is properly tokenized
                if self._is_character_properly_tokenized(char, tokens):
                    supported.add(char)
                else:
                    unsupported.add(char)
                    tokenization_issues.append({
                        'char': char,
                        'tokens': tokens,
                        'issue': 'improper_tokenization'
                    })
                
                # Test character in context (with surrounding text)
                context_test = f"test {char} context"
                context_tokens = tokenizer.tokenize(context_test)
                
                # Verify character appears in context tokenization
                if not self._character_in_tokens(char, context_tokens):
                    if char in supported:
                        supported.remove(char)
                        unsupported.add(char)
                    tokenization_issues.append({
                        'char': char,
                        'context': context_test,
                        'tokens': context_tokens,
                        'issue': 'missing_in_context'
                    })
                    
            except Exception as e:
                unsupported.add(char)
                tokenization_issues.append({
                    'char': char,
                    'error': str(e),
                    'issue': 'tokenization_error'
                })
        
        return {
            'supported': supported,
            'unsupported': unsupported,
            'total': len(char_set),
            'coverage_percentage': (len(supported) / len(char_set)) * 100 if char_set else 0.0,
            'issues': tokenization_issues
        }
    
    def _is_character_properly_tokenized(self, char: str, tokens: List[str]) -> bool:
        """
        Check if a character is properly tokenized.
        
        A character is considered properly tokenized if:
        1. It produces at least one token
        2. The character appears in the tokenized output
        3. It doesn't get split into multiple unrelated tokens
        """
        if not tokens:
            return False
        
        # For single characters, we expect either:
        # 1. The character itself as a token
        # 2. The character as part of a subword token (with BPE prefixes/suffixes)
        joined_tokens = ''.join(tokens).replace('▁', '').replace('Ġ', '')  # Remove BPE markers
        
        return char in joined_tokens
    
    def _character_in_tokens(self, char: str, tokens: List[str]) -> bool:
        """Check if a character appears in the tokenized output."""
        joined_tokens = ''.join(tokens).replace('▁', '').replace('Ġ', '')  # Remove BPE markers
        return char in joined_tokens
    
    def test_character_encoding(self, text: str) -> bool:
        """
        Test if a text string can be properly encoded and decoded.
        
        Args:
            text: Text to test
            
        Returns:
            True if encoding/decoding works correctly
        """
        if self.tokenizer is None:
            self.logger.logger.error("No tokenizer available for encoding test")
            return False
        
        try:
            # Test encoding
            encoded = self.tokenizer.encode(text, add_special_tokens=False)
            
            # Test decoding
            decoded = self.tokenizer.decode(encoded, skip_special_tokens=True)
            
            # Check if decoding preserves the original text (allowing for minor whitespace differences)
            normalized_original = text.strip()
            normalized_decoded = decoded.strip()
            
            is_valid = normalized_original == normalized_decoded
            
            if not is_valid:
                self.logger.logger.warning(f"Encoding/decoding mismatch: '{normalized_original}' -> '{normalized_decoded}'")
            
            return is_valid
            
        except Exception as e:
            self.logger.logger.error(f"Error during character encoding test: {e}")
            return False
    
    def generate_character_test_cases(self) -> List[str]:
        """
        Generate comprehensive test cases for character validation.
        
        Returns:
            List of test strings covering various character combinations
        """
        test_cases = []
        
        # Individual character tests
        all_chars = self.get_all_required_chars()
        for char in sorted(all_chars):
            test_cases.append(char)
        
        # Character combinations within categories
        for category, char_set in self.REQUIRED_CHARS.items():
            if len(char_set) > 1:
                # Test pairs of characters from the same category
                chars = list(char_set)
                for i in range(0, len(chars), 2):
                    if i + 1 < len(chars):
                        test_cases.append(chars[i] + chars[i + 1])
        
        # Dutch-specific test cases with diacritics
        dutch_test_cases = [
            "café",
            "naïef", 
            "coördinatie",
            "reünie",
            "Müller",
            "François",
            "José",
            "Björn",
            "Åse",
            "Zürich"
        ]
        test_cases.extend(dutch_test_cases)
        
        # Mixed character test cases
        mixed_cases = [
            "<EMAIL>",
            "€1.234,56",
            "NL91 ABNA 0417 1643 00",
            "1234 AB",
            "+31-6-12345678",
            "Straße 123, München",
            "Café de l'Europe",
            "50% korting!",
            "vraag & antwoord",
            "één, twee, drie..."
        ]
        test_cases.extend(mixed_cases)
        
        # Context test cases (characters in sentences)
        context_cases = [
            "De café is gesloten.",
            "Hij heeft een naïeve kijk op de wereld.",
            "De coördinatie verliep soepel.",
            "We organiseren een reünie.",
            "Het kostte €50,00.",
            "Mijn e-<NAME_EMAIL>.",
            "De postcode is 1234 AB.",
            "Bel naar +31-20-1234567.",
            "IBAN: NL91 ABNA 0417 1643 00"
        ]
        test_cases.extend(context_cases)
        
        self.logger.logger.info(f"Generated {len(test_cases)} character test cases")
        return test_cases
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """
        Run comprehensive character validation including all test cases.
        
        Returns:
            Dictionary with complete validation results
        """
        self.logger.logger.info("Starting comprehensive character validation")
        
        # Basic tokenizer coverage validation
        coverage_result = self.validate_tokenizer_coverage()
        
        # Generate and test all test cases
        test_cases = self.generate_character_test_cases()
        encoding_results = []
        
        for i, test_case in enumerate(test_cases):
            is_valid = self.test_character_encoding(test_case)
            encoding_results.append({
                'test_case': test_case,
                'is_valid': is_valid
            })
            
            if (i + 1) % 50 == 0:
                self.logger.logger.debug(f"Processed {i + 1}/{len(test_cases)} test cases")
        
        # Calculate encoding test statistics
        valid_encodings = sum(1 for result in encoding_results if result['is_valid'])
        encoding_success_rate = (valid_encodings / len(encoding_results)) * 100 if encoding_results else 0.0
        
        # Identify failed test cases
        failed_cases = [result for result in encoding_results if not result['is_valid']]
        
        comprehensive_result = {
            'tokenizer_coverage': coverage_result,
            'encoding_tests': {
                'total_cases': len(test_cases),
                'valid_cases': valid_encodings,
                'success_rate': encoding_success_rate,
                'failed_cases': failed_cases[:10]  # Limit to first 10 failures
            },
            'overall_valid': coverage_result.is_valid and encoding_success_rate >= 95.0,
            'summary': {
                'character_coverage': coverage_result.coverage_percentage,
                'encoding_success_rate': encoding_success_rate,
                'unsupported_characters': list(coverage_result.unsupported_chars),
                'total_test_cases': len(test_cases)
            }
        }
        
        self.logger.logger.info(f"Comprehensive validation complete:")
        self.logger.logger.info(f"  Character coverage: {coverage_result.coverage_percentage:.1f}%")
        self.logger.logger.info(f"  Encoding success rate: {encoding_success_rate:.1f}%")
        self.logger.logger.info(f"  Overall valid: {comprehensive_result['overall_valid']}")
        
        return comprehensive_result