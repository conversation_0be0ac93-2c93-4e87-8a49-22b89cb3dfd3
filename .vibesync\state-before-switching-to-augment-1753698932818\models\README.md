# Model Directory

This directory contains RobBERT-2023 model artifacts and checkpoints for Dutch NLP tasks.

## Directory Structure

```
models/
├── README.md                    # This file
└── robbert2023-per/            # RobBERT-2023 Person NER model
    ├── README.md               # RobBERT-specific documentation
    ├── backups/                # Model backups
    ├── base-models/            # Base model checkpoints
    ├── experiments/            # Experimental model variants
    └── fine-tuned/             # Fine-tuned model checkpoints
```

## Model Overview

### RobBERT-2023-base
- **Model ID**: `DTAI-KULeuven/robbert-2023-dutch-base`
- **Architecture**: BERT-based transformer with 12 layers, 768 hidden size
- **Vocabulary**: 50,000 tokens with byte-level BPE tokenization
- **Language**: Dutch (Netherlands and Belgium)
- **Training Data**: Large-scale Dutch web text, news, and books

### Key Improvements over BERTje
- **Tokenization**: Byte-level BPE vs WordPiece (better handling of compound words)
- **Vocabulary Size**: 50k vs 30k tokens (reduced OOV rate)
- **Dutch Coverage**: Better handling of diacritics, compound names, and OCR-noisy text
- **Performance**: Improved accuracy on Dutch NLP benchmarks

## Usage

### Loading the Model
```python
from src.models.multitask_robbert import MultiTaskRobBERT

# Load with default configuration
model = MultiTaskRobBERT.from_pretrained()

# Load from specific checkpoint
model = MultiTaskRobBERT.from_pretrained("models/robbert2023-per/fine-tuned/checkpoint-1000")
```

### Model Configuration
The model is configured through `src/config/default.yaml`:

```yaml
model:
  encoder_weights: "DTAI-KULeuven/robbert-2023-dutch-base"
  max_length: 512
  num_labels:
    ner: 3  # O, B-PER, I-PER
```

## Checkpoint Management

### Saving Checkpoints
```python
# Save model and tokenizer
model.save_pretrained("models/robbert2023-per/fine-tuned/my-checkpoint")

# Save with metadata
from src.utils.checkpoint_manager import CheckpointManager
manager = CheckpointManager("models/robbert2023-per")
manager.save_checkpoint(model, "experiment-1", metadata={
    "epoch": 5,
    "loss": 0.15,
    "f1_score": 0.92
})
```

### Loading Checkpoints
```python
# Load specific checkpoint
model = MultiTaskRobBERT.from_pretrained("models/robbert2023-per/fine-tuned/experiment-1")

# List available checkpoints
manager = CheckpointManager("models/robbert2023-per")
checkpoints = manager.list_checkpoints()
```

## Performance Metrics

### Tokenization Quality
- **OOV Rate**: Near 0% (byte-level BPE handles all characters)
- **Compound Names**: Improved segmentation of Dutch surnames
- **Diacritics**: Better handling of accented characters
- **OCR Text**: More robust to scanning artifacts

### Model Performance
- **Inference Speed**: ~100 tokens/second on CPU
- **Memory Usage**: ~1.2GB RAM for base model
- **Accuracy**: Maintains high performance on Dutch NER tasks

## Troubleshooting

### Common Issues

**Model not found**
```bash
# Download base model
python scripts/fetch_checkpoints.py
```

**Memory issues**
```python
# Use CPU-only mode
import torch
torch.set_default_tensor_type('torch.FloatTensor')
```

**Tokenization errors**
```python
# Check tokenizer loading
from transformers import AutoTokenizer
tokenizer = AutoTokenizer.from_pretrained("DTAI-KULeuven/robbert-2023-dutch-base")
```

## Related Documentation

- [RobBERT-2023 API Documentation](../README_API_ROBBERT.md)
- [Token Management Guide](../README_TOKEN_MANAGEMENT.md)
- [Tokenization Analysis](../assessment_results/tokenization_analysis.md)
- [Model Assessment](../assessment_results/current_model_assessment.md)