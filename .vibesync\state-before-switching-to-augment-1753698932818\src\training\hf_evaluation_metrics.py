"""
NER evaluation metrics for Hugging Face Trainer integration.

This module provides compute_metrics function and custom callbacks for proper
NER evaluation using seqeval library with token-level precision, recall, and F1 scores.
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict

try:
    from seqeval.metrics import (
        accuracy_score,
        precision_score,
        recall_score,
        f1_score,
        classification_report
    )
    from seqeval.scheme import IOB2
    SEQEVAL_AVAILABLE = True
except ImportError:
    SEQEVAL_AVAILABLE = False

from transformers import TrainerCallback, TrainingArguments, TrainerState, TrainerControl
from ..utils.logging_utils import get_logger


def compute_metrics(eval_pred, label_list: List[str]) -> Dict[str, float]:
    """
    Compute NER metrics using seqeval for proper NER evaluation.
    
    This function handles the conversion from token-level predictions to entity-level
    evaluation, properly handling BIO tagging and ignored tokens (-100).
    
    Args:
        eval_pred: EvalPrediction object with predictions and label_ids
        label_list: List of label names (e.g., ["O", "B-PER", "I-PER"])
        
    Returns:
        Dictionary with evaluation metrics
    """
    logger = get_logger(__name__)
    
    predictions, labels = eval_pred.predictions, eval_pred.label_ids
    predictions = np.argmax(predictions, axis=2)
    
    # Remove ignored index (special tokens) and convert to labels
    true_predictions = []
    true_labels = []
    
    for prediction, label in zip(predictions, labels):
        pred_labels = []
        true_labels_seq = []
        
        for pred_id, label_id in zip(prediction, label):
            if label_id != -100:  # Ignore special tokens
                if pred_id < len(label_list):
                    pred_labels.append(label_list[pred_id])
                else:
                    pred_labels.append("O")  # Fallback for out-of-range predictions
                
                if label_id < len(label_list):
                    true_labels_seq.append(label_list[label_id])
                else:
                    true_labels_seq.append("O")  # Fallback for out-of-range labels
        
        true_predictions.append(pred_labels)
        true_labels.append(true_labels_seq)
    
    # Use seqeval if available, otherwise fall back to token-level metrics
    if SEQEVAL_AVAILABLE:
        try:
            results = _compute_seqeval_metrics(true_predictions, true_labels)
        except Exception as e:
            logger.logger.warning(f"Seqeval evaluation failed: {e}, falling back to token-level metrics")
            results = _compute_token_level_metrics(true_predictions, true_labels, label_list)
    else:
        logger.logger.warning("Seqeval not available, using token-level metrics")
        results = _compute_token_level_metrics(true_predictions, true_labels, label_list)
    
    # Add per-entity-type metrics if available
    if SEQEVAL_AVAILABLE:
        try:
            per_entity_metrics = _compute_per_entity_metrics(true_predictions, true_labels)
            results.update(per_entity_metrics)
        except Exception as e:
            logger.logger.warning(f"Per-entity metrics computation failed: {e}")
    
    return results


def _compute_seqeval_metrics(
    predictions: List[List[str]],
    labels: List[List[str]]
) -> Dict[str, float]:
    """Compute metrics using seqeval library."""
    return {
        "precision": precision_score(labels, predictions),
        "recall": recall_score(labels, predictions),
        "f1": f1_score(labels, predictions),
        "accuracy": accuracy_score(labels, predictions),
    }


def _compute_token_level_metrics(
    predictions: List[List[str]],
    labels: List[List[str]],
    label_list: List[str]
) -> Dict[str, float]:
    """Compute token-level metrics as fallback when seqeval is not available."""
    # Flatten predictions and labels
    flat_predictions = [label for seq in predictions for label in seq]
    flat_labels = [label for seq in labels for label in seq]
    
    # Calculate token-level accuracy
    correct = sum(1 for pred, true in zip(flat_predictions, flat_labels) if pred == true)
    total = len(flat_predictions)
    accuracy = correct / total if total > 0 else 0.0
    
    # Calculate per-label metrics
    label_metrics = defaultdict(lambda: {"tp": 0, "fp": 0, "fn": 0})
    
    for pred, true in zip(flat_predictions, flat_labels):
        if pred == true:
            label_metrics[pred]["tp"] += 1
        else:
            label_metrics[pred]["fp"] += 1
            label_metrics[true]["fn"] += 1
    
    # Calculate macro-averaged metrics
    precisions = []
    recalls = []
    f1s = []
    
    for label in label_list:
        if label == "O":  # Skip O label for entity-level metrics
            continue
        
        metrics = label_metrics[label]
        tp, fp, fn = metrics["tp"], metrics["fp"], metrics["fn"]
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        precisions.append(precision)
        recalls.append(recall)
        f1s.append(f1)
    
    return {
        "precision": np.mean(precisions) if precisions else 0.0,
        "recall": np.mean(recalls) if recalls else 0.0,
        "f1": np.mean(f1s) if f1s else 0.0,
        "accuracy": accuracy,
    }


def _compute_per_entity_metrics(
    predictions: List[List[str]],
    labels: List[List[str]]
) -> Dict[str, float]:
    """Compute per-entity-type metrics using seqeval."""
    if not SEQEVAL_AVAILABLE:
        return {}
    
    try:
        # Get classification report
        report = classification_report(labels, predictions, output_dict=True)
        
        per_entity_metrics = {}
        for entity_type, metrics in report.items():
            if entity_type not in ["accuracy", "macro avg", "weighted avg"]:
                per_entity_metrics[f"{entity_type}_precision"] = metrics.get("precision", 0.0)
                per_entity_metrics[f"{entity_type}_recall"] = metrics.get("recall", 0.0)
                per_entity_metrics[f"{entity_type}_f1"] = metrics.get("f1-score", 0.0)
                per_entity_metrics[f"{entity_type}_support"] = metrics.get("support", 0)
        
        return per_entity_metrics
        
    except Exception:
        return {}


class NERMetricsCallback(TrainerCallback):
    """
    Custom callback for enhanced NER metrics logging and monitoring.
    
    This callback provides additional logging and can be extended for custom
    metric tracking, confusion matrix generation, and error analysis.
    """
    
    def __init__(self, label_list: List[str]):
        """
        Initialize NER metrics callback.
        
        Args:
            label_list: List of label names
        """
        self.label_list = label_list
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        # Track best metrics
        self.best_f1 = 0.0
        self.best_epoch = 0
        self.metrics_history = []
    
    def on_evaluate(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        model=None,
        tokenizer=None,
        eval_dataloader=None,
        **kwargs
    ):
        """Called after evaluation."""
        if state.log_history:
            latest_log = state.log_history[-1]
            
            # Extract evaluation metrics
            eval_metrics = {k: v for k, v in latest_log.items() if k.startswith("eval_")}
            
            if eval_metrics:
                # Log current metrics
                self.logger.logger.info("Evaluation metrics:")
                for metric, value in eval_metrics.items():
                    if isinstance(value, (int, float)):
                        self.logger.logger.info(f"  {metric}: {value:.4f}")
                
                # Track best F1 score
                current_f1 = eval_metrics.get("eval_f1", 0.0)
                if current_f1 > self.best_f1:
                    self.best_f1 = current_f1
                    self.best_epoch = state.epoch
                    self.logger.logger.info(f"New best F1 score: {self.best_f1:.4f} at epoch {self.best_epoch}")
                
                # Store metrics history
                self.metrics_history.append({
                    "epoch": state.epoch,
                    "step": state.global_step,
                    **eval_metrics
                })
    
    def on_train_end(
        self,
        args: TrainingArguments,
        state: TrainerState,
        control: TrainerControl,
        **kwargs
    ):
        """Called at the end of training."""
        self.logger.logger.info("Training completed!")
        self.logger.logger.info(f"Best F1 score: {self.best_f1:.4f} at epoch {self.best_epoch}")
        
        # Log final metrics summary
        if self.metrics_history:
            final_metrics = self.metrics_history[-1]
            self.logger.logger.info("Final evaluation metrics:")
            for metric, value in final_metrics.items():
                if metric not in ["epoch", "step"] and isinstance(value, (int, float)):
                    self.logger.logger.info(f"  {metric}: {value:.4f}")


def validate_predictions_format(
    predictions: np.ndarray,
    labels: np.ndarray,
    label_list: List[str]
) -> Tuple[bool, List[str]]:
    """
    Validate predictions format for NER evaluation.
    
    Args:
        predictions: Model predictions array
        labels: True labels array
        label_list: List of label names
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    errors = []
    
    # Check array shapes
    if predictions.shape[:2] != labels.shape:
        errors.append(f"Shape mismatch: predictions {predictions.shape[:2]} vs labels {labels.shape}")
    
    # Check prediction values
    if predictions.ndim == 3:  # Logits format
        pred_ids = np.argmax(predictions, axis=2)
    else:
        pred_ids = predictions
    
    # Check for out-of-range predictions
    max_pred = np.max(pred_ids)
    if max_pred >= len(label_list):
        errors.append(f"Prediction ID {max_pred} exceeds label list size {len(label_list)}")
    
    # Check for out-of-range labels (excluding -100 for ignored tokens)
    valid_labels = labels[labels != -100]
    if len(valid_labels) > 0:
        max_label = np.max(valid_labels)
        if max_label >= len(label_list):
            errors.append(f"Label ID {max_label} exceeds label list size {len(label_list)}")
    
    return len(errors) == 0, errors


def create_confusion_matrix(
    predictions: List[List[str]],
    labels: List[List[str]],
    label_list: List[str]
) -> Dict[str, Any]:
    """
    Create confusion matrix for NER predictions.
    
    Args:
        predictions: Predicted label sequences
        labels: True label sequences
        label_list: List of label names
        
    Returns:
        Dictionary with confusion matrix data
    """
    # Flatten sequences
    flat_predictions = [label for seq in predictions for label in seq]
    flat_labels = [label for seq in labels for label in seq]
    
    # Create confusion matrix
    from collections import Counter
    
    confusion_data = defaultdict(lambda: defaultdict(int))
    for pred, true in zip(flat_predictions, flat_labels):
        confusion_data[true][pred] += 1
    
    # Convert to regular dict for JSON serialization
    confusion_matrix = {}
    for true_label in label_list:
        confusion_matrix[true_label] = {}
        for pred_label in label_list:
            confusion_matrix[true_label][pred_label] = confusion_data[true_label][pred_label]
    
    return {
        "matrix": confusion_matrix,
        "labels": label_list,
        "total_predictions": len(flat_predictions)
    }


# Export main functions and classes
__all__ = [
    'compute_metrics',
    'NERMetricsCallback',
    'validate_predictions_format',
    'create_confusion_matrix'
]