"""
Hugging Face Training Configuration with YAML support.

This module provides the HFTrainingConfig dataclass for configuring
Hugging Face Trainer-based training with comprehensive parameter support,
YAML loading, and TrainingArguments conversion.
"""

import os
import yaml
import torch
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from transformers import TrainingArguments
from pydantic import BaseModel, Field, field_validator


@dataclass
class HFTrainingConfig:
    """
    Hugging Face Trainer configuration with comprehensive training parameters.
    
    This configuration class supports all major training parameters needed for
    RobBERT-2023 NER training with Hugging Face Trainer, including WandB integration,
    early stopping, class weights, and hardware optimization.
    """
    
    # Model configuration
    model_name: str = "DTAI-KULeuven/robbert-2023-dutch-base"
    
    # Training parameters
    epochs: int = 3
    batch_size: int = 8
    eval_batch_size: int = 8
    learning_rate: float = 5e-5
    weight_decay: float = 0.01
    warmup_steps: int = 500
    warmup_ratio: float = 0.0
    
    # Evaluation and logging
    eval_steps: int = 100
    logging_steps: int = 50
    save_steps: int = 500
    evaluation_strategy: str = "steps"
    logging_strategy: str = "steps"
    save_strategy: str = "steps"
    
    # Early stopping
    early_stopping_patience: int = 3
    early_stopping_threshold: float = 0.001
    load_best_model_at_end: bool = True
    metric_for_best_model: str = "eval_f1"
    greater_is_better: bool = True
    
    # Class balancing (optional)
    use_class_weights: bool = False
    class_weights: Optional[Dict[str, float]] = None
    class_weight_method: str = "balanced"  # "balanced", "balanced_subsample"
    per_head_class_weights: bool = False  # Enable per-head weight computation
    class_weight_entity_types: Optional[List[str]] = None  # Entity types for per-head weights
    save_class_weights: bool = True  # Save computed weights to file
    
    # WandB integration
    wandb_project: str = "robbert2023-ner"
    wandb_entity: str = "slippydongle"
    run_name: Optional[str] = None
    wandb_tags: List[str] = field(default_factory=lambda: ["robbert-2023", "dutch-nlp", "ner"])
    wandb_notes: str = "Hugging Face Trainer integration for RobBERT-2023 NER"
    report_to: str = "wandb"
    
    # Enhanced WandB logging options
    wandb_log_confusion_matrix: bool = True
    wandb_log_per_class_metrics: bool = True
    wandb_log_model_artifacts: bool = True
    wandb_log_evaluation_tables: bool = True
    wandb_confusion_matrix_frequency: str = "epoch"  # "epoch" or "step"
    wandb_confusion_matrix_steps: int = 500
    
    # Hardware optimization
    use_gpu: bool = True
    fp16: bool = True
    bf16: bool = False
    dataloader_num_workers: int = 2
    dataloader_pin_memory: bool = True
    gradient_accumulation_steps: int = 1
    max_grad_norm: float = 1.0
    
    # Checkpointing and model management
    save_total_limit: int = 3
    push_to_hub: bool = False
    hub_model_id: Optional[str] = None
    hub_strategy: str = "every_save"
    
    # Learning rate scheduling
    lr_scheduler_type: str = "linear"
    cosine_schedule_num_cycles: float = 0.5
    polynomial_decay_power: float = 1.0
    
    # Advanced LR scheduling options
    lr_scheduler_kwargs: Optional[Dict[str, Any]] = None
    warmup_schedule_type: str = "linear"  # "linear", "cosine", "constant"
    end_learning_rate: float = 0.0  # For polynomial decay
    
    # Enhanced early stopping options
    early_stopping_monitor_metrics: List[str] = field(default_factory=lambda: ["eval_f1", "eval_loss"])
    early_stopping_threshold_type: str = "absolute"  # "absolute" or "relative"
    early_stopping_min_delta: float = 0.0001
    early_stopping_restore_best_weights: bool = True
    
    # Advanced training options
    gradient_checkpointing: bool = False
    remove_unused_columns: bool = True
    label_smoothing_factor: float = 0.0
    optim: str = "adamw_torch"
    adam_beta1: float = 0.9
    adam_beta2: float = 0.999
    adam_epsilon: float = 1e-8
    
    # Versioning and experiment tracking
    run_version: str = "v1.0"
    version_description: Optional[str] = None
    experiment_name: Optional[str] = None
    
    # Test mode settings
    test_mode: bool = False
    test_sample_limit: int = 50
    test_epochs: int = 1
    test_disable_wandb: bool = True
    
    # Output and prediction settings
    output_dir: str = "checkpoints"
    save_predictions: bool = True
    generate_model_card: bool = True
    prediction_loss_only: bool = False
    
    # Data processing
    max_length: int = 512
    truncation: bool = True
    padding: str = "max_length"
    
    # Reproducibility
    seed: int = 42
    data_seed: Optional[int] = None
    
    # Advanced features
    resume_from_checkpoint: Optional[str] = None
    ignore_data_skip: bool = False
    ddp_find_unused_parameters: bool = False
    ddp_bucket_cap_mb: Optional[int] = None
    
    def __post_init__(self):
        """Post-initialization validation and setup."""
        # Type validation
        self._validate_types()
        
        # Auto-detect GPU availability if use_gpu is True
        if self.use_gpu and not torch.cuda.is_available():
            print("Warning: GPU requested but not available. Falling back to CPU.")
            self.use_gpu = False
            self.fp16 = False
            self.bf16 = False
        
        # Disable mixed precision on CPU
        if not self.use_gpu:
            self.fp16 = False
            self.bf16 = False
        
        # Test mode overrides
        if self.test_mode:
            self.epochs = self.test_epochs
            if self.test_disable_wandb:
                self.report_to = None
        
        # Set data_seed to seed if not specified
        if self.data_seed is None:
            self.data_seed = self.seed
        
        # Validate scheduler type
        valid_schedulers = [
            "linear", "cosine", "cosine_with_restarts", "polynomial", 
            "constant", "constant_with_warmup", "inverse_sqrt", "reduce_lr_on_plateau"
        ]
        if self.lr_scheduler_type not in valid_schedulers:
            raise ValueError(f"Invalid lr_scheduler_type: {self.lr_scheduler_type}. "
                           f"Must be one of {valid_schedulers}")
        
        # Validate evaluation strategy
        valid_strategies = ["no", "steps", "epoch"]
        if self.evaluation_strategy not in valid_strategies:
            raise ValueError(f"Invalid evaluation_strategy: {self.evaluation_strategy}. "
                           f"Must be one of {valid_strategies}")
        
        # Validate early stopping threshold type
        valid_threshold_types = ["absolute", "relative"]
        if self.early_stopping_threshold_type not in valid_threshold_types:
            raise ValueError(f"Invalid early_stopping_threshold_type: {self.early_stopping_threshold_type}. "
                           f"Must be one of {valid_threshold_types}")
        
        # Validate warmup schedule type
        valid_warmup_types = ["linear", "cosine", "constant"]
        if self.warmup_schedule_type not in valid_warmup_types:
            raise ValueError(f"Invalid warmup_schedule_type: {self.warmup_schedule_type}. "
                           f"Must be one of {valid_warmup_types}")
    
    def _validate_types(self):
        """Validate field types and values."""
        # Validate integer fields
        int_fields = ['epochs', 'batch_size', 'eval_batch_size', 'eval_steps', 
                     'logging_steps', 'save_steps', 'early_stopping_patience',
                     'warmup_steps', 'dataloader_num_workers', 'gradient_accumulation_steps',
                     'save_total_limit', 'max_length', 'seed', 'test_sample_limit', 'test_epochs']
        
        for field_name in int_fields:
            value = getattr(self, field_name)
            if not isinstance(value, int) or value < 0:
                raise ValueError(f"{field_name} must be a non-negative integer, got {value}")
        
        # Validate float fields
        float_fields = ['learning_rate', 'weight_decay', 'warmup_ratio', 'early_stopping_threshold',
                       'max_grad_norm', 'cosine_schedule_num_cycles', 'polynomial_decay_power',
                       'label_smoothing_factor', 'adam_beta1', 'adam_beta2', 'adam_epsilon']
        
        for field_name in float_fields:
            value = getattr(self, field_name)
            if not isinstance(value, (int, float)) or value < 0:
                raise ValueError(f"{field_name} must be a non-negative number, got {value}")
        
        # Validate string fields
        string_fields = ['model_name', 'evaluation_strategy', 'logging_strategy', 'save_strategy',
                        'metric_for_best_model', 'wandb_project', 'wandb_entity', 'lr_scheduler_type',
                        'optim', 'run_version', 'output_dir', 'padding', 'class_weight_method']
        
        for field_name in string_fields:
            value = getattr(self, field_name)
            if not isinstance(value, str) or not value.strip():
                raise ValueError(f"{field_name} must be a non-empty string, got {value}")
        
        # Validate class weight method
        valid_weight_methods = ["balanced", "balanced_subsample"]
        if self.class_weight_method not in valid_weight_methods:
            raise ValueError(f"Invalid class_weight_method: {self.class_weight_method}. "
                           f"Must be one of {valid_weight_methods}")
    
    @classmethod
    def from_yaml(cls, yaml_path: str) -> 'HFTrainingConfig':
        """
        Load configuration from YAML file with environment variable substitution.
        
        Args:
            yaml_path: Path to YAML configuration file
            
        Returns:
            HFTrainingConfig instance
            
        Raises:
            FileNotFoundError: If YAML file doesn't exist
            yaml.YAMLError: If YAML parsing fails
            ValueError: If configuration validation fails
        """
        yaml_file = Path(yaml_path)
        
        if not yaml_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {yaml_path}")
        
        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                raw_config = yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"Failed to parse YAML configuration: {e}")
        
        # Substitute environment variables
        processed_config = cls._substitute_env_vars(raw_config)
        
        # Extract hf_training section or use root level
        if 'hf_training' in processed_config:
            config_dict = processed_config['hf_training']
        else:
            config_dict = processed_config
        
        # Filter out unknown fields
        valid_fields = {f.name for f in cls.__dataclass_fields__.values()}
        filtered_config = {k: v for k, v in config_dict.items() if k in valid_fields}
        
        try:
            return cls(**filtered_config)
        except Exception as e:
            raise ValueError(f"Configuration validation failed: {e}")
    
    @staticmethod
    def _substitute_env_vars(data: Any) -> Any:
        """Recursively substitute environment variables in configuration data."""
        import re
        
        if isinstance(data, dict):
            return {key: HFTrainingConfig._substitute_env_vars(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [HFTrainingConfig._substitute_env_vars(item) for item in data]
        elif isinstance(data, str):
            # Pattern to match ${VAR} or ${VAR:-default}
            pattern = r'\$\{([^}]+)\}'

            def replace_var(match):
                var_expr = match.group(1)
                if ':-' in var_expr:
                    var_name, default_value = var_expr.split(':-', 1)
                    return os.getenv(var_name, default_value)
                else:
                    return os.getenv(var_expr, match.group(0))  # Return original if not found

            return re.sub(pattern, replace_var, data)
        else:
            return data
    
    def to_training_args(self, output_dir: Optional[str] = None) -> TrainingArguments:
        """
        Convert to Hugging Face TrainingArguments.
        
        Args:
            output_dir: Override output directory
            
        Returns:
            TrainingArguments instance configured for training
        """
        # Use provided output_dir or default
        final_output_dir = output_dir or self.output_dir
        
        # Create run name if not provided
        run_name = self.run_name
        if run_name is None and self.experiment_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            run_name = f"{self.experiment_name}_{timestamp}_{self.run_version}"
        
        # Configure report_to based on test mode and wandb settings
        if self.test_mode and self.test_disable_wandb:
            report_to = []  # Empty list disables all reporting
        elif self.report_to is None:
            report_to = None
        else:
            report_to = self.report_to
        
        # Prepare lr_scheduler_kwargs for advanced scheduler options
        lr_scheduler_kwargs = {}
        if self.lr_scheduler_type == "cosine_with_restarts":
            lr_scheduler_kwargs["num_cycles"] = self.cosine_schedule_num_cycles
        elif self.lr_scheduler_type == "polynomial":
            lr_scheduler_kwargs["power"] = self.polynomial_decay_power
        
        return TrainingArguments(
            output_dir=final_output_dir,
            
            # Training parameters
            num_train_epochs=self.epochs,
            per_device_train_batch_size=self.batch_size,
            per_device_eval_batch_size=self.eval_batch_size,
            learning_rate=self.learning_rate,
            weight_decay=self.weight_decay,
            warmup_steps=self.warmup_steps,
            warmup_ratio=self.warmup_ratio,
            
            # Evaluation and logging
            eval_strategy=self.evaluation_strategy,  # Note: eval_strategy not evaluation_strategy
            eval_steps=self.eval_steps if self.evaluation_strategy == "steps" else None,
            logging_strategy=self.logging_strategy,
            logging_steps=self.logging_steps,
            save_strategy=self.save_strategy,
            save_steps=self.save_steps if self.save_strategy == "steps" else None,
            
            # Early stopping and model selection
            load_best_model_at_end=self.load_best_model_at_end,
            metric_for_best_model=self.metric_for_best_model,
            greater_is_better=self.greater_is_better,
            
            # WandB integration
            report_to=report_to,
            run_name=run_name,
            
            # Hardware optimization
            fp16=self.fp16 and torch.cuda.is_available(),
            bf16=self.bf16 and torch.cuda.is_available(),
            dataloader_pin_memory=self.dataloader_pin_memory,
            dataloader_num_workers=self.dataloader_num_workers,
            gradient_accumulation_steps=self.gradient_accumulation_steps,
            max_grad_norm=self.max_grad_norm,
            
            # Checkpointing
            save_total_limit=self.save_total_limit,
            push_to_hub=self.push_to_hub,
            hub_model_id=self.hub_model_id,
            hub_strategy=self.hub_strategy,
            
            # Learning rate scheduling
            lr_scheduler_type=self.lr_scheduler_type,
            lr_scheduler_kwargs=lr_scheduler_kwargs if lr_scheduler_kwargs else None,
            
            # Advanced options
            gradient_checkpointing=self.gradient_checkpointing,
            remove_unused_columns=self.remove_unused_columns,
            label_smoothing_factor=self.label_smoothing_factor,
            optim=self.optim,
            adam_beta1=self.adam_beta1,
            adam_beta2=self.adam_beta2,
            adam_epsilon=self.adam_epsilon,
            
            # Reproducibility
            seed=self.seed,
            data_seed=self.data_seed,
            
            # Advanced features
            resume_from_checkpoint=self.resume_from_checkpoint,
            ignore_data_skip=self.ignore_data_skip,
            ddp_find_unused_parameters=self.ddp_find_unused_parameters,
            ddp_bucket_cap_mb=self.ddp_bucket_cap_mb,
            
            # Prediction settings
            prediction_loss_only=self.prediction_loss_only,
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            field.name: getattr(self, field.name)
            for field in self.__dataclass_fields__.values()
        }
    
    def save_yaml(self, yaml_path: str) -> None:
        """
        Save configuration to YAML file.
        
        Args:
            yaml_path: Path to save YAML configuration
        """
        config_dict = {"hf_training": self.to_dict()}
        
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    def get_wandb_config(self) -> Dict[str, Any]:
        """
        Get WandB-specific configuration for experiment tracking.
        
        Returns:
            Dictionary with WandB configuration
        """
        return {
            "project": self.wandb_project,
            "entity": self.wandb_entity,
            "name": self.run_name,
            "tags": self.wandb_tags,
            "notes": self.wandb_notes,
            "config": {
                "model_name": self.model_name,
                "epochs": self.epochs,
                "batch_size": self.batch_size,
                "learning_rate": self.learning_rate,
                "weight_decay": self.weight_decay,
                "warmup_steps": self.warmup_steps,
                "early_stopping_patience": self.early_stopping_patience,
                "use_class_weights": self.use_class_weights,
                "run_version": self.run_version,
                "experiment_name": self.experiment_name,
            },
            "enhanced_logging": {
                "log_confusion_matrix": self.wandb_log_confusion_matrix,
                "log_per_class_metrics": self.wandb_log_per_class_metrics,
                "log_model_artifacts": self.wandb_log_model_artifacts,
                "log_evaluation_tables": self.wandb_log_evaluation_tables,
                "confusion_matrix_frequency": self.wandb_confusion_matrix_frequency,
                "confusion_matrix_steps": self.wandb_confusion_matrix_steps,
            }
        }
    
    def create_output_dir(self, base_name: str = "training") -> str:
        """
        Create timestamped output directory.
        
        Args:
            base_name: Base name for the directory
            
        Returns:
            Path to created output directory
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        dir_name = f"{base_name}_{timestamp}_{self.run_version}"
        
        if self.experiment_name:
            dir_name = f"{self.experiment_name}_{timestamp}_{self.run_version}"
        
        output_path = Path(self.output_dir) / dir_name
        output_path.mkdir(parents=True, exist_ok=True)
        
        return str(output_path)
    
    def validate_class_weights(self, label_list: List[str]) -> None:
        """
        Validate class weights against label list.
        
        Args:
            label_list: List of labels for validation
            
        Raises:
            ValueError: If class weights don't match labels
        """
        if not self.use_class_weights or not self.class_weights:
            return
        
        # Check that all labels have weights
        missing_labels = set(label_list) - set(self.class_weights.keys())
        if missing_labels:
            raise ValueError(f"Missing class weights for labels: {missing_labels}")
        
        # Check for extra weights
        extra_weights = set(self.class_weights.keys()) - set(label_list)
        if extra_weights:
            print(f"Warning: Extra class weights for unknown labels: {extra_weights}")
        
        # Validate weight values
        for label, weight in self.class_weights.items():
            if not isinstance(weight, (int, float)) or weight <= 0:
                raise ValueError(f"Invalid class weight for {label}: {weight}. Must be positive number.")
    
    def compute_and_set_class_weights(
        self,
        dataset,
        label_list: List[str],
        entity_type: Optional[str] = None,
        output_dir: Optional[str] = None
    ) -> Optional[Dict[str, float]]:
        """
        Compute and set class weights for the configuration.
        
        Args:
            dataset: Hugging Face dataset for weight computation
            label_list: List of all labels in BIO format
            entity_type: Specific entity type for per-head computation
            output_dir: Directory to save computed weights
            
        Returns:
            Computed class weights dictionary
        """
        if not self.use_class_weights:
            return None
        
        from .class_weights import ClassWeightCalculator
        
        calculator = ClassWeightCalculator()
        
        # Compute class weights
        computed_weights = calculator.compute_class_weights(
            dataset=dataset,
            label_list=label_list,
            method=self.class_weight_method,
            entity_type=entity_type
        )
        
        # Set computed weights
        self.class_weights = computed_weights
        
        # Save weights if requested and output directory provided
        if self.save_class_weights and output_dir:
            weights_path = Path(output_dir) / "class_weights.json"
            metadata = {
                "method": self.class_weight_method,
                "entity_type": entity_type,
                "label_list": label_list,
                "per_head_weights": self.per_head_class_weights
            }
            calculator.save_class_weights(computed_weights, weights_path, metadata)
        
        return computed_weights
    
    def compute_per_head_class_weights(
        self,
        dataset,
        label_list: List[str],
        output_dir: Optional[str] = None
    ) -> Optional[Dict[str, Dict[str, float]]]:
        """
        Compute per-head class weights for multi-head training.
        
        Args:
            dataset: Hugging Face dataset for weight computation
            label_list: List of all labels in BIO format
            output_dir: Directory to save computed weights
            
        Returns:
            Dictionary mapping entity types to their class weights
        """
        if not self.use_class_weights or not self.per_head_class_weights:
            return None
        
        from .class_weights import ClassWeightCalculator
        
        calculator = ClassWeightCalculator()
        
        # Compute per-head weights
        per_head_weights = calculator.compute_per_head_class_weights(
            dataset=dataset,
            label_list=label_list,
            entity_types=self.class_weight_entity_types,
            method=self.class_weight_method
        )
        
        # Save weights if requested and output directory provided
        if self.save_class_weights and output_dir:
            weights_path = Path(output_dir) / "per_head_class_weights.json"
            metadata = {
                "method": self.class_weight_method,
                "entity_types": list(per_head_weights.keys()),
                "label_list": label_list,
                "per_head_weights": True
            }
            calculator.save_class_weights(per_head_weights, weights_path, metadata)
        
        return per_head_weights


def create_default_hf_config() -> HFTrainingConfig:
    """Create default HF training configuration."""
    return HFTrainingConfig()


def create_test_hf_config() -> HFTrainingConfig:
    """Create test mode HF training configuration."""
    return HFTrainingConfig(
        test_mode=True,
        test_epochs=1,
        test_sample_limit=50,
        eval_steps=10,
        logging_steps=5,
        save_steps=20,
        early_stopping_patience=1,
    )


# Export main classes and functions
__all__ = [
    'HFTrainingConfig',
    'create_default_hf_config',
    'create_test_hf_config'
]