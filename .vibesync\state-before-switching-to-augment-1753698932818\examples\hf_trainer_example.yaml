# Example Hugging Face Trainer configuration for RobBERT-2023 NER training

hf_training:
  # Model configuration
  model_name: "DTAI-KULeuven/robbert-2023-dutch-base"
  
  # Training parameters
  epochs: 3
  batch_size: 8
  eval_batch_size: 8
  learning_rate: 5e-5
  weight_decay: 0.01
  warmup_steps: 500
  
  # Evaluation and logging
  eval_steps: 100
  logging_steps: 50
  save_steps: 500
  evaluation_strategy: "steps"
  
  # Early stopping
  early_stopping_patience: 3
  early_stopping_threshold: 0.001
  load_best_model_at_end: true
  metric_for_best_model: "eval_f1"
  greater_is_better: true
  
  # WandB integration
  wandb_project: "robbert2023-ner"
  wandb_entity: "your-wandb-entity"
  run_name: "hf-trainer-example"
  wandb_tags: ["robbert-2023", "dutch-nlp", "ner", "hf-trainer"]
  wandb_notes: "Example training run with Hugging Face Trainer"
  report_to: "wandb"
  
  # Hardware optimization
  use_gpu: true
  fp16: true
  dataloader_num_workers: 2
  dataloader_pin_memory: true
  
  # Checkpointing
  save_total_limit: 3
  output_dir: "checkpoints"
  
  # Data processing
  max_length: 512
  truncation: true
  padding: "max_length"
  
  # Reproducibility
  seed: 42
  
  # Versioning
  run_version: "v1.0"
  experiment_name: "hf-trainer-example"
  
  # Output settings
  save_predictions: true
  generate_model_card: true