# Implementation Plan

- [x] 1. Create data preprocessing utilities for sentence + entities format






  - Implement preprocess_raw_data() function to convert sentence + entities to tokenized format
  - Add support for both explicit spans (start/end) and auto-detection using regex
  - Create validation for entity text matching in sentences with span consistency checks
  - Add pre-validation step to verify sentence[start:end] matches entity text
  - Add logging for entities that cannot be aligned or are truncated
  - Write unit tests for preprocessing with various Dutch text examples
  - _Requirements: 1.1, 1.4_

- [x] 2. Implement dynamic label discovery and BIO tagging system












  - Create label discovery from dataset entities with proper BIO tagging (O, B-LABEL, I-LABEL)
  - Implement EntitySpan and NERExample Pydantic models for data validation
  - Add support for multiple entity types (PER, LOC, ORG) with dynamic label space
  - Create label2id and id2label mapping utilities
  - Write tests for label discovery and BIO tagging correctness
  - _Requirements: 1.2, 1.3_

- [x] 3. Create Hugging Face dataset preparation pipeline





  - Implement prepare_ner_dataset() using datasets.load_dataset() for JSON files
  - Add tokenize_and_align_labels() function with proper subword alignment for RobBERT
  - Handle character-level entity spans to token-level label alignment
  - Implement stratified train/validation splitting by entity presence to prevent overfitting
  - Add support for truncation warnings when entities are cut off
  - Create dataset statistics logging (total sentences, entity coverage, label distribution)
  - _Requirements: 1.1, 1.4_

- [x] 4. Implement RobertaForTokenClassification model setup












  - Create create_ner_model() function using RobBERT-2023-base
  - Configure model with dynamic num_labels, id2label, and label2id mappings
  - Ensure compatibility with existing RobBERT tokenizer and inference pipeline
  - Add model validation and basic forward pass testing
  - Write integration tests for model initialization and prediction format
  - _Requirements: 2.1, 6.1, 6.2, 6.3_

- [x] 5. Create HFTrainingConfig with YAML support





  - Implement HFTrainingConfig dataclass with comprehensive training parameters
  - Add from_yaml() class method for loading configuration from files
  - Implement to_training_args() method for TrainingArguments conversion
  - Add support for class weights, early stopping, and hardware optimization
  - Create default configuration templates and validation
  - _Requirements: 8.2, 8.3_

- [x] 6. Implement Hugging Face Trainer integration





  - Create main training function using Trainer class instead of custom loops
  - Configure TrainingArguments with WandB integration (report_to="wandb")
  - Set up DataCollatorForTokenClassification for proper padding and batching
  - Implement automatic checkpointing with load_best_model_at_end=True and metric_for_best_model='eval_f1'
  - Add support for mixed precision training and hardware optimization
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 5.1, 5.2_

- [x] 7. Implement NER evaluation metrics with seqeval






  - Create compute_metrics() function using seqeval for proper NER evaluation
  - Calculate token-level precision, recall, F1, and accuracy
  - Add per-entity-type metrics reporting
  - Handle ignored tokens (-100) correctly in metric calculation
  - Implement evaluation during training at regular intervals
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 8. Enhance WandB integration with advanced logging






  - Configure automatic WandB logging through TrainingArguments
  - Implement CustomWandbCallback for confusion matrix and per-class metrics
  - Add model artifact logging and experiment tracking
  - Create WandB tables for detailed evaluation results
  - Ensure compatibility with existing WandB configuration system
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 9. Add early stopping and learning rate scheduling












  - Implement EarlyStoppingCallback with configurable patience and threshold
  - Configure linear warmup and cosine decay learning rate scheduling
  - Add load_best_model_at_end functionality for optimal checkpoint selection
  - Create callback system for training monitoring and control
  - Test early stopping behavior with validation metrics
  - _Requirements: 7.1, 7.2, 7.3, 7.4_

- [x] 10. Create class weight calculation for imbalanced datasets





  - Implement compute_class_weights() using sklearn for balanced training
  - Add per-head class weight computation since imbalance differs between PER, LOC, ORG heads
  - Create dataset analysis utilities for label distribution per entity type
  - Add configuration options for enabling/disabling class weights per head
  - Test class weighting effectiveness on imbalanced Dutch NER data
  - _Requirements: 4.1, 4.2_

- [x] 11. Implement per-head training isolation system





  - Create train_single_head() function for entity-type-specific training
  - Filter datasets to exclude examples without target entity type to prevent learning only "O" labels
  - Implement configurable filtering strategy: strict (only target entity) vs mixed (map non-target to "O")
  - Use 3-label scheme per head: ["O", "B-LABEL", "I-LABEL"]
  - Implement isolated checkpoint and logging directories per head
  - Create WandB run naming with entity type suffixes
  - _Requirements: 6.1, 6.2, 6.3, 8.1, 8.4_

- [x] 12. Create CLI interface with typer





  - Implement command-line interface for training with typer
  - Add support for config file loading and CLI argument overrides
  - Create commands for single-head and multi-head training
  - Add data preprocessing command for format conversion
  - Implement validation and help text for all parameters
  - _Requirements: 8.1, 8.4_

- [x] 13. Add comprehensive error handling and logging






  - Implement robust error handling for data loading and preprocessing
  - Add meaningful error messages for tokenization and alignment issues
  - Create logging for training progress, metrics, and warnings
  - Handle CUDA out of memory with automatic batch size reduction
  - Add graceful degradation when WandB is unavailable
  - _Requirements: 5.3, 8.3_

- [x] 14. Create migration utilities and compatibility layer








  - Implement data format conversion from existing JSONL to new sentence + entities format
  - Create checkpoint compatibility validation with existing inference pipeline
  - Add migration scripts for existing training data and configurations
  - Ensure backward compatibility with current model loading patterns
  - Test integration with existing API endpoints and inference scripts
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 8.1, 8.2_

- [ ] 15. Implement comprehensive testing suite




  - Create unit tests for data preprocessing and tokenization alignment
  - Add integration tests for end-to-end training pipeline
  - Implement performance tests comparing with existing custom training
  - Create tests for WandB integration and metric logging
  - Add validation tests for model compatibility and checkpoint loading
  - Implement --test-mode flag for CI with small data subset, reduced epochs, and no WandB
  - _Requirements: 4.4, 5.1, 5.2, 6.4, 8.4_

- [ ] 16. Create documentation and example configurations
  - Write comprehensive documentation for new training system
  - Create example YAML configurations for different training scenarios
  - Add migration guide from custom training to Hugging Face Trainer
  - Document data format requirements and preprocessing steps
  - Create troubleshooting guide for common training issues
  - _Requirements: 8.1, 8.2, 8.4_

- [ ] 17. Implement optional advanced features
  - Add support for pushing models to Hugging Face Hub
  - Implement gradient accumulation for large effective batch sizes
  - Add support for distributed training with multiple GPUs
  - Create model quantization options for production deployment
  - Implement automatic hyperparameter tuning with Optuna integration
  - _Requirements: 5.1, 5.2, 7.4_

- [ ] 18. Performance optimization and benchmarking
  - Benchmark training speed and memory usage against existing system
  - Optimize data loading and preprocessing for large datasets
  - Implement efficient batching and padding strategies
  - Add profiling tools for identifying performance bottlenecks
  - Create performance comparison reports and recommendations
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [ ] 19. Implement dataset caching and hashing system
  - Create dataset cache using hash of (label, input_path, filtering_strategy, tokenizer_version)
  - Store cached filtered+tokenized datasets in data/cache/{hash}/ directory
  - Implement cache validation and invalidation when parameters change
  - Add cache statistics and cleanup utilities for managing disk space
  - Create cache hit/miss logging for performance monitoring
  - _Requirements: 1.1, 1.4, 8.3_

- [ ] 20. Generate model cards and documentation
  - Create automatic model_card.md generation for each trained model
  - Include dataset info, labels, F1 scores, and inference examples in model cards
  - Add WandB run links and training configuration details
  - Generate README.md files in checkpoint directories with usage instructions
  - Implement model card templates for consistent documentation format
  - _Requirements: 8.1, 8.4_

- [ ] 21. Enhance test mode and CI support
  - Implement --test-mode flag with 50 sample limit and 1-2 epoch override
  - Add automatic config override for test mode (disable WandB, reduce resources)
  - Create Docker-compatible test mode for containerized validation
  - Add test mode validation for CI/CD pipelines
  - Implement quick smoke tests for model loading and basic inference
  - _Requirements: 5.1, 8.4_

- [ ] 22. Add inference output and prediction logging
  - Create predictions.jsonl output with sentence, true labels, and predicted labels
  - Implement validation set prediction logging during training
  - Add confidence scores and token-level prediction details
  - Create prediction analysis utilities for error analysis
  - Generate prediction summary reports with common error patterns
  - _Requirements: 4.1, 4.4, 8.4_

- [ ] 23. Implement versioning and experiment tracking
  - Add optional run_version field in configuration (e.g., "v1.2")
  - Update checkpoint naming to include version: PER_20250126_143022_v1.2/
  - Create version-based experiment grouping in WandB
  - Implement version comparison utilities for model performance tracking
  - Add version metadata to model cards and training reports
  - _Requirements: 3.4, 8.1, 8.4_

- [ ] 24. Create batch training and parallel execution
  - Implement batch_train_heads.py for training multiple entity types
  - Add support for parallel head training on multiple GPUs
  - Create job queue system for sequential training when resources are limited
  - Implement training progress monitoring across multiple heads
  - Add batch training reports and consolidated metrics
  - _Requirements: 5.1, 5.2, 8.1, 8.4_