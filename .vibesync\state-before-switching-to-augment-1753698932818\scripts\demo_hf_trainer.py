#!/usr/bin/env python3
"""
Demo script for Hugging Face Trainer integration.

This script demonstrates the complete training pipeline using the
HF Trainer with a small test dataset.
"""

import sys
import tempfile
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.training.hf_trainer import train_ner_model
from src.training.hf_config import create_test_hf_config


def main():
    """Run demo training."""
    print("🚀 Starting Hugging Face Trainer Demo")
    print("=" * 50)
    
    # Use test data
    data_path = Path(__file__).parent.parent / "data_sets" / "test_ner_data.json"
    
    if not data_path.exists():
        print(f"❌ Test data not found: {data_path}")
        return 1
    
    print(f"📊 Using training data: {data_path}")
    
    # Create test configuration
    config = create_test_hf_config()
    config.test_mode = True
    config.test_disable_wandb = True  # Disable WandB for demo
    config.epochs = 1  # Very short training for demo
    config.eval_steps = 2
    config.logging_steps = 1
    config.save_steps = 10
    
    print(f"⚙️  Configuration:")
    print(f"   - Epochs: {config.epochs}")
    print(f"   - Batch size: {config.batch_size}")
    print(f"   - Learning rate: {config.learning_rate}")
    print(f"   - Test mode: {config.test_mode}")
    print(f"   - WandB disabled: {config.test_disable_wandb}")
    
    try:
        # Create temporary output directory
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = Path(temp_dir) / "demo_output"
            
            print(f"📁 Output directory: {output_dir}")
            print("\n🏃 Starting training...")
            
            # Run training
            results = train_ner_model(
                data_path=str(data_path),
                config=config,
                output_dir=str(output_dir)
            )
            
            print("\n✅ Training completed successfully!")
            print("📈 Results:")
            
            # Print evaluation results
            eval_result = results.get("eval_result", {})
            if eval_result:
                for metric, value in eval_result.items():
                    if isinstance(value, (int, float)):
                        print(f"   - {metric}: {value:.4f}")
            
            # Print output information
            print(f"\n📁 Model saved to: {results.get('output_dir', 'N/A')}")
            
            # List output files
            if output_dir.exists():
                print("\n📄 Output files:")
                for file_path in sorted(output_dir.rglob("*")):
                    if file_path.is_file():
                        print(f"   - {file_path.relative_to(output_dir)}")
            
            print("\n🎉 Demo completed successfully!")
            return 0
            
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())