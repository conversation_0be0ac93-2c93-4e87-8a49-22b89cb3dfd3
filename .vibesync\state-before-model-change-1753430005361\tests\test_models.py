"""
Tests for model loading and basic functionality.
"""

import pytest
import torch
from unittest.mock import patch, Mock

from src.models.multitask_bertje import MultiTaskBERTje
from src.heads.ner_head import NERHead


class TestNERHead:
    """Test NER head functionality."""
    
    def test_ner_head_init(self):
        """Test NER head initialization."""
        head = NERHead(hidden_size=768, num_labels=9)
        
        assert head.num_labels == 9
        assert head.classifier.in_features == 768
        assert head.classifier.out_features == 9
    
    def test_ner_head_forward(self):
        """Test NER head forward pass."""
        head = NERHead(hidden_size=768, num_labels=9)
        
        # Test input
        batch_size, seq_len, hidden_size = 2, 10, 768
        hidden_states = torch.randn(batch_size, seq_len, hidden_size)
        
        # Forward pass without labels
        outputs = head(hidden_states)
        
        assert "logits" in outputs
        assert outputs["logits"].shape == (batch_size, seq_len, 9)
        assert "loss" not in outputs
    
    def test_ner_head_forward_with_labels(self):
        """Test NER head forward pass with labels."""
        head = NERHead(hidden_size=768, num_labels=9)
        
        # Test input
        batch_size, seq_len, hidden_size = 2, 10, 768
        hidden_states = torch.randn(batch_size, seq_len, hidden_size)
        labels = torch.randint(0, 9, (batch_size, seq_len))
        
        # Forward pass with labels
        outputs = head(hidden_states, labels)
        
        assert "logits" in outputs
        assert "loss" in outputs
        assert outputs["logits"].shape == (batch_size, seq_len, 9)
        assert outputs["loss"].item() >= 0
    
    def test_get_num_labels(self):
        """Test get_num_labels method."""
        head = NERHead(hidden_size=768, num_labels=5)
        assert head.get_num_labels() == 5


class TestMultiTaskBERTje:
    """Test multi-task BERTje model."""
    
    @pytest.mark.unit
    def test_model_init_with_mock(self, mock_model_loading):
        """Test model initialization with mocked components."""
        with patch('src.models.multitask_bertje.AutoModel') as mock_auto_model, \
             patch('src.models.multitask_bertje.AutoConfig') as mock_auto_config, \
             patch('src.models.multitask_bertje.AutoTokenizer') as mock_auto_tokenizer:
            
            # Setup mocks
            mock_config = Mock()
            mock_config.hidden_size = 768
            mock_config.num_labels = 9
            mock_config.architectures = []  # Empty list to avoid BertForTokenClassification path
            mock_auto_config.from_pretrained.return_value = mock_config
            
            mock_model = Mock()
            mock_auto_model.from_pretrained.return_value = mock_model
            
            mock_tokenizer = Mock()
            mock_auto_tokenizer.from_pretrained.return_value = mock_tokenizer
            
            # Initialize model
            model = MultiTaskBERTje("test_path")
            
            # Check initialization
            assert hasattr(model, 'encoder')
            assert hasattr(model, 'heads')
            assert 'ner' in model.heads
            assert 'compliance' in model.heads
            assert 'label' in model.heads
            assert 'reason' in model.heads
            assert 'topic' in model.heads
    
    @pytest.mark.unit
    def test_get_head(self, mock_model_loading):
        """Test get_head method."""
        with patch('src.models.multitask_bertje.AutoModel'), \
             patch('src.models.multitask_bertje.AutoConfig') as mock_config, \
             patch('src.models.multitask_bertje.AutoTokenizer'):
            
            mock_config.from_pretrained.return_value.hidden_size = 768
            mock_config.from_pretrained.return_value.num_labels = 9
            
            model = MultiTaskBERTje("test_path")
            
            # Test getting existing head
            ner_head = model.get_head('ner')
            assert ner_head is not None
            
            # Test getting non-existing head
            non_existing = model.get_head('non_existing')
            assert non_existing is None
    
    @pytest.mark.unit
    def test_add_head(self, mock_model_loading):
        """Test add_head method."""
        with patch('src.models.multitask_bertje.AutoModel'), \
             patch('src.models.multitask_bertje.AutoConfig') as mock_config, \
             patch('src.models.multitask_bertje.AutoTokenizer'):
            
            mock_config.from_pretrained.return_value.hidden_size = 768
            mock_config.from_pretrained.return_value.num_labels = 9
            
            model = MultiTaskBERTje("test_path")
            
            # Add new head
            new_head = torch.nn.Linear(768, 3)
            model.add_head('new_task', new_head)
            
            assert 'new_task' in model.heads
            assert model.get_head('new_task') is new_head
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_model_loading_real(self):
        """Test real model loading (requires model files)."""
        try:
            model = MultiTaskBERTje.from_pretrained()
            
            # Check basic structure
            assert hasattr(model, 'encoder')
            assert hasattr(model, 'heads')
            assert hasattr(model, 'tokenizer')
            
            # Check heads exist
            expected_heads = ['ner', 'compliance', 'label', 'reason', 'topic']
            for head_name in expected_heads:
                assert head_name in model.heads
            
        except Exception as e:
            pytest.skip(f"Model loading failed: {e}")
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_model_forward_real(self, sample_text):
        """Test real model forward pass (requires model files)."""
        try:
            model = MultiTaskBERTje.from_pretrained()
            model.eval()
            
            # Tokenize input
            inputs = model.tokenizer(
                sample_text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )
            
            # Forward pass
            with torch.no_grad():
                outputs = model(
                    input_ids=inputs['input_ids'],
                    attention_mask=inputs['attention_mask'],
                    heads=['ner']
                )
            
            # Check outputs
            assert 'ner' in outputs
            assert 'logits' in outputs['ner']
            
            logits = outputs['ner']['logits']
            assert logits.shape[0] == 1  # batch size
            assert logits.shape[2] == 9  # num_labels for NER
            
        except Exception as e:
            pytest.skip(f"Model forward pass failed: {e}")
    
    @pytest.mark.unit
    def test_model_forward_mock(self, mock_model_loading):
        """Test model forward pass with mocked components."""
        with patch('src.models.multitask_bertje.AutoModel') as mock_auto_model, \
             patch('src.models.multitask_bertje.AutoConfig') as mock_auto_config, \
             patch('src.models.multitask_bertje.AutoTokenizer') as mock_auto_tokenizer:
            
            # Setup mocks
            mock_config = Mock()
            mock_config.hidden_size = 768
            mock_config.num_labels = 9
            mock_config.architectures = []  # Empty list to avoid BertForTokenClassification path
            mock_auto_config.from_pretrained.return_value = mock_config
            
            # Mock encoder output
            mock_encoder_output = Mock()
            mock_encoder_output.last_hidden_state = torch.randn(1, 10, 768)
            mock_encoder_output.pooler_output = torch.randn(1, 768)
            
            mock_encoder = Mock()
            mock_encoder.return_value = mock_encoder_output
            mock_auto_model.from_pretrained.return_value = mock_encoder
            
            mock_tokenizer = Mock()
            mock_auto_tokenizer.from_pretrained.return_value = mock_tokenizer
            
            # Initialize and test model
            model = MultiTaskBERTje("test_path")
            
            # Test forward pass
            input_ids = torch.randint(0, 1000, (1, 10))
            attention_mask = torch.ones(1, 10)
            
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                heads=['ner']
            )
            
            # Check outputs
            assert 'ner' in outputs
            assert 'logits' in outputs['ner']
