"""
Tests for HFTrainingConfig with YAML support.

This module tests the HFTrainingConfig dataclass functionality including
YAML loading, TrainingArguments conversion, validation, and configuration
management.
"""

import os
import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch, MagicMock

import torch
from transformers import TrainingArguments

from src.training.hf_config import (
    HFTrainingConfig,
    create_default_hf_config,
    create_test_hf_config
)


class TestHFTrainingConfig:
    """Test HFTrainingConfig functionality."""
    
    def test_default_initialization(self):
        """Test default configuration initialization."""
        config = HFTrainingConfig()
        
        assert config.model_name == "DTAI-KULeuven/robbert-2023-dutch-base"
        assert config.epochs == 3
        assert config.batch_size == 8
        assert config.learning_rate == 5e-5
        assert config.wandb_project == "robbert2023-ner"
        assert config.wandb_entity == "slippydongle"
        assert config.early_stopping_patience == 3
        assert config.use_class_weights is False
        assert config.test_mode is False
    
    def test_custom_initialization(self):
        """Test custom configuration initialization."""
        config = HFTrainingConfig(
            epochs=5,
            batch_size=16,
            learning_rate=3e-5,
            use_class_weights=True,
            class_weights={"O": 0.5, "B-PER": 2.0, "I-PER": 1.5}
        )
        
        assert config.epochs == 5
        assert config.batch_size == 16
        assert config.learning_rate == 3e-5
        assert config.use_class_weights is True
        assert config.class_weights == {"O": 0.5, "B-PER": 2.0, "I-PER": 1.5}
    
    def test_post_init_gpu_detection(self):
        """Test GPU detection in post-init."""
        with patch('torch.cuda.is_available', return_value=False):
            config = HFTrainingConfig(use_gpu=True, fp16=True)
            assert config.use_gpu is False
            assert config.fp16 is False
    
    def test_post_init_test_mode(self):
        """Test test mode overrides in post-init."""
        config = HFTrainingConfig(
            test_mode=True,
            epochs=10,
            test_epochs=2,
            report_to="wandb"
        )
        
        assert config.epochs == 2  # Overridden by test_epochs
        assert config.report_to is None  # Disabled in test mode
    
    def test_post_init_scheduler_validation(self):
        """Test learning rate scheduler validation."""
        with pytest.raises(ValueError, match="Invalid lr_scheduler_type"):
            HFTrainingConfig(lr_scheduler_type="invalid_scheduler")
    
    def test_post_init_evaluation_strategy_validation(self):
        """Test evaluation strategy validation."""
        with pytest.raises(ValueError, match="Invalid evaluation_strategy"):
            HFTrainingConfig(evaluation_strategy="invalid_strategy")
    
    def test_from_yaml_basic(self):
        """Test loading configuration from YAML file."""
        yaml_content = {
            "hf_training": {
                "epochs": 5,
                "batch_size": 16,
                "learning_rate": 3e-5,
                "wandb_project": "test-project"
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(yaml_content, f)
            temp_path = f.name
        
        try:
            config = HFTrainingConfig.from_yaml(temp_path)
            assert config.epochs == 5
            assert config.batch_size == 16
            assert config.learning_rate == 3e-5
            assert config.wandb_project == "test-project"
        finally:
            os.unlink(temp_path)
    
    def test_from_yaml_env_substitution(self):
        """Test environment variable substitution in YAML."""
        yaml_content = {
            "hf_training": {
                "model_name": "${TEST_MODEL_NAME:-default-model}",
                "wandb_project": "${TEST_PROJECT:-default-project}",
                "epochs": 3
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(yaml_content, f)
            temp_path = f.name
        
        try:
            # Test with environment variable set
            with patch.dict(os.environ, {'TEST_MODEL_NAME': 'env-model', 'TEST_PROJECT': 'env-project'}):
                config = HFTrainingConfig.from_yaml(temp_path)
                assert config.model_name == "env-model"
                assert config.wandb_project == "env-project"
            
            # Test with default values
            with patch.dict(os.environ, {}, clear=True):
                config = HFTrainingConfig.from_yaml(temp_path)
                assert config.model_name == "default-model"
                assert config.wandb_project == "default-project"
        finally:
            os.unlink(temp_path)
    
    def test_from_yaml_root_level(self):
        """Test loading configuration from root level YAML."""
        yaml_content = {
            "epochs": 4,
            "batch_size": 12,
            "learning_rate": 4e-5
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(yaml_content, f)
            temp_path = f.name
        
        try:
            config = HFTrainingConfig.from_yaml(temp_path)
            assert config.epochs == 4
            assert config.batch_size == 12
            assert config.learning_rate == 4e-5
        finally:
            os.unlink(temp_path)
    
    def test_from_yaml_file_not_found(self):
        """Test error handling for missing YAML file."""
        with pytest.raises(FileNotFoundError):
            HFTrainingConfig.from_yaml("nonexistent.yaml")
    
    def test_from_yaml_invalid_yaml(self):
        """Test error handling for invalid YAML."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("invalid: yaml: content: [")
            temp_path = f.name
        
        try:
            with pytest.raises(yaml.YAMLError):
                HFTrainingConfig.from_yaml(temp_path)
        finally:
            os.unlink(temp_path)
    
    def test_from_yaml_invalid_config(self):
        """Test error handling for invalid configuration values."""
        yaml_content = {
            "hf_training": {
                "epochs": "invalid",  # Should be int
                "batch_size": -1      # Should be positive
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(yaml_content, f)
            temp_path = f.name
        
        try:
            with pytest.raises(ValueError):
                HFTrainingConfig.from_yaml(temp_path)
        finally:
            os.unlink(temp_path)
    
    def test_to_training_args_basic(self):
        """Test conversion to TrainingArguments."""
        config = HFTrainingConfig(
            epochs=3,
            batch_size=8,
            learning_rate=5e-5,
            output_dir="test_output"
        )
        
        training_args = config.to_training_args()
        
        assert isinstance(training_args, TrainingArguments)
        assert training_args.num_train_epochs == 3
        assert training_args.per_device_train_batch_size == 8
        assert training_args.learning_rate == 5e-5
        assert training_args.output_dir == "test_output"
    
    def test_to_training_args_with_override(self):
        """Test TrainingArguments conversion with output_dir override."""
        config = HFTrainingConfig(output_dir="default_output")
        training_args = config.to_training_args(output_dir="override_output")
        
        assert training_args.output_dir == "override_output"
    
    def test_to_training_args_wandb_config(self):
        """Test WandB configuration in TrainingArguments."""
        config = HFTrainingConfig(
            report_to="wandb",
            run_name="test_run"
        )
        
        training_args = config.to_training_args()
        assert training_args.report_to == ["wandb"]
        assert training_args.run_name == "test_run"
    
    def test_to_training_args_test_mode(self):
        """Test TrainingArguments in test mode."""
        config = HFTrainingConfig(
            test_mode=True,
            test_disable_wandb=True,
            report_to="wandb"
        )
        
        training_args = config.to_training_args()
        assert training_args.report_to == []  # Empty list disables reporting
    
    @patch('torch.cuda.is_available', return_value=True)
    def test_to_training_args_gpu_settings(self, mock_cuda):
        """Test GPU-specific settings in TrainingArguments."""
        config = HFTrainingConfig(
            use_gpu=True,
            fp16=True,
            bf16=False
        )
        
        training_args = config.to_training_args()
        assert training_args.fp16 is True
        assert training_args.bf16 is False
    
    @patch('torch.cuda.is_available', return_value=False)
    def test_to_training_args_cpu_settings(self, mock_cuda):
        """Test CPU-specific settings in TrainingArguments."""
        config = HFTrainingConfig(
            use_gpu=False,
            fp16=True,  # Should be disabled on CPU
            bf16=True   # Should be disabled on CPU
        )
        
        training_args = config.to_training_args()
        assert training_args.fp16 is False
        assert training_args.bf16 is False
    
    def test_to_dict(self):
        """Test conversion to dictionary."""
        config = HFTrainingConfig(
            epochs=5,
            batch_size=16,
            learning_rate=3e-5
        )
        
        config_dict = config.to_dict()
        
        assert isinstance(config_dict, dict)
        assert config_dict["epochs"] == 5
        assert config_dict["batch_size"] == 16
        assert config_dict["learning_rate"] == 3e-5
        assert "model_name" in config_dict
    
    def test_save_yaml(self):
        """Test saving configuration to YAML file."""
        config = HFTrainingConfig(
            epochs=5,
            batch_size=16,
            learning_rate=3e-5,
            wandb_project="test-save"
        )
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            temp_path = f.name
        
        try:
            config.save_yaml(temp_path)
            
            # Load and verify
            with open(temp_path, 'r') as f:
                saved_data = yaml.safe_load(f)
            
            assert "hf_training" in saved_data
            assert saved_data["hf_training"]["epochs"] == 5
            assert saved_data["hf_training"]["batch_size"] == 16
            assert saved_data["hf_training"]["wandb_project"] == "test-save"
        finally:
            os.unlink(temp_path)
    
    def test_get_wandb_config(self):
        """Test WandB configuration extraction."""
        config = HFTrainingConfig(
            wandb_project="test-project",
            wandb_entity="test-entity",
            run_name="test-run",
            wandb_tags=["tag1", "tag2"],
            wandb_notes="test notes",
            epochs=5,
            batch_size=16
        )
        
        wandb_config = config.get_wandb_config()
        
        assert wandb_config["project"] == "test-project"
        assert wandb_config["entity"] == "test-entity"
        assert wandb_config["name"] == "test-run"
        assert wandb_config["tags"] == ["tag1", "tag2"]
        assert wandb_config["notes"] == "test notes"
        assert wandb_config["config"]["epochs"] == 5
        assert wandb_config["config"]["batch_size"] == 16
    
    def test_create_output_dir(self):
        """Test output directory creation."""
        config = HFTrainingConfig(
            output_dir="test_checkpoints",
            run_version="v1.0"
        )
        
        output_dir = config.create_output_dir("training")
        
        assert "training_" in output_dir
        assert "v1.0" in output_dir
        assert Path(output_dir).exists()
        
        # Cleanup
        import shutil
        shutil.rmtree(output_dir)
    
    def test_create_output_dir_with_experiment_name(self):
        """Test output directory creation with experiment name."""
        config = HFTrainingConfig(
            output_dir="test_checkpoints",
            experiment_name="ner_experiment",
            run_version="v2.0"
        )
        
        output_dir = config.create_output_dir()
        
        assert "ner_experiment_" in output_dir
        assert "v2.0" in output_dir
        assert Path(output_dir).exists()
        
        # Cleanup
        import shutil
        shutil.rmtree(output_dir)
    
    def test_validate_class_weights_valid(self):
        """Test class weights validation with valid weights."""
        config = HFTrainingConfig(
            use_class_weights=True,
            class_weights={"O": 0.5, "B-PER": 2.0, "I-PER": 1.5}
        )
        
        label_list = ["O", "B-PER", "I-PER"]
        # Should not raise any exception
        config.validate_class_weights(label_list)
    
    def test_validate_class_weights_missing_labels(self):
        """Test class weights validation with missing labels."""
        config = HFTrainingConfig(
            use_class_weights=True,
            class_weights={"O": 0.5, "B-PER": 2.0}  # Missing I-PER
        )
        
        label_list = ["O", "B-PER", "I-PER"]
        with pytest.raises(ValueError, match="Missing class weights"):
            config.validate_class_weights(label_list)
    
    def test_validate_class_weights_invalid_values(self):
        """Test class weights validation with invalid weight values."""
        config = HFTrainingConfig(
            use_class_weights=True,
            class_weights={"O": 0.5, "B-PER": -1.0, "I-PER": 1.5}  # Negative weight
        )
        
        label_list = ["O", "B-PER", "I-PER"]
        with pytest.raises(ValueError, match="Invalid class weight"):
            config.validate_class_weights(label_list)
    
    def test_validate_class_weights_disabled(self):
        """Test class weights validation when disabled."""
        config = HFTrainingConfig(use_class_weights=False)
        
        label_list = ["O", "B-PER", "I-PER"]
        # Should not raise any exception
        config.validate_class_weights(label_list)


class TestConfigurationFactories:
    """Test configuration factory functions."""
    
    def test_create_default_hf_config(self):
        """Test default configuration factory."""
        config = create_default_hf_config()
        
        assert isinstance(config, HFTrainingConfig)
        assert config.epochs == 3
        assert config.batch_size == 8
        assert config.test_mode is False
    
    def test_create_test_hf_config(self):
        """Test test configuration factory."""
        config = create_test_hf_config()
        
        assert isinstance(config, HFTrainingConfig)
        assert config.test_mode is True
        assert config.test_epochs == 1
        assert config.test_sample_limit == 50
        assert config.eval_steps == 10
        assert config.early_stopping_patience == 1


class TestConfigurationIntegration:
    """Test configuration integration with existing templates."""
    
    def test_load_default_template(self):
        """Test loading default configuration template."""
        template_path = "src/config/hf_training_default.yaml"
        
        if Path(template_path).exists():
            config = HFTrainingConfig.from_yaml(template_path)
            assert isinstance(config, HFTrainingConfig)
            assert config.epochs >= 1
            assert config.batch_size >= 1
    
    def test_load_test_template(self):
        """Test loading test configuration template."""
        template_path = "src/config/hf_training_test.yaml"
        
        if Path(template_path).exists():
            config = HFTrainingConfig.from_yaml(template_path)
            assert isinstance(config, HFTrainingConfig)
            assert config.test_mode is True
            assert config.test_epochs == 1
    
    def test_load_class_weights_template(self):
        """Test loading class weights configuration template."""
        template_path = "src/config/hf_training_class_weights.yaml"
        
        if Path(template_path).exists():
            config = HFTrainingConfig.from_yaml(template_path)
            assert isinstance(config, HFTrainingConfig)
            assert config.use_class_weights is True
            assert config.class_weights is not None
            assert len(config.class_weights) > 0


if __name__ == "__main__":
    pytest.main([__file__])