#!/usr/bin/env python3
"""
Document Processing Pipeline for BERTje NER model.
Processes all .txt files in process_data/input/ directory and generates detailed NER reports.
"""

import os
import sys
import torch
import logging
import argparse
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple
import traceback

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.models.multitask_bertje import MultiTaskBERTje
from src.config import load_config


def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Setup logging for document processing."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def ensure_directories_exist(input_dir: Path, output_dir: Path) -> None:
    """Ensure input and output directories exist."""
    input_dir.mkdir(parents=True, exist_ok=True)
    output_dir.mkdir(parents=True, exist_ok=True)


def find_txt_files(input_dir: Path) -> List[Path]:
    """Find all .txt files in the input directory."""
    return list(input_dir.glob("*.txt"))


def extract_entities_from_predictions(text: str, predictions: torch.Tensor,
                                    tokenizer, logits: torch.Tensor) -> List[Dict]:
    """Extract entities from model predictions with improved subword handling."""

    # CoNLL-2002 label mapping (from model config)
    id2label = {
        0: 'B-LOC', 1: 'B-MISC', 2: 'B-ORG', 3: 'B-PER', 4: 'I-LOC',
        5: 'I-MISC', 6: 'I-ORG', 7: 'I-PER', 8: 'O'
    }

    # Get tokens and predictions
    tokens = tokenizer.convert_ids_to_tokens(tokenizer.encode(text))
    predictions = predictions[:len(tokens)]

    entities = []
    current_entity = None

    for i, (token, pred_id) in enumerate(zip(tokens, predictions)):
        if token in ['[CLS]', '[SEP]', '[PAD]']:
            continue

        label = id2label.get(pred_id.item(), 'O')
        confidence = torch.softmax(logits[i], dim=-1).max().item()

        if label.startswith('B-'):
            # Start of new entity
            if current_entity:
                # Clean up the previous entity text
                current_entity['text'] = clean_entity_text(current_entity['text'])
                entities.append(current_entity)

            entity_type = label[2:]
            # Handle subword tokens properly
            entity_text = token.replace('##', '') if not token == '[UNK]' else '[UNKNOWN]'

            current_entity = {
                'text': entity_text,
                'label': entity_type,
                'confidence': confidence,
                'start_token': i,
                'tokens': [token],
                'has_unknown': token == '[UNK]'
            }
        elif label.startswith('I-') and current_entity:
            # Continuation of entity
            entity_type = label[2:]
            if current_entity['label'] == entity_type:
                # Handle subword continuation
                if token == '[UNK]':
                    current_entity['text'] += '[?]'  # Mark unknown parts
                    current_entity['has_unknown'] = True
                else:
                    current_entity['text'] += token.replace('##', '')

                current_entity['confidence'] = min(current_entity['confidence'], confidence)
                current_entity['tokens'].append(token)
        else:
            # End of entity
            if current_entity:
                # Clean up the entity text before adding
                current_entity['text'] = clean_entity_text(current_entity['text'])
                entities.append(current_entity)
                current_entity = None

    # Add final entity if exists
    if current_entity:
        current_entity['text'] = clean_entity_text(current_entity['text'])
        entities.append(current_entity)

    return entities


def clean_entity_text(text: str) -> str:
    """Clean up entity text by removing artifacts and improving readability."""
    # Remove multiple consecutive unknown markers
    import re
    text = re.sub(r'\[?\?\]+', '[?]', text)

    # Clean up common artifacts
    text = text.replace('[UNKNOWN]', '[?]')

    # Remove leading/trailing punctuation that might be artifacts
    text = text.strip("'\".,;:!?")

    return text


def generate_token_predictions(text: str, predictions: torch.Tensor,
                             tokenizer, logits: torch.Tensor) -> List[Dict]:
    """Generate detailed token-level predictions with improved subword handling."""

    # CoNLL-2002 label mapping (from model config)
    id2label = {
        0: 'B-LOC', 1: 'B-MISC', 2: 'B-ORG', 3: 'B-PER', 4: 'I-LOC',
        5: 'I-MISC', 6: 'I-ORG', 7: 'I-PER', 8: 'O'
    }

    # Get tokens and predictions
    tokens = tokenizer.convert_ids_to_tokens(tokenizer.encode(text))
    predictions = predictions[:len(tokens)]

    token_predictions = []

    for i, (token, pred_id) in enumerate(zip(tokens, predictions)):
        if token in ['[CLS]', '[SEP]', '[PAD]']:
            continue

        label = id2label.get(pred_id.item(), 'O')
        confidence = torch.softmax(logits[i], dim=-1).max().item()

        # Clean up token display
        display_token = token
        if token.startswith('##'):
            display_token = f"[subword]{token}"  # Mark subwords clearly
        elif token == '[UNK]':
            display_token = "[UNKNOWN]"  # More descriptive

        token_predictions.append({
            'token': display_token,
            'original_token': token,
            'label': label,
            'confidence': confidence,
            'position': i,
            'is_subword': token.startswith('##'),
            'is_unknown': token == '[UNK]'
        })

    return token_predictions


def calculate_statistics(entities: List[Dict]) -> Dict:
    """Calculate entity statistics."""
    stats = {
        'total_entities': len(entities),
        'by_type': {'PER': 0, 'LOC': 0, 'ORG': 0, 'MISC': 0},
        'avg_confidence': 0.0,
        'min_confidence': 1.0,
        'max_confidence': 0.0
    }
    
    if entities:
        confidences = [entity['confidence'] for entity in entities]
        stats['avg_confidence'] = sum(confidences) / len(confidences)
        stats['min_confidence'] = min(confidences)
        stats['max_confidence'] = max(confidences)
        
        for entity in entities:
            entity_type = entity['label']
            if entity_type in stats['by_type']:
                stats['by_type'][entity_type] += 1
    
    return stats


def generate_detailed_report(input_file: Path, text: str, entities: List[Dict], 
                           token_predictions: List[Dict], stats: Dict, 
                           processing_time: float, model_info: Dict) -> str:
    """Generate detailed NER report in the same format as smoke test."""
    
    report_lines = []
    
    # Header
    report_lines.append("=" * 80)
    report_lines.append("BERTje Multi-task Model - Document NER Processing Report")
    report_lines.append("=" * 80)
    report_lines.append(f"Input File: {input_file.name}")
    report_lines.append(f"Processing Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append(f"Model: {model_info['model_name']}")
    report_lines.append(f"Device: {model_info['device']}")
    report_lines.append(f"Processing Duration: {processing_time:.2f} seconds")
    report_lines.append("")
    
    # Original text
    report_lines.append("ORIGINAL TEXT:")
    report_lines.append("-" * 50)
    report_lines.append(f"'{text}'")
    report_lines.append("")
    
    # Token-level predictions with explanations
    report_lines.append("TOKEN-LEVEL NER PREDICTIONS:")
    report_lines.append("-" * 50)

    # Count subwords and unknowns for summary
    subword_count = sum(1 for tp in token_predictions if tp.get('is_subword', False))
    unknown_count = sum(1 for tp in token_predictions if tp.get('is_unknown', False))

    if subword_count > 0 or unknown_count > 0:
        report_lines.append("NOTE: This text contains complex tokenization:")
        if subword_count > 0:
            report_lines.append(f"  - {subword_count} subword tokens (marked with [subword]##)")
        if unknown_count > 0:
            report_lines.append(f"  - {unknown_count} unknown tokens (marked as [UNKNOWN])")
        report_lines.append("  This is normal for emails, URLs, and non-Dutch words.")
        report_lines.append("")

    for token_pred in token_predictions:
        token = token_pred['token']
        label = token_pred['label']
        confidence = token_pred['confidence']
        report_lines.append(f"{token:20} -> {label:8} (confidence: {confidence:.3f})")
    report_lines.append("")
    
    # Extracted entities
    report_lines.append("EXTRACTED ENTITIES:")
    report_lines.append("-" * 50)
    if entities:
        for i, entity in enumerate(entities, 1):
            entity_text = entity['text']
            entity_label = entity['label']
            confidence = entity['confidence']

            # Add note for entities with unknown parts
            note = ""
            if entity.get('has_unknown', False):
                note = " [contains unknown tokens]"

            report_lines.append(f"  {i}. {entity_text} -> {entity_label} (confidence: {confidence:.3f}){note}")
    else:
        report_lines.append("  No entities detected.")
    report_lines.append("")
    
    # Statistics
    report_lines.append("PROCESSING STATISTICS:")
    report_lines.append("-" * 50)
    report_lines.append(f"Total Entities Found: {stats['total_entities']}")
    report_lines.append("Entities by Type:")
    for entity_type, count in stats['by_type'].items():
        report_lines.append(f"  {entity_type}: {count}")
    
    if stats['total_entities'] > 0:
        report_lines.append(f"Average Confidence: {stats['avg_confidence']:.3f}")
        report_lines.append(f"Confidence Range: {stats['min_confidence']:.3f} - {stats['max_confidence']:.3f}")
    
    # Success status
    report_lines.append("")
    report_lines.append("PROCESSING STATUS:")
    report_lines.append("-" * 50)
    if stats['total_entities'] > 0:
        report_lines.append("✅ SUCCESS: Entities detected and processed successfully!")
        high_conf_entities = [e for e in entities if e['confidence'] >= 0.9]
        if high_conf_entities:
            report_lines.append(f"✅ HIGH CONFIDENCE: {len(high_conf_entities)} entities with 90%+ confidence")
    else:
        report_lines.append("ℹ️  INFO: No entities detected in this document.")
    
    report_lines.append("")
    report_lines.append("=" * 80)
    
    return "\n".join(report_lines)


def process_single_document(input_file: Path, model, device, logger) -> Tuple[bool, str]:
    """Process a single document and return success status and error message."""
    
    try:
        # Read input file
        logger.info(f"Reading file: {input_file.name}")
        with open(input_file, 'r', encoding='utf-8') as f:
            text = f.read().strip()
        
        if not text:
            logger.warning(f"File {input_file.name} is empty, skipping...")
            return False, "Empty file"
        
        logger.info(f"Processing text: '{text[:100]}{'...' if len(text) > 100 else ''}'")
        
        # Record processing start time
        start_time = datetime.now()
        
        # Tokenize and run inference
        inputs = model.tokenizer(
            text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        )
        
        # Move to device
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        # Run NER inference
        with torch.no_grad():
            outputs = model(
                input_ids=inputs['input_ids'],
                attention_mask=inputs['attention_mask'],
                heads=['ner']
            )
        
        # Process results
        ner_output = outputs['ner']
        logits = ner_output['logits']
        predictions = torch.argmax(logits, dim=-1)
        
        # Extract entities and token predictions
        entities = extract_entities_from_predictions(
            text, predictions[0], model.tokenizer, logits[0]
        )
        
        token_predictions = generate_token_predictions(
            text, predictions[0], model.tokenizer, logits[0]
        )
        
        # Calculate statistics
        stats = calculate_statistics(entities)
        
        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Model information
        model_info = {
            'model_name': 'BERTje Multi-task (wietsedv/bert-base-dutch-cased-finetuned-conll2002-ner)',
            'device': str(device)
        }
        
        # Generate detailed report
        report = generate_detailed_report(
            input_file, text, entities, token_predictions, 
            stats, processing_time, model_info
        )
        
        logger.info(f"Successfully processed {input_file.name}: {stats['total_entities']} entities found")
        
        return True, report
        
    except Exception as e:
        error_msg = f"Error processing {input_file.name}: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        return False, error_msg


def move_processed_file(input_file: Path, output_dir: Path, logger) -> bool:
    """Move processed file from input to output directory."""
    try:
        destination = output_dir / input_file.name
        shutil.move(str(input_file), str(destination))
        logger.info(f"Moved {input_file.name} to output directory")
        return True
    except Exception as e:
        logger.error(f"Failed to move {input_file.name}: {str(e)}")
        return False


def write_report_file(output_dir: Path, input_filename: str, report: str, logger) -> bool:
    """Write report to output file."""
    try:
        # Generate output filename
        base_name = Path(input_filename).stem
        report_filename = f"{base_name}_result.txt"
        report_path = output_dir / report_filename

        # Write report
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)

        logger.info(f"Report written to: {report_filename}")
        return True

    except Exception as e:
        logger.error(f"Failed to write report for {input_filename}: {str(e)}")
        return False


def write_error_log(output_dir: Path, input_filename: str, error_msg: str, logger) -> None:
    """Write error log for failed processing."""
    try:
        base_name = Path(input_filename).stem
        error_filename = f"{base_name}_error.txt"
        error_path = output_dir / error_filename

        error_report = f"""
ERROR PROCESSING REPORT
=====================
File: {input_filename}
Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Error: {error_msg}

This file could not be processed due to the error above.
Please check the file format and content, then try again.
"""

        with open(error_path, 'w', encoding='utf-8') as f:
            f.write(error_report.strip())

        logger.info(f"Error log written to: {error_filename}")

    except Exception as e:
        logger.error(f"Failed to write error log for {input_filename}: {str(e)}")


def process_documents_batch(input_dir: Path, output_dir: Path,
                          move_files: bool = True, logger = None) -> Dict:
    """Process all documents in the input directory."""

    if logger is None:
        logger = setup_logging()

    logger.info("=" * 80)
    logger.info("BERTje Document Processing Pipeline")
    logger.info("=" * 80)

    # Ensure directories exist
    ensure_directories_exist(input_dir, output_dir)

    # Find input files
    txt_files = find_txt_files(input_dir)

    if not txt_files:
        logger.info(f"No .txt files found in {input_dir}")
        return {
            'total_files': 0,
            'processed_successfully': 0,
            'failed': 0,
            'files_processed': [],
            'files_failed': []
        }

    logger.info(f"Found {len(txt_files)} .txt files to process")

    # Load model (prefer improved model if available)
    logger.info("Loading BERTje multi-task model...")
    try:
        # Check for improved model first
        checkpoints_dir = Path("src/models/checkpoints")
        model_dirs = [d for d in checkpoints_dir.glob("best_compound_model_*") if d.is_dir()]
        
        if model_dirs:
            # Use the latest trained model
            latest_model_dir = max(model_dirs, key=lambda x: x.stat().st_mtime)
            logger.info(f"Using improved compound model from: {latest_model_dir}")
            model = MultiTaskBERTje.load_pretrained(str(latest_model_dir))
        else:
            logger.info("Using original pre-trained model")
            model = MultiTaskBERTje.from_pretrained()
        
        model.eval()

        # Setup device
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model.to(device)
        logger.info(f"Model loaded on {device}")

    except Exception as e:
        logger.error(f"Failed to load model: {str(e)}")
        return {
            'total_files': len(txt_files),
            'processed_successfully': 0,
            'failed': len(txt_files),
            'files_processed': [],
            'files_failed': [f.name for f in txt_files],
            'error': f"Model loading failed: {str(e)}"
        }

    # Process each file
    results = {
        'total_files': len(txt_files),
        'processed_successfully': 0,
        'failed': 0,
        'files_processed': [],
        'files_failed': []
    }

    for i, input_file in enumerate(txt_files, 1):
        logger.info(f"\nProcessing file {i}/{len(txt_files)}: {input_file.name}")

        # Process the document
        success, result = process_single_document(input_file, model, device, logger)

        if success:
            # Write report file
            if write_report_file(output_dir, input_file.name, result, logger):
                # Move original file if requested
                if move_files:
                    if move_processed_file(input_file, output_dir, logger):
                        results['processed_successfully'] += 1
                        results['files_processed'].append(input_file.name)
                    else:
                        logger.warning(f"Report generated but failed to move {input_file.name}")
                        results['processed_successfully'] += 1
                        results['files_processed'].append(input_file.name)
                else:
                    results['processed_successfully'] += 1
                    results['files_processed'].append(input_file.name)
            else:
                results['failed'] += 1
                results['files_failed'].append(input_file.name)
        else:
            # Write error log
            write_error_log(output_dir, input_file.name, result, logger)
            results['failed'] += 1
            results['files_failed'].append(input_file.name)

    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("PROCESSING SUMMARY")
    logger.info("=" * 80)
    logger.info(f"Total files: {results['total_files']}")
    logger.info(f"Successfully processed: {results['processed_successfully']}")
    logger.info(f"Failed: {results['failed']}")

    if results['files_processed']:
        logger.info("Successfully processed files:")
        for filename in results['files_processed']:
            logger.info(f"  ✅ {filename}")

    if results['files_failed']:
        logger.info("Failed files:")
        for filename in results['files_failed']:
            logger.info(f"  ❌ {filename}")

    logger.info("=" * 80)

    return results


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="Process documents with BERTje NER model",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m src.inference.process_documents
  python -m src.inference.process_documents --input-dir custom_input --output-dir custom_output
  python -m src.inference.process_documents --no-move-files --log-level DEBUG
        """
    )

    parser.add_argument(
        '--input-dir',
        type=str,
        default='process_data/input',
        help='Input directory containing .txt files (default: process_data/input)'
    )

    parser.add_argument(
        '--output-dir',
        type=str,
        default='process_data/output',
        help='Output directory for reports (default: process_data/output)'
    )

    parser.add_argument(
        '--no-move-files',
        action='store_true',
        help='Do not move processed files to output directory'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )

    args = parser.parse_args()

    # Setup logging
    logger = setup_logging(args.log_level)

    # Convert paths
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    move_files = not args.no_move_files

    # Process documents
    try:
        results = process_documents_batch(input_dir, output_dir, move_files, logger)

        # Exit with appropriate code
        if results['failed'] == 0:
            logger.info("🎉 All documents processed successfully!")
            sys.exit(0)
        elif results['processed_successfully'] > 0:
            logger.warning("⚠️  Some documents processed successfully, some failed.")
            sys.exit(1)
        else:
            logger.error("❌ All documents failed to process.")
            sys.exit(2)

    except KeyboardInterrupt:
        logger.info("Processing interrupted by user.")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == '__main__':
    main()
