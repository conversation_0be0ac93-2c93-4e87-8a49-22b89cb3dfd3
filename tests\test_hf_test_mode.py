"""
Tests for --test-mode flag implementation and CI compatibility.

This module specifically tests the test mode functionality that allows
running training with reduced resources and disabled external services
for CI/CD pipelines.
"""

import pytest
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import Mock, patch

from src.training.hf_config import HFTrainingConfig, create_test_hf_config
from src.training.hf_trainer import <PERSON><PERSON><PERSON><PERSON>rain<PERSON>, train_ner_model


class TestModeConfiguration:
    """Test --test-mode flag configuration and behavior."""
    
    def test_test_mode_flag_activation(self):
        """Test that test mode can be activated via configuration."""
        # Test default config (not test mode)
        default_config = HFTrainingConfig()
        assert default_config.test_mode == False
        assert default_config.epochs == 3  # Default epochs
        assert default_config.test_disable_wandb == True  # But WandB disabled by default
        
        # Test explicit test mode
        test_config = HFTrainingConfig(test_mode=True)
        assert test_config.test_mode == True
        assert test_config.epochs == test_config.test_epochs == 1
        assert test_config.test_disable_wandb == True
    
    def test_test_mode_from_yaml(self):
        """Test test mode activation from YAML configuration."""
        yaml_content = """
hf_training:
  test_mode: true
  test_epochs: 2
  test_sample_limit: 25
  test_disable_wandb: true
  epochs: 5  # Should be overridden by test_epochs
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(yaml_content)
            yaml_path = f.name
        
        try:
            config = HFTrainingConfig.from_yaml(yaml_path)
            
            # Test mode should override epochs
            assert config.test_mode == True
            assert config.epochs == 2  # test_epochs value
            assert config.test_sample_limit == 25
            assert config.test_disable_wandb == True
            
        finally:
            Path(yaml_path).unlink()
    
    def test_test_mode_environment_variable_override(self):
        """Test test mode activation via environment variables."""
        yaml_content = """
hf_training:
  test_mode: true
  test_epochs: 2
  wandb_project: ci-test
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(yaml_content)
            yaml_path = f.name
        
        try:
            # Test with environment variables set
            os.environ['TEST_MODE'] = 'true'
            os.environ['TEST_EPOCHS'] = '2'
            os.environ['WANDB_PROJECT'] = 'ci-test'
            
            config = HFTrainingConfig.from_yaml(yaml_path)
            
            assert config.test_mode == True
            assert config.epochs == 2
            assert config.wandb_project == 'ci-test'
            
        finally:
            # Clean up environment variables
            os.environ.pop('TEST_MODE', None)
            os.environ.pop('TEST_EPOCHS', None)
            os.environ.pop('WANDB_PROJECT', None)
            Path(yaml_path).unlink()
    
    def test_create_test_hf_config_factory(self):
        """Test the create_test_hf_config factory function."""
        config = create_test_hf_config()
        
        # Verify test mode settings
        assert config.test_mode == True
        assert config.epochs == 1
        assert config.test_sample_limit == 50
        assert config.test_disable_wandb == True
        
        # Verify reduced resource settings
        assert config.eval_steps == 10
        assert config.logging_steps == 5
        assert config.save_steps == 20
        assert config.early_stopping_patience == 1
        
        # Verify CI-friendly settings
        assert config.use_gpu == False or config.use_gpu == True  # Depends on availability
        assert config.dataloader_num_workers <= 2
        assert config.seed == 42


class TestModeResourceLimitation:
    """Test resource limitation in test mode."""
    
    def test_dataset_size_limitation(self):
        """Test that datasets are limited in test mode."""
        config = create_test_hf_config()
        
        # Create sample data larger than test limit
        large_dataset = []
        for i in range(100):  # More than test_sample_limit (50)
            large_dataset.append({
                "id": i,
                "sentence": f"Test sentence {i} with person names.",
                "entities": [{"text": f"Person{i}", "label": "PER", "start": 14, "end": 14 + len(f"Person{i}")}]
            })
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(large_dataset, f)
            data_path = f.name
        
        try:
            with patch('src.data.hf_dataset_preparation.load_dataset') as mock_load_dataset:
                # Mock dataset that simulates limiting
                mock_dataset = Mock()
                mock_dataset.__len__ = Mock(return_value=len(large_dataset))
                mock_dataset.__iter__ = Mock(return_value=iter(large_dataset))
                
                # Mock select method to simulate limiting
                def mock_select(indices):
                    limited_mock = Mock()
                    limited_mock.__len__ = Mock(return_value=len(indices))
                    limited_mock.__iter__ = Mock(return_value=iter([large_dataset[i] for i in indices]))
                    return limited_mock
                
                mock_dataset.select = mock_select
                mock_dataset.map = Mock(return_value=mock_dataset)
                mock_dataset.filter = Mock(return_value=mock_dataset)
                mock_dataset.train_test_split = Mock(return_value={'train': mock_dataset, 'test': mock_dataset})
                mock_load_dataset.return_value = mock_dataset
                
                with patch('src.data.hf_dataset_preparation.AutoTokenizer'):
                    # In test mode, we would expect the dataset to be limited
                    # This is a conceptual test - actual limiting would happen in the preparator
                    assert config.test_sample_limit == 50
                    assert len(large_dataset) > config.test_sample_limit
                    
                    # Simulate what the preparator should do
                    limited_indices = list(range(min(len(large_dataset), config.test_sample_limit)))
                    limited_dataset = mock_dataset.select(limited_indices)
                    
                    # Verify limitation worked
                    assert limited_dataset.__len__() == config.test_sample_limit
        
        finally:
            Path(data_path).unlink()
    
    def test_training_parameter_reduction(self):
        """Test that training parameters are reduced for faster execution."""
        config = create_test_hf_config()
        
        # Verify reduced training parameters
        assert config.epochs == 1  # Minimal epochs
        assert config.batch_size <= 8  # Small batch size
        assert config.eval_batch_size <= 8  # Small eval batch size
        
        # Verify frequent evaluation and logging for quick feedback
        assert config.eval_steps <= 20
        assert config.logging_steps <= 10
        assert config.save_steps <= 50
        
        # Verify aggressive early stopping
        assert config.early_stopping_patience <= 3
        
        # Verify minimal checkpointing
        assert config.save_total_limit <= 3
    
    def test_hardware_optimization_for_ci(self):
        """Test hardware optimization settings for CI environments."""
        config = create_test_hf_config()
        
        # CI-friendly hardware settings
        assert config.dataloader_num_workers <= 2  # Avoid multiprocessing issues
        assert config.dataloader_pin_memory == True  # Can be True, but won't hurt on CPU
        assert config.gradient_accumulation_steps == 1  # No accumulation needed for small batches
        
        # Mixed precision should be disabled on CPU
        if not config.use_gpu:
            assert config.fp16 == False
            assert config.bf16 == False
    
    def test_reproducibility_settings(self):
        """Test reproducibility settings for consistent CI results."""
        config = create_test_hf_config()
        
        # Fixed seeds for reproducibility
        assert config.seed == 42
        assert config.data_seed == 42
        
        # Deterministic settings
        assert isinstance(config.seed, int)
        assert isinstance(config.data_seed, int)


class TestModeWandBDisabling:
    """Test WandB disabling in test mode."""
    
    def test_wandb_disabled_in_config(self):
        """Test that WandB is disabled in test mode configuration."""
        config = create_test_hf_config()
        
        assert config.test_disable_wandb == True
        assert config.report_to is None  # Should be None when disabled
    
    def test_wandb_disabled_in_training_args(self):
        """Test that WandB is disabled in TrainingArguments."""
        config = create_test_hf_config()
        training_args = config.to_training_args("/tmp/test")
        
        # report_to should be empty list or None
        assert training_args.report_to == [] or training_args.report_to is None
    
    def test_wandb_not_initialized_in_trainer(self):
        """Test that WandB is not initialized in trainer setup."""
        config = create_test_hf_config()
        trainer = HFNERTrainer(config)
        
        with patch('src.training.hf_trainer.wandb') as mock_wandb:
            trainer.setup_wandb()
            
            # WandB should not be initialized
            mock_wandb.init.assert_not_called()
    
    def test_wandb_callbacks_not_added(self):
        """Test that WandB callbacks are not added in test mode."""
        config = create_test_hf_config()
        trainer = HFNERTrainer(config)
        trainer.label_list = ["O", "B-PER", "I-PER"]
        
        callbacks = trainer.create_callbacks()
        
        # Should not have WandB-related callbacks
        from src.training.hf_wandb_integration import CustomWandbCallback
        
        wandb_callbacks = [cb for cb in callbacks if isinstance(cb, CustomWandbCallback)]
        assert len(wandb_callbacks) == 0  # No WandB callbacks in test mode


class TestModeIntegrationWithTraining:
    """Test test mode integration with actual training pipeline."""
    
    def create_test_data(self):
        """Create minimal test data."""
        data = [
            {
                "id": 1,
                "sentence": "Jan Jansen woont in Amsterdam.",
                "entities": [{"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10}]
            },
            {
                "id": 2,
                "sentence": "Marie de Wit werkt bij Google.",
                "entities": [{"text": "Marie de Wit", "label": "PER", "start": 0, "end": 12}]
            }
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(data, f)
            return f.name
    
    def test_end_to_end_test_mode_training(self):
        """Test end-to-end training in test mode."""
        data_path = self.create_test_data()
        
        try:
            with patch('src.training.hf_trainer.HFNERTrainer') as mock_trainer_class:
                # Mock trainer for test mode
                mock_trainer = Mock()
                mock_trainer.train.return_value = {
                    "train_runtime": 1.0,  # Fast training
                    "train_samples_per_second": 50.0,
                    "eval_f1": 0.80,  # Reasonable but not perfect
                    "eval_precision": 0.82,
                    "eval_recall": 0.78
                }
                mock_trainer_class.return_value = mock_trainer
                
                config = create_test_hf_config()
                
                # Run training in test mode
                result = train_ner_model(data_path=data_path, config=config)
                
                # Verify training completed quickly
                assert result["train_runtime"] <= 10.0  # Should be fast
                assert "eval_f1" in result
                
                # Verify trainer was configured correctly
                mock_trainer_class.assert_called_once()
                called_config = mock_trainer_class.call_args[0][0]
                assert called_config.test_mode == True
                assert called_config.epochs == 1
                
        finally:
            Path(data_path).unlink()
    
    def test_test_mode_with_config_file(self):
        """Test test mode activation via config file."""
        data_path = self.create_test_data()
        
        # Create test config file
        config_content = """
hf_training:
  test_mode: true
  test_epochs: 1
  test_sample_limit: 10
  batch_size: 2
  eval_steps: 5
  logging_steps: 2
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(config_content)
            config_path = f.name
        
        try:
            with patch('src.training.hf_trainer.HFNERTrainer') as mock_trainer_class:
                mock_trainer = Mock()
                mock_trainer.train.return_value = {"eval_f1": 0.75}
                mock_trainer_class.return_value = mock_trainer
                
                # Run training with config file
                result = train_ner_model(
                    data_path=data_path,
                    config_path=config_path
                )
                
                # Verify training completed
                assert "eval_f1" in result
                
                # Verify config was loaded correctly
                called_config = mock_trainer_class.call_args[0][0]
                assert called_config.test_mode == True
                assert called_config.test_sample_limit == 10
                assert called_config.batch_size == 2
                
        finally:
            Path(data_path).unlink()
            Path(config_path).unlink()
    
    def test_test_mode_error_handling(self):
        """Test error handling in test mode."""
        data_path = self.create_test_data()
        
        try:
            with patch('src.training.hf_trainer.HFNERTrainer') as mock_trainer_class:
                # Mock trainer that raises an error
                mock_trainer = Mock()
                mock_trainer.train.side_effect = Exception("Training failed")
                mock_trainer_class.return_value = mock_trainer
                
                config = create_test_hf_config()
                
                # Should propagate the error
                with pytest.raises(Exception, match="Training failed"):
                    train_ner_model(data_path=data_path, config=config)
                
        finally:
            Path(data_path).unlink()


class TestCICompatibility:
    """Test CI/CD pipeline compatibility."""
    
    def test_no_external_dependencies_in_test_mode(self):
        """Test that test mode doesn't require external dependencies."""
        config = create_test_hf_config()
        
        # Should not require WandB
        assert config.test_disable_wandb == True
        
        # Should not require GPU
        assert config.use_gpu == False or True  # Either is fine, will auto-detect
        
        # Should not require internet for model downloads (in real usage, would use cached models)
        assert config.model_name == "DTAI-KULeuven/robbert-2023-dutch-base"  # Standard model
    
    def test_deterministic_behavior(self):
        """Test deterministic behavior for CI."""
        config1 = create_test_hf_config()
        config2 = create_test_hf_config()
        
        # Both configs should have same seeds
        assert config1.seed == config2.seed == 42
        assert config1.data_seed == config2.data_seed == 42
        
        # Both should have same test mode settings
        assert config1.test_mode == config2.test_mode == True
        assert config1.test_epochs == config2.test_epochs == 1
        assert config1.test_sample_limit == config2.test_sample_limit == 50
    
    def test_resource_constraints_for_ci(self):
        """Test resource constraints suitable for CI environments."""
        config = create_test_hf_config()
        
        # Memory-friendly settings
        assert config.batch_size <= 8
        assert config.eval_batch_size <= 8
        assert config.max_length <= 512
        assert config.gradient_accumulation_steps == 1
        
        # CPU-friendly settings
        assert config.dataloader_num_workers <= 2
        assert config.fp16 == False or config.use_gpu == True  # Only if GPU available
        
        # Storage-friendly settings
        assert config.save_total_limit <= 3
        assert config.generate_model_card == True  # Can be True, doesn't hurt
    
    def test_fast_execution_settings(self):
        """Test settings optimized for fast execution."""
        config = create_test_hf_config()
        
        # Minimal training
        assert config.epochs == 1
        assert config.test_sample_limit <= 50
        
        # Frequent feedback
        assert config.eval_steps <= 20
        assert config.logging_steps <= 10
        
        # Quick stopping
        assert config.early_stopping_patience <= 3
        
        # Minimal warmup
        assert config.warmup_steps <= 100


if __name__ == "__main__":
    pytest.main([__file__, "-v"])