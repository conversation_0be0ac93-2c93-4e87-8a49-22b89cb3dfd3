"""
Hugging Face model setup for RobBERT-2023 NER training.
"""

import torch
from typing import Dict, List, Optional, Tu<PERSON>
from transformers import (
    RobertaForTokenClassification,
    AutoTokenizer,
    AutoConfig
)
from pathlib import Path

from ..utils.logging_utils import get_logger
from ..exceptions import ModelLoadingError


def create_ner_model(
    model_name: str = "DTAI-KULeuven/robbert-2023-dutch-base",
    label_list: Optional[List[str]] = None,
    cache_dir: Optional[str] = None
) -> Tuple[RobertaForTokenClassification, AutoTokenizer, Dict[str, int], Dict[int, str]]:
    """
    Create RobBERT model for token classification using Hugging Face Transformers.
    
    Args:
        model_name: Name of the RobBERT model to load
        label_list: List of NER labels. If None, uses default ["O", "B-PER", "I-PER"]
        cache_dir: Directory to cache downloaded models
        
    Returns:
        Tuple of (model, tokenizer, label2id, id2label)
        
    Raises:
        ModelLoadingError: If model or tokenizer loading fails
    """
    logger = get_logger(__name__)
    
    # Default label scheme for Dutch NER (Person entities only)
    if label_list is None:
        label_list = ["O", "B-PER", "I-PER"]
    
    # Create label mappings
    label2id = {label: i for i, label in enumerate(label_list)}
    id2label = {i: label for i, label in enumerate(label_list)}
    num_labels = len(label_list)
    
    logger.logger.info(f"Creating NER model with {num_labels} labels: {label_list}")
    logger.logger.debug(f"Label mappings - label2id: {label2id}, id2label: {id2label}")
    
    try:
        # Load tokenizer
        logger.logger.info(f"Loading tokenizer: {model_name}")
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            use_fast=True  # Use fast tokenizer for better performance
        )
        logger.logger.info(f"Tokenizer loaded successfully - vocab_size: {tokenizer.vocab_size}")
        
    except Exception as e:
        error_msg = f"Failed to load tokenizer from {model_name}"
        logger.logger.error(f"{error_msg}: {e}")
        raise ModelLoadingError(error_msg, model_name=model_name) from e
    
    try:
        # Load model configuration
        logger.logger.info(f"Loading model configuration: {model_name}")
        config = AutoConfig.from_pretrained(
            model_name,
            cache_dir=cache_dir
        )
        
        # Update config with label information
        config.num_labels = num_labels
        config.id2label = id2label
        config.label2id = label2id
        
        logger.logger.info(f"Configuration loaded - hidden_size: {config.hidden_size}")
        
    except Exception as e:
        error_msg = f"Failed to load configuration from {model_name}"
        logger.logger.error(f"{error_msg}: {e}")
        raise ModelLoadingError(error_msg, model_name=model_name) from e
    
    try:
        # Load model for token classification
        logger.logger.info(f"Loading RobertaForTokenClassification model: {model_name}")
        model = RobertaForTokenClassification.from_pretrained(
            model_name,
            config=config,
            cache_dir=cache_dir
        )
        
        # Count parameters
        num_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        model_size_mb = num_params * 4 / 1e6  # Rough estimate for float32
        
        logger.logger.info(f"Model loaded successfully:")
        logger.logger.info(f"  - Total parameters: {num_params:,}")
        logger.logger.info(f"  - Trainable parameters: {trainable_params:,}")
        logger.logger.info(f"  - Estimated size: {model_size_mb:.1f} MB")
        
        # Validate model architecture
        if not hasattr(model, 'roberta'):
            raise ModelLoadingError("Model does not have expected 'roberta' attribute", model_name=model_name)
        
        if not hasattr(model, 'classifier'):
            raise ModelLoadingError("Model does not have expected 'classifier' attribute", model_name=model_name)
        
        # Validate classifier output size
        expected_output_size = num_labels
        actual_output_size = model.classifier.out_features
        if actual_output_size != expected_output_size:
            raise ModelLoadingError(
                f"Classifier output size mismatch: expected {expected_output_size}, got {actual_output_size}",
                model_name=model_name
            )
        
        logger.logger.info("Model validation passed")
        
    except ModelLoadingError:
        raise
    except Exception as e:
        error_msg = f"Failed to load model from {model_name}"
        logger.logger.error(f"{error_msg}: {e}")
        raise ModelLoadingError(error_msg, model_name=model_name) from e
    
    return model, tokenizer, label2id, id2label


def validate_model_compatibility(
    model: RobertaForTokenClassification,
    tokenizer: AutoTokenizer,
    expected_labels: Optional[List[str]] = None
) -> bool:
    """
    Validate that the model is compatible with existing inference pipeline.
    
    Args:
        model: The RobertaForTokenClassification model
        tokenizer: The tokenizer
        expected_labels: Expected label list for validation
        
    Returns:
        True if model is compatible, False otherwise
        
    Raises:
        ModelLoadingError: If validation fails
    """
    logger = get_logger(__name__)
    
    try:
        # Check model architecture
        if not isinstance(model, RobertaForTokenClassification):
            raise ModelLoadingError("Model is not RobertaForTokenClassification instance")
        
        # Check tokenizer compatibility
        if not hasattr(tokenizer, 'vocab_size'):
            raise ModelLoadingError("Tokenizer missing vocab_size attribute")
        
        # Check label configuration
        if expected_labels:
            if not hasattr(model.config, 'id2label'):
                raise ModelLoadingError("Model config missing id2label mapping")
            
            if not hasattr(model.config, 'label2id'):
                raise ModelLoadingError("Model config missing label2id mapping")
            
            # Validate label mappings
            model_labels = list(model.config.id2label.values())
            if set(model_labels) != set(expected_labels):
                raise ModelLoadingError(
                    f"Label mismatch: expected {expected_labels}, got {model_labels}"
                )
        
        # Test basic forward pass
        logger.logger.debug("Testing basic forward pass")
        vocab_size = getattr(tokenizer, 'vocab_size', 50000)  # Handle mock tokenizers
        if hasattr(vocab_size, '__call__'):  # Handle Mock objects
            vocab_size = 50000
        test_input = torch.randint(0, vocab_size, (1, 10))
        test_attention = torch.ones(1, 10)
        
        model.eval()
        with torch.no_grad():
            outputs = model(input_ids=test_input, attention_mask=test_attention)
        
        # Validate output structure
        if not hasattr(outputs, 'logits'):
            raise ModelLoadingError("Model output missing logits")
        
        expected_shape = (1, 10, model.config.num_labels)
        if outputs.logits.shape != expected_shape:
            raise ModelLoadingError(
                f"Output shape mismatch: expected {expected_shape}, got {outputs.logits.shape}"
            )
        
        logger.logger.info("Model compatibility validation passed")
        return True
        
    except ModelLoadingError:
        raise
    except Exception as e:
        error_msg = f"Model compatibility validation failed: {e}"
        logger.logger.error(error_msg)
        raise ModelLoadingError(error_msg) from e


def test_model_forward_pass(
    model: RobertaForTokenClassification,
    tokenizer: AutoTokenizer,
    test_text: str = "Jan Jansen woont in Amsterdam."
) -> Dict[str, torch.Tensor]:
    """
    Test model forward pass with sample Dutch text.
    
    Args:
        model: The RobertaForTokenClassification model
        tokenizer: The tokenizer
        test_text: Sample text for testing
        
    Returns:
        Dictionary containing test results
        
    Raises:
        ModelLoadingError: If forward pass fails
    """
    logger = get_logger(__name__)
    
    try:
        logger.logger.info(f"Testing forward pass with text: '{test_text}'")
        
        # Tokenize input
        inputs = tokenizer(
            test_text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512,
            return_attention_mask=True
        )
        
        logger.logger.debug(f"Tokenized input shape: {inputs['input_ids'].shape}")
        logger.logger.debug(f"Tokens: {tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])}")
        
        # Forward pass
        model.eval()
        with torch.no_grad():
            outputs = model(**inputs)
        
        # Extract results
        logits = outputs.logits
        predictions = torch.argmax(logits, dim=-1)
        
        # Convert predictions to labels
        predicted_labels = []
        for pred_ids in predictions[0]:
            label = model.config.id2label[pred_ids.item()]
            predicted_labels.append(label)
        
        logger.logger.info(f"Forward pass successful:")
        logger.logger.info(f"  - Output shape: {logits.shape}")
        logger.logger.info(f"  - Predicted labels: {predicted_labels}")
        
        return {
            "logits": logits,
            "predictions": predictions,
            "predicted_labels": predicted_labels,
            "input_ids": inputs["input_ids"],
            "attention_mask": inputs["attention_mask"]
        }
        
    except Exception as e:
        error_msg = f"Model forward pass test failed: {e}"
        logger.logger.error(error_msg)
        raise ModelLoadingError(error_msg) from e