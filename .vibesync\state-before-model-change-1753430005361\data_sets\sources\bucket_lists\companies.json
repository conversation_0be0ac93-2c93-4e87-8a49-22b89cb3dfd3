[{"name": "Royal Dutch Shell", "city": "<PERSON>"}, {"name": "ING Groep", "city": "Amsterdam"}, {"name": "<PERSON><PERSON>", "city": "Zaandam"}, {"name": "ASML Holding", "city": "Veldhoven"}, {"name": "Heineken", "city": "Amsterdam"}, {"name": "Philips", "city": "Eindhoven"}, {"name": "Unilever", "city": "Rotterdam"}, {"name": "AkzoNobel", "city": "Amsterdam"}, {"name": "NXP Semiconductors", "city": "Eindhoven"}, {"name": "KLM Royal Dutch Airlines", "city": "Amstelveen"}, {"name": "Rabobank", "city": "Utrecht"}, {"name": "DSM", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Prosus", "city": "Amsterdam"}, {"name": "Airbus SE", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON> Kluwer", "city": "<PERSON><PERSON><PERSON> aan den Rijn"}, {"name": "Koninklijke KPN", "city": "Rotterdam"}, {"name": "argenx SE", "city": "Breda"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "ABN AMRO", "city": "Amsterdam"}, {"name": "SHV Holdings", "city": "Utrecht"}, {"name": "Randstad Holding", "city": "Diemen"}, {"name": "JDE <PERSON>'s", "city": "Amsterdam"}, {"name": "NN Group", "city": "<PERSON>"}, {"name": "Fugro", "city": "Leidschendam"}, {"name": "Tata Steel Nederland", "city": "IJmuiden"}, {"name": "Vopak", "city": "Rotterdam"}, {"name": "Aegon", "city": "<PERSON>"}, {"name": "Signify", "city": "Eindhoven"}, {"name": "TomTom", "city": "Amsterdam"}, {"name": "PostNL", "city": "<PERSON>"}, {"name": "Boskalis", "city": "Pa<PERSON>drecht"}, {"name": "Arcadi<PERSON>", "city": "Amsterdam"}, {"name": "Kuehne+Nagel Nederland", "city": "Rotterdam"}, {"name": "Coolblue", "city": "Rotterdam"}, {"name": "Bol.com", "city": "Utrecht"}, {"name": "Rituals", "city": "Amsterdam"}, {"name": "Action", "city": "Zwaagdijk"}, {"name": "Zalando SE", "city": "Amsterdam"}, {"name": "TenneT", "city": "Arnhem"}, {"name": "Eneco", "city": "Rotterdam"}, {"name": "Vattenfall Nederland", "city": "Amsterdam"}, {"name": "<PERSON><PERSON>der", "city": "Arnhem"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Port of Rotterdam", "city": "Rotterdam"}, {"name": "Schiphol Group", "city": "<PERSON><PERSON><PERSON>"}, {"name": "NS (Nederlandse Spoorwegen)", "city": "Utrecht"}, {"name": "Damen Shipyards", "city": "Gorinchem"}, {"name": "Fokker Technologies", "city": "Pa<PERSON>drecht"}, {"name": "<PERSON>", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Rosmalen"}, {"name": "BAM Group", "city": "Bunnik"}, {"name": "VolkerWessels", "city": "Amersfoort"}, {"name": "Ordina", "city": "Nieuwegein"}, {"name": "Capgemini Nederland", "city": "Utrecht"}, {"name": "Atos Nederland", "city": "Amstelveen"}, {"name": "Accenture Nederland", "city": "Amsterdam"}, {"name": "Deloitte Nederland", "city": "Amsterdam"}, {"name": "PwC Nederland", "city": "Amsterdam"}, {"name": "KPMG Nederland", "city": "Amstelveen"}, {"name": "EY Nederland", "city": "Amsterdam"}, {"name": "<PERSON>'s <PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON>", "city": "Zaandam"}, {"name": "Jumbo Supermarkten", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Lidl Nederland", "city": "<PERSON><PERSON>"}, {"name": "Aldi Nederland", "city": "Culemborg"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Etos", "city": "Zaandam"}, {"name": "Gall & Gall", "city": "Zaandam"}, {"name": "Booking.com", "city": "Amsterdam"}, {"name": "Takeaway.com", "city": "Amsterdam"}, {"name": "Just Eat", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Triodos Bank", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Bunq", "city": "Amsterdam"}, {"name": "Flow Traders", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "IMC Trading", "city": "Amsterdam"}, {"name": "Vermaat", "city": "IJsselstein"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Hilversum"}, {"name": "<PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON> aan den Rijn"}, {"name": "C&A Nederland", "city": "Amsterdam"}, {"name": "Primark Nederland", "city": "Amsterdam"}, {"name": "<PERSON>", "city": "Amsterdam"}, {"name": "NIBC Bank", "city": "<PERSON>"}, {"name": "LeasePlan", "city": "Amsterdam"}, {"name": "Pon Holdings", "city": "Amsterdam"}, {"name": "Gazelle", "city": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Heerenveen"}, {"name": "Sparta", "city": "Apeldoorn"}, {"name": "Koga", "city": "Heerenveen"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Verkade", "city": "Zaandam"}, {"name": "<PERSON>", "city": "Zaandam"}, {"name": "Zwanenberg Food Group", "city": "Almelo"}, {"name": "Unox", "city": "Oss"}, {"name": "Hak", "city": "<PERSON><PERSON><PERSON>"}, {"name": "FrieslandCampina", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Zoetermeer"}, {"name": "Danone Nederland", "city": "Zoetermeer"}, {"name": "Upfield", "city": "Amsterdam"}, {"name": "Koninklijke Wessanen", "city": "Amsterdam"}, {"name": "Vion Food Group", "city": "Boxtel"}, {"name": "Sligro Food Group", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Bidfood", "city": "<PERSON><PERSON>"}, {"name": "KLM Catering Services", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Sodexo Nederland", "city": "Breukelen"}, {"name": "Facilicom Group", "city": "Schiedam"}, {"name": "<PERSON><PERSON>", "city": "Almelo"}, {"name": "CSU", "city": "<PERSON><PERSON>"}, {"name": "ISS Facility Services", "city": "Utrecht"}, {"name": "Hago", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Nationale-Nederlanden", "city": "<PERSON>"}, {"name": "Achmea", "city": "<PERSON><PERSON><PERSON>"}, {"name": "ASR Nederland", "city": "Utrecht"}, {"name": "Interpolis", "city": "Tilburg"}, {"name": "Zilveren K<PERSON>", "city": "Leiden"}, {"name": "CZ Groep", "city": "Tilburg"}, {"name": "VGZ", "city": "Arnhem"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Wageningen"}, {"name": "ONVZ", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Exact Software", "city": "Delft"}, {"name": "Unit4", "city": "Utrecht"}, {"name": "Visma", "city": "Amsterdam"}, {"name": "AFAS Software", "city": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "city": "Amsterdam"}, {"name": "QuandaGo", "city": "Amsterdam"}, {"name": "New10", "city": "Amsterdam"}, {"name": "Samotics", "city": "Leiden"}, {"name": "<PERSON><PERSON>", "city": "Amsterdam"}, {"name": "EclecticIQ", "city": "Amsterdam"}, {"name": "<PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Backbase", "city": "Amsterdam"}, {"name": "MessageBird", "city": "Amsterdam"}, {"name": "SendCloud", "city": "Eindhoven"}, {"name": "Picnic", "city": "Amsterdam"}, {"name": "WeTransfer", "city": "Amsterdam"}, {"name": "Lightyear", "city": "<PERSON><PERSON><PERSON>"}, {"name": "VanM<PERSON>", "city": "Amsterdam"}, {"name": "Swapfiets", "city": "Amsterdam"}, {"name": "Ace & Tate", "city": "Amsterdam"}, {"name": "Suitsupply", "city": "Amsterdam"}, {"name": "Scotch & Soda", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Vandersterre", "city": "Bodegraven"}, {"name": "Koninklijke Tichelaar", "city": "Makkum"}, {"name": "Royal FloraHolland", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Syngenta Nederland", "city": "Enkhuizen"}, {"name": "Corteva Agriscience Nederland", "city": "Bergschenhoek"}, {"name": "Bayer Nederland", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Koppert Biological Systems", "city": "Berkel en Rodenrijs"}, {"name": "<PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON>", "city": "Warmenhuizen"}, {"name": "<PERSON><PERSON>", "city": "Enkhuizen"}, {"name": "HZPC Holland", "city": "Mets<PERSON>ier"}, {"name": "Royal Cosun", "city": "Breda"}, {"name": "<PERSON><PERSON>", "city": "Veendam"}, {"name": "Agrifirm", "city": "Apeldoorn"}, {"name": "ForFarmers", "city": "<PERSON><PERSON>"}, {"name": "CRV", "city": "Arnhem"}, {"name": "<PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Nedap", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "VDL Groep", "city": "Eindhoven"}, {"name": "Daf Trucks", "city": "Eindhoven"}, {"name": "Scania Nederland", "city": "Zwolle"}, {"name": "IHC Merwede", "city": "Sliedrecht"}, {"name": "Royal HaskoningDHV", "city": "Amersfoort"}, {"name": "Grundfos Nederland", "city": "Almere"}, {"name": "<PERSON><PERSON>", "city": "Deventer"}, {"name": "Witteveen+Bos", "city": "Deventer"}, {"name": "Sweco Nederland", "city": "De Bilt"}, {"name": "Antea Group Nederland", "city": "Heerenveen"}, {"name": "Royal BAM Group", "city": "Bunnik"}, {"name": "Strukton", "city": "Utrecht"}, {"name": "Ballast Nedam", "city": "Nieuwegein"}, {"name": "Koninklijke VolkerWessels", "city": "Amersfoort"}, {"name": "TBI Holdings", "city": "Rotterdam"}, {"name": "<PERSON><PERSON> Biologics", "city": "Leiden"}, {"name": "MSD Nederland", "city": "Haarlem"}, {"name": "Pfizer Nederland", "city": "Capelle aan den IJssel"}, {"name": "Amgen Nederland", "city": "Breda"}, {"name": "Genmab", "city": "Utrecht"}, {"name": "Merus", "city": "Utrecht"}, {"name": "Pharming Group", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Galapagos", "city": "Leiden"}, {"name": "Qiagen Nederland", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Medtronic Nederland", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Stryker Nederland", "city": "Amsterdam"}, {"name": "BD (Becton Dickinson) Nederland", "city": "Drachten"}, {"name": "Koninklijke Bibliotheek", "city": "<PERSON>"}, {"name": "Rijksmuseum", "city": "Amsterdam"}, {"name": "Van <PERSON> Museum", "city": "Amsterdam"}, {"name": "Stedelijk Museum", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Kröller-Müller Museum", "city": "O<PERSON>lo"}, {"name": "Nederlands Openluchtmuseum", "city": "Arnhem"}, {"name": "Tropenmuseum", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Het Concertgebouw", "city": "Amsterdam"}, {"name": "Koninklijke Schouwburg", "city": "<PERSON>"}, {"name": "Pathé Nederland", "city": "Amsterdam"}, {"name": "TivoliVredenburg", "city": "Utrecht"}, {"name": "Ziggo Dome", "city": "Amsterdam"}, {"name": "AFAS Live", "city": "Amsterdam"}, {"name": "Holland Casino", "city": "Amsterdam"}, {"name": "Lotto Nederland", "city": "Rijswijk"}, {"name": "Toto", "city": "Rijswijk"}, {"name": "Nationale Postcode Loterij", "city": "Amsterdam"}, {"name": "VriendenLoterij", "city": "Amsterdam"}, {"name": "BankGiro Loterij", "city": "Amsterdam"}, {"name": "Greenpeace Nederland", "city": "Amsterdam"}, {"name": "Oxfam Novib", "city": "<PERSON>"}, {"name": "KWF Kankerbestrijding", "city": "Amsterdam"}, {"name": "Natuurmonumenten", "city": "Amersfoort"}, {"name": "WWF Nederland", "city": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "city": "<PERSON>"}, {"name": "<PERSON>en zonder Grenzen", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Stichting AAP", "city": "Almelo"}, {"name": "Stichting DOEN", "city": "Amsterdam"}, {"name": "<PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Plan International Nederland", "city": "Amsterdam"}, {"name": "Terre des Hommes", "city": "<PERSON>"}, {"name": "SOS Kinderdorpen", "city": "Amsterdam"}, {"name": "War Child Nederland", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Stichting Vluchteling", "city": "<PERSON>"}, {"name": "Amnesty International Nederland", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "ACCESS", "city": "<PERSON>"}, {"name": "Benelux Organisation for Intellectual Property (BOIP)", "city": "<PERSON>"}, {"name": "Netherlands Cancer Institute", "city": "Amsterdam"}, {"name": "<PERSON><PERSON>", "city": "Rotterdam"}, {"name": "UMC Utrecht", "city": "Utrecht"}, {"name": "Radboudumc", "city": "Nijmegen"}, {"name": "LUMC", "city": "Leiden"}, {"name": "Amsterdam UMC", "city": "Amsterdam"}, {"name": "Maastricht UMC+", "city": "Maastricht"}, {"name": "VUmc", "city": "Amsterdam"}, {"name": "TNO", "city": "<PERSON>"}, {"name": "Wageningen University & Research", "city": "Wageningen"}, {"name": "Koninklijke Nederlandse Akademie van Wetenschappen", "city": "Amsterdam"}, {"name": "NWO (Nederlandse Organisatie voor Wetenschappelijk Onderzoek)", "city": "<PERSON>"}, {"name": "Deltares", "city": "Delft"}, {"name": "NIVEL", "city": "Utrecht"}, {"name": "RIVM", "city": "Bilthoven"}, {"name": "KNMI", "city": "De Bilt"}, {"name": "CBS (Centraal Bureau voor de Statistiek)", "city": "<PERSON>"}, {"name": "RVO (Rijksdienst voor Ondernemend Nederland)", "city": "<PERSON>"}, {"name": "Nationale Ombudsman", "city": "<PERSON>"}, {"name": "UWV", "city": "Amsterdam"}, {"name": "Belastingdienst", "city": "<PERSON>"}, {"name": "DUO (Dienst Uitvoering Onderwijs)", "city": "Groningen"}, {"name": "Kadaster", "city": "Apeldoorn"}, {"name": "Rijkswaterstaat", "city": "Utrecht"}, {"name": "ProRail", "city": "Utrecht"}, {"name": "GVB", "city": "Amsterdam"}, {"name": "RET", "city": "Rotterdam"}, {"name": "HTM", "city": "<PERSON>"}, {"name": "Connexxion", "city": "Hilversum"}, {"name": "Arriva Nederland", "city": "Heerenveen"}, {"name": "Qbuzz", "city": "Amersfoort"}, {"name": "EBS", "city": "Purmerend"}, {"name": "KLM Engineering & Maintenance", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Shell Nederland", "city": "<PERSON>"}, {"name": "Nike European Headquarters", "city": "Hilversum"}, {"name": "Accell Group", "city": "Heerenveen"}, {"name": "<PERSON>", "city": "Rotterdam"}, {"name": "AkzoNobel Decorative Paints", "city": "Sassenheim"}, {"name": "DSM Nutritional Products", "city": "Delft"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Tata Steel Europe", "city": "IJmuiden"}, {"name": "<PERSON>", "city": "Terneuzen"}, {"name": "Sabic Europe", "city": "Sittard"}, {"name": "Chemelot Campus", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Brightlands Maastricht Health Campus", "city": "Maastricht"}, {"name": "Eindhoven University of Technology", "city": "Eindhoven"}, {"name": "Delft University of Technology", "city": "Delft"}, {"name": "University of Amsterdam", "city": "Amsterdam"}, {"name": "Leiden University", "city": "Leiden"}, {"name": "Utrecht University", "city": "Utrecht"}, {"name": "Erasmus University Rotterdam", "city": "Rotterdam"}, {"name": "VU University Amsterdam", "city": "Amsterdam"}, {"name": "Radboud University", "city": "Nijmegen"}, {"name": "Maastricht University", "city": "Maastricht"}, {"name": "Hanze University of Applied Sciences", "city": "Groningen"}, {"name": "Fontys University of Applied Sciences", "city": "Eindhoven"}, {"name": "Avans University of Applied Sciences", "city": "Breda"}, {"name": "Saxion University of Applied Sciences", "city": "Enschede"}, {"name": "NHL Stenden University", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Zuyd University of Applied Sciences", "city": "Maastricht"}, {"name": "<PERSON>stein", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "MVRDV", "city": "Rotterdam"}, {"name": "UNStudio", "city": "Amsterdam"}, {"name": "OMA", "city": "Rotterdam"}, {"name": "Mecanoo", "city": "Delft"}, {"name": "<PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Diemen"}, {"name": "Powerhouse Company", "city": "Rotterdam"}, {"name": "Neutelings Riedijk Architects", "city": "Rotterdam"}, {"name": "<PERSON> Architects", "city": "Amsterdam"}, {"name": "KAAN Architecten", "city": "Rotterdam"}, {"name": "Civic Architects", "city": "Amsterdam"}, {"name": "Superuse Studios", "city": "Rotterdam"}, {"name": "Barcode Architects", "city": "Rotterdam"}, {"name": "ORANGE Architects", "city": "Rotterdam"}, {"name": "KCAP Architects & Planners", "city": "Rotterdam"}, {"name": "MVSA Architects", "city": "Amsterdam"}, {"name": "Arup Netherlands", "city": "Amsterdam"}, {"name": "Royal HaskoningDHV", "city": "Amersfoort"}, {"name": "Sweco Nederland", "city": "De Bilt"}, {"name": "Witteveen+Bos", "city": "Deventer"}, {"name": "Antea Group Nederland", "city": "Heerenveen"}, {"name": "<PERSON><PERSON>", "city": "Deventer"}, {"name": "Strukton", "city": "Utrecht"}, {"name": "Ballast Nedam", "city": "Nieuwegein"}, {"name": "TBI Holdings", "city": "Rotterdam"}, {"name": "<PERSON><PERSON>jmans Infra", "city": "Rosmalen"}, {"name": "<PERSON><PERSON>", "city": "Rotterdam"}, {"name": "BAM Infra Nederland", "city": "Go<PERSON>"}, {"name": "VolkerRail", "city": "<PERSON><PERSON>"}, {"name": "Heerema Marine Contractors", "city": "Leiden"}, {"name": "Allseas", "city": "Delft"}, {"name": "SBM Offshore", "city": "Schiedam"}, {"name": "Huisman Equipment", "city": "Schiedam"}, {"name": "IHC Merwede", "city": "Sliedrecht"}, {"name": "Fokker Services", "city": "Hoofddorp"}, {"name": "KLM Cityhopper", "city": "Amstelveen"}, {"name": "Transavia", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Martinair", "city": "<PERSON><PERSON><PERSON>"}, {"name": "TUI Nederland", "city": "Rijswijk"}, {"name": "Corendon Airlines Europe", "city": "Lijnden"}, {"name": "DSM Materials", "city": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Arnhem"}, {"name": "PPG Industries Nederland", "city": "Amsterdam"}, {"name": "Lubrizol Nederland", "city": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Delamine", "city": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Zeolyst International", "city": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "VDL Nedcar", "city": "Born"}, {"name": "Scania Production Zwolle", "city": "Zwolle"}, {"name": "DAF Trucks Nederland", "city": "Eindhoven"}, {"name": "Tesla Nederland", "city": "Amsterdam"}, {"name": "Lightyear One", "city": "<PERSON><PERSON><PERSON>"}, {"name": "TomTom Navigation", "city": "Amsterdam"}, {"name": "Nexperia", "city": "Nijmegen"}, {"name": "Philips Lighting", "city": "Eindhoven"}, {"name": "Signify Nederland", "city": "Eindhoven"}, {"name": "KPN Business", "city": "Amsterdam"}, {"name": "VodafoneZiggo", "city": "Utrecht"}, {"name": "T-Mobile Nederland", "city": "<PERSON>"}, {"name": "Cisco Nederland", "city": "Amsterdam"}, {"name": "Microsoft Nederland", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Google Nederland", "city": "Amsterdam"}, {"name": "Amazon Nederland", "city": "Amsterdam"}, {"name": "IBM Nederland", "city": "Amsterdam"}, {"name": "Oracle Nederland", "city": "Utrecht"}, {"name": "SAP Nederland", "city": "<PERSON>"}, {"name": "Salesforce Nederland", "city": "Amsterdam"}, {"name": "Accenture Technology Solutions", "city": "Amsterdam"}, {"name": "Atlassian Nederland", "city": "Amsterdam"}, {"name": "ServiceNow Nederland", "city": "Amsterdam"}, {"name": "Workday Nederland", "city": "Amsterdam"}, {"name": "<PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Royal Smilde Foods", "city": "Heerenveen"}, {"name": "Hell<PERSON>", "city": "<PERSON><PERSON>"}, {"name": "<PERSON> der Meulen Meesterbakkers", "city": "<PERSON><PERSON>"}, {"name": "Steensma Food Ingredients", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "DOC Kaas", "city": "<PERSON>og<PERSON><PERSON>"}, {"name": "Frysian Egg", "city": "Drachten"}, {"name": "<PERSON><PERSON>mans Meelfabrieken", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "city": "Zaandam"}, {"name": "Jumbo Distribution Center", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Lidl Distribution Center", "city": "Almere"}, {"name": "Aldi Distribution Center", "city": "Culemborg"}, {"name": "Kruidvat Distribution", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Etos Distribution", "city": "Zaandam"}, {"name": "Gall & Gall Distribution", "city": "Zaandam"}, {"name": "Action Distribution Center", "city": "Zwaagdijk"}, {"name": "Primark Distribution Nederland", "city": "Amsterdam"}, {"name": "Hunkemöller Distribution", "city": "Hilversum"}, {"name": "Zeeman Distribution", "city": "<PERSON><PERSON><PERSON> aan den Rijn"}, {"name": "C&A Distribution Nederland", "city": "Amsterdam"}, {"name": "Suitsupply Distribution", "city": "Amsterdam"}, {"name": "Scotch & Soda Distribution", "city": "Amsterdam"}, {"name": "Ganni <PERSON>", "city": "Amsterdam"}, {"name": "Denham Distribution", "city": "Amsterdam"}, {"name": "Ace & Tate Distribution", "city": "Amsterdam"}, {"name": "Rituals Distribution", "city": "Amsterdam"}, {"name": "Coolblue Distribution", "city": "Tilburg"}, {"name": "Bol.com Distribution Center", "city": "Waalwijk"}, {"name": "Zalando Nederland Distribution", "city": "Bleiswijk"}, {"name": "Picnic Distribution", "city": "Utrecht"}, {"name": "SendCloud Distribution", "city": "Eindhoven"}, {"name": "WeTransfer Headquarters", "city": "Amsterdam"}, {"name": "Booking.com Headquarters", "city": "Amsterdam"}, {"name": "Takeaway.com Headquarters", "city": "Amsterdam"}, {"name": "Mollie Headquarters", "city": "Amsterdam"}, {"name": "Backbase Headquarters", "city": "Amsterdam"}, {"name": "MessageBird Headquarters", "city": "Amsterdam"}, {"name": "Bynder Headquarters", "city": "Amsterdam"}, {"name": "AFAS Software Headquarters", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Visma Nederland", "city": "Amsterdam"}, {"name": "Exact Software Headquarters", "city": "Delft"}, {"name": "Unit4 Headquarters", "city": "Utrecht"}, {"name": "QuandaGo Headquarters", "city": "Amsterdam"}, {"name": "Samotics Headquarters", "city": "Leiden"}, {"name": "Aidence Headquarters", "city": "Amsterdam"}, {"name": "EclecticIQ Headquarters", "city": "Amsterdam"}, {"name": "Smiler Headquarters", "city": "Amsterdam"}, {"name": "New10 Headquarters", "city": "Amsterdam"}, {"name": "Bunq Headquarters", "city": "Amsterdam"}, {"name": "Knab Headquarters", "city": "Amsterdam"}, {"name": "Triodos Bank Headquarters", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Flow Traders Headquarters", "city": "Amsterdam"}, {"name": "Optiver Headquarters", "city": "Amsterdam"}, {"name": "IMC Trading Headquarters", "city": "Amsterdam"}, {"name": "Van <PERSON>t Kempen Headquarters", "city": "Amsterdam"}, {"name": "NIBC Bank Headquarters", "city": "<PERSON>"}, {"name": "LeasePlan Headquarters", "city": "Amsterdam"}, {"name": "Pon Holdings Headquarters", "city": "Amsterdam"}, {"name": "Vermaat Headquarters", "city": "IJsselstein"}, {"name": "KLM Catering Services", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Sodexo Nederland", "city": "Breukelen"}, {"name": "Facilicom Group", "city": "Schiedam"}, {"name": "Asito Headquarters", "city": "Almelo"}, {"name": "CSU Headquarters", "city": "<PERSON><PERSON>"}, {"name": "ISS Facility Services Nederland", "city": "Utrecht"}, {"name": "Hago Nederland", "city": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON> Biologics", "city": "Leiden"}, {"name": "MSD Nederland", "city": "Haarlem"}, {"name": "Pfizer Nederland", "city": "Capelle aan den IJssel"}, {"name": "Amgen Nederland", "city": "Breda"}, {"name": "Genmab Netherlands", "city": "Utrecht"}, {"name": "Merus Netherlands", "city": "Utrecht"}, {"name": "Pharming Group", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Galapagos Netherlands", "city": "Leiden"}, {"name": "Qiagen Nederland", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Medtronic Nederland", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Stryker Nederland", "city": "Amsterdam"}, {"name": "BD Nederland", "city": "Drachten"}, {"name": "PendraCare", "city": "Groningen"}, {"name": "IMDS", "city": "Groningen"}, {"name": "Abbott Medical Optics", "city": "Groningen"}, {"name": "Ophtec", "city": "Groningen"}, {"name": "Menicon Nederland", "city": "Groningen"}, {"name": "PRA International", "city": "Groningen"}, {"name": "QPS Netherlands", "city": "Groningen"}, {"name": "Syncom", "city": "Groningen"}, {"name": "PerkinElmer Nederland", "city": "Groningen"}, {"name": "Alvimedica Nederland", "city": "Groningen"}, {"name": "As<PERSON>las Pharma Europe", "city": "Leiden"}, {"name": "Royal FloraHolland Aalsmeer", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Syngenta Nederland", "city": "Enkhuizen"}, {"name": "Corteva Agriscience Nederland", "city": "Bergschenhoek"}, {"name": "Bayer Nederland Agriculture", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Koppert Biological Systems", "city": "Berkel en Rodenrijs"}, {"name": "Rijk Zwaan Headquarters", "city": "<PERSON>"}, {"name": "<PERSON><PERSON>", "city": "Warmenhuizen"}, {"name": "<PERSON><PERSON>", "city": "Enkhuizen"}, {"name": "HZPC Holland", "city": "Mets<PERSON>ier"}, {"name": "Royal Cosun", "city": "Breda"}, {"name": "Avebe Headquarters", "city": "Veendam"}, {"name": "Agrifirm Headquarters", "city": "Apeldoorn"}, {"name": "ForFarmers Nederland", "city": "<PERSON><PERSON>"}, {"name": "CRV Headquarters", "city": "Arnhem"}, {"name": "Lely Industries", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Nedap Headquarters", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Tropenmuseum", "city": "Amsterdam"}, {"name": "Rijksmuseum Amsterdam", "city": "Amsterdam"}, {"name": "Van <PERSON> Museum", "city": "Amsterdam"}, {"name": "Stedelijk Museum Amsterdam", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Kröller-Müller Museum", "city": "O<PERSON>lo"}, {"name": "Nederlands Openluchtmuseum", "city": "Arnhem"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Het Concertgebouw", "city": "Amsterdam"}, {"name": "Koninklijke Schouwburg", "city": "<PERSON>"}, {"name": "Pathé Nederland Headquarters", "city": "Amsterdam"}, {"name": "TivoliVredenburg", "city": "Utrecht"}, {"name": "Ziggo Dome", "city": "Amsterdam"}, {"name": "AFAS Live", "city": "Amsterdam"}, {"name": "Holland Casino Amsterdam", "city": "Amsterdam"}, {"name": "Lotto Nederland Headquarters", "city": "Rijswijk"}, {"name": "Toto Headquarters", "city": "Rijswijk"}, {"name": "Nationale Postcode Loterij", "city": "Amsterdam"}, {"name": "VriendenLoterij", "city": "Amsterdam"}, {"name": "BankGiro Loterij", "city": "Amsterdam"}, {"name": "Greenpeace Nederland", "city": "Amsterdam"}, {"name": "Oxfam Novib", "city": "<PERSON>"}, {"name": "KWF Kankerbestrijding", "city": "Amsterdam"}, {"name": "Natuurmonumenten", "city": "Amersfoort"}, {"name": "WWF Nederland", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Rode Kruis Nederland", "city": "<PERSON>"}, {"name": "Artsen zonder Grenzen Nederland", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Stichting AAP", "city": "Almelo"}, {"name": "Stichting DOEN", "city": "Amsterdam"}, {"name": "<PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Plan International Nederland", "city": "Amsterdam"}, {"name": "Terre des Hommes Nederland", "city": "<PERSON>"}, {"name": "SOS Kinderdorpen Nederland", "city": "Amsterdam"}, {"name": "War Child Nederland", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Stichting Vluchteling", "city": "<PERSON>"}, {"name": "Amnesty International Nederland", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "ACCESS Netherlands", "city": "<PERSON>"}, {"name": "Netherlands Cancer Institute", "city": "Amsterdam"}, {"name": "TNO Research", "city": "<PERSON>"}, {"name": "Wageningen University & Research", "city": "Wageningen"}, {"name": "Koninklijke Nederlandse Akademie van Wetenschappen", "city": "Amsterdam"}, {"name": "NWO (Nederlandse Organisatie voor Wetenschappelijk Onderzoek)", "city": "<PERSON>"}, {"name": "Deltares", "city": "Delft"}, {"name": "NIVEL", "city": "Utrecht"}, {"name": "RIVM", "city": "Bilthoven"}, {"name": "KNMI", "city": "De Bilt"}, {"name": "CBS (Centraal Bureau voor de Statistiek)", "city": "<PERSON>"}, {"name": "RVO (Rijksdienst voor Ondernemend Nederland)", "city": "<PERSON>"}, {"name": "Nationale Ombudsman", "city": "<PERSON>"}, {"name": "UWV Headquarters", "city": "Amsterdam"}, {"name": "Belastingdienst Headquarters", "city": "<PERSON>"}, {"name": "DUO (Dienst Uitvoering Onderwijs)", "city": "Groningen"}, {"name": "Kadaster Nederland", "city": "Apeldoorn"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Delft"}, {"name": "De Fietsfabriek", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Bloemenwinkel Roos & Bloem", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Scheveningen"}, {"name": "Café De Drie Gezusters", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>t <PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Tuincentrum GroenRijk", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Amersfoort"}, {"name": "<PERSON>", "city": "Alkmaar"}, {"name": "Restaurant De Linde", "city": "Nijmegen"}, {"name": "Houtbewerking Jansen", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Maastricht"}, {"name": "Fietsenwinkel De Trappers", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Zwolle"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Arnhem"}, {"name": "De Groene Groenteboer", "city": "Utrecht"}, {"name": "Café De Posthoorn", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "Bouwbedrijf Smit & Zn", "city": "Apeldoorn"}, {"name": "<PERSON>", "city": "<PERSON>"}, {"name": "Kwekerij De Bloemenhof", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON> Smids<PERSON>", "city": "Dordrecht"}, {"name": "Restaurant Het Anker", "city": "Enschede"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Breda"}, {"name": "Fietsenmaker De Snelheid", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Go<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Go<PERSON>"}, {"name": "Café De Vissershaven", "city": "Vlissingen"}, {"name": "Tuinontwerp GroenVisie", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Deventer"}, {"name": "<PERSON>", "city": "Maastricht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Bodegraven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Hilversum"}, {"name": "Restaurant De Gouden Leeuw", "city": "Arnhem"}, {"name": "<PERSON>", "city": "Utrecht"}, {"name": "Bloemenatelier Fleur", "city": "Amsterdam"}, {"name": "Houtfabriek De Eik", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "<PERSON>", "city": "Leiden"}, {"name": "Café De Kroon", "city": "Nijmegen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Almere"}, {"name": "De <PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Modeboetiek Chic", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Peddels", "city": "Zwolle"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "Restaurant De Molenwiek", "city": "Delft"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Breda"}, {"name": "Tuincentrum De Groene Vinger", "city": "Apeldoorn"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "<PERSON>", "city": "Rotterdam"}, {"name": "Brouwer<PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Eindhoven"}, {"name": "B<PERSON>emenwin<PERSON>", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amersfoort"}, {"name": "<PERSON>", "city": "Scheveningen"}, {"name": "Café Het Wapen", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Kaasboetiek De Koe", "city": "Alkmaar"}, {"name": "Restaurant De Zwaan", "city": "Maastricht"}, {"name": "Fietsenmaker De Wiel", "city": "Nijmegen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Arnhem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "<PERSON>", "city": "<PERSON>"}, {"name": "Bouwonderneming De Fundering", "city": "Rotterdam"}, {"name": "<PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Café De Brug", "city": "Delft"}, {"name": "Meubelatelier De Plank", "city": "Eindhoven"}, {"name": "Kapsalon Knip & Kleur", "city": "Breda"}, {"name": "<PERSON> B<PERSON>mand", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "<PERSON>etsenwinkel De Ketting", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Zwolle"}, {"name": "Restaurant De Toren", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "Tuincentrum De Spade", "city": "Nijmegen"}, {"name": "<PERSON>", "city": "Groningen"}, {"name": "B<PERSON><PERSON><PERSON><PERSON>", "city": "Bodegraven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Arnhem"}, {"name": "<PERSON><PERSON><PERSON><PERSON> De Melkboer", "city": "<PERSON><PERSON>"}, {"name": "Café De Haven", "city": "Scheveningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Bloemenatelier De Lelie", "city": "Utrecht"}, {"name": "<PERSON>", "city": "Vlissingen"}, {"name": "Restaurant De Oude Molen", "city": "Leiden"}, {"name": "Fietsenmaker De Band", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Breda"}, {"name": "<PERSON>", "city": "Maastricht"}, {"name": "Bouwonderne<PERSON>", "city": "Apeldoorn"}, {"name": "De Chocoladefabriek", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Modeboetiek De Stof", "city": "Groningen"}, {"name": "Café De Pijp", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Nijmegen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Arnhem"}, {"name": "Fietsenwinkel De Versnelling", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Restaurant De Brasserie", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Zwolle"}, {"name": "Tuincentrum De Bloem", "city": "Amersfoort"}, {"name": "<PERSON>", "city": "Tilburg"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Delft"}, {"name": "Kaasboetiek De Geit", "city": "Alkmaar"}, {"name": "Café De Markt", "city": "Breda"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Nijmegen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>l", "city": "Rotterdam"}, {"name": "Bloemenatelier De Bloesem", "city": "<PERSON>"}, {"name": "<PERSON>", "city": "Scheveningen"}, {"name": "Restaurant De Kade", "city": "Groningen"}, {"name": "Fietsenmaker De Spoke", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Arnhem"}, {"name": "De Groenteboer", "city": "Leiden"}, {"name": "Bouwonderne<PERSON>", "city": "Eindhoven"}, {"name": "De Chocoladebar", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Maastricht"}, {"name": "Café De Ster", "city": "<PERSON>"}, {"name": "Meubelatelier De Bank", "city": "Zwolle"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Nijmegen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> De Trap", "city": "Breda"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Restaurant De Schuur", "city": "Tilburg"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Tuincentrum De Plant", "city": "<PERSON>"}, {"name": "<PERSON>", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Café De Tuin", "city": "Arnhem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Bloemenatelier <PERSON>rc<PERSON>", "city": "Utrecht"}, {"name": "<PERSON>", "city": "Vlissingen"}, {"name": "Restaurant De Haven", "city": "<PERSON>"}, {"name": "Fietsenmaker De <PERSON>", "city": "Nijmegen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Breda"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amersfoort"}, {"name": "De Groentetuin", "city": "Zwolle"}, {"name": "Bouwonderneming De Vloer", "city": "Leiden"}, {"name": "<PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Modeboetiek De Knoop", "city": "Groningen"}, {"name": "Café De Molen", "city": "Maastricht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Spiegel", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Fietsenwinkel De Pedalen", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Nijmegen"}, {"name": "Restaurant De Brug", "city": "Arnhem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Tuincentrum De Boom", "city": "Breda"}, {"name": "<PERSON>", "city": "Amersfoort"}, {"name": "Brouwerij <PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Zwolle"}, {"name": "Kaasboetiek <PERSON>", "city": "Alkmaar"}, {"name": "Café De Stad", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "Bloemenatelier De Orchidee", "city": "Eindhoven"}, {"name": "<PERSON>", "city": "Scheveningen"}, {"name": "Restaurant De Poort", "city": "Utrecht"}, {"name": "Fietsenmaker <PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Nijmegen"}, {"name": "<PERSON>", "city": "Haarlem"}, {"name": "Bou<PERSON><PERSON>neming De Trap", "city": "Amsterdam"}, {"name": "<PERSON>", "city": "Breda"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "city": "Arnhem"}, {"name": "Café De Kade", "city": "Zwolle"}, {"name": "Meubelatelier De Hout", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Maastricht"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Eindhoven"}, {"name": "Fietsenwinkel De Stuur", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Restaurant De Vaart", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Tuincentrum De Tuin", "city": "Nijmegen"}, {"name": "<PERSON>", "city": "Breda"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Café De Werf", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Zwolle"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Bloemenatelier De Zonnebloem", "city": "Groningen"}, {"name": "<PERSON>", "city": "Vlissingen"}, {"name": "Restaurant De Dreef", "city": "Arnhem"}, {"name": "Fietsen<PERSON>", "city": "Eindhoven"}, {"name": "Bakker<PERSON><PERSON>", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Maastricht"}, {"name": "<PERSON>", "city": "Rotterdam"}, {"name": "Bouwonderneming De Fundament", "city": "<PERSON>"}, {"name": "De Chocoladeproef", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Modeboetiek De Naad", "city": "Nijmegen"}, {"name": "Café De Straat", "city": "Breda"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON> De Franje", "city": "Zwolle"}, {"name": "B<PERSON><PERSON>en<PERSON><PERSON> De Hyacint", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Fietsenwinkel De Balans", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Restaurant De Wal", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Tuincentrum De Blad", "city": "Rotterdam"}, {"name": "<PERSON>", "city": "Arnhem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Nijmegen"}, {"name": "Kaasboetiek De Boter", "city": "Alkmaar"}, {"name": "Café De Brasserie", "city": "Maastricht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Breda"}, {"name": "Bloemenatelier <PERSON>", "city": "Zwolle"}, {"name": "<PERSON>", "city": "Scheveningen"}, {"name": "Restaurant De Markt", "city": "Amersfoort"}, {"name": "Fietsenmaker De Versnelling", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Eindhoven"}, {"name": "De Groentemarkt", "city": "Haarlem"}, {"name": "Bouwonderneming De Steiger", "city": "Utrecht"}, {"name": "De <PERSON>de<PERSON>", "city": "Rotterdam"}, {"name": "Advocatenka<PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "Café De Jonge Dikkert", "city": "Amstelveen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Bloemenwinkel De Orchidee", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Fietsenmaker Van Dam", "city": "Alkmaar"}, {"name": "Restaurant De Vier Seizoenen", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Noordwijk"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Meubelmakerij Ho<PERSON>werk", "city": "Tilburg"}, {"name": "Kapsalon Haarstudio 23", "city": "Utrecht"}, {"name": "<PERSON> G<PERSON>", "city": "Utrecht"}, {"name": "Brouwerij Poesiat & Kater", "city": "Amsterdam"}, {"name": "Tuincentrum Overvecht", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Haarlem", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Café De Rat", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 010", "city": "Rotterdam"}, {"name": "Bloemenwinkel Bloem & Zo", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Alkmaar"}, {"name": "Fietsenwinkel Bike Totaal", "city": "<PERSON>"}, {"name": "Restaurant De Lachende Javaan", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Noordwijk"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Kapsalon Haar & Zo", "city": "Amsterdam"}, {"name": "<PERSON>", "city": "Scheveningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Jeugd", "city": "Rotterdam"}, {"name": "Adviesbureau De Vries", "city": "Tilburg"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "Café De Toeter", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Alkmaar"}, {"name": "Bloemenwinkel Flora", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Fietsenmaker De <PERSON>", "city": "<PERSON>"}, {"name": "Restaurant Het Zwaantje", "city": "Amsterdam"}, {"name": "Kwekeri<PERSON>lo<PERSON>", "city": "Noordwijk"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Kapsalon Knippen & Stijlen", "city": "Haarlem"}, {"name": "De Groenteboer <PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amersfoort"}, {"name": "Tuincentrum De Bosrand", "city": "<PERSON><PERSON><PERSON> aan den Rijn"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Alkmaar"}, {"name": "Café De Drie Graefjes", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "B<PERSON><PERSON>en<PERSON><PERSON> De Bloemist", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Oss"}, {"name": "Fietsenwinkel De Fietsfabriek", "city": "Haarlem"}, {"name": "Restaurant De Oude Rosmolen", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Lisse"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mee<PERSON>", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON> De Knipsalon", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Adviesbureau KplusV", "city": "Arnhem"}, {"name": "<PERSON>", "city": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Café De Plaats", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Bloemenwinkel Bloemen van <PERSON>", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Fietsenmaker Bike Repair", "city": "Leiden"}, {"name": "Restaurant De Kromme Dissel", "city": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Noordwijk"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "Kapsalon Studio 54", "city": "Groningen"}, {"name": "De Groenteman Haarlem", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Tuincentrum Coppelmans", "city": "Oss"}, {"name": "Wijnwinkel Wijn & Spijs", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Café De Prins", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Alkmaar"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Zwolle"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Restaurant De Lindeboom", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Lisse"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Meubelmakerij Hout & Vorm", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "<PERSON>", "city": "Scheveningen"}, {"name": "Advocate<PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Architectenbureau De Zwarte Hond", "city": "Groningen"}, {"name": "Adviesbureau Berenschot", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Café De Oude Jan", "city": "Delft"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Amersfoort"}, {"name": "Fietsenmaker De Fietswerkplaats", "city": "Amsterdam"}, {"name": "Restaurant De Boterlap", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>we<PERSON><PERSON><PERSON>stree<PERSON>", "city": "Noordwijk"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Meubelmakerij De Houtfabriek", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Hair by Design", "city": "Groningen"}, {"name": "De Groenteboer Amersfoort", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Breda"}, {"name": "Tuincentrum De Driesprong", "city": "Zoetermeer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> De Wijnkelder", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Alkmaar"}, {"name": "Café De Kroeg", "city": "Nijmegen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Fietsenwinkel De Fietsgarage", "city": "Haarlem"}, {"name": "Restaurant De Gouden Karper", "city": "Driebergen"}, {"name": "Kwekerij De Bloemenweide", "city": "Lisse"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Meubelma<PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Ka<PERSON>alon De Haarstudio", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr. <PERSON><PERSON><PERSON><PERSON>", "city": "Noordwijk"}, {"name": "Architectenbureau Broekbakema", "city": "Rotterdam"}, {"name": "Adviesbureau Van Oord", "city": "Zeeland"}, {"name": "De Visboer Volendam", "city": "Volendam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Zwolle"}, {"name": "Café De Visscher", "city": "Scheveningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Bloemenwinkel De Bloemenschuur", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "Fietsen<PERSON> De Tweewieler", "city": "Leiden"}, {"name": "Restaurant De Oude School", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Noordwijk"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "Meubelmakerij <PERSON> Werkplaats", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Ha<PERSON>", "city": "Haarlem"}, {"name": "<PERSON> G<PERSON> Leiden", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "Tuincentrum De Oude Tol", "city": "Oss"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Café De Stad Utrecht", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Bloemenwinkel De Bloemenhoek", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Fietsenwinkel De Fietswinkel", "city": "Alkmaar"}, {"name": "Restaurant De Drie Haringen", "city": "Leiden"}, {"name": "Kwe<PERSON><PERSON><PERSON> De Bloemenboer", "city": "Lisse"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Meubelmakerij Hout & Staal", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Hairpoint", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Architectenbureau Cepezed", "city": "Delft"}, {"name": "Adviesbureau P2", "city": "Groningen"}, {"name": "<PERSON> V<PERSON>", "city": "Zoutelande"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Amersfoort"}, {"name": "Café De Korenbeurs", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Bloemenwinkel De Bloemenmand", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "Fietsenmaker De Fietsdokter", "city": "Utrecht"}, {"name": "Restaurant De Oude Brug", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Noordwijk"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "De Groenteboer Utrecht", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Delft"}, {"name": "Tuincentrum De Tuinboer", "city": "Alkmaar"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> De Wijnboetiek", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Café De Markt", "city": "Haarlem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Bloemenwinkel De Bloemenwinkel", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Amersfoort"}, {"name": "Fietsenwinkel De Fietsboer", "city": "Groningen"}, {"name": "Restaurant De Oude Haven", "city": "Amsterdam"}, {"name": "Kwekerij De Bloemkwekerij", "city": "Lisse"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "Kapsalon Haar & Huid", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Amsterdam"}, {"name": "Architectenbureau De Twee Snoeken", "city": "<PERSON>"}, {"name": "Adviesbureau TwynstraGudde", "city": "Amersfoort"}, {"name": "<PERSON>", "city": "Vlissingen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Café De Oude Markt", "city": "Nijmegen"}, {"name": "Adviesbureau Van der Linden", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Jeugd", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 010", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Software Ontwikkeling Codezilla", "city": "Utrecht"}, {"name": "Adviesbureau KplusV", "city": "Arnhem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON> <PERSON> Tekenbureau", "city": "Apeldoorn"}, {"name": "Raadge<PERSON>d <PERSON>genieursbureau Dokkum", "city": "Dokkum"}, {"name": "Software Ontwikkelaar WebWhales", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Adviesbureau De Vries", "city": "Tilburg"}, {"name": "Architectenbureau De Twee Snoeken", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Heerenveen"}, {"name": "Politiepost <PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Industrieterrein De Vaart", "city": "Almere"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf KEMA", "city": "Arnhem"}, {"name": "Adviesbureau P2", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Alkmaar"}, {"name": "Architectenbureau Cepezed", "city": "Delft"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar AppVantage", "city": "Eindhoven"}, {"name": "Adviesbureau TwynstraGudde", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "<PERSON>ieken<PERSON><PERSON>", "city": "Apeldoorn"}, {"name": "Politiepost Utrecht Overvecht", "city": "Utrecht"}, {"name": "Industrieterrein Westpoort", "city": "Amsterdam"}, {"name": "Opslagbedrijf <PERSON>", "city": "Haarlem"}, {"name": "Milieukeuringsbedrijf SGS Nederland", "city": "Spijkenisse"}, {"name": "Adviesbureau Berenschot", "city": "Utrecht"}, {"name": "Architectenbureau Broekbakema", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Advocate<PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Software Ontwikkelaar Nedbase", "city": "Middelburg"}, {"name": "Ziekenhuis St Jansdal", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Politiepost Groningen Centrum", "city": "Groningen"}, {"name": "Industrieterrein Eemhaven", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Milieukeuringsbedrijf TUV Nederland", "city": "Arnhem"}, {"name": "Adviesbureau Van Oord", "city": "Middelburg"}, {"name": "Architectenbureau De Zwarte Hond", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "Advocatenkantoor Mr. <PERSON><PERSON><PERSON><PERSON>", "city": "Noordwijk"}, {"name": "Software Ontwikkelaar Pixelpillow", "city": "Utrecht"}, {"name": "Adviesbur<PERSON>", "city": "Utrecht"}, {"name": "Ziekenhui<PERSON>", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Politiepost Rotterdam Zuid", "city": "Rotterdam"}, {"name": "Industrieterrein De Hurk", "city": "Eindhoven"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rijswijk"}, {"name": "Adviesbureau Royal HaskoningDHV", "city": "Amersfoort"}, {"name": "Architectenbureau Kraaijvanger", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Haarlem"}, {"name": "Software Ontwikkelaar Brightin", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "T  iel"}, {"name": "Politiepost Leiden Centrum", "city": "Leiden"}, {"name": "Industrieter<PERSON><PERSON>", "city": "<PERSON><PERSON>k<PERSON>"}, {"name": "Opslagbedrijf De Opslagbox", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf Bureau Veritas", "city": "Amersfoort"}, {"name": "Adviesbureau Arcadis", "city": "Arnhem"}, {"name": "Architectenbureau Inbo", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Groningen"}, {"name": "Software Ontwikkelaar CodeGorilla", "city": "Groningen"}, {"name": "<PERSON><PERSON>en<PERSON><PERSON>", "city": "Drachten"}, {"name": "Politiepost Den Bosch Centrum", "city": "<PERSON>"}, {"name": "Industrieterrein <PERSON>", "city": "Zutphen"}, {"name": "Opslagbedrijf City Box", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Adviesbureau Sweco Nederland", "city": "De Bilt"}, {"name": "Architectenbureau DP6", "city": "Delft"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amersfoort"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar Q42", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Politiepost Haarlem Centrum", "city": "Haarlem"}, {"name": "Industrieterrein De Boekelermeer", "city": "Alkmaar"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Lloyd’s Register", "city": "Rotterdam"}, {"name": "Adviesbureau Antea Group", "city": "Heerenveen"}, {"name": "Architectenbureau Atelier Pro", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar W3S", "city": "Zoetermeer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>og<PERSON><PERSON>"}, {"name": "Politiepost Amersfoort Centrum", "city": "Amersfoort"}, {"name": "Industrieterrein <PERSON>", "city": "<PERSON>"}, {"name": "Opslagbedrijf Storage Share", "city": "Eindhoven"}, {"name": "Milieukeuringsbedrijf DNV GL", "city": "Arnhem"}, {"name": "Adviesbureau Tauw", "city": "Deventer"}, {"name": "Architectenbureau Mecanoo", "city": "Delft"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Amsterdam"}, {"name": "Software Ontwikkelaar Webs", "city": "Eindhoven"}, {"name": "Ziekenhuis <PERSON>", "city": "Dokkum"}, {"name": "Politiepost Nijmegen Centrum", "city": "Nijmegen"}, {"name": "Industrieterrein De Meern", "city": "Utrecht"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Milieukeuringsbedrij<PERSON>+", "city": "Rotterdam"}, {"name": "Adviesbureau Witteveen+Bos", "city": "Deventer"}, {"name": "Architectenbureau <PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Leiden"}, {"name": "Software Ontwikkelaar Info Support", "city": "Veenendaal"}, {"name": "Ziekenhuis <PERSON>", "city": "Stadskanaal"}, {"name": "Politiepost Alkmaar Centrum", "city": "Alkmaar"}, {"name": "Industrieterrein Europoort", "city": "Rotterdam"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Milieukeuringsbedrijf <PERSON>s", "city": "Amsterdam"}, {"name": "Adviesbureau DHV", "city": "Amersfoort"}, {"name": "Architectenbureau VenhoevenCS", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "<PERSON>"}, {"name": "Software Ontwikkelaar Redkiwi", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Emmen"}, {"name": "Politiepost Eindhoven Centrum", "city": "Eindhoven"}, {"name": "Industrieterrein Lage Weide", "city": "Utrecht"}, {"name": "Opslagbedrijf Boxx Opslag", "city": "Leiden"}, {"name": "Milieukeuring<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Adviesbureau Ecorys", "city": "Rotterdam"}, {"name": "Architectenbureau Van den Broek en Bakema", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar Triple", "city": "Alkmaar"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Politiepost Zwolle Centrum", "city": "Zwolle"}, {"name": "Industrieterrein Botlek", "city": "Rotterdam"}, {"name": "Opslagbedrijf Mini Opslag", "city": "Amsterdam"}, {"name": "Milieukeurings<PERSON><PERSON><PERSON><PERSON> Nederland", "city": "Rijswijk"}, {"name": "Adviesbureau Haskoning", "city": "Nijmegen"}, {"name": "Architectenbureau Barcode Architects", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Groningen"}, {"name": "Software Ontwikkelaar Ymor", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Roermond"}, {"name": "Politiepost Maastricht Centrum", "city": "Maastricht"}, {"name": "Industrieterrein A12 Corridor", "city": "Zoetermeer"}, {"name": "Op<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "Milieukeuringsbedrijf TNO", "city": "Delft"}, {"name": "Adviesbureau AT Osborne", "city": "Baarn"}, {"name": "Architectenbureau Powerhouse Company", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Alkmaar"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Haarlem"}, {"name": "Software Ontwikkelaar Iquality", "city": "Nijmegen"}, {"name": "Ziekenhuis Medisch Spectrum Twente", "city": "Enschede"}, {"name": "Politiepost Arnhem Centrum", "city": "Arnhem"}, {"name": "Industrieterrein <PERSON>", "city": "Rijswijk"}, {"name": "Opslagbedrijf De Opslagloods", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf SGS Intron", "city": "Sittard"}, {"name": "Adviesbureau Rebel", "city": "Rotterdam"}, {"name": "Architectenbureau MVSA Architects", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Leiden"}, {"name": "Software Ontwikkelaar CoolProfs", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Zwolle"}, {"name": "Politiepost Tilburg Centrum", "city": "Tilburg"}, {"name": "Industrieterrein De Spaanse Polder", "city": "Rotterdam"}, {"name": "Opslagbedrijf <PERSON>", "city": "Groningen"}, {"name": "Mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Eindhoven"}, {"name": "Adviesbureau Buck Consultants", "city": "Nijmegen"}, {"name": "Architectenbureau <PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar Luminis", "city": "Arnhem"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Arnhem"}, {"name": "Politiepost Delft Centrum", "city": "Delft"}, {"name": "Industrieterrein De Kade", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "Milieukeuringsbedrijf BVI", "city": "Utrecht"}, {"name": "Adviesbureau Deerns", "city": "Rijswijk"}, {"name": "Architectenbureau ORANGE Architects", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Amsterdam"}, {"name": "Software Ontwikkelaar 4net", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Politiepost Leeuwarden Centrum", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Industrieterrein <PERSON>", "city": "Schiedam"}, {"name": "Opslagbedrijf Storex", "city": "Amsterdam"}, {"name": "Milieukeuringsbedrijf DEKRA Certification", "city": "Arnhem"}, {"name": "Adviesbureau Grontmij", "city": "De Bilt"}, {"name": "Architectenbureau Neutelings Riedijk", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Groningen"}, {"name": "Software Ontwikkelaar Incentro", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Sittard"}, {"name": "Politiepost Apeldoorn Centrum", "city": "Apeldoorn"}, {"name": "Industrieterrein <PERSON>", "city": "Barneveld"}, {"name": "Opslagbedrijf De Opslagkamer", "city": "<PERSON>"}, {"name": "Milie<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Apeldoorn"}, {"name": "Adviesbureau B<PERSON>", "city": "Groningen"}, {"name": "Architectenbureau Space & Matter", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar Ordina", "city": "Nieuwegein"}, {"name": "Ziekenhuis MCL", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Politiepost Enschede Centrum", "city": "Enschede"}, {"name": "Industrieterrein De <PERSON>", "city": "Alkmaar"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf KEMA Quality", "city": "Arnhem"}, {"name": "Adviesbureau HEVO", "city": "<PERSON>"}, {"name": "Architectenbureau MVRDV", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Amsterdam"}, {"name": "Software Ontwikkelaar Avanade", "city": "Utrecht"}, {"name": "Ziekenhuis St. Antonius", "city": "Nieuwegein"}, {"name": "Politiepost Breda Centrum", "city": "Breda"}, {"name": "Industrieterrein De Ecofactorij", "city": "Apeldoorn"}, {"name": "Opslagbedrijf De Opslagruimte", "city": "Rotterdam"}, {"name": "Mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Breda"}, {"name": "Adviesbureau Balance", "city": "Amsterdam"}, {"name": "Architectenbureau UNStudio", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar Capgemini", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Cat<PERSON>", "city": "Eindhoven"}, {"name": "Politiepost Heerlen Centrum", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Industrieterrein <PERSON>", "city": "Amersfoort"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Adviesbureau APPM", "city": "Hoofddorp"}, {"name": "Architectenbureau KAW", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Haarlem"}, {"name": "Software Ontwikkelaar Atos", "city": "Amersfoort"}, {"name": "Ziekenhuis Amstelland", "city": "Amstelveen"}, {"name": "Politiepost Almere Centrum", "city": "Almere"}, {"name": "Industrieterrein De Kronkels", "city": "Almere"}, {"name": "Opslagbedrijf De Opslag", "city": "Groningen"}, {"name": "Milieukeuringsbedrijf KEMA Testing", "city": "Arnhem"}, {"name": "Adviesbureau BMC", "city": "Amersfoort"}, {"name": "Architectenbureau De Architecten NV", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar KPN ICT Consulting", "city": "Amsterdam"}, {"name": "Ziekenhuis Deventer Ziekenhuis", "city": "Deventer"}, {"name": "Politiepost Zutphen Centrum", "city": "Zutphen"}, {"name": "Industrieterrein De <PERSON>steden", "city": "Enschede"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON> Opslag", "city": "Haarlem"}, {"name": "Milieukeuringsbedrijf Applus+ Nederland", "city": "Rotterdam"}, {"name": "Adviesbureau Van Beek", "city": "Amsterdam"}, {"name": "Architectenbureau Van <PERSON> Berg", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "<PERSON>"}, {"name": "Software Ontwikkelaar Codebasics", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Hilversum"}, {"name": "Politiepost Haarlem Noord", "city": "Haarlem"}, {"name": "Industrieterrein <PERSON>", "city": "Wijk bij Du<PERSON>e"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Adviesbureau De Groene Lijn", "city": "Leiden"}, {"name": "Architectenbureau Van Eijk", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar Webdelta", "city": "Groningen"}, {"name": "Zieken<PERSON><PERSON> Flevoz<PERSON>s", "city": "Almere"}, {"name": "Politiepost Zwolle Zuid", "city": "Zwolle"}, {"name": "Industrieterrein <PERSON>", "city": "Middelburg"}, {"name": "Opslagbedrijf <PERSON>", "city": "Amersfoort"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Arnhem"}, {"name": "Adviesbureau Van der Meer", "city": "Tilburg"}, {"name": "Architectenbureau De Jong", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar Bitfactory", "city": "Rotterdam"}, {"name": "<PERSON>iek<PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Politiepost Eindhoven Zuid", "city": "Eindhoven"}, {"name": "Industrieterrein De Wetering", "city": "Utrecht"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rijswijk"}, {"name": "Adviesbureau Buro C5", "city": "Amsterdam"}, {"name": "Architectenbureau Van <PERSON>", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Amsterdam"}, {"name": "Software Ontwikkelaar Frontmen", "city": "Utrecht"}, {"name": "Ziekenhuis Wilhelm<PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Politiepost Rotterdam Centrum", "city": "Rotterdam"}, {"name": "Industrieterrein De <PERSON>h", "city": "<PERSON><PERSON>"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "Milieukeuringsbedrijf DEKRA Nederland", "city": "Utrecht"}, {"name": "Adviesbureau B<PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Architectenbureau Vocus", "city": "Bussum"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Groningen"}, {"name": "Software Ontwikkelaar Mangrove", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Politiepost Arnhem Zuid", "city": "Arnhem"}, {"name": "Industrieterrein <PERSON>", "city": "Barneveld"}, {"name": "Opslagbedrijf De Opslagmeester", "city": "Amsterdam"}, {"name": "Milieukeuringsbedrijf Bureau Veritas Nederland", "city": "Amersfoort"}, {"name": "Adviesbureau Van <PERSON> Horst", "city": "<PERSON>"}, {"name": "Architectenbureau Team 4", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Leiden"}, {"name": "Software Ontwikkelaar Netvlies", "city": "Breda"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Hoofddorp"}, {"name": "Politiepost Nijmegen Zuid", "city": "Nijmegen"}, {"name": "Industrieterrein De O<PERSON>erhorn", "city": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf SGS Nederland B.V.", "city": "Spijkenisse"}, {"name": "Adviesbureau B<PERSON>", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Architectenbureau ZZDP", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar Websolve", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>og<PERSON><PERSON>"}, {"name": "Politiepost Tilburg Zuid", "city": "Tilburg"}, {"name": "Industrieterrein De Kanaalzone", "city": "Terneuzen"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Milieukeuringsbedrijf TUV Rheinland", "city": "<PERSON><PERSON>"}, {"name": "Adviesbureau Aveco de <PERSON>", "city": "Amersfoort"}, {"name": "Architectenbureau DENC", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Haarlem"}, {"name": "Software Ontwikkelaar 100grams", "city": "Amsterdam"}, {"name": "Ziekenhuis <PERSON>", "city": "Rotterdam"}, {"name": "Politiepost Maastricht Zuid", "city": "Maastricht"}, {"name": "Industrieterrein <PERSON>", "city": "Amersfoort"}, {"name": "Opslagbedrijf De Opslagplaats", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Lloyd's Register Nederland", "city": "Rotterdam"}, {"name": "Adviesbureau B<PERSON>", "city": "Groningen"}, {"name": "Architectenbureau Van der Linde", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Amsterdam"}, {"name": "Software Ontwikkelaar Wonderkind", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Geldrop"}, {"name": "Politiepost Breda Zuid", "city": "Breda"}, {"name": "Industrieterrein <PERSON>", "city": "Schiedam"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON> Heijde Opslag", "city": "<PERSON>"}, {"name": "Milieukeuring<PERSON><PERSON><PERSON><PERSON><PERSON> Certificatie", "city": "Apeldoorn"}, {"name": "Adviesbureau Buro 5", "city": "Maastricht"}, {"name": "Architectenbureau LIAG", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar Codewave", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Politiepost Delft Zuid", "city": "Delft"}, {"name": "Industrieterrein De <PERSON>steden", "city": "Enschede"}, {"name": "Opslagbedrijf <PERSON> Opslaghal", "city": "Haarlem"}, {"name": "Milieukeuringsbedrijf <PERSON> Certification", "city": "Utrecht"}, {"name": "Adviesbureau <PERSON>", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Veen", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Leiden"}, {"name": "Software Ontwikkelaar Codeklopper", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Elisabeth-TweeSteden", "city": "Tilburg"}, {"name": "Politiepost Leeuwarden Zuid", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Industrieterrein De Ecofactorij", "city": "Apeldoorn"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON> Opslag", "city": "Amsterdam"}, {"name": "Milieukeuringsbedrijf DNV Nederland", "city": "Arnhem"}, {"name": "Adviesbureau Buro ZP", "city": "Utrecht"}, {"name": "Architectenbureau De Architect", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "<PERSON>"}, {"name": "Software Ontwikkelaar CodeCreators", "city": "Eindhoven"}, {"name": "<PERSON>ieken<PERSON><PERSON> <PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Politiepost Amersfoort Zuid", "city": "Amersfoort"}, {"name": "Industrieterrein De Kronkels", "city": "Almere"}, {"name": "Opslagbedrijf De Opslagboer", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Applus+ Certification", "city": "Rotterdam"}, {"name": "Adviesbureau Buro Delta", "city": "Arnhem"}, {"name": "Architectenbureau Van <PERSON>", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Haarlem"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar Webmen", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Politiepost Enschede Zuid", "city": "Enschede"}, {"name": "Industrieterrein De Kade", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON> Opslag", "city": "Leiden"}, {"name": "Milieukeuringsbedrijf TUV Nederland B.V.", "city": "Arnhem"}, {"name": "Adviesbureau B<PERSON>", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Wal", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar CodeZap", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Politiepost Alkmaar Zuid", "city": "Alkmaar"}, {"name": "Industrieterrein De <PERSON>", "city": "Alkmaar"}, {"name": "Opslagbedrijf <PERSON> Opslagwinkel", "city": "Groningen"}, {"name": "Milieukeuringsbedrijf Eurofins Milieu", "city": "Amsterdam"}, {"name": "Adviesbureau Buro Plan", "city": "Rotterdam"}, {"name": "Architectenbureau Van der Burg", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Groningen"}, {"name": "Software Ontwikkelaar CodeSpark", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Politiepost Heerlen Zuid", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Industrieterrein <PERSON>", "city": "Rijswijk"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON> Opslag", "city": "Amsterdam"}, {"name": "Mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rijswijk"}, {"name": "Adviesbureau Buro Stad", "city": "<PERSON>"}, {"name": "Architectenbureau De Ruiter", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Haarlem"}, {"name": "Software Ontwikkelaar CodeVibe", "city": "Utrecht"}, {"name": "Ziekenhui<PERSON> G<PERSON>", "city": "Delft"}, {"name": "Politiepost Apeldoorn Zuid", "city": "Apeldoorn"}, {"name": "Industrieterrein De Spaanse Polder", "city": "Rotterdam"}, {"name": "Opslagbedrijf De Opslagloods", "city": "Tilburg"}, {"name": "Milieukeuringsbedrijf SGS Intron B.V.", "city": "Sittard"}, {"name": "Adviesbureau B<PERSON>", "city": "Groningen"}, {"name": "Architectenbureau Van der Heijden", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Leiden"}, {"name": "Software Ontwikkelaar CodeMinds", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Maxima MC", "city": "Veldhoven"}, {"name": "Politiepost Utrecht Zuid", "city": "Utrecht"}, {"name": "Industrieterrein De Wetering-<PERSON>ar", "city": "Haarlem"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON> Opslag", "city": "<PERSON>"}, {"name": "Milieukeuringsbedrijf Bureau Veritas Milieu", "city": "Amersfoort"}, {"name": "Adviesbureau Buro Visie", "city": "Utrecht"}, {"name": "Architectenbureau De Jonge", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Tilburg"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Amsterdam"}, {"name": "Software Ontwikkelaar CodeStorm", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Dordrecht"}, {"name": "Politiepost Nijmegen Noord", "city": "Nijmegen"}, {"name": "Industrieterrein <PERSON>", "city": "Alkmaar"}, {"name": "Opslagbedrijf De Opslagbox", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf TUV SUD Nederland", "city": "Arnhem"}, {"name": "Adviesbureau Buro Stad en Land", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Sluis", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "<PERSON>"}, {"name": "Software Ontwikkelaar CodeFlow", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Politiepost Arnhem Noord", "city": "Arnhem"}, {"name": "Industrieterrein De Harselaar West", "city": "Barneveld"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON>den Opslag", "city": "Amsterdam"}, {"name": "Mi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Apeldoorn"}, {"name": "Adviesbureau Buro Horizon", "city": "Rotterdam"}, {"name": "Architectenbureau Van <PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Groningen"}, {"name": "Software Ontwikkelaar CodeBlitz", "city": "Amsterdam"}, {"name": "Ziekenhuis BovenIJ", "city": "Amsterdam"}, {"name": "Politiepost Tilburg Noord", "city": "Tilburg"}, {"name": "Industrieterrein De Kanaalstreek", "city": "Terneuzen"}, {"name": "Opslagbedrijf De Opslagruimte", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf DNV GL Certification", "city": "Arnhem"}, {"name": "Adviesbureau <PERSON>", "city": "Leiden"}, {"name": "Architectenbureau De Boer", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Haarlem"}, {"name": "Software Ontwikkelaar CodeRush", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Politiepost Maastricht Noord", "city": "Maastricht"}, {"name": "Industrieterrein De Boekelermeer Zuid", "city": "Alkmaar"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON>slag", "city": "<PERSON>"}, {"name": "Milieukeuringsbedrijf SGS Certification", "city": "Spijkenisse"}, {"name": "Adviesbureau B<PERSON>", "city": "Utrecht"}, {"name": "Architectenbureau Van <PERSON>", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Amsterdam"}, {"name": "Software Ontwikkelaar CodeNinja", "city": "Utrecht"}, {"name": "Ziekenhuis Admiraal De Ruy<PERSON>", "city": "Goes"}, {"name": "Politiepost Breda Noord", "city": "Breda"}, {"name": "Industrieterrein De Hurk Zuid", "city": "Eindhoven"}, {"name": "Opslagbedrijf De Opslaghal Utrecht", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf Bureau Veritas Certification", "city": "Amersfoort"}, {"name": "Adviesbureau B<PERSON>", "city": "<PERSON>"}, {"name": "Architectenbureau De Groot", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar CodeMaster", "city": "Groningen"}, {"name": "<PERSON>ieken<PERSON><PERSON> Gelderse <PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Politiepost Delft Noord", "city": "Delft"}, {"name": "Industrieterrein De Meern Zuid", "city": "Utrecht"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON> Opslag", "city": "Amsterdam"}, {"name": "Milieukeuringsbedrijf TUV Nederland Certificatie", "city": "Arnhem"}, {"name": "Adviesbureau <PERSON>", "city": "Rotterdam"}, {"name": "Architectenbureau Van der Heijden", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Haarlem"}, {"name": "Software Ontwikkelaar CodeVision", "city": "Eindhoven"}, {"name": "Ziekenhuis Zaans Medisch Centrum", "city": "Zaandam"}, {"name": "Politiepost Leeuwarden Noord", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Industrieterrein De A12 Corridor", "city": "Zoetermeer"}, {"name": "Opslagbedrijf De Opslagboer Rotterdam", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Kiwa Nederland B.V.", "city": "Rijswijk"}, {"name": "Adviesbureau B<PERSON>", "city": "Amsterdam"}, {"name": "Architectenbureau De Ruiter", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Leiden"}, {"name": "Software Ontwikkelaar CodeCraft", "city": "Amsterdam"}, {"name": "<PERSON>ieken<PERSON><PERSON>", "city": "Bergen op Zoom"}, {"name": "Politiepost Enschede Noord", "city": "Enschede"}, {"name": "Industrieterrein De Wetering Oost", "city": "Utrecht"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Eurofins Certification", "city": "Amsterdam"}, {"name": "Adviesbureau Buro Ontwerp", "city": "<PERSON>"}, {"name": "Architectenbureau Van der Wal", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar CodeWave", "city": "Groningen"}, {"name": "Ziekenhuis Amphia", "city": "Breda"}, {"name": "Politiepost Arnhem Velperpoort", "city": "Arnhem"}, {"name": "Industrieterrein De Kade Zuid", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Opslagbedrijf De Opslagwinkel Utrecht", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf SGS Milieu", "city": "Spijkenisse"}, {"name": "Adviesbureau Buro <PERSON>", "city": "Amsterdam"}, {"name": "Architectenbureau De Boer", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar CodeBoost", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Roosendaal"}, {"name": "Politiepost Tilburg West", "city": "Tilburg"}, {"name": "Industrieterrein De Hoogh Oost", "city": "<PERSON><PERSON>"}, {"name": "Op<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Bureau Veritas Milieu B.V.", "city": "Amersfoort"}, {"name": "Adviesbureau Buro <PERSON>miek", "city": "Utrecht"}, {"name": "Architectenbureau Van <PERSON>", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Groningen"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "<PERSON>"}, {"name": "Software Ontwikkelaar CodeCore", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>ziekenhui<PERSON> Koningin Beat<PERSON>", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Politiepost Maastricht West", "city": "Maastricht"}, {"name": "Industrieterrein De Oosterhorn Zuid", "city": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Opslagbedrijf De Opslaghal Amersfoort", "city": "Amersfoort"}, {"name": "Milieukeuringsbedrijf Kiwa Certification B.V.", "city": "Apeldoorn"}, {"name": "Adviesbureau Buro Strategie", "city": "Rotterdam"}, {"name": "Architectenbureau De Ha<PERSON>", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Leiden"}, {"name": "Software Ontwikkelaar CodePulse", "city": "Groningen"}, {"name": "<PERSON>ieken<PERSON><PERSON> ZorgSaam", "city": "Terneuzen"}, {"name": "Politiepost Breda West", "city": "Breda"}, {"name": "Industrieterrein De Kanaalzone Zuid", "city": "Terneuzen"}, {"name": "Op<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf TUV Rheinland Certification", "city": "Arnhem"}, {"name": "Adviesbureau B<PERSON>", "city": "Amsterdam"}, {"name": "Architectenbureau Van <PERSON>", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar CodeStream", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Politiepost Delft West", "city": "Delft"}, {"name": "Industrieterrein De Wetering West", "city": "Utrecht"}, {"name": "Opslagbedrijf De Opslagboer Den Haag", "city": "<PERSON>"}, {"name": "Milieukeuringsbedrijf SGS Nederland Certification", "city": "Spijkenisse"}, {"name": "Adviesbureau Buro Connect", "city": "Groningen"}, {"name": "Architectenbureau De Groot", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar CodeTrend", "city": "Utrecht"}, {"name": "Ziekenhui<PERSON>", "city": "Boxmeer"}, {"name": "Politiepost Leeuwarden West", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Industrieterrein De Boekelermeer Noord", "city": "Alkmaar"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf Eurofins Milieu B.V.", "city": "Amsterdam"}, {"name": "Adviesbureau Buro Samenwerking", "city": "<PERSON>"}, {"name": "Architectenbureau Van der Sluis", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Groningen"}, {"name": "Software Ontwikkelaar CodeSparkle", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Heerenveen"}, {"name": "Politiepost Arnhem Rijnboog", "city": "Arnhem"}, {"name": "Industrieterrein De Hurk Noord", "city": "Eindhoven"}, {"name": "Opslagbedrijf De Opslagwinkel Rotterdam", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Bureau Veritas Nederland B.V.", "city": "Amersfoort"}, {"name": "Adviesbureau Buro Impact", "city": "Utrecht"}, {"name": "Architectenbureau De Ruiter", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Amsterdam"}, {"name": "Software Ontwikkelaar CodeDynamic", "city": "Groningen"}, {"name": "Ziekenhuis St. Jansdal", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Politiepost Tilburg Oost", "city": "Tilburg"}, {"name": "Industrieterrein De Ecofactorij <PERSON>", "city": "Apeldoorn"}, {"name": "Op<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf Kiwa Nederland Certification", "city": "Rijswijk"}, {"name": "Adviesbureau De Vries B.V.", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Laan B.V.", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr. <PERSON> N.V.", "city": "<PERSON>"}, {"name": "Software Ontwikkelaar CodeZap B.V.", "city": "Eindhoven"}, {"name": "Ziekenhuis St. Antonius", "city": "Nieuwegein"}, {"name": "Politiepost Amsterdam Zuid", "city": "Amsterdam"}, {"name": "Industrieterrein De Schie Noord", "city": "Schiedam"}, {"name": "Opslagbed<PERSON><PERSON><PERSON> B.V.", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf EcoTest B.V.", "city": "Arnhem"}, {"name": "Adviesbureau Van der Berg B.V.", "city": "Utrecht"}, {"name": "Architectenbureau De Wit N.V.", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Leiden"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Haarlem"}, {"name": "Software Ontwikkelaar CodeTrend B.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Cat<PERSON>", "city": "Eindhoven"}, {"name": "Politiepost Rotterdam Noord", "city": "Rotterdam"}, {"name": "Industrieterrein <PERSON>", "city": "Zutphen"}, {"name": "Opslagbedrijf De Opslagwinkel B.V.", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf SGS Nederland B.V.", "city": "Spijkenisse"}, {"name": "Adviesbureau Buro Visie B.V.", "city": "<PERSON>"}, {"name": "Architectenbureau De Ruiter B.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Groningen"}, {"name": "Advocatenkantoor Mr. <PERSON> N.V.", "city": "Leiden"}, {"name": "Software Ontwikkelaar CodeDynamic B.V.", "city": "Rotterdam"}, {"name": "Ziekenhuis Amstelland", "city": "Amstelveen"}, {"name": "Politiepost Utrecht Noord", "city": "Utrecht"}, {"name": "Industrieterrein De Kanaalzone Noord", "city": "Terneuzen"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON> B.V.", "city": "<PERSON>"}, {"name": "Milieukeuringsbedrijf TUV Nederland B.V.", "city": "Arnhem"}, {"name": "Adviesbureau Buro Stad B.V.", "city": "Rotterdam"}, {"name": "Architectenbureau Van der Meer B.V.", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Groningen"}, {"name": "Software Ontwikkelaar CodeCore B.V.", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Politiepost Haarlem Zuid", "city": "Haarlem"}, {"name": "Industrieterrein De Boekelermeer Oost", "city": "Alkmaar"}, {"name": "Opslagbedrijf De Opslagboer B.V.", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Kiwa Nederland B.V.", "city": "Rijswijk"}, {"name": "Adviesbureau Buro Plan B.V.", "city": "Amsterdam"}, {"name": "Architectenbureau De Groot B.V.", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>al B.V.", "city": "Leiden"}, {"name": "Advocatenkantoor Mr. <PERSON> Wit N.V.", "city": "Utrecht"}, {"name": "Software Ontwikkelaar CodeBoost B.V.", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Arnhem"}, {"name": "Politiepost Tilburg Zuid", "city": "Tilburg"}, {"name": "Industrieterrein De Hurk Oost", "city": "Eindhoven"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Amsterdam"}, {"name": "Milieukeuringsbedrijf Bureau Veritas B.V.", "city": "Amersfoort"}, {"name": "Adviesbureau Buro Dynamiek B.V.", "city": "Rotterdam"}, {"name": "Architectenbureau Van der Heijden B.V.", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Amsterdam"}, {"name": "Software Ontwikkelaar CodeSpark B.V.", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Zwolle"}, {"name": "Politiepost Nijmegen Zuid", "city": "Nijmegen"}, {"name": "Industrieterrein De Wetering Zuid", "city": "Utrecht"}, {"name": "Opslagbedrijf De Opslaghal B.V.", "city": "Haarlem"}, {"name": "Milieukeuringsbedrijf DNV Nederland B.V.", "city": "Arnhem"}, {"name": "Adviesbureau Buro Horizon B.V.", "city": "Leiden"}, {"name": "Architectenbureau De Boer B.V.", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> N.V.", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Groningen"}, {"name": "Software Ontwikkelaar CodeNinja B.V.", "city": "Utrecht"}, {"name": "Ziekenhuis Medisch Spectrum Twente", "city": "Enschede"}, {"name": "Politiepost Arnhem Noord", "city": "Arnhem"}, {"name": "Industrieterrein De Ecofactorij <PERSON>ord", "city": "Apeldoorn"}, {"name": "Opslagbed<PERSON><PERSON><PERSON> Rotterdam B.V.", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Applus+ Nederland B.V.", "city": "Rotterdam"}, {"name": "Adviesbureau Buro Innovatie B.V.", "city": "<PERSON>"}, {"name": "Architectenbureau Van der Wal B.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>root N.V.", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Leiden"}, {"name": "Software Ontwikkelaar CodeMaster B.V.", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Sittard"}, {"name": "Politiepost Maastricht Zuid", "city": "Maastricht"}, {"name": "Industrieterrein De Kanaalzone Zuid", "city": "Terneuzen"}, {"name": "Opslagbedrijf De Opslagruimte B.V.", "city": "Amsterdam"}, {"name": "Milieukeuringsbedrijf TNO Nederland B.V.", "city": "Delft"}, {"name": "Adviesbureau Buro Connect B.V.", "city": "Rotterdam"}, {"name": "Architectenbureau De Ruiter N.V.", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr. <PERSON> N.V.", "city": "Utrecht"}, {"name": "Software Ontwikkelaar CodeCraft B.V.", "city": "Amsterdam"}, {"name": "Ziekenhuis MCL", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Politiepost Breda Zuid", "city": "Breda"}, {"name": "Industrieterrein De Boekelermeer Noord", "city": "Alkmaar"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON>al Utrecht B.V.", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf Kiwa CMR B.V.", "city": "Apeldoorn"}, {"name": "Adviesbureau Buro Samen B.V.", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Laan N.V.", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> De Wit B.V.", "city": "Rotterdam"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "<PERSON>"}, {"name": "Software Ontwikkelaar CodePulse B.V.", "city": "Groningen"}, {"name": "Ziekenhuis Amphia", "city": "Breda"}, {"name": "Politiepost Delft Zuid", "city": "Delft"}, {"name": "Industrieterrein De Hurk Zuid", "city": "Eindhoven"}, {"name": "Opslagbedrijf De Opslagboer Utrecht B.V.", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf SGS Intron B.V.", "city": "Sittard"}, {"name": "Adviesbureau Buro Strategie B.V.", "city": "Amsterdam"}, {"name": "Architectenbureau De Groot B.V.", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Leiden"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar CodeStream B.V.", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Roosendaal"}, {"name": "Politiepost Tilburg Noord", "city": "Tilburg"}, {"name": "Industrieterrein De Wetering Noord", "city": "Utrecht"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON> Rotterdam B.V.", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Eurofins Milieu B.V.", "city": "Amsterdam"}, {"name": "Adviesbureau Buro Samenwerking B.V.", "city": "<PERSON>"}, {"name": "Architectenbureau Van der Meer N.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Groningen"}, {"name": "Advocatenkantoor Mr. <PERSON> N.V.", "city": "Utrecht"}, {"name": "Software Ontwikkelaar CodeSparkle B.V.", "city": "Rotterdam"}, {"name": "<PERSON>ieken<PERSON><PERSON> Gelderse <PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Politiepost Arnhem Zuid", "city": "Arnhem"}, {"name": "Industrieterrein De Ecofactorij <PERSON>", "city": "Apeldoorn"}, {"name": "Opslagbedrijf De Opslaghal Amersfoort B.V.", "city": "Amersfoort"}, {"name": "Milie<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Inspecta B.V.", "city": "Apeldoorn"}, {"name": "Adviesbureau Buro Impact B.V.", "city": "Amsterdam"}, {"name": "Architectenbureau De Wit N.V.", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Leiden"}, {"name": "Software Ontwikkelaar CodeDynamic N.V.", "city": "Eindhoven"}, {"name": "Ziekenhuis Zaans Medisch Centrum", "city": "Zaandam"}, {"name": "Politiepost Leeuwarden Zuid", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Industrieterrein De Kanaalzone Oost", "city": "Terneuzen"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON>den Utrecht B.V.", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf TUV Rheinland B.V.", "city": "Arnhem"}, {"name": "Adviesbureau Buro Stadszaken B.V.", "city": "Rotterdam"}, {"name": "Architectenbureau De Ruiter B.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Groningen"}, {"name": "Software Ontwikkelaar CodeCraft N.V.", "city": "Utrecht"}, {"name": "<PERSON>ieken<PERSON><PERSON> ZorgSaam", "city": "Terneuzen"}, {"name": "Politiepost Enschede Zuid", "city": "Enschede"}, {"name": "Industrieterrein De Boekelermeer Zuid", "city": "Alkmaar"}, {"name": "Opslagbedrijf De Opslagwinkel Rotterdam B.V.", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf SGS Certification B.V.", "city": "Spijkenisse"}, {"name": "Adviesbureau Buro Ontwerp B.V.", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Laan B.V.", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> De Wit B.V.", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr. <PERSON> N.V.", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar CodePulse N.V.", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Politiepost Maastricht Noord", "city": "Maastricht"}, {"name": "Industrieterrein De Hurk Noord", "city": "Eindhoven"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON> Utrecht B.V.", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf Bureau Veritas Nederland B.V.", "city": "Amersfoort"}, {"name": "Adviesbureau Buro Innovatie N.V.", "city": "<PERSON>"}, {"name": "Architectenbureau De Groot N.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Wal N.V.", "city": "Leiden"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar CodeStream N.V.", "city": "Groningen"}, {"name": "Ziekenhuis BovenIJ", "city": "Amsterdam"}, {"name": "Politiepost Tilburg West", "city": "Tilburg"}, {"name": "Industrieterrein De Wetering Oost", "city": "Utrecht"}, {"name": "Opslagbedrijf De Opslagboer Rotterdam B.V.", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Kiwa Nederland Certification B.V.", "city": "Rijswijk"}, {"name": "Adviesbureau Buro Connect N.V.", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Meer B.V.", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> N.V.", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr. <PERSON> N.V.", "city": "Groningen"}, {"name": "Software Ontwikkelaar CodeSparkle N.V.", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Politiepost Arnhem Velperpoort", "city": "Arnhem"}, {"name": "Industrieterrein De Ecofactorij Oost", "city": "Apeldoorn"}, {"name": "Opslagbed<PERSON><PERSON><PERSON>jden Amersfoort B.V.", "city": "Amersfoort"}, {"name": "Milieukeuringsbedrijf TUV Nederland Certification B.V.", "city": "Arnhem"}, {"name": "Adviesbureau Buro Samen B.V.", "city": "Utrecht"}, {"name": "Architectenbureau De Wit B.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> N.V.", "city": "Rotterdam"}, {"name": "Advocatenkantoor Mr. <PERSON> N.V.", "city": "Leiden"}, {"name": "Software Ontwikkelaar CodeDynamic B.V.", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Politiepost Nijmegen Noord", "city": "Nijmegen"}, {"name": "Industrieterrein De Kanaalzone West", "city": "Terneuzen"}, {"name": "Opslagbedrijf De Opslaghal Utrecht B.V.", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf SGS Milieu B.V.", "city": "Spijkenisse"}, {"name": "Adviesbureau Buro Strategie N.V.", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Heijden N.V.", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er N.V.", "city": "Groningen"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar CodeCraft B.V.", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Utrecht"}, {"name": "Politiepost Breda Noord", "city": "Breda"}, {"name": "Industrieterrein De Boekelermeer West", "city": "Alkmaar"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON>al Rotterdam B.V.", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Eurofins Certification B.V.", "city": "Amsterdam"}, {"name": "Adviesbureau Buro Ontwerp N.V.", "city": "Rotterdam"}, {"name": "Architectenbureau De Boer N.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Groningen"}, {"name": "Software Ontwikkelaar CodePulse B.V.", "city": "Eindhoven"}, {"name": "Ziekenhui<PERSON> G<PERSON>", "city": "Delft"}, {"name": "Politiepost Maastricht West", "city": "Maastricht"}, {"name": "Industrieterrein De Hurk West", "city": "Eindhoven"}, {"name": "Opslagbedrijf De Opslagwinkel Utrecht B.V.", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf Bureau Veritas Certification B.V.", "city": "Amersfoort"}, {"name": "Adviesbureau Buro Innovatie B.V.", "city": "Utrecht"}, {"name": "Architectenbureau Van der Wal N.V.", "city": "Leiden"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr. <PERSON> N.V.", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar CodeSpark B.V.", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Politiepost Tilburg Oost", "city": "Tilburg"}, {"name": "Industrieterrein De Wetering West", "city": "Utrecht"}, {"name": "Opslagbed<PERSON><PERSON><PERSON> Rotterdam B.V.", "city": "Rotterdam"}, {"name": "Milieukeurings<PERSON><PERSON><PERSON>f <PERSON> Testing B.V.", "city": "Rijswijk"}, {"name": "Adviesbureau Buro Connect B.V.", "city": "Amsterdam"}, {"name": "Architectenbureau De Ruiter N.V.", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er N.V.", "city": "Groningen"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "<PERSON>"}, {"name": "Software Ontwikkelaar CodeDynamic N.V.", "city": "Eindhoven"}, {"name": "<PERSON>ieken<PERSON><PERSON>", "city": "Bergen op Zoom"}, {"name": "Politiepost Arnhem Rijnboog", "city": "Arnhem"}, {"name": "Industrieterrein De Ecofactorij West", "city": "Apeldoorn"}, {"name": "Opslagbedrijf De Opslagboer Amersfoort B.V.", "city": "Amersfoort"}, {"name": "Milieukeuringsbedrijf SGS Nederland N.V.", "city": "Spijkenisse"}, {"name": "Adviesbureau Buro Samenwerking N.V.", "city": "Rotterdam"}, {"name": "Architectenbureau De Wit B.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Leiden"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Utrecht"}, {"name": "Software Ontwikkelaar CodeCraft N.V.", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Heerenveen"}, {"name": "Politiepost Nijmegen West", "city": "Nijmegen"}, {"name": "Industrieterrein De Kanaalzone Noord", "city": "Terneuzen"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON>al Utrecht B.V.", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf TUV Rheinland Certification B.V.", "city": "Arnhem"}, {"name": "Adviesbureau Buro Stadszaken N.V.", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Heijden B.V.", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Rotterdam"}, {"name": "Advocatenkantoor Mr. <PERSON> N.V.", "city": "Leiden"}, {"name": "Software Ontwikkelaar CodePulse B.V.", "city": "Amsterdam"}, {"name": "Ziekenhuis St. Jansdal", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Politiepost Breda West", "city": "Breda"}, {"name": "Industrieterrein De Boekelermeer Oost", "city": "Alkmaar"}, {"name": "Opslagbedrijf De Opslaghal Rotterdam B.V.", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Eurofins Milieu N.V.", "city": "Amsterdam"}, {"name": "Adviesbureau Buro Ontwerp B.V.", "city": "Utrecht"}, {"name": "Architectenbureau De Boer B.V.", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> N.V.", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr. <PERSON> Wit N.V.", "city": "<PERSON>"}, {"name": "Software Ontwikkelaar CodeSpark N.V.", "city": "Eindhoven"}, {"name": "<PERSON>ieken<PERSON><PERSON> ZorgSaam", "city": "Terneuzen"}, {"name": "Politiepost Maastricht Oost", "city": "Maastricht"}, {"name": "Industrieterrein De Hurk Oost", "city": "Eindhoven"}, {"name": "Opslagbed<PERSON><PERSON><PERSON> Amersfoort B.V.", "city": "Amersfoort"}, {"name": "Milieukeuringsbedrijf Bureau Veritas Nederland N.V.", "city": "Amersfoort"}, {"name": "Adviesbureau Buro Innovatie N.V.", "city": "Rotterdam"}, {"name": "Architectenbureau Van der Wal B.V.", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>root N.V.", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr<PERSON>", "city": "Amsterdam"}, {"name": "Software Ontwikkelaar CodeDynamic B.V.", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Politiepost Tilburg Zuid", "city": "Tilburg"}, {"name": "Industrieterrein De Wetering Zuid", "city": "Utrecht"}, {"name": "Opslagbedrijf De Opslagwinkel Amersfoort B.V.", "city": "Amersfoort"}, {"name": "Milieukeuringsbedrijf Kiwa Nederland B.V.", "city": "Rijswijk"}, {"name": "Adviesbureau Buro Connect N.V.", "city": "Leiden"}, {"name": "Architectenbureau De Ruiter N.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Rotterdam"}, {"name": "Advocatenkantoor Mr. <PERSON> N.V.", "city": "Utrecht"}, {"name": "Software Ontwikkelaar CodeCraft B.V.", "city": "Eindhoven"}, {"name": "Ziekenhuis BovenIJ", "city": "Amsterdam"}, {"name": "Politiepost Arnhem Noord", "city": "Arnhem"}, {"name": "Industrieterrein De Ecofactorij <PERSON>ord", "city": "Apeldoorn"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON>den Utrecht B.V.", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf SGS Intron N.V.", "city": "Sittard"}, {"name": "Adviesbureau Buro Samen B.V.", "city": "<PERSON>"}, {"name": "Architectenbureau De Wit N.V.", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Amsterdam"}, {"name": "Advocatenkantoor Mr. <PERSON> N.V.", "city": "Groningen"}, {"name": "Software Ontwikkelaar CodePulse N.V.", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Leiden"}, {"name": "Politiepost Nijmegen Zuid", "city": "Nijmegen"}, {"name": "Industrieterrein De Kanaalzone Zuid", "city": "Terneuzen"}, {"name": "Opslagbedrijf De Opslaghal Amersfoort B.V.", "city": "Amersfoort"}, {"name": "Milieukeuringsbedrijf TUV Nederland B.V.", "city": "Arnhem"}, {"name": "Adviesbureau Buro Strategie B.V.", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Heijden B.V.", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er N.V.", "city": "<PERSON>"}, {"name": "Advocatenkantoor Mr. <PERSON>", "city": "Rotterdam"}, {"name": "Software Ontwikkelaar CodeSpark B.V.", "city": "Groningen"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Politiepost Breda Zuid", "city": "Breda"}, {"name": "Industrieterrein De Boekelermeer Noord", "city": "Alkmaar"}, {"name": "Opslag<PERSON><PERSON><PERSON><PERSON>al Rotterdam B.V.", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Eurofins Certification B.V.", "city": "Amsterdam"}, {"name": "Adviesbureau Buro Ontwerp N.V.", "city": "Leiden"}, {"name": "Architectenbureau De Boer B.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Utrecht"}, {"name": "Advocatenkantoor Mr. <PERSON> Wit N.V.", "city": "<PERSON>"}, {"name": "Software Ontwikkelaar CodeDynamic N.V.", "city": "Eindhoven"}, {"name": "Ziekenhui<PERSON> G<PERSON>", "city": "Delft"}, {"name": "Politiepost Maastricht Noord", "city": "Maastricht"}, {"name": "Industrieterrein De Hurk Zuid", "city": "Eindhoven"}, {"name": "Opslagbedrijf De Opslagwinkel Utrecht B.V.", "city": "Utrecht"}, {"name": "Milieukeuringsbedrijf Bureau Veritas Nederland B.V.", "city": "Amersfoort"}, {"name": "Adviesbureau Buro Innovatie B.V.", "city": "Amsterdam"}, {"name": "Architectenbureau Van der Wal N.V.", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> B.V.", "city": "Groningen"}, {"name": "Advocatenkantoor Mr. <PERSON> N.V.", "city": "Leiden"}, {"name": "Software Ontwikkelaar CodePulse B.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Politiepost Tilburg Noord", "city": "Tilburg"}, {"name": "Industrieterrein De Wetering Noord", "city": "Utrecht"}, {"name": "Opslagbedrijf De Opslaghal Rotterdam B.V.", "city": "Rotterdam"}, {"name": "Milieukeuringsbedrijf Kiwa Nederland Certification B.V.", "city": "Rijswijk"}, {"name": "AKD N.V.", "city": "Rotterdam"}, {"name": "Ho<PERSON>off N.V.", "city": "Amsterdam"}, {"name": "De Brauw Blackstone Westbroek N.V.", "city": "Amsterdam"}, {"name": "Stibbe N.V.", "city": "Amsterdam"}, {"name": "Pels Rijcken & Droogleever Fortuijn N.V.", "city": "<PERSON>"}, {"name": "NautaDutilh N.V.", "city": "Amsterdam"}, {"name": "Loyens & Loeff N.V.", "city": "Amsterdam"}, {"name": "CMS Derks Star Busmann N.V.", "city": "Utrecht"}, {"name": "Van <PERSON>ne N.V.", "city": "Amsterdam"}, {"name": "Dirkzwager Advocaten & Notarissen N.V.", "city": "Arnhem"}, {"name": "<PERSON>an N.V.", "city": "Amsterdam"}, {"name": "Buren N.V.", "city": "<PERSON>"}, {"name": "Ploum Lodder Princen N.V.", "city": "Rotterdam"}, {"name": "Allen & Overy Nederland N.V.", "city": "Amsterdam"}, {"name": "<PERSON> Nederland B.V.", "city": "Amsterdam"}, {"name": "Hogan Lovells Nederland B.V.", "city": "Amsterdam"}, {"name": "Simmons & Simmons N.V.", "city": "Amsterdam"}, {"name": "Van <PERSON> & Keulen N.V.", "city": "Utrecht"}, {"name": "Wijn & Stael Advocaten N.V.", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>sen N.V.", "city": "Zwolle"}, {"name": "Arcadis Nederland B.V.", "city": "Arnhem"}, {"name": "Royal HaskoningDHV B.V.", "city": "Amersfoort"}, {"name": "Tauw B.V.", "city": "Deventer"}, {"name": "Antea Group Nederland B.V.", "city": "<PERSON><PERSON>er<PERSON>"}, {"name": "Sweco Nederland B.V.", "city": "De Bilt"}, {"name": "Witteveen+Bos Raadgevend Ingenieurs B.V.", "city": "Deventer"}, {"name": "Grontmij Nederland B.V.", "city": "De Bilt"}, {"name": "ATKB Milieu en Water B.V.", "city": "Wageningen"}, {"name": "<PERSON><PERSON> Blauw B.V.", "city": "Wageningen"}, {"name": "Eco Consult B.V.", "city": "Utrecht"}, {"name": "Milieu Service Nederland B.V.", "city": "Amsterdam"}, {"name": "DCMR Milieudienst Rijnmond", "city": "Schiedam"}, {"name": "Omgevingsdienst Noord-Holland Noord", "city": "<PERSON><PERSON>"}, {"name": "Omgevingsdienst Zuid-Holland Zuid", "city": "Dordrecht"}, {"name": "Omgevingsdienst Midden- en West-Brabant", "city": "Tilburg"}, {"name": "Omgevingsdienst Regio Nijmegen", "city": "Nijmegen"}, {"name": "Omgevingsdienst Groningen", "city": "Groningen"}, {"name": "Berenschot B.V.", "city": "Utrecht"}, {"name": "PBL Planbureau voor de Leefomgeving", "city": "<PERSON>"}, {"name": "Movares Nederland B.V.", "city": "Utrecht"}, {"name": "Eversheds Sutherland Nederland B.V.", "city": "Amsterdam"}, {"name": "Hekkelman Advocaten N.V.", "city": "Nijmegen"}, {"name": "<PERSON><PERSON>n", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Gresnigt & Van Ki<PERSON>sluis Advocaten", "city": "Utrecht"}, {"name": "BvdV Advocaten", "city": "Breda"}, {"name": "<PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Croon Advocaten", "city": "Alkmaar"}, {"name": "Her<PERSON><PERSON>s Advocaten", "city": "Breda"}, {"name": "Lexence N.V.", "city": "Amsterdam"}, {"name": "Kneppelhout & Korthals Advocaten", "city": "Rotterdam"}, {"name": "AKD N.V.", "city": "Rotterdam"}, {"name": "Ho<PERSON>off N.V.", "city": "Amsterdam"}, {"name": "Loyens & Loeff N.V.", "city": "Amsterdam"}, {"name": "Pels Rijcken & Droogleever Fortuijn N.V.", "city": "<PERSON>"}, {"name": "Stibbe N.V.", "city": "Amsterdam"}, {"name": "NautaDutilh N.V.", "city": "Amsterdam"}, {"name": "Van <PERSON>ne N.V.", "city": "Amsterdam"}, {"name": "Dirkzwager Advocaten & Notarissen N.V.", "city": "Arnhem"}, {"name": "<PERSON>an N.V.", "city": "Amsterdam"}, {"name": "Ploum Lodder Princen N.V.", "city": "Rotterdam"}, {"name": "Wijn & Stael Advocaten N.V.", "city": "Utrecht"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>sen N.V.", "city": "Zwolle"}, {"name": "Hekkelman Advocaten N.V.", "city": "Nijmegen"}, {"name": "Buren N.V.", "city": "<PERSON>"}, {"name": "Van <PERSON> & Keulen N.V.", "city": "Utrecht"}, {"name": "Arcadis Nederland B.V.", "city": "Arnhem"}, {"name": "Royal HaskoningDHV B.V.", "city": "Amersfoort"}, {"name": "Tauw B.V.", "city": "Deventer"}, {"name": "Antea Group Nederland B.V.", "city": "<PERSON><PERSON>er<PERSON>"}, {"name": "Sweco Nederland B.V.", "city": "De Bilt"}, {"name": "Witteveen+Bos Raadgevend Ingenieurs B.V.", "city": "Deventer"}, {"name": "Grontmij Nederland B.V.", "city": "De Bilt"}, {"name": "ATKB Milieu en Water B.V.", "city": "Wageningen"}, {"name": "<PERSON><PERSON> Blauw B.V.", "city": "Wageningen"}, {"name": "Eco Consult B.V.", "city": "Utrecht"}, {"name": "Milieu Service Nederland B.V.", "city": "Amsterdam"}, {"name": "DCMR Milieudienst Rijnmond", "city": "Schiedam"}, {"name": "Omgevingsdienst Noord-Holland Noord", "city": "<PERSON><PERSON>"}, {"name": "Omgevingsdienst Zuid-Holland Zuid", "city": "Dordrecht"}, {"name": "Omgevingsdienst Midden- en West-Brabant", "city": "Tilburg"}, {"name": "Omgevingsdienst Regio Nijmegen", "city": "Nijmegen"}, {"name": "Omgevingsdienst Groningen", "city": "Groningen"}, {"name": "Berenschot B.V.", "city": "Utrecht"}, {"name": "PBL Planbureau voor de Leefomgeving", "city": "<PERSON>"}, {"name": "Movares Nederland B.V.", "city": "Utrecht"}, {"name": "Eversheds Sutherland Nederland B.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON>n", "city": "<PERSON><PERSON><PERSON>"}, {"name": "Gresnigt & Van Ki<PERSON>sluis Advocaten", "city": "Utrecht"}, {"name": "BvdV Advocaten", "city": "Breda"}, {"name": "<PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Croon Advocaten", "city": "Alkmaar"}, {"name": "Her<PERSON><PERSON>s Advocaten", "city": "Breda"}, {"name": "Lexence N.V.", "city": "Amsterdam"}, {"name": "Kneppelhout & Korthals Advocaten", "city": "Rotterdam"}, {"name": "Trip Advocaten & Notarissen", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Van Traa Advocaten N.V.", "city": "Rotterdam"}, {"name": "Köster Advocaten N.V.", "city": "Haarlem"}, {"name": "<PERSON>", "city": "<PERSON>"}, {"name": "Ekelmans Advocaten", "city": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "city": "Rotterdam"}, {"name": "Simmons & Simmons N.V.", "city": "Amsterdam"}, {"name": "Allen & Overy Nederland N.V.", "city": "Amsterdam"}, {"name": "<PERSON> Nederland B.V.", "city": "Amsterdam"}, {"name": "Hogan Lovells Nederland B.V.", "city": "Amsterdam"}, {"name": "<PERSON>t Advocaten | Mediators", "city": "Tilburg"}, {"name": "<PERSON>", "city": "Breda"}, {"name": "Schuitemaker Advocaten", "city": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "city": "Enschede"}, {"name": "Holland Van G<PERSON>n", "city": "Eindhoven"}, {"name": "Brinkhof Advocaten N.V.", "city": "Amsterdam"}, {"name": "KienhuisHoving Advocaten en Notarissen", "city": "Enschede"}, {"name": "<PERSON><PERSON>", "city": "Groningen"}, {"name": "Banning N.V.", "city": "<PERSON>"}, {"name": "Ventoux Advocaten", "city": "Utrecht"}, {"name": "LGL Legal Advocaten", "city": "Alkmaar"}, {"name": "<PERSON>", "city": "Eindhoven"}, {"name": "Smit & De Hart Advocaten", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Hoogenraad & Haak Advocaten", "city": "Amsterdam"}, {"name": "HabrakenRutten Advocaten", "city": "Rotterdam"}, {"name": "Borsboom & Hamm N.V.", "city": "Rotterdam"}, {"name": "Blenheim Advocaten", "city": "Amsterdam"}, {"name": "Leijnse Artz Advocaten", "city": "Rotterdam"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Stek Advocaten B.V.", "city": "Amsterdam"}, {"name": "Van der Steenhoven Advocaten N.V.", "city": "Amsterdam"}, {"name": "<PERSON><PERSON>", "city": "<PERSON>"}, {"name": "Sijben & Partners Advocaten", "city": "Roermond"}, {"name": "Van der Meer & Philipsen Advocaten", "city": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "city": "<PERSON><PERSON>"}, {"name": "Bruggink & Van der Velden Advocaten", "city": "Utrecht"}, {"name": "<PERSON>", "city": "Amsterdam"}, {"name": "<PERSON> Woude Advocaten", "city": "Groningen"}, {"name": "<PERSON> Damstra Advocaten", "city": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Brouwer & Brouwer Advocaten", "city": "Apeldoorn"}, {"name": "<PERSON>an Advocaten & Notarissen", "city": "Groningen"}, {"name": "<PERSON> Putt Advocaten", "city": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON> der <PERSON>", "city": "Bergen op Zoom"}, {"name": "Louwers Advocaten", "city": "Eindhoven"}, {"name": "<PERSON>", "city": "<PERSON>"}, {"name": "EcoScan Milieuadvies B.V.", "city": "Rotterdam"}, {"name": "Milieuadviesbureau Drijver en Partners B.V.", "city": "Amsterdam"}, {"name": "Eco-Result B.V.", "city": "Eindhoven"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Adviesbureau Adb B.V.", "city": "Nijmegen"}, {"name": "<PERSON><PERSON> Boot B.V.", "city": "<PERSON>"}, {"name": "M+P Raadgevende Ingenieurs B.V.", "city": "Vught"}, {"name": "Geofoxx Milieuadvies B.V.", "city": "Tilburg"}, {"name": "Eco Reest B.V.", "city": "<PERSON><PERSON>"}, {"name": "Milieuadviesbureau Van der Meulen B.V.", "city": "Utrecht"}, {"name": "Buro O3 B.V.", "city": "Arnhem"}, {"name": "DGE <PERSON>vies B.V.", "city": "Rotterdam"}]