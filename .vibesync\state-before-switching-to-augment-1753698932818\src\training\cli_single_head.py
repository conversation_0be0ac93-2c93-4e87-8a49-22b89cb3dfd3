#!/usr/bin/env python3
"""
Command-line interface for single-head training.

This script provides a CLI for training individual entity type heads
with configurable filtering strategies and isolated checkpoints.
"""

import sys
import argparse
from pathlib import Path
from typing import Optional, List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.training.hf_config import HFTrainingConfig
from src.training.hf_single_head_trainer import train_single_head
from src.utils.logging_utils import get_logger


def create_parser() -> argparse.ArgumentParser:
    """Create command-line argument parser."""
    parser = argparse.ArgumentParser(
        description="Train single entity type head for RobBERT-2023 NER",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Train PER head with strict filtering
  python -m src.training.cli_single_head --entity-type PER --data data_sets/dutch_ner.json

  # Train LOC head with mixed filtering
  python -m src.training.cli_single_head --entity-type LOC --data data_sets/dutch_ner.json --filtering-strategy mixed

  # Train with custom config
  python -m src.training.cli_single_head --entity-type ORG --data data_sets/dutch_ner.json --config config/training.yaml

  # Resume from checkpoint
  python -m src.training.cli_single_head --entity-type PER --data data_sets/dutch_ner.json --resume-from-checkpoint checkpoints/PER_20250126_143022_v1.0/checkpoint-500
        """
    )
    
    # Required arguments
    parser.add_argument(
        "--entity-type",
        type=str,
        required=True,
        choices=["PER", "LOC", "ORG", "MISC"],
        help="Entity type to train (PER, LOC, ORG, MISC)"
    )
    
    parser.add_argument(
        "--data",
        type=str,
        required=True,
        help="Path to training data (JSON/JSONL file with sentence + entities format)"
    )
    
    # Optional arguments
    parser.add_argument(
        "--filtering-strategy",
        type=str,
        default="strict",
        choices=["strict", "mixed"],
        help="Dataset filtering strategy (default: strict)"
    )
    
    parser.add_argument(
        "--config",
        type=str,
        help="Path to YAML configuration file"
    )
    
    parser.add_argument(
        "--output-dir",
        type=str,
        help="Output directory for checkpoints (auto-generated if not provided)"
    )
    
    parser.add_argument(
        "--resume-from-checkpoint",
        type=str,
        help="Path to checkpoint to resume training from"
    )
    
    # Training parameters (override config)
    parser.add_argument(
        "--epochs",
        type=int,
        help="Number of training epochs"
    )
    
    parser.add_argument(
        "--batch-size",
        type=int,
        help="Training batch size"
    )
    
    parser.add_argument(
        "--learning-rate",
        type=float,
        help="Learning rate"
    )
    
    parser.add_argument(
        "--max-length",
        type=int,
        help="Maximum sequence length"
    )
    
    parser.add_argument(
        "--train-split",
        type=float,
        default=0.8,
        help="Training split ratio (default: 0.8)"
    )
    
    # WandB settings
    parser.add_argument(
        "--wandb-project",
        type=str,
        help="WandB project name"
    )
    
    parser.add_argument(
        "--wandb-entity",
        type=str,
        help="WandB entity name"
    )
    
    parser.add_argument(
        "--run-name",
        type=str,
        help="WandB run name (will be suffixed with entity type)"
    )
    
    parser.add_argument(
        "--run-version",
        type=str,
        help="Training run version (e.g., v1.0, v2.1)"
    )
    
    # Advanced options
    parser.add_argument(
        "--test-mode",
        action="store_true",
        help="Enable test mode (reduced epochs, sample limit, no WandB)"
    )
    
    parser.add_argument(
        "--use-class-weights",
        action="store_true",
        help="Enable class weight calculation for imbalanced datasets"
    )
    
    parser.add_argument(
        "--early-stopping-patience",
        type=int,
        help="Early stopping patience (epochs)"
    )
    
    parser.add_argument(
        "--seed",
        type=int,
        help="Random seed for reproducibility"
    )
    
    # Hardware options
    parser.add_argument(
        "--no-gpu",
        action="store_true",
        help="Disable GPU usage (force CPU training)"
    )
    
    parser.add_argument(
        "--fp16",
        action="store_true",
        help="Enable mixed precision training (FP16)"
    )
    
    return parser


def validate_arguments(args: argparse.Namespace) -> None:
    """Validate command-line arguments."""
    # Check data file exists
    data_path = Path(args.data)
    if not data_path.exists():
        raise FileNotFoundError(f"Data file not found: {args.data}")
    
    # Check config file exists if provided
    if args.config:
        config_path = Path(args.config)
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {args.config}")
    
    # Check checkpoint exists if provided
    if args.resume_from_checkpoint:
        checkpoint_path = Path(args.resume_from_checkpoint)
        if not checkpoint_path.exists():
            raise FileNotFoundError(f"Checkpoint not found: {args.resume_from_checkpoint}")
    
    # Validate train split
    if not 0.0 < args.train_split < 1.0:
        raise ValueError(f"Train split must be between 0.0 and 1.0, got: {args.train_split}")
    
    # Validate numeric parameters
    if args.epochs is not None and args.epochs <= 0:
        raise ValueError(f"Epochs must be positive, got: {args.epochs}")
    
    if args.batch_size is not None and args.batch_size <= 0:
        raise ValueError(f"Batch size must be positive, got: {args.batch_size}")
    
    if args.learning_rate is not None and args.learning_rate <= 0:
        raise ValueError(f"Learning rate must be positive, got: {args.learning_rate}")
    
    if args.max_length is not None and args.max_length <= 0:
        raise ValueError(f"Max length must be positive, got: {args.max_length}")


def create_config_from_args(args: argparse.Namespace) -> HFTrainingConfig:
    """Create training configuration from command-line arguments."""
    # Load base configuration
    if args.config:
        config = HFTrainingConfig.from_yaml(args.config)
    else:
        config = HFTrainingConfig()
    
    # Override with command-line arguments
    if args.epochs is not None:
        config.epochs = args.epochs
    
    if args.batch_size is not None:
        config.batch_size = args.batch_size
    
    if args.learning_rate is not None:
        config.learning_rate = args.learning_rate
    
    if args.max_length is not None:
        config.max_length = args.max_length
    
    if args.wandb_project is not None:
        config.wandb_project = args.wandb_project
    
    if args.wandb_entity is not None:
        config.wandb_entity = args.wandb_entity
    
    if args.run_name is not None:
        config.run_name = args.run_name
    
    if args.run_version is not None:
        config.run_version = args.run_version
    
    if args.test_mode:
        config.test_mode = True
        config.test_epochs = 1
        config.test_sample_limit = 50
        config.test_disable_wandb = True
    
    if args.use_class_weights:
        config.use_class_weights = True
    
    if args.early_stopping_patience is not None:
        config.early_stopping_patience = args.early_stopping_patience
    
    if args.seed is not None:
        config.seed = args.seed
    
    if args.no_gpu:
        config.use_gpu = False
        config.fp16 = False
    
    if args.fp16:
        config.fp16 = True
    
    return config


def main():
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    # Setup logging
    logger = get_logger("cli_single_head")
    
    try:
        # Validate arguments
        validate_arguments(args)
        
        # Create configuration
        config = create_config_from_args(args)
        
        # Log training setup
        logger.logger.info("Starting single-head training with configuration:")
        logger.logger.info(f"  - Entity type: {args.entity_type}")
        logger.logger.info(f"  - Data path: {args.data}")
        logger.logger.info(f"  - Filtering strategy: {args.filtering_strategy}")
        logger.logger.info(f"  - Train split: {args.train_split}")
        logger.logger.info(f"  - Epochs: {config.epochs}")
        logger.logger.info(f"  - Batch size: {config.batch_size}")
        logger.logger.info(f"  - Learning rate: {config.learning_rate}")
        logger.logger.info(f"  - Max length: {config.max_length}")
        logger.logger.info(f"  - Test mode: {config.test_mode}")
        logger.logger.info(f"  - Use GPU: {config.use_gpu}")
        logger.logger.info(f"  - Mixed precision: {config.fp16}")
        
        if args.output_dir:
            logger.logger.info(f"  - Output directory: {args.output_dir}")
        
        if args.resume_from_checkpoint:
            logger.logger.info(f"  - Resume from: {args.resume_from_checkpoint}")
        
        # Run training
        results = train_single_head(
            entity_type=args.entity_type,
            data_path=args.data,
            filtering_strategy=args.filtering_strategy,
            config=config,
            output_dir=args.output_dir,
            resume_from_checkpoint=args.resume_from_checkpoint
        )
        
        # Log success
        logger.logger.info("Single-head training completed successfully!")
        logger.logger.info(f"Model saved to: {results['output_dir']}")
        logger.logger.info(f"WandB run: {results.get('wandb_run_name', 'N/A')}")
        
        # Print final metrics
        if "eval_result" in results:
            print("\nFinal Evaluation Metrics:")
            for key, value in results["eval_result"].items():
                if isinstance(value, (int, float)):
                    print(f"  {key}: {value:.4f}")
        
        return 0
        
    except KeyboardInterrupt:
        logger.logger.info("Training interrupted by user")
        return 1
        
    except Exception as e:
        logger.logger.error(f"Training failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())