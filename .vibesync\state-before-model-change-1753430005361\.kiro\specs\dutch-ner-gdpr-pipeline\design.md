# Design Document

## Overview

The Dutch NER GDPR Pipeline is a comprehensive on-premises solution that extends the existing multi-head BERTje architecture to support an expanded entity set with GDPR compliance checking. The system implements a fully reproducible pipeline from data collection to deployment, optimized for CPU execution while maintaining high accuracy for Dutch language processing.

The design builds upon the existing BERTje foundation but significantly expands the entity recognition capabilities and adds sophisticated compliance checking mechanisms. The pipeline ensures complete reproducibility through automated data scraping, dataset construction, model training, evaluation, and quantization processes.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Data Pipeline"
        DS[Data Scraper] --> DP[Data Processor]
        DP --> DB[Dataset Builder]
        DB --> DV[Data Validator]
    end
    
    subgraph "Model Architecture"
        BE[BERTje Encoder] --> MH[Multi-Head Layer]
        MH --> NH[NER Head]
        MH --> CH[Compliance Head]
        MH --> LH[Label Head]
        MH --> RH[Reason Head]
    end
    
    subgraph "Training Pipeline"
        TR[Trainer] --> EV[Evaluator]
        EV --> QU[Quantizer]
        QU --> MO[Model Optimizer]
    end
    
    subgraph "Inference Layer"
        CLI[CLI Interface]
        API[FastAPI Server]
        CLI --> IE[Inference Engine]
        API --> IE
        IE --> BE
    end
    
    subgraph "Compliance System"
        GC[GDPR Checker] --> LS[Label Suggester]
        LS --> LB[Legal Basis Recommender]
    end
    
    DS --> TR
    QU --> IE
    NH --> GC
```

### Extended Entity Architecture

The system supports a comprehensive Dutch entity set with hybrid recognition approaches:

- **ML-Based Entities**: PER (Person), LOC (Location), ORG (Organization), ADDRESS (full addresses), PHONE (Dutch phone formats), MONEY (Dutch currency expressions)
- **Regex-Based Entities**: EMAIL (X@Y.Z pattern), POSTCODE (XXXX YY/XXXXYY format), IBAN (Dutch/EU format validation)
- **Hybrid Validation**: ML predictions validated against regex patterns for structured entities

### Character Support Architecture

The system ensures comprehensive character recognition:

- **Latin Base Characters**: a-z, A-Z (standard ASCII range)
- **Latin Diacritics**: ë, ï, é, è, ö, ê, ü, ç, à, û, î, ñ, ä, ô
- **Special Characters**: !, @, #, $, %, ^, &, *, (, ), -, _, +, /, =, :, ;, ", ', ,, ., ?, \, €
- **Tokenizer Validation**: Pre-training verification of character coverage in BERTje tokenizer

### GDPR Compliance Architecture

```mermaid
graph LR
    subgraph "Compliance Pipeline"
        NE[Named Entities] --> GR[GDPR Rules Engine]
        GR --> CV[Compliance Validator]
        CV --> LS[Label Suggester]
        CV --> LB[Legal Basis Recommender]
        
        subgraph "Rule Categories"
            PR[Personal Data Rules]
            SR[Sensitive Data Rules]
            CR[Consent Rules]
            LR[Legitimate Interest Rules]
        end
        
        GR --> PR
        GR --> SR
        GR --> CR
        GR --> LR
    end
```

## Components and Interfaces

### 1. Data Collection and Processing

#### Data Scraper (`src/data/scrapers/`)
```python
class DutchDataScraper:
    """Scrapes Dutch text data from configurable sources."""
    
    def scrape_news_sources(self) -> List[Document]
    def scrape_government_data(self) -> List[Document]
    def scrape_legal_documents(self) -> List[Document]
    def validate_scraped_data(self, documents: List[Document]) -> bool
```

#### Dataset Builder (`src/data/dataset_builder.py`)
```python
class ExtendedDatasetBuilder:
    """Builds training datasets with extended entity annotations."""
    
    def build_ner_dataset(self, raw_data: List[Document]) -> Dataset
    def build_compliance_dataset(self, annotated_data: List[Document]) -> Dataset
    def create_balanced_splits(self, dataset: Dataset) -> Dict[str, Dataset]
    def validate_dataset_quality(self, dataset: Dataset) -> QualityMetrics
```

### 2. Extended Model Architecture

#### Enhanced Multi-Head Model (`src/models/extended_multitask_bertje.py`)
```python
class ExtendedMultiTaskBERTje(MultiTaskBERTje):
    """Extended BERTje with additional entity types and compliance checking."""
    
    def __init__(self, config: ModelConfig):
        super().__init__()
        self.extended_ner_head = ExtendedNERHead(config.extended_entities)
        self.compliance_head = GDPRComplianceHead(config.compliance)
        self.label_suggestion_head = LabelSuggestionHead(config.labels)
        self.legal_basis_head = LegalBasisHead(config.legal_bases)
```

#### Extended NER Head (`src/heads/extended_ner_head.py`)
```python
class ExtendedNERHead(nn.Module):
    """NER head supporting extended Dutch entity set."""
    
    ENTITY_LABELS = {
        'PER': 0, 'LOC': 1, 'ORG': 2, 'ADDRESS': 3, 'POSTCODE': 4,
        'EMAIL': 5, 'PHONE': 6, 'IBAN': 7, 'MONEY': 8, 'O': 9
    }
    
    def __init__(self, hidden_size: int, num_labels: int = 19):  # BIO tagging
        super().__init__()
        self.classifier = nn.Linear(hidden_size, num_labels)
        self.dropout = nn.Dropout(0.1)
```

#### GDPR Compliance Head (`src/heads/gdpr_compliance_head.py`)
```python
class GDPRComplianceHead(nn.Module):
    """Binary classification for GDPR compliance checking."""
    
    def __init__(self, hidden_size: int):
        super().__init__()
        self.compliance_classifier = nn.Linear(hidden_size, 2)  # Compliant/Non-compliant
        self.confidence_threshold = 0.7
    
    def forward(self, hidden_states, entity_positions=None):
        # Focus on entity-containing sequences
        return self.compliance_classifier(hidden_states)
```

### 3. Character Validation and Regex System

#### Character Validator (`src/validation/character_validator.py`)
```python
class CharacterValidator:
    """Validates BERTje tokenizer character coverage."""
    
    REQUIRED_CHARS = {
        'latin_base': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
        'latin_diacritics': 'ëïéèöêüçàûîñäô',
        'special_chars': '!@#$%^&*()-_+/=:;"\',?.\\€'
    }
    
    def validate_tokenizer_coverage(self, tokenizer) -> ValidationResult
    def test_character_encoding(self, text: str) -> bool
    def generate_character_test_cases(self) -> List[str]
```

#### Regex Entity Recognizer (`src/recognition/regex_recognizer.py`)
```python
class RegexEntityRecognizer:
    """Handles regex-based entity recognition for structured data."""
    
    PATTERNS = {
        'EMAIL': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
        'POSTCODE': r'\b\d{4}\s?[A-Z]{2}\b',
        'IBAN': r'\b[A-Z]{2}\d{2}[A-Z0-9]{4}\d{7}([A-Z0-9]?){0,16}\b'
    }
    
    def recognize_entities(self, text: str) -> List[RegexEntity]
    def validate_ml_predictions(self, ml_entities: List[Entity]) -> List[Entity]
    def get_pattern_confidence(self, entity_type: str, text: str) -> float
```

#### Whitelist/Blacklist Manager (`src/filtering/word_filter.py`)
```python
class WordFilter:
    """Manages whitelist/blacklist filtering with configurable matching."""
    
    def __init__(self, whitelist_path: str = None, blacklist_path: str = None):
        self.whitelist = self.load_wordlist(whitelist_path or 'whitelist.json')
        self.blacklist = self.load_wordlist(blacklist_path or 'blacklist.json')
    
    def should_exclude_entity(self, entity: Entity, whole_word_only: bool = True) -> bool
    def apply_custom_labels(self, entity: Entity, custom_labels: Dict[str, str]) -> Entity
    def filter_entities(self, entities: List[Entity], cli_params: FilterParams) -> List[Entity]
```

### 4. GDPR Compliance System

#### GDPR Rules Engine (`src/compliance/gdpr_engine.py`)
```python
class GDPRRulesEngine:
    """Implements GDPR/AVG compliance rules for Dutch entities."""
    
    PERSONAL_DATA_ENTITIES = ['PER', 'EMAIL', 'PHONE', 'IBAN', 'ADDRESS']
    SENSITIVE_DATA_ENTITIES = ['PER']  # Context-dependent
    
    def check_compliance(self, entities: List[Entity], context: str) -> ComplianceResult
    def suggest_redaction_label(self, entity: Entity) -> str
    def recommend_legal_basis(self, entity: Entity, context: str) -> LegalBasis
    def assess_existing_labels(self, document: RedactedDocument) -> AssessmentResult
```

#### Label Suggester (`src/compliance/label_suggester.py`)
```python
class LabelSuggester:
    """Suggests appropriate redaction labels for GDPR compliance."""
    
    REDACTION_LABELS = {
        'PER': '[PERSOON]',
        'EMAIL': '[EMAIL]',
        'PHONE': '[TELEFOON]',
        'IBAN': '[BANKREKENING]',
        'ADDRESS': '[ADRES]',
        'POSTCODE': '[POSTCODE]'
    }
    
    def suggest_label(self, entity: Entity) -> str
    def validate_existing_label(self, entity: Entity, current_label: str) -> bool
```

### 4. Training and Evaluation Pipeline

#### Extended Trainer (`src/training/extended_trainer.py`)
```python
class ExtendedMultiTaskTrainer:
    """Trainer for extended multi-task model with weighted loss."""
    
    def __init__(self, model: ExtendedMultiTaskBERTje, config: TrainingConfig):
        self.model = model
        self.loss_weights = {
            'extended_ner': 1.0,
            'compliance': 2.0,  # Higher weight for compliance
            'label_suggestion': 1.5,
            'legal_basis': 1.5
        }
    
    def train_epoch(self, dataloader: DataLoader) -> Dict[str, float]
    def evaluate_model(self, eval_dataloader: DataLoader) -> EvaluationMetrics
```

#### Model Quantizer (`src/optimization/quantizer.py`)
```python
class CPUOptimizer:
    """Optimizes model for CPU inference through quantization."""
    
    def quantize_model(self, model: ExtendedMultiTaskBERTje) -> torch.jit.ScriptModule
    def optimize_for_cpu(self, model: torch.jit.ScriptModule) -> torch.jit.ScriptModule
    def benchmark_performance(self, model: torch.jit.ScriptModule) -> PerformanceMetrics
```

### 5. Inference Interfaces

#### Enhanced CLI (`src/inference/extended_cli.py`)
```python
class ExtendedCLI:
    """Command-line interface for extended NER and compliance checking."""
    
    def process_text(self, text: str, tasks: List[str]) -> ProcessingResult
    def process_file(self, file_path: str, output_path: str) -> None
    def batch_process(self, input_dir: str, output_dir: str) -> BatchResult
    def compliance_check(self, text: str) -> ComplianceReport
    def assess_redacted_document(self, document_path: str) -> AssessmentResult
    def apply_word_filters(self, entities: List[Entity], filter_params: FilterParams) -> List[Entity]
    
    # CLI parameter handling
    def parse_whitelist_params(self, whitelist_args: List[str]) -> List[WordlistEntry]
    def parse_blacklist_params(self, blacklist_args: List[str]) -> List[WordlistEntry]
    def load_wordlist_files(self, whitelist_file: str, blacklist_file: str) -> FilterParams
```

#### Document Assessor (`src/assessment/document_assessor.py`)
```python
class DocumentAssessor:
    """Assesses pre-labeled documents for GDPR compliance accuracy."""
    
    def assess_redacted_document(self, document: GDPRComplianceDocument) -> AssessmentResult
    def validate_redaction_labels(self, sections: List[GDPRDocumentSection]) -> List[ValidationError]
    def check_legal_basis_accuracy(self, section: GDPRDocumentSection) -> LegalBasisValidation
    def suggest_improvements(self, document: GDPRComplianceDocument) -> List[ImprovementSuggestion]
    def compare_with_model_predictions(self, original_text: str, redacted_sections: List[GDPRDocumentSection]) -> ComparisonResult
```

#### Enhanced FastAPI (`src/inference/extended_api.py`)
```python
class ExtendedAPI:
    """FastAPI server with extended endpoints."""
    
    @app.post("/predict/extended")
    async def predict_extended(request: ExtendedPredictionRequest)
    
    @app.post("/compliance/check")
    async def check_compliance(request: ComplianceRequest)
    
    @app.post("/compliance/suggest")
    async def suggest_corrections(request: CorrectionRequest)
```

## Data Models

### Core Data Models

```python
@dataclass
class ExtendedEntity:
    """Extended entity with compliance metadata."""
    text: str
    label: str
    start: int
    end: int
    confidence: float
    is_personal_data: bool
    gdpr_category: str
    suggested_redaction: Optional[str] = None
    legal_basis: Optional[str] = None

@dataclass
class GDPRDocumentSection:
    """Individual section in GDPR compliance document."""
    text: str
    original_text: str
    reason: str
    article: str
    section: str
    subsection: str

@dataclass
class GDPRComplianceDocument:
    """Complete GDPR compliance assessment document."""
    document_id: str
    sections: List[GDPRDocumentSection]
    overall_compliance: bool
    assessment_timestamp: str
    model_version: str

@dataclass
class ComplianceResult:
    """GDPR compliance check result."""
    is_compliant: bool
    confidence: float
    violations: List[ComplianceViolation]
    suggestions: List[ComplianceSuggestion]
    gdpr_document: Optional[GDPRComplianceDocument] = None

@dataclass
class ComplianceViolation:
    """Specific GDPR compliance violation."""
    entity: ExtendedEntity
    violation_type: str
    severity: str
    description: str
    suggested_fix: str

@dataclass
class ProcessingResult:
    """Complete processing result with entities and compliance."""
    text: str
    entities: List[ExtendedEntity]
    compliance: ComplianceResult
    processing_time: float
    model_version: str

@dataclass
class WordlistEntry:
    """Entry in whitelist/blacklist with metadata."""
    word: str
    label: Optional[str] = None
    reason: Optional[str] = None
    whole_word_only: bool = True
    custom_redaction: Optional[str] = None

@dataclass
class FilterParams:
    """CLI parameters for word filtering."""
    whitelist_words: List[WordlistEntry] = None
    blacklist_words: List[WordlistEntry] = None
    whitelist_file: Optional[str] = None
    blacklist_file: Optional[str] = None
```

### Configuration Models

```python
@dataclass
class ExtendedModelConfig:
    """Configuration for extended model."""
    base_model_path: str
    extended_entities: Dict[str, int]
    compliance_threshold: float = 0.7
    quantization_enabled: bool = True
    cpu_optimization: bool = True

@dataclass
class ScrapingConfig:
    """Configuration for data scraping."""
    news_sources: List[str]
    government_sources: List[str]
    legal_sources: List[str]
    max_documents_per_source: int = 1000
    quality_threshold: float = 0.8
```

## Error Handling

### Hierarchical Error Handling

```python
class PipelineError(Exception):
    """Base exception for pipeline errors."""
    pass

class DataScrapingError(PipelineError):
    """Errors during data collection."""
    pass

class ModelTrainingError(PipelineError):
    """Errors during model training."""
    pass

class ComplianceError(PipelineError):
    """Errors in compliance checking."""
    pass

class InferenceError(PipelineError):
    """Errors during inference."""
    pass
```

### Error Recovery Strategies

1. **Data Collection Failures**: Retry with exponential backoff, fallback to cached data
2. **Training Failures**: Checkpoint recovery, reduced batch size fallback
3. **Compliance Failures**: Graceful degradation to basic NER
4. **Inference Failures**: Model reload, CPU fallback from GPU

## Testing Strategy

### Test Categories

#### 1. Unit Tests
- Individual component testing (heads, compliance engine, scrapers)
- Mock data testing for reproducibility
- Edge case handling (malformed entities, empty text)

#### 2. Integration Tests
- End-to-end pipeline testing
- Multi-head model integration
- API endpoint testing with real data

#### 3. Compliance Tests
- GDPR rule validation
- Label suggestion accuracy
- Legal basis recommendation correctness

#### 4. Performance Tests
- CPU inference benchmarking
- Memory usage profiling
- Batch processing scalability

#### 5. Reproducibility Tests
- Deterministic training validation
- Cross-platform consistency
- Version compatibility testing

### Test Data Strategy

```python
class TestDataManager:
    """Manages test datasets for comprehensive testing."""
    
    def create_synthetic_dutch_data(self) -> Dataset
    def create_compliance_test_cases(self) -> List[ComplianceTestCase]
    def create_edge_case_scenarios(self) -> List[EdgeCase]
    def validate_test_coverage(self) -> CoverageReport
```

### Evaluation Metrics

#### NER Performance Metrics
- Per-entity F1 scores for all 9 entity types
- Macro and micro-averaged F1 scores
- Confusion matrices for entity classification
- Out-of-vocabulary entity handling

#### Compliance Metrics
- Binary classification accuracy for compliance checking
- Precision/recall for violation detection
- Label suggestion accuracy
- Legal basis recommendation accuracy

#### System Performance Metrics
- Inference latency (CPU vs GPU)
- Memory consumption profiling
- Throughput measurements (texts/second)
- Model size after quantization

## Implementation Phases

### Phase 1: Extended Entity Recognition
- Implement extended NER head with 9 entity types
- Create training data for new entity types
- Validate entity recognition accuracy

### Phase 2: GDPR Compliance System
- Implement compliance checking logic
- Create label suggestion system
- Develop legal basis recommendation

### Phase 3: Reproducible Pipeline
- Implement data scraping infrastructure
- Create automated training pipeline
- Add model quantization and optimization

### Phase 4: Interface Enhancement
- Extend CLI with new capabilities
- Enhance FastAPI with compliance endpoints
- Add comprehensive documentation

### Phase 5: Production Optimization
- Implement CPU optimizations
- Add monitoring and logging
- Create deployment configurations