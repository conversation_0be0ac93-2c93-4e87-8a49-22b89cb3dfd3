#!/usr/bin/env python3
"""
Class Weight Calculation Demo for Dutch NER Data.

This script demonstrates how to compute class weights for imbalanced
Dutch NER datasets using the ClassWeightCalculator utility.
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from datasets import Dataset
from src.training.class_weights import (
    ClassWeightCalculator,
    compute_class_weights,
    compute_per_head_class_weights,
    analyze_dataset_imbalance
)
from src.training.hf_config import HFTrainingConfig


def create_sample_dutch_ner_data() -> List[Dict[str, Any]]:
    """Create sample imbalanced Dutch NER dataset for demonstration."""
    
    # Simulate realistic imbalanced Dutch NER data
    # In real datasets, O labels are much more frequent than entity labels
    examples = []
    
    # Many examples with only O labels (common in real datasets)
    no_entity_sentences = [
        "Dit is een gewone zin zonder entiteiten.",
        "De kat zit op de mat.",
        "Het weer is vandaag erg mooi.",
        "Ik ga naar de winkel om boodschappen te doen.",
        "De auto staat geparkeerd voor het huis.",
        "We hebben gisteren een film gekeken.",
        "Het boek ligt op de tafel.",
        "De kinderen spelen in de tuin.",
        "De trein vertrekt om acht uur.",
        "Het restaurant is vandaag gesloten."
    ]
    
    for i, sentence in enumerate(no_entity_sentences):
        examples.append({
            'id': i,
            'sentence': sentence,
            'entities': [],
            'labels': [0] * 10  # All O labels (simplified for demo)
        })
    
    # Fewer examples with PER entities
    per_examples = [
        {
            'id': 100,
            'sentence': "Jan Jansen woont in Amsterdam.",
            'entities': [{'text': 'Jan Jansen', 'label': 'PER'}],
            'labels': [0, 1, 2, 0, 0, 0, 0, 0, 0, 0]  # O, B-PER, I-PER, O, ...
        },
        {
            'id': 101,
            'sentence': "Marie van der Berg werkt bij het ziekenhuis.",
            'entities': [{'text': 'Marie van der Berg', 'label': 'PER'}],
            'labels': [0, 1, 2, 2, 2, 0, 0, 0, 0, 0]  # O, B-PER, I-PER, I-PER, I-PER, O, ...
        },
        {
            'id': 102,
            'sentence': "Piet de Vries is een bekende schrijver.",
            'entities': [{'text': 'Piet de Vries', 'label': 'PER'}],
            'labels': [0, 1, 2, 2, 0, 0, 0, 0, 0, 0]  # O, B-PER, I-PER, I-PER, O, ...
        }
    ]
    
    examples.extend(per_examples)
    
    # Even fewer examples with LOC entities
    loc_examples = [
        {
            'id': 200,
            'sentence': "Amsterdam is de hoofdstad van Nederland.",
            'entities': [{'text': 'Amsterdam', 'label': 'LOC'}, {'text': 'Nederland', 'label': 'LOC'}],
            'labels': [0, 3, 0, 0, 0, 0, 3, 0, 0, 0]  # O, B-LOC, O, O, O, O, B-LOC, O, ...
        },
        {
            'id': 201,
            'sentence': "Rotterdam heeft een grote haven.",
            'entities': [{'text': 'Rotterdam', 'label': 'LOC'}],
            'labels': [0, 3, 0, 0, 0, 0, 0, 0, 0, 0]  # O, B-LOC, O, ...
        }
    ]
    
    examples.extend(loc_examples)
    
    # Very few examples with ORG entities (most imbalanced)
    org_examples = [
        {
            'id': 300,
            'sentence': "KLM is een Nederlandse luchtvaartmaatschappij.",
            'entities': [{'text': 'KLM', 'label': 'ORG'}],
            'labels': [0, 5, 0, 0, 0, 0, 0, 0, 0, 0]  # O, B-ORG, O, ...
        }
    ]
    
    examples.extend(org_examples)
    
    return examples


def demonstrate_class_weight_calculation():
    """Demonstrate class weight calculation with sample data."""
    
    print("🔍 Class Weight Calculation Demo for Dutch NER")
    print("=" * 60)
    
    # Create sample dataset
    print("\n📊 Creating sample imbalanced Dutch NER dataset...")
    sample_data = create_sample_dutch_ner_data()
    dataset = Dataset.from_list(sample_data)
    
    # Define label scheme
    label_list = ["O", "B-PER", "I-PER", "B-LOC", "I-LOC", "B-ORG", "I-ORG"]
    
    print(f"Dataset size: {len(dataset)} examples")
    print(f"Label scheme: {label_list}")
    
    # Initialize calculator
    calculator = ClassWeightCalculator()
    
    # 1. Analyze dataset imbalance
    print("\n📈 Analyzing dataset label distribution...")
    analysis = calculator.analyze_label_distribution(dataset, label_list)
    
    print(f"Total labels: {analysis['total_labels']}")
    print(f"Unique labels: {analysis['unique_labels']}")
    print(f"Imbalance ratio: {analysis['imbalance_ratio']:.2f}")
    print("\nLabel distribution:")
    for label, count in analysis['label_counts'].items():
        percentage = analysis['label_percentages'][label]
        print(f"  {label}: {count} ({percentage:.1f}%)")
    
    # 2. Compute overall class weights
    print("\n⚖️ Computing overall class weights...")
    overall_weights = calculator.compute_class_weights(
        dataset=dataset,
        label_list=label_list,
        method="balanced"
    )
    
    print("Overall class weights:")
    for label, weight in sorted(overall_weights.items()):
        print(f"  {label}: {weight:.4f}")
    
    # 3. Compute per-head class weights
    print("\n🎯 Computing per-head class weights...")
    per_head_weights = calculator.compute_per_head_class_weights(
        dataset=dataset,
        label_list=label_list,
        method="balanced"
    )
    
    for entity_type, weights in per_head_weights.items():
        print(f"\n{entity_type} head weights:")
        for label, weight in weights.items():
            print(f"  {label}: {weight:.4f}")
    
    # 4. Analyze per-entity-type distributions
    print("\n🔍 Per-entity-type analysis...")
    for entity_type in ["PER", "LOC", "ORG"]:
        print(f"\n{entity_type} entity analysis:")
        entity_analysis = calculator.analyze_label_distribution(
            dataset, label_list, entity_type=entity_type
        )
        
        print(f"  Total labels: {entity_analysis['total_labels']}")
        print(f"  Imbalance ratio: {entity_analysis['imbalance_ratio']:.2f}")
        print("  Label distribution:")
        for label, count in entity_analysis['label_counts'].items():
            percentage = entity_analysis['label_percentages'][label]
            print(f"    {label}: {count} ({percentage:.1f}%)")
    
    # 5. Demonstrate HFTrainingConfig integration
    print("\n⚙️ HFTrainingConfig integration demo...")
    config = HFTrainingConfig(
        use_class_weights=True,
        class_weight_method="balanced",
        per_head_class_weights=True,
        save_class_weights=True
    )
    
    print(f"Class weights enabled: {config.use_class_weights}")
    print(f"Method: {config.class_weight_method}")
    print(f"Per-head weights: {config.per_head_class_weights}")
    
    # Compute weights using config
    computed_weights = config.compute_and_set_class_weights(
        dataset=dataset,
        label_list=label_list
    )
    
    print("\nWeights computed via config:")
    for label, weight in sorted(computed_weights.items()):
        print(f"  {label}: {weight:.4f}")
    
    # 6. Save weights demonstration
    print("\n💾 Saving class weights...")
    output_dir = Path("examples/output")
    output_dir.mkdir(exist_ok=True)
    
    # Save overall weights
    calculator.save_class_weights(
        overall_weights,
        output_dir / "overall_class_weights.json",
        metadata={
            "method": "balanced",
            "dataset_size": len(dataset),
            "label_list": label_list,
            "imbalance_ratio": analysis['imbalance_ratio']
        }
    )
    
    # Save per-head weights
    calculator.save_class_weights(
        per_head_weights,
        output_dir / "per_head_class_weights.json",
        metadata={
            "method": "balanced",
            "dataset_size": len(dataset),
            "entity_types": list(per_head_weights.keys())
        }
    )
    
    print(f"✅ Weights saved to {output_dir}")
    
    # 7. Effectiveness demonstration
    print("\n📊 Class weighting effectiveness:")
    print("Without class weights:")
    print("  - O labels dominate training (high frequency)")
    print("  - Entity labels get insufficient attention")
    print("  - Model biased toward predicting O")
    
    print("\nWith class weights:")
    print("  - Rare entity labels get higher weights")
    print("  - Training focuses more on entity detection")
    print("  - Better balance between precision and recall")
    
    print(f"\nExample weight ratios:")
    o_weight = overall_weights.get("O", 1.0)
    per_weight = overall_weights.get("B-PER", 1.0)
    org_weight = overall_weights.get("B-ORG", 1.0)
    
    print(f"  B-PER is {per_weight/o_weight:.1f}x more important than O")
    print(f"  B-ORG is {org_weight/o_weight:.1f}x more important than O")
    
    print("\n✨ Demo completed successfully!")


def demonstrate_convenience_functions():
    """Demonstrate convenience functions."""
    
    print("\n🚀 Convenience Functions Demo")
    print("=" * 40)
    
    # Create simple dataset
    sample_data = create_sample_dutch_ner_data()[:5]  # Just first 5 examples
    dataset = Dataset.from_list(sample_data)
    label_list = ["O", "B-PER", "I-PER", "B-LOC", "I-LOC"]
    
    # Test convenience functions
    print("\n1. compute_class_weights() function:")
    weights = compute_class_weights(dataset, label_list)
    for label, weight in sorted(weights.items()):
        print(f"   {label}: {weight:.4f}")
    
    print("\n2. compute_per_head_class_weights() function:")
    per_head = compute_per_head_class_weights(dataset, label_list)
    for entity_type, entity_weights in per_head.items():
        print(f"   {entity_type}: {list(entity_weights.keys())}")
    
    print("\n3. analyze_dataset_imbalance() function:")
    analysis = analyze_dataset_imbalance(dataset, label_list)
    print(f"   Imbalance ratio: {analysis['imbalance_ratio']:.2f}")
    print(f"   Most common: {analysis['most_common_label']}")


if __name__ == "__main__":
    try:
        demonstrate_class_weight_calculation()
        demonstrate_convenience_functions()
        
    except Exception as e:
        print(f"\n❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)