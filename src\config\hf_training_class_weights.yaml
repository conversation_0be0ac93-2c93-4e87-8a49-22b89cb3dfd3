# Hugging Face Training Configuration with Class Weights
# Example configuration for handling imbalanced NER datasets

hf_training:
  # Model configuration
  model_name: "${ROBBERT_MODEL_NAME:-DTAI-KULeuven/robbert-2023-dutch-base}"
  
  # Training parameters
  epochs: 5  # More epochs for imbalanced data
  batch_size: 8
  eval_batch_size: 8
  learning_rate: 3.0e-5  # Slightly lower LR for stability
  weight_decay: 0.01
  warmup_steps: 1000  # More warmup for stability
  warmup_ratio: 0.1
  
  # Evaluation and logging
  eval_steps: 200
  logging_steps: 100
  save_steps: 1000
  evaluation_strategy: "steps"
  logging_strategy: "steps"
  save_strategy: "steps"
  
  # Early stopping (more patient for imbalanced data)
  early_stopping_patience: 5
  early_stopping_threshold: 0.0005
  load_best_model_at_end: true
  metric_for_best_model: "eval_f1"
  greater_is_better: true
  
  # Class balancing (enabled with example weights)
  use_class_weights: true
  class_weights:
    "O": 0.3        # Reduce weight for majority class
    "B-PER": 3.0    # Increase weight for minority class
    "I-PER": 2.5    # Increase weight for minority class
  
  # WandB integration
  wandb_project: "${WANDB_PROJECT:-robbert2023-ner-balanced}"
  wandb_entity: "${WANDB_ENTITY:-slippydongle}"
  run_name: null
  wandb_tags:
    - "robbert-2023"
    - "dutch-nlp"
    - "ner"
    - "class-weights"
    - "imbalanced"
  wandb_notes: "Class-weighted training for imbalanced NER dataset"
  report_to: "wandb"
  
  # Hardware optimization
  use_gpu: true
  fp16: true
  bf16: false
  dataloader_num_workers: 2
  dataloader_pin_memory: true
  gradient_accumulation_steps: 2  # Effective batch size = 16
  max_grad_norm: 1.0
  
  # Checkpointing and model management
  save_total_limit: 5  # Keep more checkpoints for analysis
  push_to_hub: false
  hub_model_id: null
  hub_strategy: "every_save"
  
  # Learning rate scheduling (cosine for better convergence)
  lr_scheduler_type: "cosine"
  cosine_schedule_num_cycles: 0.5
  polynomial_decay_power: 1.0
  
  # Advanced training options
  gradient_checkpointing: false
  remove_unused_columns: true
  label_smoothing_factor: 0.1  # Label smoothing for regularization
  optim: "adamw_torch"
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_epsilon: 1.0e-8
  
  # Versioning and experiment tracking
  run_version: "v1.0"
  version_description: "Class-weighted training for imbalanced data"
  experiment_name: "ner_class_weighted"
  
  # Test mode settings
  test_mode: false
  test_sample_limit: 50
  test_epochs: 1
  test_disable_wandb: true
  
  # Output and prediction settings
  output_dir: "checkpoints"
  save_predictions: true
  generate_model_card: true
  prediction_loss_only: false
  
  # Data processing
  max_length: 512
  truncation: true
  padding: "max_length"
  
  # Reproducibility
  seed: 42
  data_seed: null
  
  # Advanced features
  resume_from_checkpoint: null
  ignore_data_skip: false
  ddp_find_unused_parameters: false
  ddp_bucket_cap_mb: null