#!/usr/bin/env python3
"""
Integration test script for migration utilities and compatibility layer.

This script performs comprehensive testing of:
1. Data format migration from JSONL to sentence+entities
2. Checkpoint compatibility validation and migration
3. API endpoint compatibility with migrated models
4. Configuration migration to HF Trainer format
5. End-to-end workflow validation

Usage:
    python scripts/test_migration_integration.py [--test-data-dir] [--output-dir]
"""

import sys
import os
import json
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse
import traceback

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.migration_utils import (
    DataFormatMigrator,
    CheckpointCompatibilityValidator,
    ConfigurationMigrator
)
from src.utils.compatibility_layer import (
    BackwardCompatibilityManager,
    adapt_response_for_compatibility,
    validate_request_for_compatibility,
    ensure_checkpoint_compatibility
)
from src.utils.logging_utils import get_logger


class MigrationIntegrationTester:
    """Comprehensive integration tester for migration utilities."""
    
    def __init__(self, output_dir: Optional[Path] = None):
        self.logger = get_logger("migration_integration_test", level="INFO")
        self.output_dir = output_dir or Path("test_migration_output")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.test_results = {
            "data_migration_tests": [],
            "checkpoint_compatibility_tests": [],
            "config_migration_tests": [],
            "api_compatibility_tests": [],
            "integration_tests": [],
            "summary": {
                "total_tests": 0,
                "passed_tests": 0,
                "failed_tests": 0,
                "warnings": 0
            }
        }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all integration tests."""
        self.logger.logger.info("Starting comprehensive migration integration tests")
        
        try:
            # Test data migration
            self.test_data_format_migration()
            
            # Test checkpoint compatibility
            self.test_checkpoint_compatibility()
            
            # Test configuration migration
            self.test_configuration_migration()
            
            # Test API compatibility
            self.test_api_compatibility()
            
            # Test end-to-end integration
            self.test_end_to_end_integration()
            
            # Generate summary
            self._generate_test_summary()
            
            self.logger.logger.info("All integration tests completed")
            
            return self.test_results
            
        except Exception as e:
            self.logger.logger.error(f"Integration test suite failed: {e}")
            self.logger.logger.error(traceback.format_exc())
            raise
    
    def test_data_format_migration(self):
        """Test data format migration functionality."""
        self.logger.logger.info("Testing data format migration...")
        
        test_cases = [
            {
                "name": "tokenized_jsonl_to_sentence_entities",
                "description": "Convert tokenized JSONL to sentence+entities format",
                "data": [
                    {
                        "tokens": ["Jan", "Jansen", "woont", "in", "Amsterdam"],
                        "labels": ["B-PER", "I-PER", "O", "O", "O"],
                        "id": 1
                    },
                    {
                        "tokens": ["Marie", "de", "Wit", "werkt", "bij", "Google"],
                        "labels": ["B-PER", "I-PER", "I-PER", "O", "O", "O"],
                        "id": 2
                    }
                ]
            },
            {
                "name": "conll_format_to_sentence_entities",
                "description": "Convert CoNLL format to sentence+entities format",
                "data": [
                    {
                        "words": ["Piet", "van", "der", "Berg", "is", "dokter"],
                        "ner_tags": [1, 2, 2, 2, 0, 0],
                        "id": 1
                    }
                ]
            },
            {
                "name": "sentence_entities_validation",
                "description": "Validate existing sentence+entities format",
                "data": [
                    {
                        "id": 1,
                        "sentence": "Lisa Smit studeert aan de universiteit.",
                        "entities": [
                            {"text": "Lisa Smit", "label": "PER", "start": 0, "end": 9}
                        ]
                    }
                ]
            }
        ]
        
        migrator = DataFormatMigrator()
        
        for test_case in test_cases:
            test_result = {
                "test_name": test_case["name"],
                "description": test_case["description"],
                "status": "FAILED",
                "error": None,
                "stats": None,
                "warnings": []
            }
            
            try:
                # Create test input file
                test_dir = self.output_dir / "data_migration_tests" / test_case["name"]
                test_dir.mkdir(parents=True, exist_ok=True)
                
                input_file = test_dir / "input.jsonl"
                with open(input_file, 'w', encoding='utf-8') as f:
                    for record in test_case["data"]:
                        f.write(json.dumps(record) + '\n')
                
                # Run migration
                output_file = test_dir / "output.json"
                stats = migrator.convert_jsonl_to_sentence_entities(input_file, output_file)
                
                # Validate output
                if output_file.exists():
                    with open(output_file, 'r', encoding='utf-8') as f:
                        converted_data = json.load(f)
                    
                    # Basic validation
                    if len(converted_data) > 0:
                        for example in converted_data:
                            if "sentence" not in example or "entities" not in example:
                                raise ValueError("Invalid output format")
                        
                        test_result["status"] = "PASSED"
                        test_result["stats"] = stats
                        
                        if stats["errors"] > 0:
                            test_result["warnings"].append(f"Conversion had {stats['errors']} errors")
                        
                        self.logger.logger.info(f"✓ {test_case['name']}: {stats['converted_examples']} examples converted")
                    else:
                        raise ValueError("No data converted")
                else:
                    raise ValueError("Output file not created")
                
            except Exception as e:
                test_result["error"] = str(e)
                self.logger.logger.error(f"✗ {test_case['name']}: {e}")
            
            self.test_results["data_migration_tests"].append(test_result)
            self._update_summary(test_result)
    
    def test_checkpoint_compatibility(self):
        """Test checkpoint compatibility validation and migration."""
        self.logger.logger.info("Testing checkpoint compatibility...")
        
        # Create mock checkpoints for testing
        test_checkpoints = [
            {
                "name": "hf_trainer_checkpoint",
                "description": "HF Trainer compatible checkpoint",
                "config": {
                    "model_type": "roberta",
                    "num_labels": 3,
                    "id2label": {"0": "O", "1": "B-PER", "2": "I-PER"},
                    "label2id": {"O": 0, "B-PER": 1, "I-PER": 2},
                    "hidden_size": 768,
                    "architectures": ["RobertaForTokenClassification"]
                },
                "has_training_args": True,
                "expected_compatible": True
            },
            {
                "name": "legacy_checkpoint",
                "description": "Legacy format checkpoint",
                "config": {
                    "model_type": "roberta",
                    "num_labels": 3,
                    "hidden_size": 768
                },
                "has_training_args": False,
                "expected_compatible": True
            },
            {
                "name": "incompatible_checkpoint",
                "description": "Incompatible checkpoint",
                "config": {
                    "model_type": "bert",  # Wrong model type
                    "num_labels": 5,      # Wrong number of labels
                    "hidden_size": 512    # Wrong hidden size
                },
                "has_training_args": False,
                "expected_compatible": False
            }
        ]
        
        validator = CheckpointCompatibilityValidator()
        
        for checkpoint_spec in test_checkpoints:
            test_result = {
                "test_name": checkpoint_spec["name"],
                "description": checkpoint_spec["description"],
                "status": "FAILED",
                "error": None,
                "compatibility_report": None,
                "migration_report": None,
                "warnings": []
            }
            
            try:
                # Create mock checkpoint
                checkpoint_dir = self.output_dir / "checkpoint_tests" / checkpoint_spec["name"]
                checkpoint_dir.mkdir(parents=True, exist_ok=True)
                
                # Create config.json
                with open(checkpoint_dir / "config.json", 'w') as f:
                    json.dump(checkpoint_spec["config"], f, indent=2)
                
                # Create dummy model weights
                import torch
                dummy_weights = {
                    "roberta.embeddings.word_embeddings.weight": torch.randn(1000, 768),
                    "classifier.weight": torch.randn(3, 768),
                    "classifier.bias": torch.randn(3)
                }
                torch.save(dummy_weights, checkpoint_dir / "pytorch_model.bin")
                
                # Create training_args.bin if specified
                if checkpoint_spec["has_training_args"]:
                    from unittest.mock import MagicMock
                    mock_training_args = MagicMock()
                    torch.save(mock_training_args, checkpoint_dir / "training_args.bin")
                
                # Test compatibility validation
                compatibility_report = validator.validate_checkpoint_compatibility(checkpoint_dir)
                test_result["compatibility_report"] = compatibility_report
                
                # Check if compatibility matches expectation
                is_compatible = compatibility_report["is_compatible"]
                expected_compatible = checkpoint_spec["expected_compatible"]
                
                if is_compatible == expected_compatible:
                    # Test migration if compatible
                    if is_compatible:
                        migration_dir = checkpoint_dir.parent / f"{checkpoint_spec['name']}_migrated"
                        migration_report = validator.migrate_checkpoint_format(
                            checkpoint_dir, migration_dir, "hf_trainer"
                        )
                        test_result["migration_report"] = migration_report
                        
                        if migration_report["success"]:
                            test_result["status"] = "PASSED"
                            self.logger.logger.info(f"✓ {checkpoint_spec['name']}: Compatible and migrated")
                        else:
                            test_result["warnings"].append("Migration failed despite compatibility")
                    else:
                        test_result["status"] = "PASSED"
                        self.logger.logger.info(f"✓ {checkpoint_spec['name']}: Correctly identified as incompatible")
                else:
                    raise ValueError(f"Compatibility mismatch: expected {expected_compatible}, got {is_compatible}")
                
            except Exception as e:
                test_result["error"] = str(e)
                self.logger.logger.error(f"✗ {checkpoint_spec['name']}: {e}")
            
            self.test_results["checkpoint_compatibility_tests"].append(test_result)
            self._update_summary(test_result)
    
    def test_configuration_migration(self):
        """Test configuration migration functionality."""
        self.logger.logger.info("Testing configuration migration...")
        
        # Sample legacy configuration
        legacy_config = {
            "model": {
                "model_name": "DTAI-KULeuven/robbert-2023-dutch-base",
                "max_length": 512,
                "heads": ["ner"]
            },
            "training": {
                "epochs": 5,
                "batch_size": 16,
                "learning_rate": 2e-5,
                "weight_decay": 0.01,
                "warmup_steps": 1000,
                "max_grad_norm": 1.0,
                "loss_weights": {"ner": 1.0}
            },
            "wandb": {
                "project": "test-project",
                "entity": "test-entity"
            },
            "evaluation": {
                "eval_steps": 200,
                "logging_steps": 100,
                "save_steps": 1000
            }
        }
        
        test_result = {
            "test_name": "legacy_config_migration",
            "description": "Migrate legacy YAML config to HF Trainer format",
            "status": "FAILED",
            "error": None,
            "migration_report": None,
            "warnings": []
        }
        
        try:
            # Create test directory
            config_test_dir = self.output_dir / "config_migration_tests"
            config_test_dir.mkdir(parents=True, exist_ok=True)
            
            # Create legacy config file
            import yaml
            legacy_config_file = config_test_dir / "legacy_config.yaml"
            with open(legacy_config_file, 'w') as f:
                yaml.dump(legacy_config, f, default_flow_style=False)
            
            # Migrate configuration
            migrator = ConfigurationMigrator()
            hf_config_file = config_test_dir / "hf_trainer_config.yaml"
            migration_report = migrator.migrate_training_config(
                legacy_config_file, hf_config_file, "hf_trainer"
            )
            
            test_result["migration_report"] = migration_report
            
            # Validate migrated configuration
            if migration_report["success"] and hf_config_file.exists():
                with open(hf_config_file, 'r') as f:
                    migrated_config = yaml.safe_load(f)
                
                # Check for HF training section
                if "hf_training" in migrated_config:
                    hf_training = migrated_config["hf_training"]
                    
                    # Verify key parameters were migrated
                    expected_mappings = {
                        "epochs": 5,
                        "batch_size": 16,
                        "learning_rate": 2e-5,
                        "wandb_project": "test-project",
                        "wandb_entity": "test-entity"
                    }
                    
                    for key, expected_value in expected_mappings.items():
                        if hf_training.get(key) != expected_value:
                            raise ValueError(f"Parameter {key} not migrated correctly")
                    
                    test_result["status"] = "PASSED"
                    self.logger.logger.info("✓ Configuration migration successful")
                    
                    # Add warnings for unmapped parameters
                    if migration_report.get("warnings"):
                        test_result["warnings"].extend(migration_report["warnings"])
                else:
                    raise ValueError("HF training section not found in migrated config")
            else:
                raise ValueError("Migration failed or output file not created")
                
        except Exception as e:
            test_result["error"] = str(e)
            self.logger.logger.error(f"✗ Configuration migration: {e}")
        
        self.test_results["config_migration_tests"].append(test_result)
        self._update_summary(test_result)
    
    def test_api_compatibility(self):
        """Test API compatibility functions."""
        self.logger.logger.info("Testing API compatibility...")
        
        test_cases = [
            {
                "name": "response_adaptation",
                "description": "Test API response adaptation for backward compatibility",
                "test_func": self._test_response_adaptation
            },
            {
                "name": "request_validation",
                "description": "Test API request validation and legacy task mapping",
                "test_func": self._test_request_validation
            },
            {
                "name": "checkpoint_compatibility_check",
                "description": "Test checkpoint compatibility checking function",
                "test_func": self._test_checkpoint_compatibility_check
            }
        ]
        
        for test_case in test_cases:
            test_result = {
                "test_name": test_case["name"],
                "description": test_case["description"],
                "status": "FAILED",
                "error": None,
                "warnings": []
            }
            
            try:
                test_case["test_func"]()
                test_result["status"] = "PASSED"
                self.logger.logger.info(f"✓ {test_case['name']}: API compatibility test passed")
                
            except Exception as e:
                test_result["error"] = str(e)
                self.logger.logger.error(f"✗ {test_case['name']}: {e}")
            
            self.test_results["api_compatibility_tests"].append(test_result)
            self._update_summary(test_result)
    
    def _test_response_adaptation(self):
        """Test API response adaptation."""
        # Test NER response adaptation
        original_response = {
            "text": "Jan Jansen woont in Amsterdam",
            "results": {
                "ner": [
                    {"text": "Jan Jansen", "label": "PER", "start": 0, "end": 10, "confidence": 0.95}
                ]
            }
        }
        
        adapted_response = adapt_response_for_compatibility(original_response)
        
        # Verify structure
        if "text" not in adapted_response or "results" not in adapted_response:
            raise ValueError("Response structure not maintained")
        
        # Verify NER entities
        ner_entities = adapted_response["results"]["ner"]
        if not isinstance(ner_entities, list) or len(ner_entities) == 0:
            raise ValueError("NER entities not properly adapted")
        
        entity = ner_entities[0]
        required_fields = ["text", "label", "start", "end", "confidence"]
        for field in required_fields:
            if field not in entity:
                raise ValueError(f"Required field {field} missing from entity")
    
    def _test_request_validation(self):
        """Test API request validation."""
        # Test legacy task name mapping
        original_request = {
            "text": "Test text",
            "tasks": ["person_extraction", "entity_recognition", "named_entity"]
        }
        
        validated_request = validate_request_for_compatibility(original_request)
        
        # Verify task mapping
        expected_tasks = ["ner", "ner", "ner"]
        if validated_request["tasks"] != expected_tasks:
            raise ValueError(f"Task mapping failed: expected {expected_tasks}, got {validated_request['tasks']}")
        
        # Test single task string
        single_task_request = {"text": "Test text", "tasks": "person_extraction"}
        validated_single = validate_request_for_compatibility(single_task_request)
        
        if validated_single["tasks"] != "ner":
            raise ValueError("Single task mapping failed")
    
    def _test_checkpoint_compatibility_check(self):
        """Test checkpoint compatibility checking function."""
        # Create temporary mock checkpoint
        with tempfile.TemporaryDirectory() as temp_dir:
            checkpoint_dir = Path(temp_dir) / "test_checkpoint"
            checkpoint_dir.mkdir()
            
            # Create minimal valid checkpoint
            config = {
                "model_type": "roberta",
                "num_labels": 3,
                "architectures": ["RobertaForTokenClassification"]
            }
            with open(checkpoint_dir / "config.json", 'w') as f:
                json.dump(config, f)
            
            import torch
            torch.save({}, checkpoint_dir / "pytorch_model.bin")
            
            # Test compatibility check
            is_compatible = ensure_checkpoint_compatibility(checkpoint_dir)
            
            if not isinstance(is_compatible, bool):
                raise ValueError("Compatibility check should return boolean")
    
    def test_end_to_end_integration(self):
        """Test end-to-end integration workflow."""
        self.logger.logger.info("Testing end-to-end integration...")
        
        test_result = {
            "test_name": "end_to_end_workflow",
            "description": "Complete migration workflow from legacy to HF Trainer",
            "status": "FAILED",
            "error": None,
            "workflow_steps": [],
            "warnings": []
        }
        
        try:
            # Step 1: Create legacy data
            legacy_data = [
                {
                    "tokens": ["Tom", "de", "Jong", "speelt", "voetbal"],
                    "labels": ["B-PER", "I-PER", "I-PER", "O", "O"],
                    "id": 1
                },
                {
                    "tokens": ["Anna", "Bakker", "is", "verpleegster"],
                    "labels": ["B-PER", "I-PER", "O", "O"],
                    "id": 2
                }
            ]
            
            integration_dir = self.output_dir / "integration_test"
            integration_dir.mkdir(parents=True, exist_ok=True)
            
            # Create legacy JSONL file
            legacy_file = integration_dir / "legacy_data.jsonl"
            with open(legacy_file, 'w') as f:
                for record in legacy_data:
                    f.write(json.dumps(record) + '\n')
            
            test_result["workflow_steps"].append("✓ Created legacy data file")
            
            # Step 2: Migrate data format
            migrator = DataFormatMigrator()
            migrated_data_file = integration_dir / "migrated_data.json"
            migration_stats = migrator.convert_jsonl_to_sentence_entities(
                legacy_file, migrated_data_file
            )
            
            if migration_stats["converted_examples"] != 2:
                raise ValueError("Data migration failed")
            
            test_result["workflow_steps"].append("✓ Migrated data format")
            
            # Step 3: Create and migrate configuration
            legacy_config = {
                "training": {"epochs": 3, "batch_size": 8, "learning_rate": 5e-5},
                "wandb": {"project": "integration-test"}
            }
            
            import yaml
            legacy_config_file = integration_dir / "legacy_config.yaml"
            with open(legacy_config_file, 'w') as f:
                yaml.dump(legacy_config, f)
            
            config_migrator = ConfigurationMigrator()
            hf_config_file = integration_dir / "hf_config.yaml"
            config_migration_report = config_migrator.migrate_training_config(
                legacy_config_file, hf_config_file, "hf_trainer"
            )
            
            if not config_migration_report["success"]:
                raise ValueError("Configuration migration failed")
            
            test_result["workflow_steps"].append("✓ Migrated configuration")
            
            # Step 4: Create mock checkpoint and test compatibility
            checkpoint_dir = integration_dir / "mock_checkpoint"
            checkpoint_dir.mkdir()
            
            checkpoint_config = {
                "model_type": "roberta",
                "num_labels": 3,
                "id2label": {"0": "O", "1": "B-PER", "2": "I-PER"},
                "architectures": ["RobertaForTokenClassification"]
            }
            
            with open(checkpoint_dir / "config.json", 'w') as f:
                json.dump(checkpoint_config, f)
            
            import torch
            torch.save({}, checkpoint_dir / "pytorch_model.bin")
            
            # Test checkpoint compatibility
            is_compatible = ensure_checkpoint_compatibility(checkpoint_dir)
            if not is_compatible:
                test_result["warnings"].append("Mock checkpoint not compatible")
            
            test_result["workflow_steps"].append("✓ Validated checkpoint compatibility")
            
            # Step 5: Test API compatibility
            sample_response = {
                "text": "Integration test",
                "results": {"ner": [{"text": "test", "label": "PER", "start": 0, "end": 4, "confidence": 0.9}]}
            }
            
            adapted_response = adapt_response_for_compatibility(sample_response)
            if "results" not in adapted_response or "ner" not in adapted_response["results"]:
                raise ValueError("API compatibility test failed")
            
            test_result["workflow_steps"].append("✓ Validated API compatibility")
            
            test_result["status"] = "PASSED"
            self.logger.logger.info("✓ End-to-end integration test completed successfully")
            
        except Exception as e:
            test_result["error"] = str(e)
            self.logger.logger.error(f"✗ End-to-end integration test failed: {e}")
        
        self.test_results["integration_tests"].append(test_result)
        self._update_summary(test_result)
    
    def _update_summary(self, test_result: Dict[str, Any]):
        """Update test summary statistics."""
        self.test_results["summary"]["total_tests"] += 1
        
        if test_result["status"] == "PASSED":
            self.test_results["summary"]["passed_tests"] += 1
        else:
            self.test_results["summary"]["failed_tests"] += 1
        
        if test_result.get("warnings"):
            self.test_results["summary"]["warnings"] += len(test_result["warnings"])
    
    def _generate_test_summary(self):
        """Generate comprehensive test summary."""
        summary = self.test_results["summary"]
        
        self.logger.logger.info("\n" + "="*60)
        self.logger.logger.info("MIGRATION INTEGRATION TEST SUMMARY")
        self.logger.logger.info("="*60)
        self.logger.logger.info(f"Total tests: {summary['total_tests']}")
        self.logger.logger.info(f"Passed: {summary['passed_tests']}")
        self.logger.logger.info(f"Failed: {summary['failed_tests']}")
        self.logger.logger.info(f"Warnings: {summary['warnings']}")
        
        success_rate = (summary['passed_tests'] / summary['total_tests']) * 100 if summary['total_tests'] > 0 else 0
        self.logger.logger.info(f"Success rate: {success_rate:.1f}%")
        
        # Save detailed results
        results_file = self.output_dir / "integration_test_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        self.logger.logger.info(f"\nDetailed results saved to: {results_file}")
        
        # Create human-readable report
        self._create_test_report()
    
    def _create_test_report(self):
        """Create human-readable test report."""
        summary = self.test_results["summary"]
        
        report_content = f"""# Migration Integration Test Report

## Summary

- **Total Tests**: {summary['total_tests']}
- **Passed**: {summary['passed_tests']}
- **Failed**: {summary['failed_tests']}
- **Warnings**: {summary['warnings']}
- **Success Rate**: {(summary['passed_tests'] / summary['total_tests']) * 100 if summary['total_tests'] > 0 else 0:.1f}%

## Test Categories

### Data Migration Tests
"""
        
        for test in self.test_results["data_migration_tests"]:
            status_icon = "✓" if test["status"] == "PASSED" else "✗"
            report_content += f"- {status_icon} **{test['test_name']}**: {test['description']}\n"
            if test.get("error"):
                report_content += f"  - Error: {test['error']}\n"
            if test.get("warnings"):
                for warning in test["warnings"]:
                    report_content += f"  - Warning: {warning}\n"
        
        report_content += "\n### Checkpoint Compatibility Tests\n"
        for test in self.test_results["checkpoint_compatibility_tests"]:
            status_icon = "✓" if test["status"] == "PASSED" else "✗"
            report_content += f"- {status_icon} **{test['test_name']}**: {test['description']}\n"
            if test.get("error"):
                report_content += f"  - Error: {test['error']}\n"
        
        report_content += "\n### Configuration Migration Tests\n"
        for test in self.test_results["config_migration_tests"]:
            status_icon = "✓" if test["status"] == "PASSED" else "✗"
            report_content += f"- {status_icon} **{test['test_name']}**: {test['description']}\n"
            if test.get("error"):
                report_content += f"  - Error: {test['error']}\n"
        
        report_content += "\n### API Compatibility Tests\n"
        for test in self.test_results["api_compatibility_tests"]:
            status_icon = "✓" if test["status"] == "PASSED" else "✗"
            report_content += f"- {status_icon} **{test['test_name']}**: {test['description']}\n"
            if test.get("error"):
                report_content += f"  - Error: {test['error']}\n"
        
        report_content += "\n### Integration Tests\n"
        for test in self.test_results["integration_tests"]:
            status_icon = "✓" if test["status"] == "PASSED" else "✗"
            report_content += f"- {status_icon} **{test['test_name']}**: {test['description']}\n"
            if test.get("workflow_steps"):
                for step in test["workflow_steps"]:
                    report_content += f"  - {step}\n"
            if test.get("error"):
                report_content += f"  - Error: {test['error']}\n"
        
        report_content += f"""
## Recommendations

Based on the test results:

1. **Data Migration**: {"All data migration tests passed successfully." if all(t["status"] == "PASSED" for t in self.test_results["data_migration_tests"]) else "Some data migration tests failed - review error messages and fix issues before production use."}

2. **Checkpoint Compatibility**: {"All checkpoint compatibility tests passed." if all(t["status"] == "PASSED" for t in self.test_results["checkpoint_compatibility_tests"]) else "Some checkpoint compatibility issues detected - ensure checkpoints are properly migrated."}

3. **Configuration Migration**: {"Configuration migration working correctly." if all(t["status"] == "PASSED" for t in self.test_results["config_migration_tests"]) else "Configuration migration issues detected - review parameter mappings."}

4. **API Compatibility**: {"API compatibility layer functioning properly." if all(t["status"] == "PASSED" for t in self.test_results["api_compatibility_tests"]) else "API compatibility issues detected - test with actual API endpoints."}

5. **Integration**: {"End-to-end integration working correctly." if all(t["status"] == "PASSED" for t in self.test_results["integration_tests"]) else "Integration issues detected - review complete workflow."}

## Next Steps

1. Address any failed tests before proceeding with migration
2. Review warnings and ensure they don't impact your use case
3. Test with your actual data and configurations
4. Validate with existing inference pipelines
5. Update documentation and training procedures

## Files Generated

- `integration_test_results.json`: Detailed test results in JSON format
- Test artifacts in subdirectories for manual inspection
"""
        
        report_file = self.output_dir / "integration_test_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        self.logger.logger.info(f"Test report saved to: {report_file}")


def main():
    """Main function for integration testing."""
    parser = argparse.ArgumentParser(
        description="Run comprehensive migration integration tests"
    )
    parser.add_argument(
        "--output-dir",
        type=Path,
        default=Path("test_migration_output"),
        help="Output directory for test results (default: test_migration_output)"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Initialize tester
    tester = MigrationIntegrationTester(args.output_dir)
    
    try:
        # Run all tests
        results = tester.run_all_tests()
        
        # Determine exit code
        if results["summary"]["failed_tests"] == 0:
            print("\n✓ All integration tests passed!")
            return 0
        else:
            print(f"\n✗ {results['summary']['failed_tests']} tests failed")
            return 1
            
    except Exception as e:
        print(f"\n✗ Integration test suite failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())