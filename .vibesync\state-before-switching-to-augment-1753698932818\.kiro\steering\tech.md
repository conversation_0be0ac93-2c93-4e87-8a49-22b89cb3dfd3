# Technology Stack

## Core Technologies

### Python Environment
- **Python Version**: 3.9-3.11 (strict requirement)
- **Package Manager**: pip with requirements.txt
- **Virtual Environment**: Standard venv recommended

### Machine Learning Stack
- **Framework**: PyTorch 2.3.x
- **Transformers**: Hugging Face Transformers 4.41.x
- **Model**: RobBERT-2023 (`DTAI-KULeuven/robbert-2023-dutch-base`)
- **Datasets**: Hugging Face Datasets 2.20.x
- **Training**: Accelerate 0.29.x for distributed training
- **Evaluation**: Hugging Face Evaluate 0.4.x

### Web Framework & APIs
- **API Framework**: FastAPI 0.111.x
- **ASGI Server**: Uvicorn 0.30.x with standard extras
- **Data Validation**: Pydantic 2.7.x

### Configuration & Data
- **Configuration**: YAML with Pydantic validation
- **Environment Variables**: python-dotenv 1.0.x
- **Data Processing**: scikit-learn 1.3.x

### Testing & Quality
- **Testing Framework**: pytest 8.2.x
- **Code Quality**: Type hints with Pydantic models
- **Model Hub**: huggingface-hub 0.23.x

## Build System & Commands

### Environment Setup
```bash
# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # Linux/macOS
.\venv\Scripts\activate   # Windows

# Install dependencies
pip install -r requirements.txt

# Download model checkpoints
python scripts/fetch_checkpoints.py
```

### Development Commands
```bash
# Run smoke test (validates model loading and basic inference)
python scripts/smoke_test.py

# Run full test suite
pytest tests/ -v

# Run specific test categories
pytest tests/ -m "not slow"  # Skip slow tests
pytest tests/ -m "unit"      # Unit tests only
pytest tests/ -m "integration"  # Integration tests only
```

### Training Commands
```bash
# Train NER head only
python -m src.training.train_multitask \
    --heads ner \
    --epochs 5 \
    --batch-size 8 \
    --learning-rate 2e-5

# Train multiple heads
python -m src.training.train_multitask \
    --heads ner,compliance,topic \
    --epochs 3 \
    --batch-size 16 \
    --output-dir models/custom_checkpoint
```

### Inference Commands
```bash
# Start API server
uvicorn src.inference.api_fastapi:app --host 0.0.0.0 --port 8000 --reload

# Batch processing
python -m src.inference.cli_batch \
    --tasks ner \
    --input data/documents.jsonl \
    --output results/ner_results.jsonl \
    --batch-size 16
```

### Model Validation
```bash
# Enhanced NER testing
python scripts/enhanced_ner_test.py

# Model validation
python scripts/validate_model.py
```

## Architecture Patterns

### Configuration Management
- **Pattern**: Pydantic-based configuration with YAML files
- **Environment Variables**: Support for `${VAR:-default}` syntax
- **Validation**: Strict type checking and validation rules
- **Location**: `src/config/default.yaml` with `src/config/__init__.py` loader

### Model Architecture
- **Pattern**: Multi-head architecture with shared encoder
- **Base Model**: RobBERT encoder with task-specific heads
- **Heads**: Modular head design (NER, compliance, label, reason, topic)
- **Loading**: Support for both pre-trained and custom checkpoints

### Error Handling
- **Pattern**: Hierarchical exception classes
- **Recovery**: Graceful degradation and fallback strategies
- **Logging**: Structured logging with configurable levels

### Testing Strategy
- **Unit Tests**: Mock-based testing for individual components
- **Integration Tests**: End-to-end pipeline testing
- **Fixtures**: Comprehensive pytest fixtures for test data
- **Markers**: Test categorization (unit, integration, slow)

## Development Guidelines

### Code Style
- **Type Hints**: Required for all public functions and methods
- **Docstrings**: Google-style docstrings for all public APIs
- **Imports**: Absolute imports from src package root
- **Error Handling**: Specific exception types with clear messages

### Performance Considerations
- **CPU Optimization**: Default to CPU inference with optional GPU
- **Memory Management**: Batch size configuration for memory constraints
- **Model Quantization**: Support for quantized models for production
- **Caching**: Model loading optimization and checkpoint management

### Dependencies
- **Version Pinning**: Strict version ranges to ensure reproducibility
- **Minimal Dependencies**: Only essential packages included
- **Optional Dependencies**: GPU support and development tools separate
- **Security**: Regular dependency updates and vulnerability scanning