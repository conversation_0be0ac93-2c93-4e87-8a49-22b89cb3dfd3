#!/usr/bin/env python3
"""
Test script for the updated RobBERT-2023 FastAPI inference server.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.inference.api_fastapi import app, extract_ner_entities, create_annotated_text, create_html_highlighted_text
from src.models.multitask_robbert import MultiTaskRobBERT
from src.inference.api_fastapi import NEREntity
import torch

def test_model_loading():
    """Test that the RobBERT model loads correctly."""
    print("Testing model loading...")
    try:
        model = MultiTaskRobBERT.from_pretrained()
        print("✓ Model loaded successfully")
        print(f"✓ Tokenizer type: {type(model.tokenizer)}")
        print(f"✓ Available heads: {list(model.heads.keys())}")
        return model
    except Exception as e:
        print(f"✗ Model loading failed: {e}")
        return None

def test_tokenization(model):
    """Test RobBERT tokenization."""
    print("\nTesting tokenization...")
    test_text = "<PERSON> w<PERSON> in Amsterdam."
    
    try:
        # Test tokenizer
        inputs = model.tokenizer.tokenizer(
            test_text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        )
        
        print(f"✓ Input text: {test_text}")
        print(f"✓ Input IDs shape: {inputs['input_ids'].shape}")
        print(f"✓ Attention mask shape: {inputs['attention_mask'].shape}")
        
        # Test token alignment
        words = test_text.split()
        alignment = model.tokenizer.tokenize_with_alignment(words)
        print(f"✓ Tokens: {alignment.tokens}")
        print(f"✓ Word IDs: {alignment.word_ids}")
        
        return inputs
    except Exception as e:
        print(f"✗ Tokenization failed: {e}")
        return None

def test_model_inference(model, inputs):
    """Test model inference."""
    print("\nTesting model inference...")
    
    try:
        model.eval()
        with torch.no_grad():
            outputs = model(
                input_ids=inputs['input_ids'],
                attention_mask=inputs['attention_mask'],
                heads=['ner']
            )
        
        print(f"✓ Model inference successful")
        print(f"✓ Output keys: {list(outputs.keys())}")
        
        if 'ner' in outputs:
            logits = outputs['ner']['logits']
            print(f"✓ NER logits shape: {logits.shape}")
            predictions = torch.argmax(logits, dim=-1)
            print(f"✓ Predictions shape: {predictions.shape}")
            return outputs
        else:
            print("✗ No NER output found")
            return None
            
    except Exception as e:
        print(f"✗ Model inference failed: {e}")
        return None

def test_entity_extraction(model):
    """Test entity extraction functionality."""
    print("\nTesting entity extraction...")
    
    test_text = "Jan Jansen en Marie de Wit wonen in Amsterdam."
    
    try:
        # Tokenize
        inputs = model.tokenizer.tokenizer(
            test_text,
            return_tensors="pt",
            truncation=True,
            padding=True,
            max_length=512
        )
        
        # Run inference
        model.eval()
        with torch.no_grad():
            outputs = model(
                input_ids=inputs['input_ids'],
                attention_mask=inputs['attention_mask'],
                heads=['ner']
            )
        
        # Extract entities
        logits = outputs['ner']['logits']
        predictions = torch.argmax(logits, dim=-1)
        entities = extract_ner_entities(
            test_text,
            predictions[0],
            model.tokenizer,
            logits[0]
        )
        
        print(f"✓ Test text: {test_text}")
        print(f"✓ Found {len(entities)} entities:")
        for entity in entities:
            print(f"  - {entity.text} ({entity.label}) [{entity.start}:{entity.end}] conf={entity.confidence:.3f}")
        
        return entities
        
    except Exception as e:
        print(f"✗ Entity extraction failed: {e}")
        return None

def test_annotation_functions():
    """Test annotation helper functions."""
    print("\nTesting annotation functions...")
    
    # Create sample entities
    entities = [
        NEREntity(text="Jan Jansen", label="PER", start=0, end=10, confidence=0.95),
        NEREntity(text="Marie de Wit", label="PER", start=14, end=26, confidence=0.88)
    ]
    
    test_text = "Jan Jansen en Marie de Wit wonen in Amsterdam."
    
    try:
        # Test annotated text
        annotated = create_annotated_text(test_text, entities)
        print(f"✓ Annotated text: {annotated}")
        
        # Test HTML output
        html = create_html_highlighted_text(test_text, entities)
        print(f"✓ HTML output generated (length: {len(html)} chars)")
        
        return True
        
    except Exception as e:
        print(f"✗ Annotation functions failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=== RobBERT-2023 API Test Suite ===\n")
    
    # Test model loading
    model = test_model_loading()
    if not model:
        print("\n✗ Cannot proceed without model")
        return False
    
    # Test tokenization
    inputs = test_tokenization(model)
    if not inputs:
        print("\n✗ Cannot proceed without tokenization")
        return False
    
    # Test model inference
    outputs = test_model_inference(model, inputs)
    if not outputs:
        print("\n✗ Cannot proceed without inference")
        return False
    
    # Test entity extraction
    entities = test_entity_extraction(model)
    if entities is None:
        print("\n✗ Entity extraction failed")
        return False
    
    # Test annotation functions
    if not test_annotation_functions():
        print("\n✗ Annotation functions failed")
        return False
    
    print("\n=== All Tests Passed! ===")
    print("✓ RobBERT-2023 model integration successful")
    print("✓ API endpoints should work correctly")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)