hf_training:
  # Model configuration
  model_name: "DTAI-KULeuven/robbert-2023-dutch-base"
  
  # Test mode settings
  test_mode: true
  test_epochs: 1
  test_sample_limit: 50
  test_disable_wandb: true
  
  # Training parameters (reduced for testing)
  epochs: 1
  batch_size: 4
  eval_batch_size: 4
  learning_rate: 5e-5
  weight_decay: 0.01
  warmup_steps: 10
  
  # Evaluation and logging (frequent for testing)
  eval_steps: 10
  logging_steps: 5
  save_steps: 20
  evaluation_strategy: "steps"
  
  # Early stopping (reduced patience)
  early_stopping_patience: 1
  early_stopping_threshold: 0.001
  load_best_model_at_end: true
  metric_for_best_model: "eval_f1"
  
  # Hardware optimization
  use_gpu: true
  fp16: true
  dataloader_num_workers: 1
  dataloader_pin_memory: false
  
  # Checkpointing
  save_total_limit: 2
  output_dir: "test_checkpoints"
  
  # Data processing
  max_length: 256
  
  # Reproducibility
  seed: 42
  
  # Versioning
  run_version: "test"