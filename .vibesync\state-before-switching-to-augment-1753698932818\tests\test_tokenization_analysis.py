"""
Integration tests for tokenization analysis and validation.
"""

import pytest
from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment


class TestTokenizationAnalysis:
    """Test tokenization analysis for Dutch text."""
    
    @pytest.fixture
    def tokenizer(self):
        """Create tokenizer instance for testing."""
        return RobBERTTokenizerWithAlignment()
    
    def test_compound_name_tokenization(self, tokenizer):
        """Test tokenization of compound Dutch names."""
        test_cases = [
            {
                "words": ["Jan-Willem", "van", "der", "Berg"],
                "labels": ["B-PER", "I-PER", "I-PER", "I-PER"],
                "description": "Compound first name with prefixes"
            },
            {
                "words": ["<PERSON>-Claire", "de", "<PERSON>"],
                "labels": ["B-PER", "I-PER", "I-PER"],
                "description": "Compound first name with prefix"
            },
            {
                "words": ["van", "der", "Waals"],
                "labels": ["B-<PERSON><PERSON>", "I-<PERSON><PERSON>", "I-PER"],
                "description": "Name starting with prefixes"
            }
        ]
        
        for case in test_cases:
            alignment = tokenizer.tokenize_with_alignment(case["words"], case["labels"])
            
            # Basic validation
            assert len(alignment.tokens) > 0, f"Failed for: {case['description']}"
            assert len(alignment.token_labels) == len(alignment.tokens)
            assert alignment.original_labels == case["labels"]
            
            # Check that we have proper B-/I- tag handling
            has_b_per = any(label == "B-PER" for label in alignment.token_labels)
            has_i_per = any(label == "I-PER" for label in alignment.token_labels)
            
            if "B-PER" in case["labels"]:
                assert has_b_per, f"Missing B-PER in tokenized labels for: {case['description']}"
            if "I-PER" in case["labels"]:
                assert has_i_per, f"Missing I-PER in tokenized labels for: {case['description']}"
    
    def test_diacritics_and_special_characters(self, tokenizer):
        """Test handling of diacritics and special characters."""
        test_cases = [
            {
                "words": ["José", "María", "González"],
                "labels": ["B-PER", "I-PER", "I-PER"],
                "description": "Spanish names with diacritics"
            },
            {
                "words": ["François", "Müller"],
                "labels": ["B-PER", "I-PER"],
                "description": "Names with French and German diacritics"
            },
            {
                "words": ["café", "naïef", "coördinatie"],
                "labels": ["O", "O", "O"],
                "description": "Dutch words with diacritics"
            }
        ]
        
        for case in test_cases:
            alignment = tokenizer.tokenize_with_alignment(case["words"], case["labels"])
            
            # Should handle diacritics without errors
            assert len(alignment.tokens) > 0, f"Failed for: {case['description']}"
            assert len(alignment.token_labels) == len(alignment.tokens)
            
            # Validate that no errors occurred during tokenization
            validation = tokenizer.validate_alignment(case["words"], case["labels"])
            assert validation["valid"], f"Validation failed for: {case['description']}"
    
    def test_email_and_url_tokenization(self, tokenizer):
        """Test tokenization of emails and URLs."""
        test_cases = [
            {
                "words": ["<EMAIL>", "werkt", "hier"],
                "labels": ["B-PER", "O", "O"],
                "description": "Email address as person identifier"
            },
            {
                "words": ["Bezoek", "www.example.nl", "voor", "info"],
                "labels": ["O", "O", "O", "O"],
                "description": "URL in text"
            },
            {
                "words": ["Contact:", "<EMAIL>"],
                "labels": ["O", "O"],
                "description": "Contact email"
            }
        ]
        
        for case in test_cases:
            alignment = tokenizer.tokenize_with_alignment(case["words"], case["labels"])
            
            # Should handle complex tokens without errors
            assert len(alignment.tokens) > 0, f"Failed for: {case['description']}"
            assert len(alignment.token_labels) == len(alignment.tokens)
            
            # Email/URL will likely be split into multiple subword tokens
            # This is expected behavior for byte-level BPE
            stats = tokenizer.validate_alignment(case["words"], case["labels"])["stats"]
            assert stats["total_tokens"] >= stats["total_words"], f"Token count issue for: {case['description']}"
    
    def test_oov_rate_analysis(self, tokenizer):
        """Test out-of-vocabulary rate analysis."""
        # Test with various Dutch text samples
        test_texts = [
            ["Dit", "is", "een", "Nederlandse", "zin"],
            ["Geachte", "heer", "Jansen", "hartelijk", "dank"],
            ["Amsterdam", "Rotterdam", "Den", "Haag", "Utrecht"],
            ["maandag", "dinsdag", "woensdag", "donderdag", "vrijdag"],
            ["één", "twee", "drie", "vier", "vijf"]
        ]
        
        total_words = 0
        total_tokens = 0
        
        for words in test_texts:
            alignment = tokenizer.tokenize_with_alignment(words)
            
            # Count non-special tokens
            non_special_tokens = sum(1 for wid in alignment.word_ids if wid is not None)
            
            total_words += len(words)
            total_tokens += non_special_tokens
        
        # With byte-level BPE, we should have very low OOV rate
        # The token count should be close to word count for common Dutch words
        token_to_word_ratio = total_tokens / total_words if total_words > 0 else 0
        
        # Ratio should be reasonable (not too high, indicating good tokenization)
        assert 1.0 <= token_to_word_ratio <= 2.0, f"Token-to-word ratio too high: {token_to_word_ratio}"
    
    def test_alignment_validation_comprehensive(self, tokenizer):
        """Comprehensive test of alignment validation."""
        test_case = {
            "words": ["Jan-Willem", "van", "der", "Berg", "woont", "in", "Amsterdam"],
            "labels": ["B-PER", "I-PER", "I-PER", "I-PER", "O", "O", "O"]
        }
        
        validation = tokenizer.validate_alignment(
            test_case["words"], 
            test_case["labels"], 
            verbose=False
        )
        
        assert validation["valid"]
        
        stats = validation["stats"]
        assert stats["total_words"] == len(test_case["words"])
        assert stats["total_tokens"] > 0
        assert stats["special_tokens"] >= 2  # At least CLS and SEP
        
        # Should have some subword splits for compound names
        assert stats["subword_splits"] >= 0
        
        # Should have B- to I- conversions for multi-token entities
        assert stats["b_to_i_conversions"] >= 0
    
    def test_model_input_encoding(self, tokenizer):
        """Test encoding for model input."""
        words = ["Jan", "Jansen", "werkt", "bij", "Google"]
        labels = ["B-PER", "I-PER", "O", "O", "O"]
        
        encoding = tokenizer.encode_for_model(words, labels, max_length=64)
        
        # Check tensor shapes
        assert encoding["input_ids"].shape == (1, 64)
        assert encoding["attention_mask"].shape == (1, 64)
        assert encoding["labels"].shape == (1, 64)
        
        # Check that labels are valid
        label_ids = encoding["labels"].squeeze().tolist()
        assert all(0 <= lid <= 2 for lid in label_ids), "Invalid label IDs found"
        
        # Check that we have some non-zero labels (not all padding)
        assert any(lid > 0 for lid in label_ids), "No entity labels found in encoding"
    
    def test_prediction_decoding(self, tokenizer):
        """Test decoding of model predictions."""
        words = ["Jan", "Jansen", "woont", "in", "Amsterdam"]
        
        # Encode the text
        encoding = tokenizer.encode_for_model(words, max_length=32)
        input_ids = encoding["input_ids"]
        
        # Create mock predictions (simulate model output)
        import torch
        predictions = torch.zeros(1, 32, dtype=torch.long)
        predictions[0, 1] = 1  # B-PER
        predictions[0, 2] = 2  # I-PER
        
        entities = tokenizer.decode_predictions(input_ids, predictions)
        
        # Should return a list of entities
        assert isinstance(entities, list)
        
        # Each entity should have required fields
        for entity in entities:
            assert "text" in entity
            assert "label" in entity
            assert "start_token" in entity
            assert "end_token" in entity
            assert "tokens" in entity
            
            # Label should be valid
            assert entity["label"] in ["PER"], f"Invalid entity label: {entity['label']}"