"""
Loss functions for multi-task training.
"""

import torch
import torch.nn as nn
from typing import Dict, Optional


class MultiTaskLoss(nn.Module):
    """Weighted combination of losses for multiple tasks."""
    
    def __init__(self, loss_weights: Optional[Dict[str, float]] = None):
        super().__init__()
        self.loss_weights = loss_weights or {}
        
        # Default loss functions for each task type
        self.loss_functions = {
            'ner': nn.CrossEntropyLoss(ignore_index=-100),
            'compliance': nn.CrossEntropyLoss(),
            'label': nn.CrossEntropyLoss(),
            'reason': nn.CrossEntropyLoss(),
            'topic': nn.CrossEntropyLoss()
        }
    
    def forward(self, predictions: Dict[str, torch.Tensor], 
                targets: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Compute weighted multi-task loss.
        
        Args:
            predictions: Dict of task predictions
            targets: Dict of task targets
            
        Returns:
            Dict containing individual and total losses
        """
        losses = {}
        total_loss = 0.0
        
        for task_name in predictions:
            if task_name in targets and task_name in self.loss_functions:
                pred = predictions[task_name]
                target = targets[task_name]
                
                # Compute task-specific loss
                if task_name == 'ner':
                    # Token-level classification
                    loss = self.loss_functions[task_name](
                        pred.view(-1, pred.size(-1)),
                        target.view(-1)
                    )
                else:
                    # Sequence-level classification
                    loss = self.loss_functions[task_name](pred, target)
                
                # Apply weight
                weight = self.loss_weights.get(task_name, 1.0)
                weighted_loss = weight * loss
                
                losses[f'{task_name}_loss'] = loss
                losses[f'{task_name}_weighted_loss'] = weighted_loss
                total_loss += weighted_loss
        
        losses['total_loss'] = total_loss
        return losses
    
    def update_weights(self, new_weights: Dict[str, float]):
        """Update loss weights."""
        self.loss_weights.update(new_weights)


class FocalLoss(nn.Module):
    """Focal loss for handling class imbalance."""
    
    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        ce_loss = nn.functional.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss