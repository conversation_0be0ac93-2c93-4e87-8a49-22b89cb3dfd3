# Multi-head RobBERT for Dutch NLP

A production-ready Python project hosting a multi-head RobBERT model for Dutch NLP tasks, featuring Named Entity Recognition (NER) and additional classification heads. Built with clean architecture, proper isolation, and cross-platform compatibility.

## 🚀 Features

- **Multi-task Architecture**: Support for NER, compliance, label, reason, and topic classification
- **RobBERT-2023 Model**: Uses `DTAI-KULeuven/robbert-2023-dutch-base` with byte-level BPE tokenization, 50k vocabulary, and automatic label alignment
- **Production Ready**: FastAPI server, batch processing CLI, comprehensive configuration
- **Cross-platform**: Linux/macOS/Windows support with Python 3.9-3.11
- **GPU/CPU Support**: Automatic device detection and optimization
- **Clean Architecture**: Modular design with separate heads, models, training, and inference components

### 🇳🇱 RobBERT-2023 Advantages

- **Zero OOV Tokens**: Byte-level BPE handles any Unicode character
- **Dutch Compound Names**: Excellent handling of surnames like "<PERSON> <PERSON>", "<PERSON><PERSON><PERSON>"
- **Diacritics Support**: Perfect tokenization of "<PERSON>", "<PERSON>", "<PERSON>"
- **OCR Robustness**: Better handling of scanning artifacts and noisy text
- **Automatic Label Alignment**: Word-level labels automatically aligned to subword tokens
- **Larger Vocabulary**: 50,000 tokens vs 30,000 in BERTje for better coverage

## 📋 Table of Contents

- [Quick Start](#-quick-start)
- [Installation](#-installation)
- [Usage Examples](#-usage-examples)
- [API Reference](#-api-reference)
- [Training Guide](#-training-guide)
- [Configuration](#-configuration)
- [Troubleshooting](#-troubleshooting)
- [Contributing](#-contributing)
- [License](#-license)

## ⚡ Quick Start

1. **Clone and setup environment:**
```bash
git clone <repository-url>
cd robbert-ner
python -m venv venv
source venv/bin/activate  # On Windows: .\venv\Scripts\activate
pip install -r requirements.txt
```

2. **Download model checkpoints:**
```bash
python scripts/fetch_checkpoints.py
```

3. **Run smoke test:**
```bash
python scripts/smoke_test.py
```

4. **Start API server:**
```bash
uvicorn src.inference.api_fastapi:app --host 0.0.0.0 --port 8000 --reload
```

## 🔧 Installation

### Prerequisites

- Python 3.9-3.11
- 4GB+ RAM (8GB+ recommended for training)
- Optional: CUDA-compatible GPU for faster inference/training

### Step-by-step Installation

1. **Create virtual environment:**
```bash
python -m venv venv
```

2. **Activate environment:**
```bash
# Linux/macOS
source venv/bin/activate

# Windows
.\venv\Scripts\activate
```

3. **Upgrade packaging tools:**
```bash
python -m pip install --upgrade pip setuptools wheel
```

4. **Install dependencies:**
```bash
pip install -r requirements.txt
```

5. **Download model checkpoints:**
```bash
python scripts/fetch_checkpoints.py
```

### Environment Variables

Set optional environment variables:

```bash
export ROBBERT_WEIGHTS_DIR="models/robbert2023-per"  # Custom model path
```

## 💡 Usage Examples

### Named Entity Recognition

```python
from src.models.multitask_robbert import MultiTaskRobBERT
from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment
import torch

# Load model with RobBERT-2023
model = MultiTaskRobBERT.from_pretrained()
model.eval()

# Process Dutch text with automatic label alignment
text = "Linda Jansen woont in Amsterdam en werkt bij Google."
tokenizer = RobBERTTokenizerWithAlignment()

# Tokenize with word-level alignment
words = text.split()
alignment = tokenizer.tokenize_with_alignment(words)

# Convert to model inputs
inputs = tokenizer.tokenizer(text, return_tensors="pt", truncation=True, padding=True)

with torch.no_grad():
    outputs = model(input_ids=inputs['input_ids'],
                   attention_mask=inputs['attention_mask'],
                   heads=['ner'])

# Extract entities with improved Dutch handling
ner_logits = outputs['ner']['logits']
predictions = torch.argmax(ner_logits, dim=-1)

# RobBERT-2023 benefits:
# - Better handling of "Linda Jansen" (compound names)
# - Improved tokenization of Dutch text
# - Zero OOV tokens with byte-level BPE
```

### Batch Processing

```bash
# Process JSONL file
python -m src.inference.cli_batch \
    --tasks ner \
    --input data/documents.jsonl \
    --output results/ner_results.jsonl \
    --batch-size 16

# Process CSV file
python -m src.inference.cli_batch \
    --tasks ner,compliance \
    --input data/documents.csv \
    --output results/results.csv \
    --batch-size 32
```

### API Usage

```python
import requests

# Health check
response = requests.get("http://localhost:8000/ping")
print(response.json())

# NER prediction
data = {
    "text": "Linda Jansen woont in Amsterdam.",
    "tasks": ["ner"]
}
response = requests.post("http://localhost:8000/predict", json=data)
print(response.json())
```

### Training Custom Heads

```bash
# Train NER head only
python -m src.training.train_multitask \
    --heads ner \
    --epochs 5 \
    --batch-size 8 \
    --learning-rate 2e-5 \
    --data training_data.jsonl

# Train multiple heads
python -m src.training.train_multitask \
    --heads ner,compliance,topic \
    --epochs 3 \
    --batch-size 16 \
    --output-dir models/custom_checkpoint
```

## 📚 Documentation

### Core Documentation
- [RobBERT-2023 API Guide](README_API_ROBBERT.md) - Complete API documentation with RobBERT-specific features
- [Token Management Guide](README_TOKEN_MANAGEMENT.md) - Adding custom tokens and vocabulary management
- [RobBERT Tokenization Guide](docs/robbert_tokenization_guide.md) - Detailed tokenization and label alignment documentation

### Model Documentation
- [Model Directory Guide](models/README.md) - Overview of model artifacts and checkpoints
- [RobBERT-2023 Model Guide](models/robbert2023-per/README.md) - Specific documentation for Person NER model

### Technical Documentation
- [Performance Baseline](docs/performance_baseline.md) - Performance metrics and benchmarks
- [Checkpoint Management](docs/checkpoint_management.md) - Model checkpoint handling
- [End-to-End Pipeline Validation](docs/end_to_end_pipeline_validation.md) - Pipeline testing and validation

## 📚 API Reference

### FastAPI Endpoints

#### Health Check
```http
GET /ping
```

**Response:**
```json
{
  "status": "healthy",
  "model_loaded": true,
  "available_heads": ["ner", "compliance", "label", "reason", "topic"]
}
```

#### Prediction
```http
POST /predict
```

**Request Body:**
```json
{
  "text": "Linda Jansen woont in Amsterdam.",
  "tasks": ["ner", "compliance"]  // Optional, defaults to all heads
}
```

**Query Parameters:**
- `tasks` (optional): Comma-separated list of tasks (e.g., `?tasks=ner,compliance`)

**Response:**
```json
{
  "text": "Linda Jansen woont in Amsterdam.",
  "results": {
    "ner": [
      {
        "text": "Linda Jansen",
        "label": "PER",
        "start": 0,
        "end": 12,
        "confidence": 0.95
      },
      {
        "text": "Amsterdam",
        "label": "LOC",
        "start": 22,
        "end": 31,
        "confidence": 0.92
      }
    ],
    "compliance": {
      "predicted_class": 0,
      "confidence": 0.87,
      "probabilities": [0.87, 0.13]
    }
  }
}
```

### CLI Commands

#### Training
```bash
python -m src.training.train_multitask [OPTIONS]
```

**Options:**
- `--heads`: List of heads to train (default: ner)
- `--epochs`: Number of training epochs (default: 3)
- `--batch-size`: Training batch size (default: 8)
- `--learning-rate`: Learning rate (default: 2e-5)
- `--output-dir`: Output directory for saved models
- `--config`: Configuration file path
- `--data`: Path to training data (JSONL format)

#### Batch Inference
```bash
python -m src.inference.cli_batch [OPTIONS]
```

**Options:**
- `--tasks`: Tasks to run (default: ner)
- `--input`: Input file path (required)
- `--output`: Output file path (required)
- `--input-format`: Input format (jsonl, csv, txt)
- `--output-format`: Output format (jsonl, csv)
- `--batch-size`: Batch size for processing (default: 16)
- `--model-path`: Custom model path
- `--config`: Configuration file path

## 🎯 Training Guide

### Data Format

Training data should be in JSONL format with the following structure:

```json
{"text": "Linda Jansen woont in Amsterdam.", "ner": ["B-PER", "I-PER", "O", "O", "B-LOC"], "compliance": 0}
{"text": "Google is een technologiebedrijf.", "ner": ["B-ORG", "O", "O", "O"], "compliance": 1}
```

### Training Process

1. **Prepare your data:**
```python
from src.data.dataset_builder import build_redaction_dataset

dataset = build_redaction_dataset("training_data.jsonl")
print(f"Train samples: {len(dataset['train'])}")
print(f"Validation samples: {len(dataset['validation'])}")
```

2. **Configure training:**
Edit `src/config/default.yaml` to adjust training parameters:

```yaml
training:
  epochs: 5
  batch_size: 16
  learning_rate: 2e-5
  loss_weights:
    ner: 1.0
    compliance: 2.0
```

3. **Start training:**
```bash
python -m src.training.train_multitask \
    --heads ner,compliance \
    --data training_data.jsonl \
    --epochs 5 \
    --batch-size 16
```

4. **Monitor progress:**
Training logs and checkpoints are saved to `src/models/checkpoints/<timestamp>/`

### Fine-tuning Tips

- **Learning Rate**: Start with 2e-5, reduce if loss doesn't decrease
- **Batch Size**: Increase for stability, decrease if out of memory
- **Loss Weights**: Adjust based on task importance and data balance
- **Epochs**: Monitor validation loss to avoid overfitting

## ⚙️ Configuration

### Configuration File Structure

The main configuration is in `src/config/default.yaml`:

```yaml
model:
  encoder_weights: "${ROBBERT_WEIGHTS_DIR:-models/robbert2023-per}"
  max_length: 512
  num_labels:
    ner: 9        # CoNLL-2002 NER tags
    compliance: 2
    label: 15
    reason: 10
    topic: 8

training:
  epochs: 3
  batch_size: 8
  learning_rate: 2.0e-5
  warmup_steps: 500
  weight_decay: 0.01

  loss_weights:
    compliance: 2.0
    label: 1.0
    reason: 1.0
    topic: 1.0
    ner: 0.5

inference:
  batch_size: 16
  thresholds:
    compliance: 0.50
    topic: 0.50
    label: 0.40
    reason: 0.45

  api:
    host: "0.0.0.0"
    port: 8000
    workers: 1

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
```

### Environment Variables

- `ROBBERT_WEIGHTS_DIR`: Custom path to model weights
- `CUDA_VISIBLE_DEVICES`: GPU device selection

### Configuration Loading

```python
from src.config import load_config

config = load_config('src/config/default.yaml')
print(f"Model path: {config.model.encoder_weights}")
print(f"NER labels: {config.model.num_labels.ner}")
```

## 🔧 Troubleshooting

### Common Issues

#### Model Loading Issues

**Problem**: `FileNotFoundError: Configuration file not found`
```bash
# Solution: Ensure you're in the project root directory
cd /path/to/robbert-ner
python scripts/smoke_test.py
```

**Problem**: `ImportError: cannot import name 'load_config'`
```bash
# Solution: Install dependencies and check Python path
pip install -r requirements.txt
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

#### Memory Issues

**Problem**: CUDA out of memory during training
```bash
# Solution: Reduce batch size
python -m src.training.train_multitask --batch-size 4
```

**Problem**: CPU memory issues during inference
```bash
# Solution: Reduce inference batch size
python -m src.inference.cli_batch --batch-size 8
```

#### Performance Issues

**Problem**: Slow inference on CPU
- Use smaller batch sizes
- Consider using GPU if available
- Enable CPU optimizations:
```python
import torch
torch.set_num_threads(4)  # Adjust based on your CPU
```

**Problem**: Low NER accuracy
- Ensure you're using the correct pre-trained model
- Check if your text domain matches training data
- Consider fine-tuning on domain-specific data

#### Tokenization Issues

**Problem**: Unexpected token splits with RobBERT-2023
```python
# Debug tokenization
from src.data.tokenizer_utils import RobBERTTokenizerWithAlignment
tokenizer = RobBERTTokenizerWithAlignment()

text = "Jan-Willem van der Berg"
tokens = tokenizer.tokenizer.tokenize(text)
print(f"Tokens: {tokens}")
# Expected: ["Jan", "-", "Willem", "Ġvan", "Ġder", "ĠBerg"]
```

**Problem**: Label alignment errors
```python
# Test alignment
words = ["Jan-Willem", "woont"]
labels = ["B-PER", "O"]

alignment = tokenizer.tokenize_with_alignment(words, labels)
print("Word IDs:", alignment.word_ids)
print("Token labels:", alignment.token_labels)
```

**Problem**: Text too long for tokenizer
```python
# Solution: Split long text
def split_long_text(text, max_length=400):
    words = text.split()
    chunks = [words[i:i+max_length] for i in range(0, len(words), max_length)]
    return [" ".join(chunk) for chunk in chunks]
```

#### API Issues

**Problem**: FastAPI server won't start
```bash
# Check if port is available
netstat -an | grep 8000

# Use different port
uvicorn src.inference.api_fastapi:app --port 8001
```

**Problem**: API returns 503 Service Unavailable
- Check model loading in server logs
- Ensure sufficient memory is available
- Verify model files are downloaded correctly

### Debugging Tips

1. **Enable debug logging:**
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **Check model files:**
```bash
ls -la models/robbert2023-per/
# Should contain: config.json, pytorch_model.bin, tokenizer_config.json, vocab.json
```

3. **Validate configuration:**
```python
from src.config import load_config
config = load_config('src/config/default.yaml')
print(config.model_dump_json(indent=2))
```

4. **Test individual components:**
```python
# Test tokenizer
from transformers import AutoTokenizer
tokenizer = AutoTokenizer.from_pretrained("DTAI-KULeuven/robbert-2023-dutch-base")
tokens = tokenizer("Test text")
print(tokens)

# Test model loading
from src.models.multitask_robbert import MultiTaskRobBERT
model = MultiTaskRobBERT.from_pretrained()
print(f"Available heads: {list(model.heads.keys())}")
```

### Getting Help

- Check the project issues page for known problems
- Review the [RobBERT documentation](https://github.com/iPieter/RobBERT)
- Ensure you're using compatible versions (Python 3.9-3.11)

## 🤝 Contributing

### Development Setup

1. **Clone repository:**
```bash
git clone <repository-url>
cd bertjeNER
```

2. **Setup development environment:**
```bash
python -m venv venv
source venv/bin/activate  # Windows: .\venv\Scripts\activate
pip install -r requirements.txt
```

3. **Run tests:**
```bash
pytest tests/ -v
```

4. **Run smoke test:**
```bash
python scripts/smoke_test.py
```

### Code Style

- Follow PEP 8 style guidelines
- Use type hints where possible
- Add docstrings to all public functions
- Keep functions focused and modular

### Adding New Task Heads

1. **Create head class:**
```python
# src/heads/new_task_head.py
import torch.nn as nn

class NewTaskHead(nn.Module):
    def __init__(self, hidden_size: int, num_labels: int):
        super().__init__()
        self.classifier = nn.Linear(hidden_size, num_labels)

    def forward(self, hidden_states, labels=None):
        logits = self.classifier(hidden_states)
        # Add loss calculation if labels provided
        return {"logits": logits}
```

2. **Update configuration:**
```yaml
# src/config/default.yaml
model:
  num_labels:
    new_task: 5  # Add your task
```

3. **Register in multitask model:**
```python
# src/models/multitask_bertje.py
from ..heads.new_task_head import NewTaskHead

# Add to __init__ method
self.heads['new_task'] = NewTaskHead(
    hidden_size=self.config.hidden_size,
    num_labels=config.model.num_labels.new_task
)
```

### Testing

- Write unit tests for new functionality
- Ensure all existing tests pass
- Test with different input formats and edge cases
- Validate cross-platform compatibility

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### Third-party Licenses

- **BERTje Model**: Apache 2.0 License (University of Groningen)
- **Transformers**: Apache 2.0 License (Hugging Face)
- **PyTorch**: BSD License (Facebook)

## 🙏 Acknowledgments

- [BERTje](https://github.com/wietsedv/bertje) by Wietse de Vries et al. (University of Groningen)
- [Hugging Face Transformers](https://github.com/huggingface/transformers) library
- [FastAPI](https://fastapi.tiangolo.com/) framework
- Google's TensorFlow Research Cloud (TFRC) for supporting BERTje research

## 📊 Model Performance

### Named Entity Recognition (CoNLL-2002)

| Model | F1 Score | Precision | Recall |
|-------|----------|-----------|--------|
| **BERTje** | **90.24** | 90.39 | 90.09 |
| mBERT | 88.61 | 88.94 | 88.29 |
| BERT-NL | 85.05 | 85.32 | 84.78 |
| RobBERT | 84.72 | 84.91 | 84.53 |

### Supported Entity Types

- **PER**: Person names (Linda Jansen, Jan de Wit)
- **LOC**: Locations (Amsterdam, Nederland)
- **ORG**: Organizations (Google, Universiteit van Amsterdam)
- **MISC**: Miscellaneous entities (Dutch, Euro)

## 🔗 Related Projects

- [BERTje Original Repository](https://github.com/wietsedv/bertje)
- [RobBERT](https://github.com/iPieter/RobBERT) - Alternative Dutch BERT
- [spaCy Dutch Models](https://spacy.io/models/nl) - Alternative NLP pipeline

---

**Built with ❤️ for Dutch NLP**